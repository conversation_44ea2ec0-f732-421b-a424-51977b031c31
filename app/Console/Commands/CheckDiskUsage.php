<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Notifications\DiskSpaceLowNotification;
use App\Notifications\DiskSpaceFullNotification;
use App\Models\User;

class CheckDiskUsage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'disk:check
                            {--path= : Specific path to check (if not provided, checks all configured disks)}
                            {--all-disks : Check all mounted filesystems}
                            {--list-disks : List all available disks/mount points}
                            {--exclude= : Comma-separated list of mount points to exclude}
                            {--warning-threshold=80 : Warning threshold percentage}
                            {--critical-threshold=95 : Critical threshold percentage}
                            {--notify-emails= : Comma-separated list of email addresses to notify}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check disk usage for one or multiple disks and send notifications when thresholds are exceeded';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Handle list-disks option
        if ($this->option('list-disks')) {
            $this->listAvailableDisks();
            return 0;
        }

        // Check if monitoring is enabled
        if (!config('disk-monitoring.schedule.enabled', true)) {
            $this->info('Disk monitoring is disabled in configuration');
            return 0;
        }

        // Get configuration values with command line overrides
        $warningThreshold = (int) ($this->option('warning-threshold') ?: config('disk-monitoring.thresholds.warning', 80));
        $criticalThreshold = (int) ($this->option('critical-threshold') ?: config('disk-monitoring.thresholds.critical', 95));
        $notifyEmails = $this->option('notify-emails') ?: config('disk-monitoring.notifications.emails');
        $excludePaths = $this->option('exclude') ? explode(',', $this->option('exclude')) : [];

        // Determine which paths to check
        $pathsToCheck = $this->getPathsToCheck($excludePaths);

        if (empty($pathsToCheck)) {
            $this->error('No valid paths to monitor');
            return 1;
        }

        $this->info('Starting disk usage monitoring...');
        $this->newLine();

        // Check each path
        foreach ($pathsToCheck as $pathConfig) {
            $path = $pathConfig['path'];
            $pathWarning = $pathConfig['warning'] ?? $warningThreshold;
            $pathCritical = $pathConfig['critical'] ?? $criticalThreshold;

            $this->checkPath($path, $pathWarning, $pathCritical, $notifyEmails);
            $this->newLine();
        }

        return 0;
    }

    /**
     * Get the list of paths to check based on options and configuration
     */
    private function getPathsToCheck(array $excludePaths = []): array
    {
        $pathsToCheck = [];

        // If specific path is provided, use only that
        if ($this->option('path')) {
            $pathsToCheck[] = [
                'path' => $this->option('path'),
                'warning' => null,
                'critical' => null
            ];
            return $pathsToCheck;
        }

        // If --all-disks option is used OR configured to monitor all disks
        if ($this->option('all-disks') || config('disk-monitoring.monitor_all_disks', false)) {
            $mountedDisks = $this->getMountedFilesystems();
            $configExcludes = config('disk-monitoring.exclude_paths', []);
            $allExcludes = array_merge($excludePaths, $configExcludes);

            foreach ($mountedDisks as $disk) {
                $mountPoint = $disk['mount_point'];
                $shouldExclude = false;

                // Check if mount point should be excluded (exact match or prefix match)
                foreach ($allExcludes as $exclude) {
                    if ($mountPoint === $exclude || strpos($mountPoint, $exclude . '/') === 0) {
                        $shouldExclude = true;
                        break;
                    }
                }

                if (!$shouldExclude) {
                    $pathsToCheck[] = [
                        'path' => $mountPoint,
                        'warning' => null,
                        'critical' => null,
                        'filesystem' => $disk['filesystem'],
                        'type' => $disk['type']
                    ];
                }
            }
            return $pathsToCheck;
        }

        // Use configured paths
        $defaultPath = config('disk-monitoring.default_path', '/');
        if (!in_array($defaultPath, $excludePaths)) {
            $pathsToCheck[] = [
                'path' => $defaultPath,
                'warning' => null,
                'critical' => null
            ];
        }

        // Add additional configured paths
        $additionalPaths = config('disk-monitoring.additional_paths', []);
        foreach ($additionalPaths as $path => $thresholds) {
            if (!in_array($path, $excludePaths)) {
                $pathsToCheck[] = [
                    'path' => $path,
                    'warning' => $thresholds['warning'] ?? null,
                    'critical' => $thresholds['critical'] ?? null
                ];
            }
        }

        return $pathsToCheck;
    }

    /**
     * Get all mounted filesystems on the server
     */
    private function getMountedFilesystems(): array
    {
        $mountedDisks = [];

        try {
            // Read /proc/mounts to get mounted filesystems
            $mounts = file_get_contents('/proc/mounts');
            if ($mounts === false) {
                return $mountedDisks;
            }

            $lines = explode("\n", trim($mounts));
            foreach ($lines as $line) {
                if (empty($line)) continue;

                $parts = preg_split('/\s+/', $line);
                if (count($parts) < 3) continue;

                $filesystem = $parts[0];
                $mountPoint = $parts[1];
                $fsType = $parts[2];

                // Filter out virtual/special filesystems
                if ($this->shouldIncludeFilesystem($filesystem, $mountPoint, $fsType)) {
                    $mountedDisks[] = [
                        'filesystem' => $filesystem,
                        'mount_point' => $mountPoint,
                        'type' => $fsType
                    ];
                }
            }
        } catch (\Exception $e) {
            $this->warn("Could not read mounted filesystems: " . $e->getMessage());
        }

        return $mountedDisks;
    }

    /**
     * Determine if a filesystem should be included in monitoring
     */
    private function shouldIncludeFilesystem(string $filesystem, string $mountPoint, string $fsType): bool
    {
        // Exclude virtual/special filesystems based on configuration
        $excludeTypes = config('disk-monitoring.disk_detection.exclude_filesystem_types', [
            'proc', 'sysfs', 'devtmpfs', 'devpts', 'tmpfs', 'cgroup', 'cgroup2',
            'pstore', 'bpf', 'tracefs', 'debugfs', 'mqueue', 'hugetlbfs',
            'systemd-1', 'autofs', 'rpc_pipefs', 'nfsd'
        ]);

        if (in_array($fsType, $excludeTypes)) {
            return false;
        }

        // Exclude special mount points based on configuration
        $excludeMountPrefixes = config('disk-monitoring.disk_detection.exclude_mount_prefixes', [
            '/dev', '/proc', '/sys', '/run', '/boot/efi'
        ]);

        foreach ($excludeMountPrefixes as $exclude) {
            if (strpos($mountPoint, $exclude) === 0) {
                return false;
            }
        }

        // Check if directory exists and we can get disk usage
        if (!is_dir($mountPoint)) {
            return false;
        }

        $totalSpace = disk_total_space($mountPoint);
        if ($totalSpace === false) {
            return false;
        }

        // Check minimum disk size if configured
        $minSize = config('disk-monitoring.disk_detection.minimum_disk_size', 0);
        if ($minSize > 0 && $totalSpace < $minSize) {
            return false;
        }

        return true;
    }

    /**
     * List all available disks/mount points
     */
    private function listAvailableDisks(): void
    {
        $this->info('Available disks and mount points:');
        $this->newLine();

        $mountedDisks = $this->getMountedFilesystems();

        if (empty($mountedDisks)) {
            $this->warn('No mounted filesystems found or unable to read mount information');
            return;
        }

        $headers = ['Filesystem', 'Mount Point', 'Type', 'Total Size', 'Used', 'Available', 'Usage %'];
        $rows = [];

        foreach ($mountedDisks as $disk) {
            $diskUsage = $this->getDiskUsage($disk['mount_point']);
            if ($diskUsage) {
                $rows[] = [
                    $disk['filesystem'],
                    $disk['mount_point'],
                    $disk['type'],
                    $diskUsage['total_formatted'],
                    $diskUsage['used_formatted'],
                    $diskUsage['free_formatted'],
                    $diskUsage['usage_percentage'] . '%'
                ];
            }
        }

        $this->table($headers, $rows);
    }

    /**
     * Check disk usage for a specific path
     */
    private function checkPath(string $path, int $warningThreshold, int $criticalThreshold, ?string $notifyEmails): void
    {
        // Get disk usage information
        $diskUsage = $this->getDiskUsage($path);

        if (!$diskUsage) {
            $this->error("Could not retrieve disk usage information for path: {$path}");
            if (config('disk-monitoring.logging.enabled', true)) {
                Log::error("Disk usage check failed for path: {$path}");
            }
            return;
        }

        $usagePercentage = $diskUsage['usage_percentage'];
        $this->info("📁 Checking: {$path}");
        $this->info("💾 Usage: {$usagePercentage}% ({$diskUsage['used_formatted']} / {$diskUsage['total_formatted']})");
        $this->info("💿 Available: {$diskUsage['free_formatted']}");

        // Check thresholds and send notifications
        $this->checkAndNotify($diskUsage, $warningThreshold, $criticalThreshold, $notifyEmails);
    }

    /**
     * Get disk usage information for the specified path
     */
    private function getDiskUsage(string $path): ?array
    {
        if (!is_dir($path)) {
            return null;
        }

        $totalBytes = disk_total_space($path);
        $freeBytes = disk_free_space($path);
        
        if ($totalBytes === false || $freeBytes === false) {
            return null;
        }

        $usedBytes = $totalBytes - $freeBytes;
        $usagePercentage = round(($usedBytes / $totalBytes) * 100, 2);

        return [
            'path' => $path,
            'total_bytes' => $totalBytes,
            'used_bytes' => $usedBytes,
            'free_bytes' => $freeBytes,
            'usage_percentage' => $usagePercentage,
            'total_formatted' => $this->formatBytes($totalBytes),
            'used_formatted' => $this->formatBytes($usedBytes),
            'free_formatted' => $this->formatBytes($freeBytes),
        ];
    }

    /**
     * Check thresholds and send appropriate notifications
     */
    private function checkAndNotify(array $diskUsage, int $warningThreshold, int $criticalThreshold, ?string $notifyEmails): void
    {
        $usagePercentage = $diskUsage['usage_percentage'];
        $path = $diskUsage['path'];

        // Get throttle settings from config and ensure they are integers
        $warningThrottleHours = (int) config('disk-monitoring.notifications.throttle.warning', 2);
        $criticalThrottleHours = (int) config('disk-monitoring.notifications.throttle.critical', 4);

        // Create cache keys for notification throttling (sanitize path for cache key)
        $sanitizedPath = str_replace(['/', '\\', ' '], '_', $path);
        $warningCacheKey = "disk_warning_sent_{$sanitizedPath}";
        $criticalCacheKey = "disk_critical_sent_{$sanitizedPath}";

        // Check critical threshold first
        if ($usagePercentage >= $criticalThreshold) {
            // Only send critical notification if we haven't sent one recently
            if (!Cache::has($criticalCacheKey)) {
                $this->sendNotification(new DiskSpaceFullNotification($diskUsage), $notifyEmails);
                Cache::put($criticalCacheKey, true, now()->addHours($criticalThrottleHours));
                $this->warn("CRITICAL: Disk usage is at {$usagePercentage}% - notification sent");

                if (config('disk-monitoring.logging.enabled', true)) {
                    Log::critical("Disk space critical", [
                        'path' => $path,
                        'usage_percentage' => $usagePercentage,
                        'free_space' => $diskUsage['free_formatted']
                    ]);
                }
            } else {
                $this->warn("CRITICAL: Disk usage is at {$usagePercentage}% - notification throttled");
            }
        }
        // Check warning threshold
        elseif ($usagePercentage >= $warningThreshold) {
            // Only send warning notification if we haven't sent one recently
            if (!Cache::has($warningCacheKey)) {
                $this->sendNotification(new DiskSpaceLowNotification($diskUsage), $notifyEmails);
                Cache::put($warningCacheKey, true, now()->addHours($warningThrottleHours));
                $this->warn("WARNING: Disk usage is at {$usagePercentage}% - notification sent");

                if (config('disk-monitoring.logging.enabled', true)) {
                    Log::warning("Disk space low", [
                        'path' => $path,
                        'usage_percentage' => $usagePercentage,
                        'free_space' => $diskUsage['free_formatted']
                    ]);
                }
            } else {
                $this->warn("WARNING: Disk usage is at {$usagePercentage}% - notification throttled");
            }
        } else {
            // Clear cache keys when usage is below thresholds
            Cache::forget($warningCacheKey);
            Cache::forget($criticalCacheKey);
            $this->info("Disk usage is within normal limits");
        }
    }

    /**
     * Send notification to specified recipients
     */
    private function sendNotification($notification, ?string $notifyEmails): void
    {
        $recipients = [];

        // Add specified email addresses
        if ($notifyEmails) {
            $emails = array_map('trim', explode(',', $notifyEmails));
            foreach ($emails as $email) {
                if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $recipients[] = $email;
                }
            }
        }

        // Fallback to admin users if no specific emails provided and fallback is enabled
        if (empty($recipients) && config('disk-monitoring.notifications.fallback_to_admins', true)) {
            // Check if User model has is_admin column, otherwise use a different approach
            try {
                $adminUsers = User::where('is_admin', true)->get();
                foreach ($adminUsers as $admin) {
                    $recipients[] = $admin->email;
                }
            } catch (\Exception $e) {
                // If is_admin column doesn't exist, try to get first few users as fallback
                $this->warn("Could not find admin users, using first user as fallback");
                $fallbackUser = User::first();
                if ($fallbackUser) {
                    $recipients[] = $fallbackUser->email;
                }
            }
        }

        if (empty($recipients)) {
            $this->error("No recipients configured for disk usage notifications");
            return;
        }

        // Send notifications
        foreach ($recipients as $email) {
            try {
                Notification::route('mail', $email)->notify($notification);
                $this->info("Notification sent to: {$email}");
            } catch (\Exception $e) {
                $this->error("Failed to send notification to {$email}: " . $e->getMessage());
                if (config('disk-monitoring.logging.enabled', true)) {
                    Log::error("Failed to send disk usage notification to {$email}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Format bytes into human readable format
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
