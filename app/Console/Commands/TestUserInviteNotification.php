<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\UserInvitation;
use App\Notifications\UserInvitationNotification;
use Illuminate\Support\Str;

class TestUserInviteNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    // protected $signature = 'app:test-user-invite-notification';
    protected $signature = 'test:user-invite {userId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send UserInvitationNotification to a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = User::find($this->argument('userId'));

        if (!$user) {
            $this->error('User not found.');
            return 1;
        }

        $invitation = UserInvitation::create([
            'user_id' => $user->id,
            'token' => Str::random(40),
        ]);

        $user->notify(new UserInvitationNotification($invitation));

        $this->info("Notification sent to user: {$user->email}");
        return 0;
    }
}
