<?php

function isValidDirectoryName(string $name): bool
{
   // Trim spaces from start and end
   $name = trim($name);

   // Check for empty name or reserved names ('.' and '..')
   if ($name === '' || $name === '.' || $name === '..') {
      return false;
   }

   // Reserved characters across Windows and Unix-like systems
   $invalidChars = '/[\/\\\\\?\%\*\:\|\\"<>\x00-\x1F]/';

   // Check for invalid characters
   if (preg_match($invalidChars, $name)) {
      return false;
   }

   // Enforce a reasonable length (typically 255 max for most filesystems)
   if (strlen($name) > 255) {
      return false;
   }

   return true;
}

function isDirNameTaken(string $name): bool
{
   // Check if the name is already in use
   if (file_exists(config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($name))) {
      return true;
   } else {
      return false;
   }

}
