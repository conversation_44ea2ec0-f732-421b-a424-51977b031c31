<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use App\Models\AccountRequest;
use App\Models\User;
use App\Models\Lab;
use App\Models\UserInvitation;
use Illuminate\Support\Facades\Notification;
use App\Notifications\NewLabCreatedNotification;
use App\Notifications\NewUserCreatedNotification;
use App\Notifications\UserInvitationNotification;
use Illuminate\Support\Str;
use App\Models\CbProject;
use App\Models\BeProject;
use App\Models\Assertion;
use App\Models\Themelink;
use App\Helpers\isDirNameTaken;

class AdminController extends Controller
{
    /** 
     * Account Requests
     */
    public function allRequests()
    {
        $requests = AccountRequest::all()->groupBy('status');
        $pending = $requests->get('pending', collect());
        $approved = $requests->get('approved', collect());
        $denied = $requests->get('denied', collect());
        return view('admin.account_requests.account_requests', compact('pending', 'approved', 'denied'));
    }

    public function singleRequestDetail($requestId)
    {
        $accountRequest = AccountRequest::findOrFail($requestId);
        return view('admin.account_requests.account_request_details', compact('accountRequest'));
    }

    public function approveRequest(Request $request, $requestId)
    {
      
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            'organization' => 'required|string|max:255',
            'pi_name' => 'required|string|max:255',
            'pi_email' => 'required|string|email|max:255|unique:labs,pi_email',
            'notes' => 'nullable|string',
            'directory' => 'required|string|regex:/^[a-zA-Z0-9-_]+$/|unique:labs,directory',
        ]);

        $accountRequest = AccountRequest::findOrFail($requestId);

        if($validated) {
            if($accountRequest->status !== 'pending') {
                return back()->with('error', 'Request has already been processed.');
            }
            if(isDirNameTaken($request->input('directory'))) {
                return back()->with('error', 'Directory name already in use.');
            }

            // Create Lab
            $lab = Lab::create([
                'name' => $request->input('organization'),
                'pi_name' => $request->input('pi_name'),
                'pi_email' => $request->input('pi_email'),
                'organization' => $request->input('organization'),
                'notes' => $request->input('notes'),
                'directory' => $request->input('directory'),
            ]);

            // Create User from Account Request
            $user = User::create([
                'name' => $request->input('name'),
                'email' => $request->input('email'),
                'password' => $accountRequest->password, // Already hashed in model
                'lab_id' => $lab->id,
                'lab_admin' => true,
            ]);

            // Get sudo users for notifications
            $sudos = User::where('sudo', 1)->get();
            Notification::send($sudos, new NewUserCreatedNotification($user));
            Notification::send($sudos, new NewLabCreatedNotification($lab));

            if($lab->pi_email !== $user->email) {
                $pi_user = User::create([
                    'name' => $request->input('pi_name'),
                    'email' => $request->input('pi_email'),
                    'lab_id' => $lab->id,
                    'lab_admin' => true,
                ]);
                // Create an invitation token
                $invitation = UserInvitation::create([
                    'user_id' => $user->id,
                    'token' => Str::random(40),
                ]);
                $pi_user->notify(new UserInvitationNotification($invitation));
                Notification::send($sudos, new NewUserCreatedNotification($pi_user));
            }

            // Change status of Account Request
            $accountRequest->update(['status' => 'approved']);

            // Create legacy lab account files
            $this->createLegacyLabAccountFiles($lab);

            return redirect()->route('admin.accountRequests')->with('status', 'User approved and created successfully.');
        }


        // // Create User from Account Request
        // User::create([
        //     'name' => $accountRequest->name,
        //     'email' => $accountRequest->email,
        //     'password' => $accountRequest->password, // Already hashed in model
        // ]);

        // // Delete the request
        // $accountRequest->delete();

        // return back()->with('success', 'User approved and created successfully.');
    }

    public function denyRequest($requestId) {
        $accountRequest = AccountRequest::findOrFail($requestId);
        $accountRequest->update(['status' => 'denied']);
        return redirect()->route('admin.accountRequests')->with('status', 'Request denied successfully.');
    }

    /**
     * Manually Create a New Account and/or Lab Page/Form
     */
    public function manualCreate()
    {
        $labs = Lab::all();
        return view('admin.account_requests.manual_create_account', compact('labs'));
    }
    /**
     * Manually Create a New Account and/or Lab Store to Database
     */
    public function manualCreateStore(Request $request)
    {

        // Cast checkboxes to boolean
        $request->merge([
            'lab_admin' => $request->has('lab_admin'),
            'sudo' => $request->has('sudo'),
        ]);
        // $request->lab_admin = $request->has('lab_admin');
        // $request->sudo = $request->has('sudo');

        // dd($request->all());
        // Validate the input based on the selected radio option
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
            /**
             * Conditional Switch for Validation 
             * 'new': Creating New Lab for New User
             * 'existing': Selects Existing Lab and Adding New User to Lab
             */
            'lab-choice' => ['required', 'in:new,existing'],
            // Validation for existing lab option
            'lab-id' => ['required_if:lab-choice,existing', 'exists:labs,id'],
            // Validation for creating a new lab
            'lab_name' => 'required_if:lab-choice,new|string|max:255|unique:labs,name',
            'organization' => 'required_if:lab-choice,new|string|max:255',
            'pi_name' => 'required_if:lab-choice,new|string|max:255',
            'pi_email' => 'required_if:lab-choice,new|string|email|max:255|unique:labs,pi_email',
            'directory' => 'required_if:lab-choice,new|string|regex:/^[a-zA-Z0-9-_]+$/|unique:labs,directory',
            'notes' => 'nullable|string',
            // Validation for Lab Admin
            'lab_admin' => 'nullable|boolean',
            // Validation for "sudo" checkbox
            'sudo' => 'nullable|boolean',
        ]);

        // Get sudo users for notifications
        $sudos = User::where('sudo', 1)->get();

        // Determine if user is creating a new lab or joining an existing one
        if ($request->input('lab-choice') === 'new') {
            // Create a new lab
            $lab = Lab::create([
                'name' => $request->lab_name,
                'organization' => $request->organization,
                'pi_name' => $request->pi_name,
                'pi_email' => $request->pi_email,
                'directory' => $request->directory,
                'notes' => $request->notes,
            ]);
            
            Notification::send($sudos, new NewLabCreatedNotification($lab));
            // Assign the lab ID to the user
            $labId = $lab->id;
            // Create legacy lab account files
            $this->createLegacyLabAccountFiles($lab);
        } else {
            // Use the selected existing lab ID
            $labId = $request->input('lab-id');
        }

        // Create the user and associate them with the lab
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'lab_id' => $labId,
            'lab_admin' => $request->lab_admin,
            'sudo' => $request->sudo,
        ]);

        if($user->lab_admin === false) {
            if($user->sudo === true || $request->input('lab-choice') === 'new' && $user->email === $lab->pi_email) {
                $user->lab_admin = true;
                $user->save();
            }
        }
        // Create an invitation token
        $invitation = UserInvitation::create([
            'user_id' => $user->id,
            'token' => Str::random(40),
        ]);
        $user->notify(new UserInvitationNotification($invitation));
        Notification::send($sudos, new NewUserCreatedNotification($user));

        if($request->input('lab-choice') === 'new' && $lab->pi_email !== $user->email) {
            if (!User::where('email', $lab->pi_email)->exists()) {
                // Create PI user if a new lab has been created 
                // and the User created is not the PI of the Lab
                $pi_user = User::create([
                    'name' => $request->pi_name,
                    'email' => $request->pi_email,
                    'lab_id' => $labId,
                    'lab_admin' => true,
                ]);
                $pi_user->notify(new UserInvitationNotification($invitation));
                Notification::send($sudos, new NewUserCreatedNotification($pi_user));
            }
        }
        return redirect()->route('admin.accountRequests')->with('status', 'User created successfully.');
    }

    private function createLegacyLabAccountFiles($data)
    {
        /**
         *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']);
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'failed';
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'processing';
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'trash';
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'account_info';
    	 *  mkdir($newdir, 0777);
    	 *  $path = $newdir.DIRECTORY_SEPARATOR.'info.json';
    	 *  $file = fopen($path, 'w');
    	 *  flock($file, LOCK_EX);
    	 *  fwrite($file, json_encode($acctinfo, JSON_PRETTY_PRINT));
         */
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory), 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'failed', 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'processing', 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'trash', 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'account_info', 
            0777,
            true
        );
        file_put_contents(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'account_info' . DIRECTORY_SEPARATOR . 'info.json', 
            json_encode($data, JSON_PRETTY_PRINT)
        );
    }
    /** 
     * Lab Management
     */
    public function allLabs()
    {
        $labs = Lab::all()->groupBy('active');
        $active = $labs->get('1', collect());
        $inactive = $labs->get('0', collect()); 
        return view('admin.labs.labs', compact('active', 'inactive'));
    }

    public function singleLabDetail($labId)
    {
        $lab = Lab::findOrFail($labId);
        return view('admin.labs.lab_details', compact('lab'));
    }

    public function updateLab(Request $request, $labId)
    {
        $lab = Lab::findOrFail($labId);
        $request->merge([
            'directory' => strtoupper($request->input('directory'))
        ]);
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'pi_name' => 'required|string|max:255',
            'pi_email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('labs', 'pi_email')->ignore($labId),
            ],
            'organization' => 'required|string|max:255',
            'notes' => 'nullable|string',
            'directory' => [
                'required',
                'string',
                'regex:/^[a-zA-Z0-9-_]+$/',
                Rule::unique('labs', 'directory')->ignore($labId),
            ],
            'active' => 'required|boolean',
        ]);

        if($validated) {
            if($lab->directory !== $request->input('directory')) {
                $dirBackup = $lab->directory;
                $source = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}";
                $target = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$request->input('directory')}";
                $mvSuccess = rename($source,$target);
                
                if($mvSuccess) {
                    // Update CbProject, BeProject, and ThemeLink directories
                    $lab->cbProjects->each(function($project) use ($source, $target) {
                        $project->directory = str_replace($source, $target, $project->directory);
                        $project->save();
                    });
                    $lab->beProjects->each(function($project) use ($source, $target) {
                        $project->directory = str_replace($source, $target, $project->directory);
                        $project->save();
                    });
                    $lab->themelinks->each(function($themelink) use ($source, $target) {
                        $themelink->directory = str_replace($source, $target, $themelink->directory);
                        $themelink->save();
                    });
                } else {
                    $lab->directory = $dirBackup;
                    $msg = 'ERROR: Failed to move Lab directory' ;
                    return redirect()->route('admin.labs')->with('error', $msg);
                }
            }
            $lab->update($request->all());
            $msg = 'Lab updated successfully.';
            return redirect()->route('admin.labs')->with('status', $msg);
        }
    }

    /** 
     * User Management
     */
     public function allUsers()
     {
         $users = User::all()->groupBy('active');
         $active = $users->get('1',collect());
         $inactive = $users->get('0',collect());
         return view('admin.users.users', compact('active','inactive'));
     }

    public function singleUserDetail($userId)
    {
        $user = User::findOrFail($userId);
        $labs = Lab::all();
        return view('admin.users.user_details', compact('user', 'labs'));
    }

    public function updateUser(Request $request, $userId)
    {
        $user = User::findOrFail($userId);
        $request->merge([
            'lab_id' => $request->input('lab_id') ?? $user->lab_id,
            'lab_admin' => $request->input('lab_admin') ?? $user->lab_admin,
            'sudo' => $request->input('sudo') ?? $user->sudo,
        ]);
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'string',
                'email',
                'max:255',
                Rule::unique('users', 'email')->ignore($userId),
            ],
            'lab_id' => 'required|integer',
            'lab_admin' => 'required|boolean',
            'sudo' => 'required|boolean',
        ]);

        if($validated) {
            $user->update($request->all());
            return redirect()->route('admin.users')->with('status', 'User updated successfully.');
        }
    }

    public function singleLabUsers($labId)
    {
        $lab = Lab::findOrFail($labId);
        $piEmail = $lab->pi_email ?? null;
        $users = User::where('lab_id', $labId)->get()->groupBy('active');
        $active = $users->get('1', collect())->sortBy([
            fn($user) => $user->email !== $piEmail,
            ['sudo', 'desc'],
            ['lab_admin', 'desc'],
        ]);
        $inactive = $users->get('0', collect())->sortBy([
            fn($user) => $user->email !== $piEmail,
            ['sudo', 'desc'],
            ['lab_admin', 'desc'],
        ]);
        
        return view('admin.users.lab_users', compact('lab', 'active', 'inactive'));
    }



    /** 
     * Project Management
     * /admin/cbProject/details/{$id}
     * /admin/beProject/details/{$id}
     * /admin/assertion/details/{$id}
     * /admin/themelink/details/{$id}
     */
    public function singleProjectDetail($projectType, $projectId)
    {
        switch ($projectType) {
            case 'cbProject':
                $project = CbProject::findOrFail($projectId);
                if($project) {
                    return view('admin.projects.cbproject_details', compact('project'));
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;
            
            case 'beProject':
                $project = BeProject::findOrFail($projectId);
                if($project) {
                    return view('admin.projects.beproject_details', compact('project'));
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;

            case 'assertion':
                $project = Assertion::findOrFail($projectId);
                if($project) {
                    return view('admin.projects.assertion_details', compact('project'));
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;

            case 'themelink':
                $project = Themelink::findOrFail($projectId);
                if($project) {
                    return view('admin.projects.themelink_details', compact('project'));
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;

            default:
                return redirect()->back()->with('error', 'Invalid project type.');
                break;
        }
    }
    public function singleProjectUpdate(Request $request,$projectType, $projectId)
    {
        switch ($projectType) {
            case 'cbProject':
                $project = CbProject::findOrFail($projectId);
                if($project) {
                    $project->update($request->all());
                    return redirect('/admin/user/details/'.$project->user_id)->with('status', 'Project Details Updated.');
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;
            
            case 'beProject':
                $project = BeProject::findOrFail($projectId);
                if($project) {
                    $project->update($request->all());
                    return redirect('/admin/user/details/'.$project->user_id)->with('status', 'Project Details Updated.');
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;

            case 'assertion':
                $project = Assertion::findOrFail($projectId);
                if($project) {
                    $project->update($request->all());
                    return redirect('/admin/user/details/'.$project->user_id)->with('status', 'Project Details Updated.');
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;

            case 'themelink':
                $project = Themelink::findOrFail($projectId);
                if($project) {
                    $project->update($request->all());
                    return redirect('/admin/user/details/'.$project->user_id)->with('status', 'Project Details Updated.');
                } else {
                    return redirect()->back()->with('error', 'Project not found.');
                }
                break;

            default:
                return redirect()->back()->with('error', 'Invalid project type.');
                break;
        }
    }
    public function viewAssertion($assertionId)
    {
        $assertion = Assertion::findOrFail($assertionId);
        return view('admin.projects.assertion_viewer', compact('assertion'));
    }
}
