<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\AccountRequest;
use App\Models\User;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use App\Notifications\NewAccountRequestNotification;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Str;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest');
        $this->middleware('throttle:5,60')->only('register'); // 5 attempts per minute
    }

    public function register(Request $request)
    {
        // Check honeypot field - if filled, it's likely a bot
        if ($request->filled('website_url')) {
            // Silently redirect to avoid alerting bots
            return redirect('/login')->with('status', 'Your request has been submitted for approval.');
        }

        // Rate limiting by IP
        $key = 'registration_' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            return redirect('/login')
                ->withErrors(['email' => "Too many registration attempts. Please try again in {$seconds} seconds."]);
        }
        
        RateLimiter::hit($key, 60*10); // Keep record for 10 minutes

        // Validate the request
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:account_requests,email|unique:users,email',
            'organization' => 'required|string|max:255',
            'pi_name' => 'required|string|max:255',
            'pi_email' => 'required|string|email|max:255|unique:labs,pi_email',
            'notes' => 'nullable|string',
            'password' => 'required|string|min:8|confirmed',
        ], [
            'pi_email.unique' => 'There is already an account for this PI\'s lab. Contact the PI to add you to the existing lab account.',
        ]);

        $accountRequest = AccountRequest::create($request->all());
        $accountRequest->password = Hash::make($request->password);
        $accountRequest->save();

        // Rate limit notifications to admins (max 10 per hour)
        $notificationKey = 'admin_notifications';
        if (!RateLimiter::tooManyAttempts($notificationKey, 10)) {
            RateLimiter::hit($notificationKey, 60*60); // Keep record for 1 hour
            $sudos = User::where('sudo', 1)->get();
            Notification::send($sudos, new NewAccountRequestNotification($accountRequest));
        }

        return redirect('/login')->with('status', 'Your request has been submitted for approval.');
    }

    // /**
    //  * Get a validator for an incoming registration request.
    //  *
    //  * @param  array  $data
    //  * @return \Illuminate\Contracts\Validation\Validator
    //  */
    // protected function validator(array $data)
    // {
    //     return Validator::make($data, [
    //         'name' => ['required', 'string', 'max:255'],
    //         'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
    //         'password' => ['required', 'string', 'min:8', 'confirmed'],
    //     ]);
    // }

    // /**
    //  * Create a new user instance after a valid registration.
    //  *
    //  * @param  array  $data
    //  * @return \App\Models\User
    //  */
    // protected function create(array $data)
    // {
    //     $user = User::create([
    //         'email' => $data['email'],
    //         'password' => Hash::make($data['password']),
    //     ]);

    //     $account_request = AccountRequest::create([
    //         'name' => $data['name'],
    //         'organization' => $data['organization'],
    //         'user_id' => $user->id,
    //         'notes' => $data['notes'],
    //     ]);

    //     Mail::to('<EMAIL>')->send(new RegistrationRequest($user, $account_request));

    //     return $user;
    // }
}
