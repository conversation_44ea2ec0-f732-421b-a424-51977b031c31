<?php

namespace App\Http\Controllers;

use App\Models\BeProject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class BeProjectController extends Controller
{
    public function toggleShared(Request $request, $id)
    {
        $project = BeProject::find($id);
        if (!$project) {
            return response()->json(['error' => 'Project not found'], 404);
        }
        
        session_start();
        if($_SESSION['Id'] === $project->user->id) {
            // Toggle the 'shared' column
            $project->shared = !$project->shared;
            $project->save();
            $response = response()->json(['success' => true, 'shared' => $project->shared]);
        } else {
            $response = response()->json(['success' => false, 'shared' => $project->shared]);
        }

        return $response;
    }
}
