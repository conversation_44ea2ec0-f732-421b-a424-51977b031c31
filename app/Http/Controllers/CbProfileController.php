<?php

namespace App\Http\Controllers;

use App\Models\CbProfile;
use Illuminate\Http\Request;

class CbProfileController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CbProfile $cbProfile)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(cbProfile $cbProfile)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CbProfile $cbProfile)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CbProfile $cbProfile)
    {
        //
    }
}
