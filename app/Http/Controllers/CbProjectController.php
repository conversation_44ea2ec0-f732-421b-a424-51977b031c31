<?php

namespace App\Http\Controllers;

use App\Models\CbProject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CbProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(CbProject $cbProject)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CbProject $cbProject)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CbProject $cbProject)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CbProject $cbProject)
    {
        //
    }

    public function toggleShared(Request $request, $id)
    {
        $project = CbProject::find($id);
        if (!$project) {
            return response()->json(['error' => 'Project not found'], 404);
        }
        
        session_start();
        if($_SESSION['Id'] === $project->user->id) {
            // Toggle the 'shared' column
            $project->shared = !$project->shared;
            $project->save();
            $response = response()->json(['success' => true, 'shared' => $project->shared]);
        } else {
            $response = response()->json(['success' => false, 'shared' => $project->shared]);
        }

        return $response;
    }
}
