<?php

namespace App\Http\Controllers;

use App\Http\Requests\AccountUpdateRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;
use App\Models\User;
use App\Models\Lab;

class LabController extends Controller
{
    /**
     * Display the user's lab form.
     */
    public function edit(Request $request): View
    {
        return view('lab.edit', [
            'lab' => $request->user()->lab,
            'users' => User::where('lab_id', $request->user()->lab_id)->get()
        ]);
    }

    /**
     * Remove user's from lab.
     */
    public function removeUsers(Request $request): RedirectResponse
    {
        // dd($request->filled('user_ids'));
        if($request->filled('user_ids') && Auth::user()->lab_admin ||
            $request->filled('user_ids') && Auth::user()->sudo) 
        {
            $user_ids = $request->input('user_ids');
            foreach($user_ids as $user_id)
            {
                $user = User::find($user_id);
                $user->lab_id = null;
                $user->lab_admin = false;
                $user->save();
            }
            $message = 'Lab Users Updated.';
        } else 
        {
            $message = 'Lab Users Unchanged!';
        }
        // return redirect('/lab')->with('status', $message);
        return Redirect::route('lab.edit')->with('status', $message);
        // return view('lab.edit', [
        //     'lab' => $request->user()->lab,
        //     'users' => User::where('lab_id', $request->user()->lab_id)->get()
        // ]);
    }

    /**
     * Assign user's Lab Admin Status.
     */
    public function updateLabAdmin(Request $request): RedirectResponse
    {
        $lab = ($request->has('lab_id')) ? Lab::first($request->input('lab_id')) : Auth::user()->lab;
        if (Auth::user()->lab_admin || Auth::user()->sudo) 
        {
            $me = Auth::user();
            $pi = $lab->pi;
            $admins = $request->input('admin_ids');
            $norms = $request->input('non_admin_ids');
            if($admins !== null)
            {
                foreach ($admins as $adminId) 
                {
                    $user = User::find($adminId);
                    if($user !== $me || $user !== $pi )
                    {
                        $user->lab_admin = true;
                        $user->save();
                    }
                }
            }
            if($norms !== null)
            {
                foreach($norms as $normId)
                {
                    $user = User::find($normId);
                    if ($user !== $me || $user !== $pi) {
                        $user->lab_admin = false;
                        $user->save();
                    }
                }
            }
            $message = 'Lab Users Updated.';
        } else {
            $message = 'You are NOT authorized to do this!';
        }
        // return redirect('/lab')->with('status', $message);
        return Redirect::route('lab.edit')->with('status', $message);
        // return view('lab.edit', [
        //     'lab' => $request->user()->lab,
        //     'users' => User::where('lab_id', $request->user()->lab_id)->get()
        // ]);
    }

    /**
     * Update the user's lab information.
     */
    public function update(Request $request, Lab $lab): RedirectResponse
    {
        $validated = $request->validate([
            'pi_email' =>  [
                'sometimes',
                'string',
                'email',
                'max:255',
                Rule::unique('labs', 'pi_email')->ignore($lab->id),
            ],
            'organization' => 'sometimes|string|max:255',
            'notes' => 'nullable|string',
        ]);
        if($validated) {
            $lab->fill($validated);
            $lab->save();
        }

        return Redirect::route('lab.edit')->with('status', 'Lab Information Updated');
    }

    /**
     * Delete the user's lab.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validateWithBag('labDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $lab = $request->user()->lab;

        Auth::logout();

        $lab->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }
}

