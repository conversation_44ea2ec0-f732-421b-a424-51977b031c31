<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use App\Models\UserInvitation;
use App\Models\User;

class SetPasswordController extends Controller
{
    public function showSetPasswordForm($token)
    {
        $invitation = UserInvitation::where('token', $token)->where('used', false)->firstOrFail();
        return view('auth.set-password', compact('invitation'));
    }

    public function setPassword(Request $request, $token)
    {
        $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $invitation = UserInvitation::where('token', $token)->where('used', false)->firstOrFail();
        $user = $invitation->user;

        // Update the user’s password
        $user->update([
            'password' => Hash::make($request->password),
        ]);

        // Mark the invitation as used
        $invitation->update(['used' => true]);

        return redirect('/login')->with('status', 'Your password has been set. You can now log in.');
    }
}
