<?php

namespace App\Http\Controllers;

use App\Models\Themelink;
use Illuminate\Http\Request;

class ThemelinkController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(Themelink $themelink)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Themelink $themelink)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Themelink $themelink)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Themelink $themelink)
    {
        //
    }
}
