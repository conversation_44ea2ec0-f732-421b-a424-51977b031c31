<?php

namespace App\Http\Controllers;

use App\Http\Requests\AccountUpdateRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;
use App\Models\User;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use App\Notifications\NewUserCreatedNotification;
use App\Notifications\UserInvitationNotification;
use App\Models\UserInvitation;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    /**
     * Display the user's account form.
     */
    public function edit(Request $request): View
    {
        return view('account.edit', [
            'user' => $request->user(),
        ]);
    }

    /**
     * Update the user's account information.
     */
    public function update(AccountUpdateRequest $request): RedirectResponse
    {
        $request->user()->fill($request->validated());

        if ($request->user()->isDirty('email')) {
            $request->user()->email_verified_at = null;
        }

        $request->user()->save();

        return Redirect::route('account.edit')->with('status', 'Account Information Updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        $request->validateWithBag('userDeletion', [
            'password' => ['required', 'current_password'],
        ]);

        $user = $request->user();

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/');
    }

    /**
     * Get a validator for an incoming admin created users.
     *
     * @param  array  $data
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validator(array $data)
    {
        return Validator::make($data, [
            'lab_id' => ['required', 'exists:labs,id'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
        ]);
    }


    // Create a new user via Lab admin.

    protected function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'lab_id' => 'required|exists:labs,id',
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email',
        ]);

        if ($validated) {
            $user = User::create([
                'name' => $request->input('name'),
                'email' => $request->input('email'),
                'lab_id' => $request->input('lab_id'),
            ]);
            // Create an invitation token
            $invitation = UserInvitation::create([
                'user_id' => $user->id,
                'token' => Str::random(40),
            ]);
            $user->notify(new UserInvitationNotification($invitation));

            // Get sudo users for notifications
            $sudos = User::where('sudo', 1)->get();
            Notification::send($sudos, new NewUserCreatedNotification($user));
            $message = 'User created and invitation sent.';
        }
 
        return Redirect::route('lab.edit')->with('status', $message);
    }

    protected function updatePassword(Request $request): RedirectResponse
    {
        $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|confirmed|min:8|string'
        ]);
        $auth = Auth::user();

        // The passwords matches
        if (!Hash::check($request->get('current_password'), $auth->password)) {
            return back()->with('error', "Current Password is Incorrect");
        }

        // Current password and new password same
        if (strcmp($request->get('current_password'), $request->new_password) == 0) {
            return redirect()->back()->with("error", "New Password cannot be same as your current password.");
        }

        $user =  User::find($auth->id);
        $user->password =  Hash::make($request->new_password);
        $user->save();
        return back()->with('status', "Password Changed Successfully");
    }

}
