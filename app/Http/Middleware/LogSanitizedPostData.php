<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class LogSanitizedPostData
{
    private $sensitiveFields = [
        'password',
        'password_confirmation',
        'current_password',
        'new_password',
        'new_password_confirmation',
    ];
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->isMethod('post')) {
            $postData = $request->all();
            foreach ($this->sensitiveFields as $field) {
                if (isset($postData[$field])) {
                    $postData[$field] = '***REDACTED***';
                }
            }
            Log::channel('post_requests')->info('POST Data: ', [
                'uri' => $request->path(),
                'ip_address' => $request->ip(),
                'post_data' => $postData
            ]);
        }
        return $next($request);
    }
}
