<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Assertion extends Model
{
    /** @use HasFactory<\Database\Factories\AssertionFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'base',
        'is_base_lib',
        'secondary',
        'is_secondary_lib',
        'is_lib_assertion',
        'is_lib_to_lib_assertion',
        'engine',
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

}
