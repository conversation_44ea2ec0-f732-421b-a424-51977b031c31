<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AssertionLibrary extends Model
{
    /** @use HasFactory<\Database\Factories\AssertionLibraryFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'projects',
        'project_ids',
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
            'projects' => 'array',
            'project_ids' => 'array',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class,'user_id');
    }

}
