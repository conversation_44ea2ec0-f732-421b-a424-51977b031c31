<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BeProfile extends Model
{
    /** @use HasFactory<\Database\Factories\BeProfileFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'be_project_id',
        'profile',
        'shared'
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
            'profile' => 'array',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(BeProject::class);
    }

}
