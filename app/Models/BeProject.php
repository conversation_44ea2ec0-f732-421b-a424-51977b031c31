<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class BeProject extends Model
{
    /** @use HasFactory<\Database\Factories\BeProjectFactory> */
    use HasFactory, HasUlids;

    protected $fillable = [
        'name',
        'user_id',
        'status',
        'field',
        'value',
        'method',
        'terms',
        'directory',
        'shared'
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
            'value' => 'array',
            'terms' => 'array',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function profiles(): HasOne
    {
        return $this->hasOne(BeProfile::class);
    }

}
