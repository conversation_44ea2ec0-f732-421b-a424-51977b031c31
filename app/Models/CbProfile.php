<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CbProfile extends Model
{
    protected $fillable = [
        'user_id',
        'cb_project_id',
        'profile',
        'shared'
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
            'profile' => 'array',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(CbProject::class);
    }
}
