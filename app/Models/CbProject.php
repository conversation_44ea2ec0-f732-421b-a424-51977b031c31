<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;
// use Illuminate\Database\Eloquent\Relations\HasMany; // Add this to profiles() to allow each user to have a individual profile for each project
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUlids;

class CbProject extends Model
{
    use HasFactory, HasUlids;

    protected $fillable = [
        'name',
        'user_id',
        'status',
        'entity_count',
        'concept_count',
        'list',
        'expression',
        'directory',
        'shared'
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
            'list' => 'array',
            'expression' => 'array',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class,'user_id');
    }

    public function folder(): BelongsTo
    {
        return $this->belongsTo(Folder::class);
    }

    public function profiles(): HasOne
    {
        return $this->hasOne(CbProfile::class);
    }

}
