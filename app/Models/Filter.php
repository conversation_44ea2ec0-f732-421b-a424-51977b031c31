<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Filter extends Model
{
    /** @use HasFactory<\Database\Factories\FilterFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'base',
        'secondary',
        'list'
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
            'list' => 'array',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

}
