<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Lab extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'pi_name',
        'pi_email',
        'organization',
        'notes',
        'active',
        'directory'
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [];
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function sharedCbProjects(): HasManyThrough
    {
        return $this->hasManyThrough(CbProject::class, User::class)
            ->where('cb_projects.shared', 1);
    }

    /**
     * Lab CbProjects, BeProjects, Assertions and ThemeLinks
     * through Lab Users Relationships
     */
    public function cbProjects()
    {
        return $this->hasManyThrough(CbProject::class, User::class);
    }

    public function beProjects()
    {
        return $this->hasManyThrough(BeProject::class, User::class);
    }

    public function sharedBeProjects() : HasManyThrough
    {
        return $this->hasManyThrough(BeProject::class, User::class)
            ->where('be_projects.shared', 1);
    }

    public function assertions()
    {
        return $this->hasManyThrough(Assertion::class, User::class);
    }

    public function themelinks()
    {
        return $this->hasManyThrough(Themelink::class, User::class);
    }

    /**
     * Relationship: The PI (Principal Investigator) belongs to the User model
     */
    public function pi()
    {
        return $this->belongsTo(User::class, 'pi_email', 'email');
    }
}
