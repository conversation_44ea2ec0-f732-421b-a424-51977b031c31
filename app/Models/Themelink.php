<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Themelink extends Model
{
    /** @use HasFactory<\Database\Factories\ThemelinkFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'base',
        'secondary',
        'directory'
    ];

    protected $hidden = [];

    protected function casts(): array
    {
        return [
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

}
