<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
        'lab_id',
        'lab_admin',
        'approved',
        'sudo'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'sudo' => 'boolean',
            'lab_admin' => 'boolean',
            'active' => 'boolean',
            // 'created_at' => 'datetime'
        ];
    }

    public function lab(): BelongsTo
    {
        return $this->belongsTo(Lab::class);
    }

    public function account_request(): HasOne
    {
        return $this->hasOne(AccountRequest::class);
    }

    public function cbProjects(): HasMany
    {
        return $this->hasMany(CbProject::class);
    }

    public function profiles(): HasMany
    {
        return $this->hasMany(CbProfile::class);
    }

    public function folders(): HasMany
    {
        return $this->hasMany(Folder::class);
    }

    public function beProjects(): HasMany
    {
        return $this->hasMany(BeProject::class);
    }

    public function assertions(): HasMany
    {
        return $this->hasMany(Assertion::class);
    }

    public function assertionLibraries(): HasMany
    {
        return $this->hasMany(AssertionLibrary::class);
    }
    public function themelinks(): HasMany
    {
        return $this->hasMany(Themelink::class);
    }
    public function filters(): HasMany
    {
        return $this->hasMany(Filter::class);
    }
}
