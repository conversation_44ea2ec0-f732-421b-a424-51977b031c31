<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DiskSpaceFullNotification extends Notification
{
    use Queueable;

    protected $diskUsage;

    /**
     * Create a new notification instance.
     */
    public function __construct(array $diskUsage)
    {
        $this->diskUsage = $diskUsage;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $serverName = config('app.name', 'Server');
        $path = $this->diskUsage['path'];
        $usagePercentage = $this->diskUsage['usage_percentage'];
        $freeSpace = $this->diskUsage['free_formatted'];
        $totalSpace = $this->diskUsage['total_formatted'];
        $usedSpace = $this->diskUsage['used_formatted'];

        $pathDescription = $path === '/' ? 'Root filesystem (/)' : "Mount point: {$path}";

        return (new MailMessage)
            ->subject("🚨 CRITICAL: {$serverName} - Disk Space Nearly Full")
            ->greeting("CRITICAL: Disk Space Alert")
            ->line("**URGENT:** The disk space on **{$serverName}** is critically low and requires immediate attention!")
            ->line('')
            ->line("**{$pathDescription}**")
            ->line("**Current Usage:** {$usagePercentage}%")
            ->line("**Used Space:** {$usedSpace}")
            ->line("**Free Space:** {$freeSpace}")
            ->line("**Total Space:** {$totalSpace}")
            ->line('')
            ->line('🚨 **IMMEDIATE ACTION REQUIRED:**')
            ->line('• **Clean up unnecessary files immediately**')
            ->line('• **Check for large log files that can be rotated**')
            ->line('• **Remove temporary files and caches**')
            ->line('• **Consider expanding storage capacity urgently**')
            ->line('• **Monitor application functionality for potential issues**')
            ->line('• **Check database logs and backups for cleanup opportunities**')
            ->line('')
            ->line('⚠️ **WARNING:** The system may experience performance issues or failures if disk space is completely exhausted.')
            ->line('This is an automated critical alert sent when disk usage exceeds the critical threshold.')
            ->salutation('Automated System Monitor - URGENT');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'disk_space_critical',
            'path' => $this->diskUsage['path'],
            'usage_percentage' => $this->diskUsage['usage_percentage'],
            'free_space' => $this->diskUsage['free_formatted'],
            'timestamp' => now()->toISOString(),
        ];
    }
}
