<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class DiskSpaceLowNotification extends Notification
{
    use Queueable;

    protected $diskUsage;

    /**
     * Create a new notification instance.
     */
    public function __construct(array $diskUsage)
    {
        $this->diskUsage = $diskUsage;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $serverName = config('app.name', 'Server');
        $path = $this->diskUsage['path'];
        $usagePercentage = $this->diskUsage['usage_percentage'];
        $freeSpace = $this->diskUsage['free_formatted'];
        $totalSpace = $this->diskUsage['total_formatted'];
        $usedSpace = $this->diskUsage['used_formatted'];

        $pathDescription = $path === '/' ? 'Root filesystem (/)' : "Mount point: {$path}";

        return (new MailMessage)
            ->subject("⚠️ {$serverName} - Low Disk Space Warning")
            ->greeting("Disk Space Warning")
            ->line("The disk space on **{$serverName}** is running low and requires attention.")
            ->line('')
            ->line("**{$pathDescription}**")
            ->line("**Current Usage:** {$usagePercentage}%")
            ->line("**Used Space:** {$usedSpace}")
            ->line("**Free Space:** {$freeSpace}")
            ->line("**Total Space:** {$totalSpace}")
            ->line('')
            ->line('**Recommended Actions:**')
            ->line('• Clean up unnecessary files and logs')
            ->line('• Check for large temporary files')
            ->line('• Consider expanding storage capacity')
            ->line('• Review application data retention policies')
            ->line('')
            ->line('This is an automated warning sent when disk usage exceeds the configured threshold.')
            ->salutation('Automated System Monitor');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'disk_space_low',
            'path' => $this->diskUsage['path'],
            'usage_percentage' => $this->diskUsage['usage_percentage'],
            'free_space' => $this->diskUsage['free_formatted'],
            'timestamp' => now()->toISOString(),
        ];
    }
}
