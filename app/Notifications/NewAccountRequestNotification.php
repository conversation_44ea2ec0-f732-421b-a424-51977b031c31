<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewAccountRequestNotification extends Notification
{
    use Queueable;

    protected $accountRequest;

    /**
     * Create a new notification instance.
     */
    public function __construct($accountRequest)
    {
        $this->accountRequest = $accountRequest;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Compbio - New Account Request')
            ->line('A new user has requested access: ' . $this->accountRequest->name)
            ->line('Email: ' . $this->accountRequest->email)
            ->line('Organization: ' . $this->accountRequest->organization)
            ->line('PI: ' . $this->accountRequest->pi_name)
            ->line('PI Email: ' . $this->accountRequest->pi_email)
            ->line('Notes: ' . $this->accountRequest->notes)
            ->action('Review Requests', url('/admin/account-requests'))
            ->line('Please review and approve.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
