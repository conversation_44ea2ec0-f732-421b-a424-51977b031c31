<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewLabCreatedNotification extends Notification
{
    use Queueable;
    protected $lab;

    /**
     * Create a new notification instance.
     */
    public function __construct($lab)
    {
        $this->lab = $lab;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Compbio - New Lab Created')
            ->line('Lab Name: ' . $this->lab->name)
            ->line('PI Name: ' . $this->lab->pi_name)
            ->line('PI Email: ' . $this->lab->pi_email)
            ->line('Organization: ' . $this->lab->organization)
            ->line('Directory: ' . $this->lab->directory)
            ->line('Notes: ' . $this->lab->notes);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
