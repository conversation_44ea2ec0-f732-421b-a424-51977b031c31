<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\UserInvitation;

class UserInvitationNotification extends Notification
{
    use Queueable;

    /**
     * The invitation instance.
     */
    protected $invitation;
    
    /**
     * Create a new notification instance.
     */
    public function __construct(UserInvitation $invitation)
    {
        $this->invitation = $invitation;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Compbio - Account Invitation')
            ->line('You have been invited to join the '.$notifiable->lab->name.' on the Compbio platform.')
            ->line('An account has been created for, '. $notifiable->name .'.')
            ->line('To accept this invitation and complete your account setup, ')
            ->line('click or copy/paste the following link into your browser.')
            ->line('This link will direct you to a page where you can set your password, ')
            ->line('which once completed, will automatically activate your account.')
            ->action('Set Your Password', url('/set-password/' . $this->invitation->token));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
