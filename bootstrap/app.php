<?php

use Symfony\Component\HttpFoundation\Request;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\LogSanitizedPostData;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->append(\App\Http\Middleware\BlockIpMiddleware::class);
        $middleware->validateCsrfTokens(
            except: [
                'logout',
                'legacy/*'
            ],
        );
        $middleware->alias([
            'sudo' => \App\Http\Middleware\Sudo::class,
        ]);
        $middleware->trustProxies(
            at: '*',
            headers: 63
        );
        $middleware->append(LogSanitizedPostData::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
