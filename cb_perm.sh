#!/bin/bash

APP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Avoid the data symlink
EXCLUDE="$APP_DIR/public/legacy/data"

# Files
find "$APP_DIR" -path "$EXCLUDE" -prune -o -type f -exec chmod 644 {} \;

# Directories
find "$APP_DIR" -path "$EXCLUDE" -prune -o -type d -exec chmod 755 {} \;

# Writable dirs
chmod -R 775 "$APP_DIR/storage" "$APP_DIR/bootstrap/cache" "$APP_DIR/public/legacy/acct/log" "$APP_DIR/public/legacy/app/visualizer/curve/download" "$APP_DIR/public/legacy/app/visualizer/conceptCapture/download" "$APP_DIR/public/legacy/app/visualizer/chart/download" "$APP_DIR/public/legacy/app/visualizer/capture/download" "$APP_DIR/public/legacy/download"
chgrp -R www-data "$APP_DIR/storage" "$APP_DIR/bootstrap/cache" "$APP_DIR/public/legacy/acct/log" "$APP_DIR/public/legacy/app/visualizer/curve/download" "$APP_DIR/public/legacy/app/visualizer/conceptCapture/download" "$APP_DIR/public/legacy/app/visualizer/chart/download" "$APP_DIR/public/legacy/app/visualizer/capture/download" "$APP_DIR/public/legacy/download"