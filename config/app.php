<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application, which will be used when the
    | framework needs to place the application's name in a notification or
    | other UI elements where an application name needs to be displayed.
    |
    */

    'name' => env('APP_NAME', 'CompbioV2.9'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'development'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', true),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | the application so that it's available within Artisan commands.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. The timezone
    | is set to "UTC" by default as it is suitable for most use cases.
    |
    */

    'timezone' => env('APP_TIMEZONE', 'UTC'),

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by Laravel's translation / localization methods. This option can be
    | set to any locale for which you plan to have translation strings.
    |
    */

    'locale' => env('APP_LOCALE', 'en'),

    'fallback_locale' => env('APP_FALLBACK_LOCALE', 'en'),

    'faker_locale' => env('APP_FAKER_LOCALE', 'en_US'),

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is utilized by Laravel's encryption services and should be set
    | to a random, 32 character string to ensure that all encrypted values
    | are secure. You should do this prior to deploying the application.
    |
    */

    'cipher' => 'AES-256-CBC',

    'key' => env('APP_KEY'),

    'previous_keys' => [
        ...array_filter(
            explode(',', env('APP_PREVIOUS_KEYS', ''))
        ),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => env('APP_MAINTENANCE_DRIVER', 'file'),
        'store' => env('APP_MAINTENANCE_STORE', 'database'),
    ],
    /*
    |--------------------------------------------------------------------------
    | Custom Environment Variables
    |--------------------------------------------------------------------------
    |
    | This value determines the custom environment variables that will be
    | available in the application. This is useful for setting
    | environment variables that are set in the .env file.
    |
    */

    'custom_env' => [
        'WEBROOT' => env('WEBROOT'),
        'ADMIN_WEBROOT' => env('ADMIN_WEBROOT'),
        'CBV_WEBROOT' => env('CBV_WEBROOT'),
        'CSS_WEBROOT' => env('CSS_WEBROOT'),
        'JS_WEBROOT' => env('JS_WEBROOT'),
        'IMAGES_WEBROOT' => env('IMAGES_WEBROOT'),
        'ABS_ROOT' => env('ABS_ROOT'),
        'APPABS_ROOT' => env('APPABS_ROOT'),
        'LIBS_ROOT' => env('LIBS_ROOT'),
        'CSS_ROOT' => env('CSS_ROOT'),
        'JS_ROOT' => env('JS_ROOT'),
        'IMAGES_ROOT' => env('IMAGES_ROOT'),
        'TEMPLATES_ROOT' => env('TEMPLATES_ROOT'),
        'CBV_VIZ_WEBROOT' => env('CBV_VIZ_WEBROOT'),
        'CSS_VIZ_WEBROOT' => env('CSS_VIZ_WEBROOT'),
        'JS_VIZ_WEBROOT' => env('JS_VIZ_WEBROOT'),
        'IMAGES_VIZ_WEBROOT' => env('IMAGES_VIZ_WEBROOT'),
        'CSS_VIZ_ROOT' => env('CSS_VIZ_ROOT'),
        'JS_VIZ_ROOT' => env('JS_VIZ_ROOT'),
        'IMAGES_VIZ_ROOT' => env('IMAGES_VIZ_ROOT'),
        'TEMPLATES_VIZ_ROOT' => env('TEMPLATES_VIZ_ROOT'),
        'ACCOUNT_STORAGE_ROOT' => env('ACCOUNT_STORAGE_ROOT'),
        'ASSERTION_ENGINE_ROOT' => env('ASSERTION_ENGINE_ROOT'),
        'RANDOM_RUN_ROOT' => env('RANDOM_RUN_ROOT'),
        'N_RANDOM_GENES' => env('N_RANDOM_GENES'),
    ],

];
