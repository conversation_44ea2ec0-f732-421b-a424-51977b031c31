<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Disk Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the disk usage monitoring
    | system. You can customize thresholds, notification settings, and
    | monitoring paths here.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Default Monitoring Configuration
    |--------------------------------------------------------------------------
    |
    | Configure the default monitoring behavior. You can monitor a single path,
    | multiple specific paths, or all mounted filesystems automatically.
    |
    */

    'default_path' => env('DISK_MONITOR_PATH', '/'),

    // Set to true to automatically monitor all mounted filesystems
    'monitor_all_disks' => env('DISK_MONITOR_ALL_DISKS', false),

    // Paths to exclude when monitoring all disks
    'exclude_paths' => array_filter(explode(',', env('DISK_MONITOR_EXCLUDE_PATHS', '/dev,/proc,/sys,/run,/snap'))),

    /*
    |--------------------------------------------------------------------------
    | Threshold Settings
    |--------------------------------------------------------------------------
    |
    | Configure the disk usage percentage thresholds that trigger notifications.
    | Warning threshold sends a low disk space alert.
    | Critical threshold sends an urgent disk full alert.
    |
    */

    'thresholds' => [
        'warning' => env('DISK_MONITOR_WARNING_THRESHOLD', 80),
        'critical' => env('DISK_MONITOR_CRITICAL_THRESHOLD', 95),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    |
    | Configure who receives disk usage notifications and how often they
    | can be sent to prevent spam.
    |
    */

    'notifications' => [
        // Email addresses to notify (comma-separated in .env)
        'emails' => env('DISK_MONITOR_EMAILS', '<EMAIL>'),
        
        // Fallback to admin users if no specific emails are configured
        'fallback_to_admins' => env('DISK_MONITOR_FALLBACK_ADMINS', true),
        
        // Throttling settings (in hours)
        'throttle' => [
            'warning' => env('DISK_MONITOR_WARNING_THROTTLE', 2),
            'critical' => env('DISK_MONITOR_CRITICAL_THROTTLE', 4),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Schedule Settings
    |--------------------------------------------------------------------------
    |
    | Configure how often the disk usage check runs. The default is every
    | 4 hours, but you can customize this based on your needs.
    |
    */

    'schedule' => [
        'frequency' => env('DISK_MONITOR_FREQUENCY', 'everyFourHours'),
        'enabled' => env('DISK_MONITOR_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Additional Paths
    |--------------------------------------------------------------------------
    |
    | You can specify additional paths to monitor beyond the default path.
    | Each path will be checked separately and can have its own thresholds.
    | This is useful for monitoring specific mount points with different
    | threshold requirements.
    |
    */

    'additional_paths' => [
        // Example configurations:
        // '/var/log' => [
        //     'warning' => 85,
        //     'critical' => 98,
        // ],
        // '/tmp' => [
        //     'warning' => 90,
        //     'critical' => 99,
        // ],
        // '/home' => [
        //     'warning' => 85,
        //     'critical' => 95,
        // ],
        // '/var/lib/mysql' => [
        //     'warning' => 80,
        //     'critical' => 90,
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Disk Detection Settings
    |--------------------------------------------------------------------------
    |
    | Configure how the system detects and filters mounted filesystems
    | when using the --all-disks option.
    |
    */

    'disk_detection' => [
        // Filesystem types to exclude from automatic detection
        'exclude_filesystem_types' => [
            'proc', 'sysfs', 'devtmpfs', 'devpts', 'tmpfs', 'cgroup',
            'cgroup2', 'pstore', 'bpf', 'tracefs', 'debugfs', 'mqueue',
            'hugetlbfs', 'systemd-1', 'autofs', 'rpc_pipefs', 'nfsd'
        ],

        // Mount point prefixes to exclude
        'exclude_mount_prefixes' => [
            '/dev', '/proc', '/sys', '/run', '/boot/efi'
        ],

        // Minimum disk size to include (in bytes, 0 = no minimum)
        'minimum_disk_size' => env('DISK_MONITOR_MIN_SIZE', 0),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    |
    | Configure logging behavior for disk monitoring events.
    |
    */

    'logging' => [
        'enabled' => env('DISK_MONITOR_LOGGING', true),
        'channel' => env('DISK_MONITOR_LOG_CHANNEL', 'single'),
    ],

];
