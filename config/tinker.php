<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Console Commands
    |--------------------------------------------------------------------------
    |
    | This option allows you to add additional Artisan commands that should
    | be available within the Tinker environment. Once the command is in
    | this array you may execute the command in Tinker using its name.
    |
    */

    'commands' => [
        // App\Console\Commands\ExampleCommand::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto Aliased Classes
    |--------------------------------------------------------------------------
    |
    | Tinker will not automatically alias classes in your vendor namespaces
    | but you may explicitly allow a subset of classes to get aliased by
    | adding the names of each of those classes to the following list.
    |
    */

    'alias' => [
        'AccountRequest' => App\Models\AccountRequest::class,
        'Assertion' => App\Models\Assertion::class,
        'AssertionLibrary' => App\Models\AssertionLibrary::class,
        'BeProfile' => App\Models\BeProfile::class,
        'BeProject' => App\Models\BeProject::class,
        'CbProfile' => App\Models\CbProfile::class,
        'CbProject' => App\Models\CbProject::class,
        'Filter' => App\Models\Filter::class,
        'Folder' => App\Models\Folder::class,
        'Lab' => App\Models\Lab::class,
        'Themelink' => App\Model\Themelink::class,
        'User' => App\Models\User::class,
        'UserInvitation' => App\Model\UserInvitation::class
    ],

    /*
    |--------------------------------------------------------------------------
    | Classes That Should Not Be Aliased
    |--------------------------------------------------------------------------
    |
    | Typically, Tinker automatically aliases classes as you require them in
    | Tinker. However, you may wish to never alias certain classes, which
    | you may accomplish by listing the classes in the following array.
    |
    */

    'dont_alias' => [
        'App\Nova',
    ],
    // 'include' => [
    //     base_path('app/Models/CbProject.php'),
    //     base_path('app/Models/Folder.php'),
    //     base_path('app/Models/User.php'),
    //     base_path('app/Models/CbProfile.php'),
    //     base_path('app/Models/Lab.php'),
    //     base_path('app/Models/AccountRequest.php'),
    // ]

];
