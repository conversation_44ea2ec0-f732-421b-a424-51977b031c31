<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AccountRequest>
 */
class AccountRequestFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'organization' => fake()->company(),
            'pi_name' => fake()->name(),
            'pi_email' => fake()->unique()->safeEmail(),
            'notes' => fake()->text(),
            'status' => fake()->randomElement(['pending', 'approved', 'denied']),
            'password' => static::$password ??= Hash::make('password'),
        ];
    }
}
