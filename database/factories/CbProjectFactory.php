<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CbProject>
 */
class CbProjectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->name,
            'user_id' => 1,
            'status' => 'complete',
            'entity_count' => $this->faker->numberBetween(100,1500),
            'concept_count' => $this->faker->numberBetween(100, 1500),
            'list' => $this->faker->words(25),
            'directory' => $this->faker->word,
            'shared' => $this->faker->numberBetween(0,1)
        ];
    }
}
