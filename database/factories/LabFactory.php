<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Lab>
 */
class LabFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $dir_name = strtoupper(fake()->lastName . '_lab');
        return [
            'name' => $dir_name,
            'pi_name' => fake()->name(),
            'pi_email' => fake()->unique()->safeEmail(),
            'organization' => fake()->company(),
            'notes' => fake()->paragraph($nbSentences = 3, $variableNbSentences = true),
            'active' => fake()->boolean($chanceOfGettingTrue = 75),
            'directory' => $dir_name,
        ];
    }
}
