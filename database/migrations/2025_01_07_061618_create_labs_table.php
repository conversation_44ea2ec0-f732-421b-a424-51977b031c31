<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('labs', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('pi_name');
            $table->string('pi_email')->unique();
            $table->string('organization')->nullable()->default(null);
            $table->text('notes')->nullable()->default(null);
            $table->boolean('active')->default(true);
            $table->string('directory')->nullable()->default(null);  
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('labs');
    }
};
