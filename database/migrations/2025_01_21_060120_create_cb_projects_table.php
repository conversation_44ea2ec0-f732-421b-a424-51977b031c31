<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cb_projects', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('status')->nullable();
            $table->integer('entity_count')->default(0);
            $table->integer('concept_count')->default(500);
            $table->json('list')->nullable()->default(null);
            $table->json('expression')->after('list')->nullable()->default(null); 
            $table->string('directory')->nullable()->default(null);
            $table->boolean('shared')->default(false);
            $table->integer('folder_id')->nullable()->default(null);
            $table->timestamps();
            $table->unique(['user_id', 'name']);
            $table->foreign('folder_id')
                ->references('id')
                ->on('folders')
                ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cb_projects');
    }
};
