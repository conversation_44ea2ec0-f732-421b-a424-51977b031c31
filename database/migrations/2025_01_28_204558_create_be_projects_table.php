<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('be_projects', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('status')->nullable()->default(null);
            $table->string('field')->nullable()->default(null);
            $table->json('value')->nullable()->default(null);
            $table->string('method')->nullable()->default(null);
            $table->json('terms')->nullable()->default(null);
            $table->string('directory')->nullable()->default(null);
            $table->boolean('shared')->default(false);
            $table->timestamps();
            $table->unique(['user_id','name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('be_projects');
    }
};
