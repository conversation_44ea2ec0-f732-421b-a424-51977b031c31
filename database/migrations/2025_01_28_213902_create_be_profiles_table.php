<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('be_profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->ulid('be_project_id');
            $table->json('profile')->nullable()->default(null);
            $table->boolean('shared')->default(false);
            $table->timestamps();
            $table->unique(['user_id','be_project_id']);
            $table->foreign('be_project_id')
                ->references('id')
                ->on('be_projects')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('be_profiles');
    }
};
