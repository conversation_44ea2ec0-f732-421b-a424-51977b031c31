<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assertions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('base');
            $table->boolean('is_base_lib')->default(false);
            $table->string('secondary');
            $table->boolean('is_secondary_lib')->default(false);
            $table->boolean('is_lib_assertion')->default(false);
            $table->boolean('is_lib_to_lib_assertion')->default(false);
            $table->boolean('use_significant')->default(false);
            $table->string('engine');
            $table->timestamps();
            $table->unique(['user_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assertions');
    }
};
