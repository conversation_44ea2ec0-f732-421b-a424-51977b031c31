<?php

namespace Database\Seeders;

use App\Models\CbProject;
use App\Models\User;
use App\Models\Lab;
use App\Models\Folder;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\Assertion;
use App\Models\AccountRequest;
use Illuminate\Database\Seeder;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    // use WithoutModelEvents;
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            LegacyDataImportSeeder::class,
        ]);
        // User::factory(10)->create();
        // $lab = Lab::factory()->create([
        //     'name' => 'cmarcum',
        //     'pi_name' => '<PERSON>',
        //     'pi_email' => '<EMAIL>',
        //     'organization' => 'GTAC@MGI',
        //     'notes' => 'Alpha and Omega',
        //     'active' => true,
        //     'directory' => 'CMARCUM',
        // ]);

        // $this->createLegacyLabAccountFiles($lab);

        // User::factory()->create([
        //     'name' => '<PERSON>',
        //     'email' => '<EMAIL>',
        //     'email_verified_at' => now(),
        //     'password' => 'admin',
        //     'lab_id' => 1,
        //     'active' => true,
        //     'lab_admin' => true,
        //     'sudo' => true
        // ]);

        // User::factory()->create([
        //     'email' => '<EMAIL>',
        //     'email_verified_at' => now(),
        //     'password' => 'password',
        //     'lab_id' => 1,
        //     'active' => true,
        //     // 'sudo' => 0
        // ]);

        // /* CbProjects create from real migrated projects */
        // $projects = json_decode(file_get_contents('database/seeders/cb_project_data.json'));
        // foreach ($projects as $project) 
        // {
        //     CbProject::factory()->create([
        //         'name' => $project->name,
        //         'user_id' => 1,
        //         'status' => $project->status,
        //         'entity_count' => $project->entity_count,
        //         'concept_count' => $project->concept_count,
        //         'created_at' => $project->created_at,
        //         'list' => $project->list,
        //         'expression' => $project->expression ?? null
        //     ]);
        // }
        
        // /* Create a Folder */
        // Folder::create([
        //     'name' => 'Autism',
        //     'user_id' => 1,
        // ]);

        // /* Add Projects to Folder */
        // CbProject::whereIn('name', ['230509_Autism', '230509_Autism_V2'])->update(['folder_id' => 1]);

        // /* Load CbProfile Data */
        // $profiles = json_decode(file_get_contents('database/seeders/cb_project_profile_data.json'));
        // foreach ($profiles as $profile) 
        // {
        //     $projectName = basename(dirname($profile->path));
        //     $project = CbProject::firstWhere('name', $projectName);
        //     if($project !== null)
        //     {
        //         CbProfile::create([
        //             'user_id' => 1,
        //             'cb_project_id' => $project->id,
        //             'profile' => $profile->contents
        //         ]);
        //     }
        // }

        // /* Load BeProject Data */
        // $bes = json_decode(file_get_contents("database/seeders/be_project_data.json"));
        // foreach ($bes as $be) 
        // {
        //     BeProject::create([
        //         'name' => $be->Name,
        //         'user_id' => 1,
        //         'status' => 'complete',
        //         'field' => (isset($be->Field_Type)) ? $be->Field_Type : null,
        //         'value' => (gettype($be->Value_Submitted) == 'string') ? (array)str_replace("'", "", $be->Value_Submitted) : $be->Value_Submitted,
        //         'method' => (isset($be->Method)) ? $be->Method : null,
        //         'terms' => (isset($be->Terms)) ? explode(",", $be->Terms) : null,
        //         'directory' => $be->Name,
        //         'shared' => rand(0, 1)
        //     ]);
        // }

        // /* Load Assertion Data */
        // $asrts = json_decode(file_get_contents('database/seeders/assertion_data.json'));
        // foreach ($asrts as $asrt) 
        // {
        //     $info = $asrt->contents;
        //     Assertion::create([
        //         'user_id' => 1,
        //         'name' => $info->name,
        //         'base' => $info->base,
        //         'is_base_lib' => $info->isBaseLib,
        //         'secondary' => $info->secondary,
        //         'is_secondary_lib' => $info->isSecondaryLib,
        //         'is_lib_assertion' => $info->isLibAssertion,
        //         'is_lib_to_lib_assertion' => $info->isLibToLibAssertion,
        //         'engine' => $info->engine,
        //     ]);
        // }
        
        // AccountRequest::factory(10)->create();
    }

        private function createLegacyLabAccountFiles($data)
    {
        /**
         *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']);
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'failed';
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'processing';
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'trash';
    	 *  mkdir($newdir, 0777);
    	 *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'account_info';
    	 *  mkdir($newdir, 0777);
    	 *  $path = $newdir.DIRECTORY_SEPARATOR.'info.json';
    	 *  $file = fopen($path, 'w');
    	 *  flock($file, LOCK_EX);
    	 *  fwrite($file, json_encode($acctinfo, JSON_PRETTY_PRINT));
         */
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory), 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'failed', 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'processing', 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'trash', 
            0777,
            true
        );
        mkdir(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'account_info', 
            0777,
            true
        );
        file_put_contents(
            config('app.custom_env.ACCOUNT_STORAGE_ROOT') . DIRECTORY_SEPARATOR . strtoupper($data->directory) . DIRECTORY_SEPARATOR . 'account_info' . DIRECTORY_SEPARATOR . 'info.json', 
            json_encode($data, JSON_PRETTY_PRINT)
        );
    }

}
