<?php
namespace Database\Seeders;
use Illuminate\Database\Seeder;
use App\Models\Lab;
use App\Models\User;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\Folder;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\AssertionLibrary;
use App\Models\Filter;
use App\Models\Themelink;
require_once("/var/www/html/config/config.php");
/* 
* Import Legacy Data
*/
class LegacyDataImportSeeder extends Seeder
{
   /**
     * Run the Legacy Data Importer
     *
     * @return void
     */
   public function run()
   {
      /* 
      * Lab and Pi Data Import from accts.php file
      */
      $sudos = [
         '<EMAIL>',
         '<EMAIL>',
         '<EMAIL>',
         '<EMAIL>',
         '<EMAIL>',
         '<EMAIL>',
         'jared.la<PERSON><PERSON><PERSON>@wustl.edu'
      ];
      $data = (object)[
         'importDateTime' => now(),
         'labs' => [],
         'users' => [],
         'cbProjects' => [],
         'cbProfiles' => [],
         'folders' => [],
         'beProjects' => [],
         'beProfiles' => [],
         'assertions' => [],
         'assertionLibraries' => [],
         'filters' => [],
         'themelinks' => [],
         'unknownUserEmails' => []
      ];
      $acctsFile = trim(file_get_contents("/var/www/html/acct/accts_mod.php"), "<?php '");
      $accts = json_decode($acctsFile);
      /* 
      * Scan Legacy Data Directories
      */
      $accountDirs = array_values(array_diff(scandir(ACCOUNT_STORAGE_ROOT),['.','..']));
      foreach($accts as $acct) {
         if(!in_array(strtolower($acct->Laboratory), array_map('strtolower',$accountDirs))) continue;
         $lab = Lab::create([
            'name' => $acct->Laboratory,
            'pi_name' => 'missing',
            'pi_email' => $acct->Email,
            'organization' => null,
            'notes' => null,
            'active' => true,
            'directory' => strtoupper($acct->Laboratory)
         ]);
         $this->createLegacyLabAccountFiles($lab);
         array_push($data->labs, $lab);
         $user = User::create([
            'name' => 'missing',
            'email' => $acct->Email,
            'email_verified_at' => now(),
            'password' => $acct->Password,
            'lab_id' => $lab->id,
            'active' => true,
            'lab_admin' => true,
            'sudo' => in_array($acct->Email, $sudos)
         ]);
         array_push($data->users, $user);
      }
      $knownUserEmails = User::all()->pluck('email')->toArray();
      foreach($accountDirs as $accountDir) {
         $lab = Lab::where('directory', strtoupper($accountDir))->first();
         if(is_null($lab)) continue;
         $labPi = $lab->pi;
         if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/projects.json")) {
            $folders = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/projects.json"))->folders ?? null;
         } else {
            $folders = null;
         }
         $projectDirs = array_values(array_diff(scandir(ACCOUNT_STORAGE_ROOT."/{$accountDir}"),['.','..','account_info','failed','trash','processing']));
         foreach($projectDirs as $projectDir) {
            if(
               file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/{$projectDir}_Genes.js") && 
               file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/{$projectDir}_Genes_HTML.json") && 
               file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/{$projectDir}_Theme_Coordinates.js") && 
               file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/Input/info.json")
            ) {
               $info = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/Input/info.json"));
               if(isset($info->Submitted_By)) {
                  if(!in_array(strtolower($info->Submitted_By), $knownUserEmails) && !in_array(strtolower($info->Submitted_By), $data->unknownUserEmails)) {
                     array_push($data->unknownUserEmails, strtolower($info->Submitted_By));
                  }
               }
               if(isset($info->Bio_Explorer) && $info->Bio_Explorer) {
                  if(
                     property_exists($info, 'Method') &&
                     property_exists($info, 'Value_Submitted') &&
                     property_exists($info, 'Terms')
                  ) {
                     $cleanProjectDir = str_replace('.', '', $projectDir);
                     $project = BeProject::create([
                        'name' => $cleanProjectDir,
                        'user_id' => $labPi->id,
                        'status' => 'complete',
                        'field' => $info->Field_Type ?? null,
                        'value' => (gettype($info->Value_Submitted) == 'string') ? (array)str_replace("'", "", $info->Value_Submitted) : $info->Value_Submitted,
                        'method' => $info->Method,
                        'terms' => (isset($info->Terms)) ? explode(",", $info->Terms) : null,
                        'directory' => config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}/{$cleanProjectDir}",
                        'shared' => false
                     ]);
                     array_push($data->beProjects, $project);
                     $this->copyItem(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}", $project->directory);
                     if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/UserData/userProfileData.txt")) {
                        $profile = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/UserData/userProfileData.txt"));
                        if(count(array_keys(get_object_vars($profile))) > 0) {
                           BeProfile::create([
                              'user_id' => $labPi->id,
                              'be_project_id' => $project->id,
                              'profile' => $profile,
                              'shared' => false
                           ]);
                           array_push($data->beProfiles, $profile);
                        }
                     }
                  }
               } else {
                  if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/Input/input.txt")) {
                     $cleanProjectDir = str_replace('.', '', $projectDir);
                     $list = explode("\n", trim(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/Input/input.txt")));
                     $project = CbProject::create([
                        'name' => $cleanProjectDir,
                        'user_id' => $labPi->id,
                        'status' => 'complete',
                        'entity_count' => count($list),
                        'concept_count' => isset($info->Num_Concepts) && $info->Num_Concepts != 'No Data...' ? $info->Num_Concepts : 500,
                        'list' => $list,
                        'directory' => config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}/{$cleanProjectDir}",
                        'shared' => false
                     ]);
                     array_push($data->cbProjects, $project);
                     $this->copyItem(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}", $project->directory);
                     if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/UserData/userProfileData.txt")) {
                        $profile = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/{$projectDir}/UserData/userProfileData.txt"));
                        if(count(array_keys(get_object_vars($profile))) > 0) {
                           CbProfile::create([
                              'user_id' => $labPi->id,
                              'cb_project_id' => $project->id,
                              'profile' => $profile,
                              'shared' => false
                           ]);
                           array_push($data->cbProfiles, $profile);
                        }
                     }
                  }
               }
               if (isset($project) && is_object($project)) {
                  $folderId = $this->getLegacyProjectFolder($project->name, $folders, $labPi->id);
                  if($folderId) {
                     $project->folder_id = $folderId;
                     $project->save();
                     $folder = Folder::find($folderId);
                     array_push($data->folders, $folder);
                  }
               }
            }
         }
         /* 
         * Migrate Filters Data
         */
         if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/filters")) {
            $filters = array_values(array_diff(scandir(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/filters"),['.','..']));
            foreach($filters as $filter) {
               if(
                  file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/filters/{$filter}/filter.json") && 
                  file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/filters/{$filter}/filterInfo.json")
               ) {
                  $filterInfo = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/filters/{$filter}/filterInfo.json"));
                  $filterList = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/filters/{$filter}/filter.json"));
                  $dbFilter = Filter::create([
                     'user_id' => $labPi->id,
                     'name' => str_replace('.', '', $filterInfo->FilterName),
                     'base' => $filterInfo->BaseProject,
                     'secondary' => $filterInfo->Project2,
                     'list' => $filterList
                  ]);
                  array_push($data->filters, $dbFilter);
                  $this->copyItem(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/filters/{$filter}", config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}/account_info/filters/{$dbFilter->name}");
               }
            }
         }
         /* 
         * Migrate Assertion2 Data
         * Dropping Support for Assertion 1
         */
         if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion2")) {
            $assertions = array_values(array_diff(scandir(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion2"),['.','..']));
            foreach($assertions as $assertion) {
               if( file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion2/{$assertion}/info.json") ) {
                  $info = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion2/{$assertion}/info.json"));
                  $dbAssertion = Assertion::create([
                     'user_id' => $labPi->id,
                     'name' => str_replace('.', '', $info->name),
                     'base' => $info->base,
                     'is_base_lib' => $info->isBaseLib,
                     'secondary' => $info->secondary,
                     'is_secondary_lib' => $info->isSecondaryLib,
                     'is_lib_assertion' => $info->isLibAssertion,
                     'is_lib_to_lib_assertion' => $info->isLibToLibAssertion,
                     'use_significant' => $info->useSignificant ?? false,
                     'engine' => $info->engine
                  ]);
                  array_push($data->assertions, $dbAssertion);
                  $this->copyItem(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion2/{$assertion}", config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}/account_info/assertion2/{$dbAssertion->name}");
               }
            }
         }
         /* 
         * Migrate Assertion Libraries
         */
         if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion_libs")) {
            $libs = array_values(array_diff(scandir(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion_libs"),['.','..']));
            foreach($libs as $lib) {
               if( file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion_libs/{$lib}/lib_info.json") ) {
                  $info = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/assertion_libs/{$lib}/lib_info.json"));
                  $projectNames = [];
                  $projectIds = [];
                  $projectUserIds = [];
                  $projectOwnwerId = $labPi->id;
                  foreach($info->Projects as $libProject) {
                     $tmpProject = $lab->cbProjects->where('name', $libProject)->first();
                     if($tmpProject) {
                        array_push($projectNames, $tmpProject->name);
                        array_push($projectIds, $tmpProject->id);
                        array_push($projectUserIds, $tmpProject->user_id);
                     } else {
                        $tmpProject = $lab->beProjects->where('name', $libProject)->first();
                        if($tmpProject) {
                           array_push($projectNames, $tmpProject->name);
                           array_push($projectIds, $tmpProject->id);
                           array_push($projectUserIds, $tmpProject->user_id);
                        }
                     }
                  }
                  if(count($projectNames) > 0) {
                     if (!empty($projectUserIds) && count(array_unique($projectUserIds)) === 1) {
                        $projectOwnwerId = $projectUserIds[0];
                        if($projectOwnwerId !== $labPi->id) {
                           if(!$lab->users->pluck('id')->contains($projectOwnwerId)) {
                              $projectOwnwerId = $labPi->id;
                           }
                        }
                     }
                     $dbAssertionLib = AssertionLibrary::create([
                        'user_id' => $projectOwnwerId,
                        'name' => $info->Name,
                        'projects' => $projectNames,
                        'project_ids' => $projectIds
                     ]);
                     array_push($data->assertionLibraries, $dbAssertionLib);
                  }
               }
            }
         }
         /* 
         * Migrate ThemeLinks Data
         */
         if(file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/themelinks")) {
            $themelinks = array_values(array_diff(scandir(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/themelinks"),['.','..']));
            foreach($themelinks as $themelink) {
               if( 
                  file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/themelinks/{$themelink}/info.json") &&
                  file_exists(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/themelinks/{$themelink}/comparisons.pdf")               
               ) {
                  $info = json_decode(file_get_contents(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/themelinks/{$themelink}/info.json"));
                  $cleanThemelink = str_replace('.', '', $themelink);
                  $dbThemelink = Themelink::create([
                     'user_id' => $labPi->id,
                     'name' => $cleanThemelink,
                     'base' => $info->base_name,
                     'secondary' => $info->secondary_name,
                     'directory' => config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}/account_info/themelinks/{$cleanThemelink}"
                  ]);
                  array_push($data->themelinks, $dbThemelink);
                  $this->copyItem(ACCOUNT_STORAGE_ROOT."/{$accountDir}/account_info/themelinks/{$themelink}", config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}/account_info/themelinks/{$cleanThemelink}");
               }
            }
         }
      }
      $filePath = storage_path('logs/legacy_import.log');
      file_put_contents($filePath, json_encode($data, JSON_PRETTY_PRINT));
   }
   /**
    * Asynchronous copy using rsync with logging.
    * Logs all output and errors to storage/logs/rsync.log.
    */
   private function copyItem($source, $destination) {
      // Ensure correct trailing slashes
      $source = rtrim($source, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
      $destination = rtrim($destination, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;

      // Escape for shell safety
      $escapedSource = escapeshellarg($source);
      $escapedDestination = escapeshellarg($destination);

      // Get full path to log file
      $logPath = storage_path('logs/rsync.log');
      $escapedLogPath = escapeshellarg($logPath);

      // Build the rsync command with output redirected to log
      $command = "rsync -a $escapedSource $escapedDestination >> $escapedLogPath 2>&1 &";

      // Execute asynchronously
      exec($command);
   }
   /*
   * Check if Legacy Project is in a Folder
   * Create Folder if it does not exist
   * Return Folder ID
   */
   private function getLegacyProjectFolder($projectName, $folders, $userId)
   {
      foreach($folders as $folderName => $folderArray) {
         if(in_array($projectName, $folderArray)) {
            if(Folder::where('name', $folderName)->where('user_id', $userId)->exists()) {
               $folder = Folder::where('name', $folderName)->where('user_id', $userId)->first();
               return $folder->id;
            } else {
               $folder = Folder::create([
                  'name' => $folderName,
                  'user_id' => $userId
               ]);
               return $folder->id;
            }
         }
      }
      return null;
   }
   /*
   * Create Legacy Lab Account Files
   */
   private function createLegacyLabAccountFiles($data)
   {
      /**
      *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']);
      *  mkdir($newdir, 0777);
      *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'failed';
      *  mkdir($newdir, 0777);
      *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'processing';
      *  mkdir($newdir, 0777);
      *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'trash';
      *  mkdir($newdir, 0777);
      *  $newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'account_info';
      *  mkdir($newdir, 0777);
      *  $path = $newdir.DIRECTORY_SEPARATOR.'info.json';
      *  $file = fopen($path, 'w');
      *  flock($file, LOCK_EX);
      *  fwrite($file, json_encode($acctinfo, JSON_PRETTY_PRINT));
      */
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory), 
         0777,
         true
      );
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/failed', 
         0777,
         true
      );
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/processing', 
         0777,
         true
      );
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/trash', 
         0777,
         true
      );
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/account_info', 
         0777,
         true
      );
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/account_info/filters', 
         0777,
         true
      );
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/account_info/assertion2', 
         0777,
         true
      );
      mkdir(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/account_info/themelinks', 
         0777,
         true
      );
      file_put_contents(
         config('app.custom_env.ACCOUNT_STORAGE_ROOT').'/'.strtoupper($data->directory).'/account_info/info.json', 
         json_encode($data, JSON_PRETTY_PRINT)
      );
   }
}