[{"path": "G:\\wamp64\\www\\compbio_dev2\\public\\legacy\\data\\CMARCUM\\account_info\\assertion2\\AD_ANIMAL2HUMAN_AE\\info.json", "contents": {"account": "RBARVE", "name": "AD_ANIMAL2HUMAN_AE", "creation_date": "2023-02-28 10:43:35", "base": "AD_LIBRARY_FEB23", "isBaseLib": true, "secondary": "AD_LIBRARY_FEB23", "isSecondaryLib": true, "isLibAssertion": true, "isLibToLibAssertion": true, "engine": "2.4.3"}}, {"path": "G:\\wamp64\\www\\compbio_dev2\\public\\legacy\\data\\CMARCUM\\account_info\\assertion2\\AD_ANIMAL2HUMAN_AE_signal\\info.json", "contents": {"account": "RBARVE", "name": "AD_ANIMAL2HUMAN_AE_signal", "creation_date": "2023-02-28 12:31:40", "base": "AD_LIBRARY_Feb23_signal", "isBaseLib": true, "secondary": "AD_LIBRARY_Feb23_signal", "isSecondaryLib": true, "isLibAssertion": true, "isLibToLibAssertion": true, "engine": "2.4.3"}}, {"path": "G:\\wamp64\\www\\compbio_dev2\\public\\legacy\\data\\CMARCUM\\account_info\\assertion2\\AD_test\\info.json", "contents": {"account": "RBARVE", "name": "AD_test", "creation_date": "2023-03-08 07:28:26", "base": "HumanAD_Down_Antonell", "isBaseLib": false, "secondary": "HumanAD_FAD-PSEN1_DOWN", "isSecondaryLib": false, "isLibAssertion": false, "isLibToLibAssertion": false, "engine": "2.4.3"}}, {"path": "G:\\wamp64\\www\\compbio_dev2\\public\\legacy\\data\\CMARCUM\\account_info\\assertion2\\asst2.0-2\\info.json", "contents": {"account": "cmarcum", "name": "asst2.0-2", "creation_date": "2020-12-15 01:39:55", "base": "lib-to-lib-1-correction", "isBaseLib": true, "secondary": "lib-to-lib-2", "isSecondaryLib": true, "isLibAssertion": true, "isLibToLibAssertion": true, "engine": "2.0"}}, {"path": "G:\\wamp64\\www\\compbio_dev2\\public\\legacy\\data\\CMARCUM\\account_info\\assertion2\\asst2.0-3\\info.json", "contents": {"account": "cmarcum", "name": "asst2.0-3", "creation_date": "2020-12-15 04:04:48", "base": "Mine-ALS_studies_up_v_A_ALS_up", "isBaseLib": true, "secondary": "TestLib1", "isSecondaryLib": true, "isLibAssertion": true, "isLibToLibAssertion": true, "engine": "2.0"}}]