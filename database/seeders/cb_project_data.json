[{"name": "230509_Autism", "created_at": "2023-05-09 10:47:32", "entity_count": 1059, "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4", "NPAS3", "NPIPB9", "NR1D2", "NREP", "NWD1", "NXPH4", "OGDH", "OR12D3", "OR2J3", "OR5BA1P", "OR5V1", "OTUD7B", "PAX4", "PBX2P1", "PCBP2P1", "PCDH17", "PELI1", "PFDN1", "PGAM1P1", "PHKBP2", "PKIA-AS1", "PLCL1", "PLEKHG7", "POC1B", "POLD2P1", "POU3F2", "PPA2", "PPDPF", "PPP1R16B", "PPP1R3B", "PPP2R2D", "PREX1", "PRKAG1", "PRKCD", "PSMB9", "PTPRF", "PUS7", "RAET1E-AS1", "RALB", "RCC2P3", "RERE", "RERE-AS1", "RN7SKP122", "RN7SKP159", "RN7SKP19", "RN7SKP222", "RN7SKP285", "RN7SKP32", "RN7SKP65", "RN7SL167P", "RN7SL423P", "RN7SL782P", "RNA5SP142", "RNA5SP169", "RNA5SP38", "RNA5SP403", "RNA5SP427", "RNA5SP459", "RNA5SP471", "RNA5SP94", "RNF111", "RNU1-61P", "RNU1-98P", "RNU11-5P", "RNU6-1029P", "RNU6-1060P", "RNU6-1116P", "RNU6-1133P", "RNU6-1246P", "RNU6-133P", "RNU6-21P", "RNU6-243P", "RNU6-334P", "RNU6-699P", "RNU7-147P", "RNU7-160P", "RP1L1", "RPL10P19", "RPL10P2", "RPL21P17", "RPL31P12", "RPL34P23", "RPL7P20", "RPL8P1", "RPLP0P1", "RPS4XP9", "RPSAP2", "RSL24D1P1", "RUNX1T1", "SATB2", "SBK1", "SCG3", "SEPTIN4", "SERPINA1", "SFTA2", "SH3D19", "SIPA1L1-AS1", "SLC17A6", "SLC25A5P5", "SLC25A6P5", "SLC35G2", "SLC38A7", "SLC39A8", "SLC44A3P1", "SMARCA2", "SMIM15-AS1", "SNHG18", "SNX19", "SPATA33", "SPCS1", "SPPL2C", "SRPK2", "ST13P7", "ST3GAL3-AS1", "STARD4-AS1", "SUMO2P1", "SUMO2P2", "SYNPR", "TAB1", "TANK-AS1", "TCF15", "TEX44", "TFAP2B", "TFAP2D", "TMPRSS5", "TOB2P1", "TRIM31-AS1", "TRIM8", "TSBP1-AS1", "TSHZ3-AS1", "UBA7", "UBQLN1P1", "UNGP1", "UNGP3", "USP4", "VENTXP5", "VN1R10P", "WNT1", "WNT4", "XRN2", "ZDHHC20P2", "ZFP57", "ZNF318", "ZNF592", "ZNF799", "ZNF800", "ABCB9", "ABCC10", "ACTG1P17", "ACTG1P22", "ACTL11P", "ACTR5", "ADGRL4", "ADTRP", "AFF3", "AKAP6", "ALPK3", "AMBRA1", "AMELY", "AMIGO1", "ANAPC4", "ANK3", "ANKS1B", "ANO5", "ANXA9", "ARFGEF2", "ARHGAP15", "ARID1B", "ARL3", "ARPP21", "ARTN", "ASAP1", "ATAT1", "ATP23", "ATP2A2", "ATP2B2", "ATXN2L", "AUTS2", "BAG5", "BANK1", "BCL11A", "BCL11B", "BEND4", "BNIP3", "BORCS7-ASMT", "BRAF", "BRD2", "BRWD1", "BSN", "BSX", "BTN2A1", "BTN2A2", "BTN3A1", "BTN3A2", "C2", "C5orf64", "C6orf15", "CA10", "CACNA1C", "CACNA1C-IT3", "CACNA1I", "CACNA2D2", "CACNB2", "CADM2", "CADPS", "CALN1", "CALU", "CAMKV", "CAMSAP2", "CAPZA1P4", "CAPZBP1", "CASC15", "CCDC68", "CCDC88C", "CD55", "CDC42", "CDH8", "CDHR4", "CDK10", "CDK4P1", "CDKAL1", "CENPBD1P1", "CENPW", "CHRNA3", "CHRNA5", "CHST1", "CISD2", "CITED1", "CKB", "CLCN3", "CLEC17A", "CLSTN2", "CMAHP", "CNKSR2", "CNNM2", "CNOT1", "CNTN4", "COA8", "COL11A1", "CPEB1", "CSDE1", "CSMD1", "CSRNP3", "CTBP2P4", "CTDSP2", "CTNNB1", "CTNND1", "CTR9", "CUEDC2", "CUL3", "CYCSP7", "CYP2D7", "CYSTM1", "DBN1", "DCAF12L2", "DCC", "DDAH1", "DDN", "DDN-AS1", "DDR1", "DDX10P2", "DDX27", "DDX3Y", "DEFB112", "DGKI", "DHFRP2", "DHRS11", "DMTF1", "DMXL2", "DOCK8", "DPF3", "DPP4", "DPP8", "DPYD", "DRAXIN", "DRD2", "DUSP6", "DYNLL1P1", "DYSF", "EEF1A2", "EFL1", "EFNA5", "EGR2", "EHMT2", "EIF1AY", "EIF4E3", "EIF4EBP2P3", "ELAVL2", "ENOX1", "EP300-AS1", "EPHX2", "ERLIN1", "ESRRB", "ESRRG", "ETF1", "EXOC4", "EXT1", "EYA1", "F2", "FADS2", "FAF2P1", "FALEC", "FAM167A-AS1", "FAM193A", "FBXL17", "FBXW12", "FBXW4", "FGF12", "FGFR1", "FGFR3P1", "FHIP1A", "FHIP1A-DT", "FHIT", "FLJ46284", "FOLH1B", "FOXB1", "FOXO3", "FOXO6", "FOXP1", "FSCN3", "FSHB", "FSTL5", "FTLP17", "FTLP18", "FURIN", "FXR1", "GABBR1", "GAD1", "GALNT1", "GALNT10", "GALNT12", "GATAD2B", "GATB", "GBF1", "GCC1", "GGNBP1", "GID4", "GIGYF2", "GLCCI1", "GLT8D1", "GMIP", "GPD2", "GPR26", "GPR89P", "GPX1", "GRAMD1B", "GRIA1", "GRID2", "GRIK1", "GRIN2A", "GRM3", "GRM5", "GUCY1A2", "GULOP", "GUSBP2", "H2AC10P", "H2AZ2", "H2BC16P", "H3C9P", "HBEGF", "HCG20", "HCG27", "HCG9", "HCN1", "HCP5", "HCP5B", "HDAC4", "HFE", "HIGD1AP18", "HINT1", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DQA1", "HLA-DQA2", "HLA-DQB1", "HLA-DQB3", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-S", "HLA-T", "HLA-U", "HLA-W", "HLA-Z", "HMGN4", "HNRNPA1P46", "HNRNPA1P48", "HS6ST3", "HSPD1P6", "HSPE1-MOB4", "HTR1A", "HYI-AS1", "IER5L", "IFITM4P", "IGSF9B", "IL1R1", "IL20RB", "IL27", "IMMP2L", "IMPA2", "INHCAP", "IPO9-AS1", "ISCA1P1", "ITGA11", "ITIH3", "ITIH4", "IVD", "JMJD1C", "KANSL1", "KCNB1", "KCNG2", "KCNJ6", "KCNN2", "KDM3B", "KDM4A", "KDM4A-AS1", "KDM5D", "KIF21B", "KIF5C", "KIZ", "KIZ-AS1", "KLC1", "KMT2A", "KMT2D", "KMT2E", "L3MBTL2", "LCORL", "LDB1", "LDHAP3", "LINC00240", "LINC00243", "LINC00343", "LINC00376", "LINC00461", "LINC00533", "LINC00691", "LINC00707", "LINC00907", "LINC00922", "LINC01012", "LINC01104", "LINC01149", "LINC01151", "LINC01239", "LINC01339", "LINC01358", "LINC01360", "LINC01415", "LINC01416", "LINC01470", "LINC01498", "LINC01572", "LINC01645", "LINC01648", "LINC01707", "LINC01830", "LINC01876", "LINC01929", "LINC01957", "LINC01958", "LINC01982", "LINC02057", "LINC02058", "LINC02153", "LINC02163", "LINC02172", "LINC02210-CRHR1", "LINC02240", "LINC02261", "LINC02295", "LINC02315", "LINC02326", "LINC02397", "LINC02404", "LINC02571", "LINC02585", "LINC02607", "LINC02694", "LINC02730", "LINC02790", "LINC02797", "LINC02829", "LINC02841", "LMAN2L", "LONRF2", "LRFN5", "LRRC20", "LRRC58", "LUZP2", "LYZL1", "MACROD2", "MAD1L1", "MAIP1", "MAML3", "MAN2A1-DT", "MANBA", "MAPT", "MAPT-AS1", "MAST3", "MCCD1P1", "MCPH1", "MDK", "MED27", "MED8", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "METTL13", "METTL15", "MGAT3", "MGAT5B", "MICA", "MICB-DT", "MICC", "MIR1302-7", "MIR137HG", "MIR2113", "MIR4432HG", "MIRLET7BHG", "MMP16", "MMS22L", "MOCS2", "MOG", "MPHOSPH9", "MPP6", "MPPED1", "MRM2", "MROH5", "MRPL35P2", "MRTFA", "MSH5", "MSL2", "MSNP1", "MSRA", "MTCO3P1", "MTCYBP27", "MTHFD1L", "MTND4LP18", "MUC22", "MUCL3", "MYBPHL", "MYL12BP3", "MYO18A", "MYO9B", "NAA11", "NAALADL2", "NAB1P1", "NAPGP2", "NCOA5", "NDUFA2", "NEDD4L", "NEGR1", "NEK4", "NEK4P1", "NEURL1", "NFIA-AS2", "NFIX", "NGEF", "NKIRAS1", "NKX2-1-AS1", "NLRP4", "Nomappedgenes", "NOTCH4", "NOVA1", "NOVA1-DT", "NPAS1", "NPAS3", "NPIPB6", "NPIPB9", "NPM3", "NR1D2", "NREP", "NRGN", "NT5C2", "NT5DC2", "NTM", "NTRK3", "OGA", "OLFM4", "OR11A1", "OR12D2", "OR2J2", "OR2M4", "OR5AZ1P", "OR5V1", "OSBPL3", "PAFAH1B2P2", "PALB2", "PARD3B", "PAUPAR", "PBX2", "PCDH9", "PCGEM1", "PCGF3", "PCLO", "PCNX1", "PDE1C", "PDE4B", "PDZPH1P", "PEF1", "PFAS", "PHKBP2", "PIK3R2", "PJA1", "PKD1L3", "PKIA-AS1", "PKN2-AS1", "PKP4", "PLCL1", "PLCL2", "PLK2", "PMFBP1", "PMM1", "POLD2P1", "POLR1H", "POLR2E", "POM121L2", "POU3F2", "PPA2", "PPM1E", "PPP1R13B", "PPP1R16A", "PPP1R16B", "PPP1R21-DT", "PPP1R2P1", "PPP2R2B", "PPP2R2D", "PPP2R5C", "PRELID1P1", "PREX1", "PRKD1", "PRKG1", "PRPF3", "PRRC2A", "PRRT1", "PRSS16", "PSMA5", "PSORS1C1", "PTCH1", "PTGES3P4", "PTH2R", "PTPRF", "PTPRO", "PUS7", "PXDNL", "QPCT", "RAET1E", "RALBP1P2", "RASGRP4", "RBFOX1", "RBL2", "RBM6", "RCOR2", "RERE", "RERE-AS1", "RFT1", "RGS6", "RHOA", "RHOJ", "RIMS1", "RN7SKP285", "RN7SKP60", "RN7SL167P", "RN7SL178P", "RN7SL423P", "RN7SL499P", "RN7SL571P", "RN7SL592P", "RN7SL714P", "RN7SL717P", "RN7SL782P", "RN7SL831P", "RN7SL89P", "RNA5SP107", "RNA5SP142", "RNA5SP169", "RNA5SP30", "RNA5SP38", "RNA5SP404", "RNA5SP87", "RNF144A", "RNF38", "RNU1-130P", "RNU1-61P", "RNU1-63P", "RNU1-98P", "RNU11-5P", "RNU6-1103P", "RNU6-1133P", "RNU6-1246P", "RNU6-21P", "RNU6-229P", "RNU6-334P", "RNU6-682P", "RNU6-819P", "RNU6-967P", "RNU7-147P", "RNU7-160P", "RPGRIP1L", "RPL10AP3", "RPL10P2", "RPL23AP12", "RPL23AP39", "RPL31P12", "RPL3P8", "RPL7P20", "RPL7P56", "RPS17P5", "RPS19BP1", "RPS23P5", "RPS6KL1", "RPSAP2", "RPSAP5", "RPTOR", "RSL24D1P1", "RSRC1", "RSU1", "RTN1", "RUNX1T1", "SATB2", "SBK1", "SCHLAP1", "SCMH1", "SEMA3A", "SEMA3F", "SEMA3F-AS1", "SEMA6D", "SEPTIN3", "SEPTIN4-AS1", "SERPINA2", "SETD6", "SF3B1", "SFTA2", "SGCZ", "SGF29", "SGO1-AS1", "SGSM2", "SHISA9", "SHMT2", "SLC12A5", "SLC22A23", "SLC30A9", "SLC35F2", "SLC39A8", "SLC52A2", "SLC6A9", "SLC8A3", "SLC9A9", "SLTM", "SMIM21", "SNAP91", "SND1", "SNORC", "SNX19", "SNX29", "SNX7", "SORBS1", "SORCS3", "SOX5", "SOX7", "SPAG16", "SPNS1", "SPPL3", "SPTLC1P2", "SREK1IP1", "SRPK2", "SRR", "SRXN1", "SSUH2", "ST3GAL3", "STAG1", "STAG1-DT", "STARD4-AS1", "STT3A", "SUMO2P2", "SYNE1", "SYNE2", "SYNGAP1", "SYNPR-AS1", "SYT1", "TAF1C", "TANK", "TANK-AS1", "TAS2R1", "TBL1Y", "TCAIM", "TCF20", "TCF4", "TCHP", "TENM2", "TET2", "TEX41", "TFB1M", "TFDP2", "THOC7", "THRB", "THSD4", "TLK1", "TMEM161B-DT", "TMEM170B", "TMEM185B", "TMEM219", "TMEM38A", "TMEM38B", "TMF1P1", "TMOD3", "TMX2", "TNRC6A", "TNXB", "TRAIP", "TRIM10", "TRIM26", "TRIM31", "TRIM33", "TRIM8", "TRIOBP", "TRMT61A", "TSBP1", "TSBP1-AS1", "TSHZ3", "TSHZ3-AS1", "TSNARE1", "TTC21B", "TTLL7", "TUBB", "TUBB3", "TWF2", "TYR", "U3", "UBQLN1P1", "UFL1-AS1", "USP4", "UTY", "VPS45", "VPS54", "VRK2", "WNT3", "WRN", "XKR6", "XRCC6", "Y_RNA", "ZBED3-AS1", "ZBED9", "ZCCHC7", "ZDHHC20P1", "ZFPM2", "ZFYVE1", "ZKSCAN3", "ZKSCAN8", "ZMIZ1", "ZMIZ2", "ZNF19", "ZNF192P2", "ZNF204P", "ZNF354A", "ZNF443", "ZNF536", "ZNF568", "ZNF600", "ZNF615", "ZNF638", "ZNF800", "ZNF804A", "ZNF823", "ZNF877P", "ZP3P2", "ZSCAN12", "ZSCAN16-AS1", "ZSCAN31", "ZSCAN9", "ZSWIM6"]}, {"name": "230509_Autism_V2", "created_at": "2023-05-09 10:47:32", "entity_count": 1059, "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"]}, {"name": "CbDevTest122022_1", "created_at": "2022-12-20 04:43:34", "entity_count": 725, "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"], "expression": ["-1.628305", "-2.525218", "-1.661387", "-1.560781", "-1.707967", "-1.776988", "-2.958527", "-2.755207", "-1.573297", "-1.711685", "-1.943028", "-1.644441", "-2.278154", "-1.59866", "-1.536072", "-1.536656", "-1.734495", "-1.583413", "-4.45301", "-1.752684", "-1.879517", "-1.60357", "-1.592105", "-1.578431", "-1.744621", "-1.679803", "-2.583532", "-1.622977", "-3.55118", "-1.675106", "-5.396096", "-1.92066", "-2.108963", "-1.823855", "-1.569379", "-1.730862", "-1.571953", "-2.888248", "-2.041097", "-1.97143", "-1.609553", "-2.494671", "-1.964063", "-2.162513", "-2.107222", "-7.941612", "-2.651498", "-1.608344", "-1.675186", "-1.573685", "-6.050337", "-1.591992", "-1.600899", "-1.524589", "-1.624835", "-1.503031", "-2.270014", "-1.608882", "-1.666738", "-3.65409", "-4.548578", "-1.553489", "-1.848759", "-1.572604", "-2.404145", "-1.588093", "-1.587439", "-1.501063", "-1.641163", "-1.540938", "-1.927199", "-2.324343", "-1.581451", "-14.929433", "-1.782553", "-2.678134", "-2.324503", "-1.747283", "-1.516982", "-3.636062", "-2.224102", "-1.747633", "-3.993563", "-1.661971", "-1.76034", "-3.329811", "-1.565455", "-1.740645", "-1.567686", "-1.664682", "-2.125716", "-1.593794", "-1.588973", "-1.99278", "-3.477606", "-1.590185", "-6.472607", "-1.533934", "-1.569951", "-1.525722", "-2.558032", "-1.532063", "-2.702196", "-2.915405", "-3.626957", "-3.371405", "-7.118405", "-1.958621", "-1.501786", "-4.854664", "-1.961705", "-1.919824", "-1.989347", "-2.655507", "-1.933472", "-1.575155", "-1.838402", "-1.675077", "-3.72992", "-1.998367", "-1.735677", "-1.860056", "-1.569103", "-2.095829", "-2.196583", "-1.618716", "-1.53779", "-1.56277", "-1.951208", "-1.864036", "-1.54967", "-1.581677", "-2.017578", "-1.630946", "-1.63604", "-1.867061", "-1.551125", "-1.639399", "-2.032388", "-1.546888", "-1.716284", "-1.873386", "-1.51271", "-1.640992", "-1.934979", "-1.557742", "-1.691098", "-1.515344", "-1.832509", "-1.573411", "-1.517955", "-1.668772", "-1.533062", "-1.657875", "-3.73157", "-4.431275", "-1.54195", "-2.254855", "-1.52879", "-1.517698", "-1.908666", "-1.6556", "-3.129344", "-1.604655", "-1.535468", "-1.750765", "-1.687318", "-1.584289", "-2.033425", "-2.264196", "-2.08174", "-1.711795", "-2.543795", "-1.821102", "-2.085795", "-2.379804", "-3.210045", "-2.115908", "-1.976307", "-1.670148", "-1.551231", "-2.151198", "-2.20139", "-2.091211", "-1.601569", "-2.237288", "-1.50721", "-1.869431", "-1.748551", "-1.784516", "-2.285185", "-1.657336", "-1.743782", "-1.594481", "-1.581173", "-1.557476", "-1.524665", "-2.114188", "-5.864105", "-4.954958", "-1.832249", "-1.637147", "-1.506882", "-1.646793", "-1.561358", "-2.78744", "-2.287391", "-1.522944", "-1.959208", "-3.134825", "-1.595263", "-1.588189", "-2.399392", "-1.602962", "-1.531358", "-1.674045", "-4.34775", "-2.63866", "-1.575606", "-1.555849", "-1.601028", "-1.718144", "-1.723766", "-1.781671", "-1.521545", "-1.619109", "-1.562051", "-1.506815", "-3.085644", "-1.595306", "-1.504786", "-1.798333", "-1.561611", "-1.7926", "-1.746864", "-1.504783", "-1.602944", "-1.538334", "-1.793478", "-1.632048", "-1.530601", "-1.638836", "-2.145173", "-1.922136", "-1.887904", "-1.612028", "-1.503622", "-1.62872", "-1.963641", "-1.750133", "-1.767258", "-3.042919", "-1.885188", "-1.944961", "-1.641522", "-1.702293", "-1.584288", "-1.752568", "-1.562114", "-1.575516", "-1.636227", "-1.879925", "-1.639569", "-1.518962", "-1.728536", "-2.026106", "-1.562806", "-1.52605", "-4.240427", "-1.780087", "-1.797225", "-1.51149", "-1.754117", "-1.568453", "-2.230746", "-5.244658", "-1.698777", "-1.564598", "-1.78717", "-1.716354", "-1.816307", "-2.681276", "-1.784954", "-1.721818", "-1.51121", "-2.454549", "-1.602362", "-1.590493", "-1.867838", "-1.523421", "-1.700794", "-1.583815", "-1.796995", "-1.737765", "-1.763915", "-1.529642", "-3.238563", "-1.58006", "-1.869852", "-2.913592", "-1.54157", "-1.527463", "-1.640132", "-1.594915", "-1.584499", "-1.51524", "-1.511091", "-1.87365", "-1.799421", "-1.795886", "-1.931966", "-1.561006", "-1.657783", "-1.546985", "-1.568637", "-1.54316", "-1.604688", "-1.936328", "-1.546139", "-1.659773", "-1.725886", "-1.831368", "-1.55597", "-1.582431", "-2.47045", "-1.657489", "-1.548402", "-1.752277", "-1.543031", "-1.700866", "-1.775592", "-1.632981", "-1.643885", "-1.613737", "-1.523494", "-1.948654", "-2.181618", "-1.512449", "-1.794462", "-1.549197", "-3.578145", "-1.851715", "-1.564384", "-1.551178", "-1.567641", "-1.76481", "-1.668212", "-1.503674", "-1.898122", "-1.534637", "-1.616887", "-1.705932", "-1.552317", "-1.720281", "-1.924974", "-1.539736", "-1.688647", "-1.532879", "-1.577566", "-1.765735", "-1.592201", "-1.691878", "-1.749845", "-1.588994", "-1.578909", "-1.543743", "-1.956201", "-5.179962", "-1.592553", "-2.596752", "-4.941559", "-2.280015", "-2.629725", "-3.038367", "-1.527824", "-1.664551", "-1.745144", "-1.544548", "-1.762501", "-1.994972", "-1.651343", "-2.344753", "-1.655423", "-1.576165", "-2.00818", "-1.619477", "-2.141771", "-3.835143", "-1.99829", "-1.570127", "-3.635355", "-1.566519", "-1.536502", "-2.085493", "-2.220303", "-1.660569", "-1.733877", "-1.673035", "-1.689636", "-1.756543", "-2.798633", "-1.726499", "-1.757449", "-1.794329", "-9.847163", "-1.506171", "-2.04533", "-1.674434", "-1.518844", "-2.478603", "-1.571104", "-2.030791", "-2.698498", "-1.674757", "-1.519466", "-2.87959", "-2.053922", "-1.757688", "-1.857258", "-1.659245", "-1.561197", "-1.645023", "-3.757027", "-1.579877", "-1.5092", "-6.033302", "-2.464834", "-2.348122", "-2.120944", "-2.189732", "-2.330316", "-1.805386", "-1.539072", "-1.572604", "-1.622266", "-2.204609", "-1.870975", "-1.797827", "-1.764506", "-1.543638", "-2.611579", "-2.771606", "-2.055345", "-2.531282", "-1.861424", "-1.576088", "-1.824787", "-11.218126", "-1.518216", "-2.204605", "-1.999374", "-1.715217", "-1.987578", "-2.319559", "-2.482654", "-2.53276", "-1.532368", "-1.671646", "-1.713202", "-1.676674", "-1.512467", "-1.875777", "-6.420488", "-2.362593", "-1.742482", "-1.501403", "-2.208904", "-1.667349", "-1.96963", "-1.911844", "-2.520585", "-3.703059", "-2.435023", "-2.21675", "-2.127687", "-1.545493", "-3.760596", "-2.167861", "-1.924233", "-1.554673", "-1.700904", "-1.524211", "-1.772247", "-1.572389", "-1.856259", "-1.627302", "-4.702818", "-1.739098", "-2.097441", "-4.21822", "-1.555622", "-1.900128", "-1.647414", "-1.80505", "-1.580107", "-1.816908", "-1.715489", "-1.535722", "-1.804277", "-1.621762", "-3.144716", "-1.99149", "-1.550448", "-3.309065", "-1.620233", "-1.521836", "-1.746458", "-2.341787", "-1.675458", "-2.147188", "-2.114074", "-1.526495", "-1.549009", "-1.809709", "-1.712396", "-1.500552", "-1.592321", "-1.789658", "-1.547615", "-1.962938", "-1.931768", "-2.196962", "-2.01577", "-1.779378", "-2.324739", "-1.70293", "-1.680979", "-6.886967", "-1.517939", "-1.697309", "-3.451519", "-1.530418", "-1.541699", "-1.521631", "-1.562913", "-2.843538", "-1.569822", "-1.695706", "-1.506754", "-4.328567", "-1.591914", "-1.750571", "-1.694676", "-2.673912", "-1.688632", "-1.664063", "-1.620269", "-1.804534", "-1.511849", "-1.576927", "-2.342886", "-1.587504", "-1.579537", "-1.911082", "-4.315262", "-1.63309", "-1.573642", "-1.596768", "-1.581437", "-2.959873", "-1.520538", "-1.6489", "-1.539497", "-1.533524", "-1.974034", "-1.695758", "-1.536939", "-2.085567", "-2.95076", "-1.559382", "-2.059928", "-2.534944", "-1.526915", "-2.713797", "-1.62542", "-1.637376", "-2.069169", "-3.436641", "-1.52843", "-1.633493", "-1.787276", "-1.500044", "-1.890076", "-1.546441", "-2.232571", "-1.770533", "-2.200566", "-1.504152", "-4.812632", "-1.584475", "-1.678675", "-1.635657", "-1.652396", "-1.539399", "-1.93221", "-2.715854", "-1.582617", "-1.614729", "-3.994538", "-2.024457", "-6.75791", "-1.848449", "-1.622823", "-2.185779", "-1.826313", "-2.2543", "-2.210382", "-2.022423", "-1.595123", "-1.661599", "-6.083886", "-1.54549", "-1.526398", "-1.74883", "-1.703398", "-1.659208", "-1.698723", "-2.230542", "-10.08821", "-2.061071", "-1.683745", "-1.50531", "-1.927408", "-1.634895", "-1.707523", "-1.84461", "-2.066619", "-2.410358", "-1.861295", "-1.89319", "-1.919387", "-1.592938", "-1.877852", "-1.829819", "-1.725878", "-1.524224", "-2.693686", "-1.519278", "-1.807839", "-2.043502", "-2.632514", "-2.339775", "-1.948605", "-1.703084", "-1.757249", "-1.800037", "-2.402638", "-1.52547", "-1.710822", "-1.505957", "-1.83111", "-1.988897", "-1.592824", "-1.527747", "-1.543721", "-1.766871", "-1.709525", "-1.891647", "-1.514576", "-1.672498", "-2.698823", "-1.812546", "-1.562665", "-1.729301", "-1.851485", "-2.035743", "-1.500748", "-1.61124", "-2.873179", "-1.573603", "-1.556209", "-1.65825", "-9.100108", "-1.543369", "-1.645969", "-1.871989", "-2.094072", "-3.453783", "-2.252827", "-1.873982", "-1.526906", "-1.57029", "-1.950993", "-1.611894", "-1.595908", "-1.866709", "-1.501026", "-1.743645", "-1.563009", "-2.077432", "-1.701412", "-1.576102", "-4.727178", "-1.745673", "-1.525295", "-1.707825", "-1.526492", "-1.642356", "-1.981719", "-1.576067", "-1.56699", "-1.66537", "-2.654315", "-1.99428", "-1.652903", "-1.592241", "-1.763643", "-1.583607", "-1.608069", "-1.755461", "-1.694599", "-1.539556", "-1.989701", "-1.753525", "-1.650411", "-2.048447", "-1.536601", "-2.221171", "-1.719961", "-1.543695", "-2.131034", "-1.532529", "-1.510847", "-1.639795", "-1.795446"]}, {"name": "devtest_02112022", "created_at": "2022-02-11 02:23:02", "entity_count": 195, "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"]}, {"name": "ExpTest", "created_at": "2018-12-14 06:12:14", "entity_count": 217, "concept_count": "150", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"], "expression": ["0.58", "0", "0.47", "0.48", "0.62", "0.59", "0.62", "0.6", "0.53", "0.56", "0.51", "0", "0.59", "0.31", "0.62", "0.42", "0.37", "0.6", "0.6", "0.29", "0.53", "0.6", "0.5", "0.5", "0.53", "0.15", "0.34", "0.26", "0.58", "0.62", "0.53", "0.34", "0.41", "0.61", "0.44", "0.36", "0.3", "0.63", "0.6", "0.54", "0.58", "0.59", "0.56", "0.59", "0.59", "0.39", "0.47", "0.6", "0.19", "0.53", "0.62", "0.52", "0.6", "0.59", "0.41", "0.54", "0.37", "0.58", "0.12", "0.3", "0", "0.49", "0.6", "0.4", "0", "0.54", "0.38", "0.56", "0.49", "0.4", "0.2", "0.44", "0.57", "0.51", "0.62", "0.6", "0.47", "0", "0.56", "0.12", "0.51", "0.26", "0.61", "0", "0.6", "0.5", "0.62", "0.39", "0.19", "0.58", "0.55", "0.19", "0.53", "0.47", "0.56", "0", "0.43", "0.41", "0.26", "0.46", "0.34", "0.39", "0.54", "0.61", "0.55", "0.48", "0.59", "0.53", "0.62", "0.41", "0.18", "0.55", "0.57", "0.4", "0.29", "0.54", "0.46", "0.56", "0", "0.62", "0.42", "0.52", "0.57", "0", "0.51", "0.55", "0.44", "0.6", "0.19", "0.53", "0", "0.51", "0.47", "0.61", "0.35", "0.57", "0.62", "0.61", "0", "0", "0.26", "0.57", "0.42", "0.03", "0.58", "0.59", "0.39", "0.63", "0", "0.14", "0.29", "0.57", "0.19", "0.42", "0.46", "0.63", "0.53", "0.24", "0.54", "0.59", "0.44", "0.41", "0.53", "0.45", "0.55", "0.58", "0.36", "0.62", "0.62", "0.62", "0.6", "0.53", "0.35", "0.59", "0.47", "0.13", "0.58", "0.61", "0.51", "0.62", "0.35", "0.52", "0.52", "0.1", "0.47", "0.58", "0.59", "0.59", "0.47", "0.61", "0.57", "0.49", "0.62", "0.57", "0.6", "0.57", "0.57", "0.39", "0.39", "0.4", "0.52", "0.5", "0.38", "0.45", "0.62", "0.61", "0.52", "0", "0.54", "0.51", "0.39", "0.6", "0.58", "0.58", "0.22", "0.2"]}, {"name": "SMJ_AF_DQ_Down", "created_at": "2024-01-04 02:33:12", "entity_count": 243, "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"], "expression": ["-11.71", "-10.6", "-9.42", "-8.77", "-8.45", "-6.37", "-6.05", "-5.29", "-4.92", "-4.83", "-3.7", "-3.61", "-3.46", "-3.06", "-3", "-2.73", "-2.71", "-2.67", "-2.64", "-2.49", "-2.42", "-2.39", "-2.37", "-2.36", "-2.34", "-2.32", "-2.32", "-2.31", "-2.3", "-2.3", "-2.25", "-2.22", "-2.19", "-2.16", "-2.15", "-2.14", "-2.12", "-2.08", "-2.07", "-2.07", "-2.07", "-2.06", "-2.06", "-2.05", "-2.05", "-2.03", "-2.02", "-1.98", "-1.97", "-1.96", "-1.96", "-1.95", "-1.95", "-1.93", "-1.92", "-1.92", "-1.91", "-1.9", "-1.88", "-1.87", "-1.87", "-1.87", "-1.87", "-1.85", "-1.85", "-1.85", "-1.84", "-1.84", "-1.83", "-1.82", "-1.81", "-1.8", "-1.79", "-1.78", "-1.77", "-1.74", "-1.74", "-1.74", "-1.74", "-1.74", "-1.73", "-1.73", "-1.73", "-1.71", "-1.7", "-1.7", "-1.69", "-1.69", "-1.69", "-1.68", "-1.68", "-1.68", "-1.68", "-1.68", "-1.67", "-1.67", "-1.67", "-1.66", "-1.66", "-1.66", "-1.65", "-1.65", "-1.65", "-1.65", "-1.64", "-1.64", "-1.63", "-1.63", "-1.63", "-1.63", "-1.62", "-1.62", "-1.62", "-1.62", "-1.62", "-1.61", "-1.61", "-1.61", "-1.61", "-1.6", "-1.6", "-1.59", "-1.58", "-1.58", "-1.58", "-1.57", "-1.57", "-1.57", "-1.57", "-1.57", "-1.57", "-1.57", "-1.56", "-1.56", "-1.56", "-1.55", "-1.54", "-1.54", "-1.53", "-1.53", "-1.52", "-1.52", "-1.52", "-1.51", "-1.51", "-1.51", "-1.51", "-1.5", "-1.5", "-1.5", "-1.5", "-1.5", "-1.5", "-1.5", "-1.49", "-1.49", "-1.49", "-1.49", "-1.48", "-1.48", "-1.48", "-1.48", "-1.48", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.46", "-1.46", "-1.45", "-1.45", "-1.45", "-1.44", "-1.44", "-1.43", "-1.43", "-1.43", "-1.43", "-1.42", "-1.42", "-1.42", "-1.42", "-1.42", "-1.41", "-1.41", "-1.41", "-1.41", "-1.4", "-1.4", "-1.4", "-1.39", "-1.39", "-1.38", "-1.38", "-1.38", "-1.37", "-1.37", "-1.37", "-1.37", "-1.36", "-1.35", "-1.35", "-1.35", "-1.35", "-1.35", "-1.34", "-1.33", "-1.33", "-1.32", "-1.32", "-1.32", "-1.32", "-1.31", "-1.31", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.29", "-1.29", "-1.28", "-1.28", "-1.28", "-1.27", "-1.27", "-1.26", "-1.26", "-1.25", "-1.25", "-1.25", "-1.25", "-1.23", "-1.23", "-1.22", "-1.21", "-1.16", "-1.12", "-1.08"]}, {"name": "SMJ_DQ_AF_UP_FC1-5", "created_at": "2024-03-07 03:50:43", "entity_count": 156, "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"], "expression": ["1.5", "1.5", "1.5", "1.51", "1.51", "1.51", "1.51", "1.51", "1.53", "1.53", "1.53", "1.53", "1.53", "1.53", "1.53", "1.53", "1.54", "1.54", "1.54", "1.54", "1.54", "1.55", "1.55", "1.55", "1.55", "1.55", "1.56", "1.56", "1.57", "1.57", "1.58", "1.58", "1.58", "1.58", "1.58", "1.58", "1.59", "1.59", "1.59", "1.6", "1.6", "1.61", "1.61", "1.61", "1.61", "1.61", "1.61", "1.62", "1.62", "1.62", "1.62", "1.62", "1.63", "1.63", "1.64", "1.64", "1.65", "1.65", "1.65", "1.66", "1.67", "1.68", "1.68", "1.69", "1.69", "1.69", "1.7", "1.71", "1.71", "1.71", "1.71", "1.71", "1.72", "1.72", "1.73", "1.73", "1.74", "1.75", "1.75", "1.76", "1.76", "1.76", "1.78", "1.78", "1.79", "1.79", "1.8", "1.8", "1.83", "1.84", "1.85", "1.86", "1.86", "1.86", "1.87", "1.9", "1.91", "1.92", "1.92", "1.95", "1.96", "1.96", "1.96", "1.97", "1.98", "2.01", "2.01", "2.03", "2.05", "2.06", "2.08", "2.09", "2.1", "2.11", "2.12", "2.13", "2.13", "2.13", "2.13", "2.13", "2.13", "2.17", "2.18", "2.21", "2.21", "2.24", "2.27", "2.27", "2.29", "2.29", "2.32", "2.36", "2.38", "2.39", "2.42", "2.53", "2.55", "2.56", "2.56", "2.71", "2.74", "2.93", "3", "3.03", "3.06", "3.13", "3.25", "3.49", "4.05", "4.38", "4.45", "4.63", "9.76", "11.31", "11.66"]}, {"name": "SMJ_NP_DQ_Down", "created_at": "2024-01-04 02:30:54", "entity_count": 442, "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"], "expression": ["-4.22", "-2.92", "-2.49", "-2.41", "-2.38", "-2.29", "-2.23", "-2.21", "-2.15", "-2.14", "-2.09", "-2.08", "-2.07", "-2.06", "-1.98", "-1.96", "-1.95", "-1.94", "-1.94", "-1.92", "-1.92", "-1.91", "-1.91", "-1.91", "-1.89", "-1.88", "-1.87", "-1.87", "-1.87", "-1.86", "-1.84", "-1.83", "-1.83", "-1.83", "-1.81", "-1.81", "-1.81", "-1.8", "-1.8", "-1.8", "-1.8", "-1.79", "-1.79", "-1.79", "-1.79", "-1.79", "-1.78", "-1.78", "-1.78", "-1.77", "-1.77", "-1.76", "-1.75", "-1.75", "-1.74", "-1.74", "-1.73", "-1.73", "-1.73", "-1.72", "-1.72", "-1.72", "-1.72", "-1.72", "-1.71", "-1.71", "-1.71", "-1.71", "-1.7", "-1.7", "-1.7", "-1.7", "-1.7", "-1.69", "-1.69", "-1.69", "-1.69", "-1.69", "-1.69", "-1.69", "-1.68", "-1.68", "-1.67", "-1.67", "-1.67", "-1.66", "-1.66", "-1.66", "-1.66", "-1.66", "-1.65", "-1.65", "-1.65", "-1.65", "-1.64", "-1.63", "-1.63", "-1.63", "-1.63", "-1.62", "-1.62", "-1.62", "-1.62", "-1.61", "-1.6", "-1.6", "-1.6", "-1.59", "-1.59", "-1.59", "-1.59", "-1.58", "-1.58", "-1.58", "-1.58", "-1.58", "-1.57", "-1.57", "-1.57", "-1.57", "-1.57", "-1.56", "-1.56", "-1.56", "-1.56", "-1.56", "-1.55", "-1.55", "-1.55", "-1.55", "-1.55", "-1.54", "-1.54", "-1.54", "-1.54", "-1.54", "-1.54", "-1.54", "-1.54", "-1.54", "-1.54", "-1.53", "-1.53", "-1.53", "-1.53", "-1.52", "-1.52", "-1.52", "-1.52", "-1.52", "-1.52", "-1.52", "-1.52", "-1.52", "-1.51", "-1.51", "-1.51", "-1.5", "-1.5", "-1.5", "-1.5", "-1.5", "-1.5", "-1.5", "-1.49", "-1.49", "-1.49", "-1.49", "-1.48", "-1.48", "-1.48", "-1.48", "-1.48", "-1.48", "-1.48", "-1.48", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.47", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.46", "-1.45", "-1.45", "-1.45", "-1.45", "-1.45", "-1.45", "-1.45", "-1.45", "-1.45", "-1.45", "-1.44", "-1.44", "-1.44", "-1.44", "-1.44", "-1.44", "-1.43", "-1.43", "-1.43", "-1.43", "-1.43", "-1.43", "-1.43", "-1.43", "-1.43", "-1.42", "-1.42", "-1.42", "-1.42", "-1.42", "-1.42", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.41", "-1.4", "-1.4", "-1.4", "-1.4", "-1.4", "-1.4", "-1.4", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.39", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.38", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.37", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.36", "-1.35", "-1.35", "-1.34", "-1.34", "-1.34", "-1.34", "-1.34", "-1.34", "-1.34", "-1.34", "-1.34", "-1.34", "-1.34", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.33", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.32", "-1.31", "-1.31", "-1.31", "-1.31", "-1.31", "-1.31", "-1.31", "-1.31", "-1.31", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.3", "-1.29", "-1.29", "-1.29", "-1.29", "-1.29", "-1.29", "-1.29", "-1.28", "-1.28", "-1.28", "-1.28", "-1.28", "-1.28", "-1.28", "-1.28", "-1.28", "-1.27", "-1.27", "-1.27", "-1.27", "-1.27", "-1.27", "-1.27", "-1.27", "-1.26", "-1.26", "-1.26", "-1.26", "-1.26", "-1.26", "-1.25", "-1.25", "-1.25", "-1.25", "-1.25", "-1.25", "-1.25", "-1.25", "-1.24", "-1.24", "-1.24", "-1.24", "-1.24", "-1.24", "-1.24", "-1.24", "-1.23", "-1.23", "-1.23", "-1.23", "-1.23", "-1.22", "-1.22", "-1.22", "-1.22", "-1.2", "-1.2", "-1.19", "-1.19", "-1.19", "-1.18", "-1.18", "-1.16", "-1.16", "-1.15", "-1.13", "-1.07"]}, {"name": "SynonymApiTest", "created_at": "2020-03-21 03:21:59", "entity_count": 195, "Context": "", "Num_Context": "0", "concept_count": "500", "status": "complete", "list": ["AACSP1", "ACADS", "ACTG1P22", "ACTR5", "ADAMTSL4-AS2", "AFG1L", "AKTIP", "ARFGEF2", "ARHGEF6", "ARL14EP", "ARL3", "AS3MT", "ATP1B3", "ATP23", "ATXN2L", "BAG5", "BANK1", "BCL10", "BNIP3", "BORCS7", "BRINP2", "BTN3A2", "C2-AS1", "C6orf15", "CACNA1C", "CACNA1I", "CARMIL1", "CCT4P1", "CDH8", "CNNM4", "COL11A1", "CTDSP2", "CWC22", "DCAF4", "DDX39BP2", "DEF8", "DEPDC1-AS1", "DESI1", "DHFRP2", "DPYD", "DYSF", "EEF1A1P11", "EIF4EBP2P3", "ELF2P4", "EPHX2", "ERCC4", "ESR2", "FBXO33", "FEZ1", "FGF8", "FKBPL", "FLJ46284", "FOXN2", "FOXO6", "FOXP1", "FTCDNL1", "GALNT3", "GAPDHP28", "GCC1", "GFRA2", "GLTP", "GPR156", "GPR89P", "GRIK3", "GRM3-AS1", "GRM5", "GSDME", "GULOP", "H2BC15", "HCG11", "HCG27", "HCG4B", "HLA-A", "HLA-B", "HLA-C", "HLA-DMA", "HLA-DMB", "HLA-DOA", "HLA-DQA1", "HLA-DQB1", "HLA-DQB3", "HLA-DRA", "HLA-DRB1", "HLA-DRB5", "HLA-DRB6", "HLA-DRB9", "HLA-G", "HLA-H", "HLA-S", "HLA-V", "HMGB3P24", "HMGB3P26", "HNRNPA1P57", "HSP90AB6P", "HTR1A", "HYI-AS1", "IGBP1P5", "IGIP", "IGSF9B", "IL1R2", "IL20RB", "IMMP2L", "IPO13", "ISCA1P1", "ITIH3", "IZUMO3", "KCNJ3", "KCTD12", "KDM4A", "KIAA1143P2", "KIZ", "KIZ-AS1", "KLC1", "KRT18P42", "L3MBTL2-AS1", "LINC00343", "LINC00529", "LINC01088", "LINC01104", "LINC01239", "LINC01288", "LINC01415", "LINC01416", "LINC01517", "LINC01550", "LINC01806", "LINC01830", "LINC01929", "LINC01947", "LINC01958", "LINC01982", "LINC02033", "LINC02058", "LINC02163", "LINC02196", "LINC02220", "LINC02295", "LINC02326", "LINC02327", "LINC02571", "LINC02730", "LINC02758", "LINC02790", "LINC02827", "LINC02913", "LONRF2", "LYPLA2P1", "MACIR", "MEF2C", "MEF2C-AS1", "MEF2C-AS2", "Metazoa_SRP", "MICA", "MICC", "MICD", "MIR129-1", "MIR1302-7", "MIR2113", "MIR3143", "MIR3660", "MIR4495", "MIR588", "MIR7154", "MMS22L", "MOB4", "MROH5", "MSH5-SAPCD1", "MST1R", "MTCO1P25", "MTCO1P6", "MTCO3P1", "MUC21", "MUCL3", "NAA40", "NAV1", "NCKIPSD", "NDRG4", "NEGR1", "NEK4P1", "NFIA", "NKX2-1-AS1", "NOP56P1", "NOTCH4"]}]