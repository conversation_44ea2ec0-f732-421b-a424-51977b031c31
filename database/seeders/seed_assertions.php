<?php
// // Register the Composer autoloader...
require '../../vendor/autoload.php';
// // <PERSON>trap <PERSON> and handle the request...
$app = require_once '../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Http\Kernel')
    ->handle(Illuminate\Http\Request::capture());

use Illuminate\Support\Facades\DB;
use App\Models\Assertion;

$asrts = json_decode(file_get_contents(".\assertion_data.json"));
foreach($asrts as $asrt)
{
    $info = $asrt->contents;
	Assertion::create([
        "user_id" => 1,
        "name" => $info->name,
        "base" => $info->base,
        "is_base_lib" => $info->isBaseLib,
        "secondary" => $info->secondary,
        "is_secondary_lib" => $info->isSecondaryLib,
        "is_lib_assertion" => $info->isLibAssertion,
        "is_lib_to_lib_assertion" => $info->isLibToLibAssertion,
        "engine" => $info->engine,
	]);
}

echo 'BioExplorer projects insertion complete.';