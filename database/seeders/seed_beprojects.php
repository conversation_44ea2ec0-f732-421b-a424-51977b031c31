<?php
// // Register the Composer autoloader...
require '../../vendor/autoload.php';
// // <PERSON>trap <PERSON> and handle the request...
$app = require_once '../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Http\Kernel')
    ->handle(Illuminate\Http\Request::capture());

use Illuminate\Support\Facades\DB;
use App\Models\BeProject;
$bes = json_decode(file_get_contents(".\be_project_data.json"));
foreach($bes as $be)
{
	BeProject::create([
		"name" => $be->Name,
		"user_id" => 1,
		"status" => 'complete',
		"field" => (isset($be->Field_Type)) ? $be->Field_Type : null,
		"value" => (gettype($be->Value_Submitted) == 'string') ? (array)str_replace("'","",$be->Value_Submitted) : $be->Value_Submitted,
		"method" => (isset($be->Method)) ? $be->Method : null,
		"terms" => (isset($be->Terms)) ? explode(",",$be->Terms) : null,
		"directory" => null,
		"shared" => rand(0,1)
	]);
}

echo 'BioExplorer projects insertion complete.';