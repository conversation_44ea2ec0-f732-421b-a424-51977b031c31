<?php
// // Register the Composer autoloader...
require '../../vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once '../../bootstrap/app.php';
$app->make('Illuminate\Contracts\Http\Kernel')
    ->handle(Illuminate\Http\Request::capture());

use Illuminate\Support\Facades\DB;
use App\Models\CbProject;
use App\Models\CbProfile;

$profiles = json_decode(file_get_contents('.\cb_project_profile_data.json'));

foreach ($profiles as $profile) {
	$project = CbProject::firstWhere('name',$profile->name);
	CbProfile::create([
		'user_id' => 1,
		'cb_project_id' => $project->id,
		'profile' => $profile->contents
	]);
}

echo 'Profile insertion complete.';