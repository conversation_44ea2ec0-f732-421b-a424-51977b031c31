# Disk Usage Monitoring System

This Laravel application includes an automated disk usage monitoring system that checks disk space multiple times per day and sends email notifications when disk space is running low or nearly full.

## Features

- **Automated Monitoring**: Runs on a configurable schedule (default: every 4 hours)
- **Multi-Disk Support**: Monitor single disk, multiple specific disks, or all mounted filesystems
- **Automatic Disk Detection**: Automatically discovers and monitors all server disks
- **Intelligent Filtering**: Excludes virtual/system filesystems automatically
- **Dual Threshold System**: Warning alerts at 80% usage, critical alerts at 95% usage
- **Email Notifications**: Sends formatted email alerts to specified recipients
- **Notification Throttling**: Prevents spam by limiting notification frequency
- **Flexible Configuration**: Monitor specific paths or all disks with custom thresholds
- **Configurable Settings**: All settings can be customized via environment variables
- **Logging Integration**: Logs all monitoring events for audit purposes

## Quick Setup

1. **Configure Environment Variables**
   
   Add these settings to your `.env` file:
   ```env
   # Enable/disable monitoring
   DISK_MONITOR_ENABLED=true

   # Single disk monitoring (default: root filesystem)
   DISK_MONITOR_PATH=/

   # Multi-disk monitoring options
   DISK_MONITOR_ALL_DISKS=false
   DISK_MONITOR_EXCLUDE_PATHS="/dev,/proc,/sys,/run"
   DISK_MONITOR_MIN_SIZE=0

   # Threshold percentages
   DISK_MONITOR_WARNING_THRESHOLD=80
   DISK_MONITOR_CRITICAL_THRESHOLD=95

   # Email recipients (comma-separated)
   DISK_MONITOR_EMAILS="<EMAIL>,<EMAIL>"

   # Schedule frequency
   DISK_MONITOR_FREQUENCY=everyFourHours
   ```

2. **Set Up Mail Configuration**
   
   Ensure your Laravel mail configuration is working:
   ```env
   MAIL_MAILER=smtp
   MAIL_HOST=your-smtp-server.com
   MAIL_PORT=587
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-password
   MAIL_FROM_ADDRESS="<EMAIL>"
   MAIL_FROM_NAME="Server Monitor"
   ```

3. **Start the Laravel Scheduler**
   
   Add this to your server's crontab:
   ```bash
   * * * * * cd /path/to/your/laravel/app && php artisan schedule:run >> /dev/null 2>&1
   ```

## Manual Testing

You can test the disk monitoring system manually with various options:

```bash
# Check disk usage with default settings (single disk or configured paths)
php artisan disk:check

# List all available disks and mount points
php artisan disk:check --list-disks

# Monitor all mounted filesystems automatically
php artisan disk:check --all-disks

# Check specific path with custom thresholds
php artisan disk:check --path=/var/log --warning-threshold=85 --critical-threshold=98

# Monitor all disks but exclude specific mount points
php artisan disk:check --all-disks --exclude="/tmp,/var/log"

# Send test notifications to specific emails
php artisan disk:check --notify-emails="<EMAIL>,<EMAIL>"

# Combine options for comprehensive testing
php artisan disk:check --all-disks --warning-threshold=75 --critical-threshold=90 --exclude="/dev,/proc"
```

## Multi-Disk Configuration

### Monitoring Strategies

The system supports three different monitoring strategies:

#### 1. Single Disk Monitoring (Default)
Monitor only the root filesystem or a specific path:
```env
DISK_MONITOR_PATH=/
DISK_MONITOR_ALL_DISKS=false
```

#### 2. Automatic All-Disk Monitoring
Automatically detect and monitor all mounted filesystems:
```env
DISK_MONITOR_ALL_DISKS=true
DISK_MONITOR_EXCLUDE_PATHS="/dev,/proc,/sys,/run,/boot/efi"
```

#### 3. Custom Multi-Disk Monitoring
Define specific paths with individual thresholds in `config/disk-monitoring.php`:
```php
'additional_paths' => [
    '/var/log' => [
        'warning' => 85,
        'critical' => 98,
    ],
    '/home' => [
        'warning' => 80,
        'critical' => 95,
    ],
    '/var/lib/mysql' => [
        'warning' => 75,
        'critical' => 90,
    ],
],
```

### Disk Discovery and Filtering

When using `--all-disks` or `DISK_MONITOR_ALL_DISKS=true`, the system automatically:

- **Discovers** all mounted filesystems from `/proc/mounts`
- **Filters out** virtual filesystems (proc, sysfs, tmpfs, etc.)
- **Excludes** system mount points (/dev, /proc, /sys, etc.)
- **Validates** that disk usage can be checked for each path
- **Applies** minimum size filters if configured

### Command Line Options for Multi-Disk

```bash
# List all discoverable disks
php artisan disk:check --list-disks

# Monitor all disks
php artisan disk:check --all-disks

# Monitor all disks except specific ones
php artisan disk:check --all-disks --exclude="/tmp,/var/cache"

# Monitor specific disk with custom thresholds
php artisan disk:check --path=/var/lib/mysql --warning-threshold=75 --critical-threshold=85
```

## Configuration Options

### Schedule Frequency Options
- `hourly` - Every hour
- `everyTwoHours` - Every 2 hours
- `everyThreeHours` - Every 3 hours
- `everyFourHours` - Every 4 hours (default)
- `everySixHours` - Every 6 hours
- `daily` - Once per day

### Notification Throttling
- **Warning notifications**: Sent at most once every 2 hours (configurable)
- **Critical notifications**: Sent at most once every 4 hours (configurable)

### Additional Paths
You can monitor multiple paths by editing `config/disk-monitoring.php`:

```php
'additional_paths' => [
    '/var/log' => [
        'warning' => 85,
        'critical' => 98,
    ],
    '/tmp' => [
        'warning' => 90,
        'critical' => 99,
    ],
],
```

## Email Notifications

### Warning Notification (80% usage)
- Subject: "⚠️ Server Name - Low Disk Space Warning"
- Contains current usage statistics
- Suggests cleanup actions

### Critical Notification (95% usage)
- Subject: "🚨 CRITICAL: Server Name - Disk Space Nearly Full"
- Urgent formatting with immediate action items
- Warns about potential system issues

## Troubleshooting

### No Notifications Received
1. Check mail configuration: `php artisan tinker` then `Mail::raw('test', function($m) { $m->to('<EMAIL>')->subject('test'); });`
2. Verify email addresses in `DISK_MONITOR_EMAILS`
3. Check Laravel logs for mail sending errors
4. Ensure queue workers are running if using queued notifications

### Command Not Running
1. Verify cron job is set up correctly
2. Check if `DISK_MONITOR_ENABLED=true` in `.env`
3. Run `php artisan schedule:list` to see scheduled commands
4. Check Laravel logs for any errors

### Permissions Issues
Ensure the web server user has read access to the paths being monitored.

## Security Considerations

- Email notifications may contain sensitive server information
- Ensure notification emails are sent to trusted recipients only
- Consider using encrypted email transport for sensitive environments
- Monitor the monitoring system itself to ensure it's functioning

## Logs

All disk monitoring events are logged to your configured Laravel log channel. Look for:
- `disk_space_low` - Warning threshold exceeded
- `disk_space_critical` - Critical threshold exceeded
- Error messages for failed checks or notifications
