#!/bin/bash

echo "🔍 Laravel Destructive Command Audit"
echo "====================================="
echo

echo "🗂️  Searching for destructive DB statements in code..."
grep -Ri --color=always -E 'DB::statement.*(DROP|TRUNCATE|DELETE)|Schema::drop|Schema::dropIfExists|Schema::dropColumns?' . || echo "✅ None found."

echo
echo "🧪 Scanning seeders for truncate/delete/drop..."
grep -Ri --color=always -E '(truncate|delete|drop)' database/seeders || echo "✅ Seeders look clean."

echo
echo "⚙️  Scanning custom Artisan commands for dangerous logic..."
grep -Ri --color=always -E '(truncate|delete|drop)' app/Console/Commands || echo "✅ No dangerous logic in custom commands."

echo
echo "📅 Checking scheduled commands in app/Console/Kernel.php..."
grep -Ri --color=always -E '(migrate|wipe|seed)' app/Console/Kernel.php || echo "✅ No destructive schedule entries found."

echo
echo "🎼 Checking composer.json for post-install/post-update Artisan commands..."
grep -Ri --color=always 'artisan' composer.json || echo "✅ composer.json does not auto-run Artisan commands."

echo
echo "⏰ Listing cron jobs for current user..."
crontab -l | grep --color=always 'artisan' || echo "✅ No Artisan commands in crontab for current user."

echo
echo "👤 Checking www-data crontab (if applicable)..."
sudo crontab -u www-data -l 2>/dev/null | grep --color=always 'artisan' || echo "✅ No Artisan commands in www-data crontab or www-data crontab not set."

echo
echo "✅ Audit Complete."
