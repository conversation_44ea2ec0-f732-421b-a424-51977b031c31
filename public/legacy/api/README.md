README

This directory is where the input files/lists will be placed. The data needs to be structured in a specific way for the program to properly process the lists. View the below sections, FILE STRUCTURE REQUIREMENTS and FILE STRUCTURE DIAGRAM, for data structure requirement instructions.

FILE STRUCTURE REQUIREMENTS

\Within the input_data directory place a directory with the desired name of the final assertion generation.  Within the created directory, create a txt file list with the desired name of the single project, contents of the file containing the list for the single project, and a directory within with the desired name of the assertion libary.  Within the library directory place the txt files, with the contents of lists, of the projects that will be within the library, each txt file with the desired name of the project the list will pertain to.

FILE STRUCTURE DIAGRAM

input_data/
|
\---AssertionGenerationName/ <-- (Final Assertion Generation Project Name)
    |--> SingleProjectName.txt <-- (Single Project Name)/(Contains List of Single Project)
    |
    \---LibraryName/ <-- (Assertion Library Name)
        LibraryProjectName1.txt <-- (Library Project Name)/(Contains List of Library Project)
        LibraryProjectName2.txt <--|
        LibraryProjectName3.txt <--|
        LibraryProjectName4.txt <--/

CONTACT

Email <EMAIL> to answer questions or if further explanation needed.