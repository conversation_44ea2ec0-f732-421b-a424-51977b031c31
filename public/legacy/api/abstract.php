<?php
session_start();
ini_set('memory_limit', '-1');
include('../config/config.php');
include('function.php');


if(isset($_SESSION['LoggedIn']) && isset($_SESSION['Sudo']))
{
	if($_SESSION['LoggedIn'] === TRUE && $_SESSION['Sudo'] === TRUE)
	{
		if(isset($_GET['account']) && isset($_GET['project']) && isset($_GET['entity']) && isset($_GET['concept']))
		{
			$account = strtoupper(trim($_GET['account']));
			$project = trim($_GET['project']);
			$entities = returnClean($_GET['entity']);
			$groups = returnEntitySearchGroups($entities);
			$concept = returnClean($_GET['concept'])[0];
			$path = ABS_ROOT."/data/{$account}/{$project}/{$project}_Genes_HTML.json";
			if(file_exists($path))
			{
				$references = json_decode(file_get_contents($path));
				// print_r($references[0]);
				$results = (object)[];
				$empty = TRUE;
				foreach($groups as $group)
				{
					$entity = array_shift($group);
					$name = "{$entity}/{$concept}";
					$results->{$name} = (object)[];
					$results->{$name}->entity = $entity;
					$results->{$name}->synonyms = $group;
					$results->{$name}->concept = $concept;
					$results->{$name}->pmids = (array)[];
					foreach($references as $reference)
					{
						if($reference->Gene_ID === $entity || in_array($reference->Gene_ID, $results->{$name}->synonyms))
						{
							if($reference->Concept === $concept)
							{
								array_push($results->{$name}->pmids, $reference->PMID);
								$empty = FALSE;
							}
						}
					}
					$results->{$name}->total = count($results->{$name}->pmids);
				}
				if($empty === TRUE)
				{
					echo 'Sorry, no matches found';
				} elseif($empty === FALSE)
				{
					displayResults($results);
				}
			} else
			{
				echo 'Invalid parameters!<br>Double check the queried account and project names for spelling errors.';
			}
		}
	}
} else
{
	echo 'Go away!';
}

?>