<?php
function getRealIpAddr()
{
	if ( !empty($_SERVER['HTTP_CLIENT_IP']) ) {
		// Check IP from internet.
		$ip = $_SERVER['HTTP_CLIENT_IP'];
	} elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']) ) {
		// Check IP is passed from proxy.
		$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
	} else {
		// Get IP address from remote address.
		$ip = $_SERVER['REMOTE_ADDR'];
	}
	return $ip;
}
function writeToLog($data)
{
	if(is_array($data))
	{
		$data = implode(' -> ', $data);
	} elseif(is_object($data))
	{
		$data = implode(' -> ', (array)$data);
	}
	if(isset($data))
	{
		file_put_contents('api.log', $data, LOCK_EX|FILE_APPEND);
	}
}
function returnBaseUrl()
{
	$baseURL = !strpos(WEBROOT, 'localhost') ? WEBROOT : 'http://at-2.wucon.wustl.edu/';
	return $baseURL;
}
function returnClean($variable)
{

	return array_map('stripslashes', array_map('trim', explode(' ', $variable)));
}
function returnEntitySearchGroups($entities)
{
	// print_r($entities);
	$base = returnBaseUrl();
	$array = (array)[];
	foreach($entities as $entity)
	{
		$synonyms = json_decode(file_get_contents("{$base}cgi-bin/GetArticle?synonyms={$entity}"), true)[0]['Synonyms'];
		$searchTerms = array_merge((array)[$entity],$synonyms);
		array_push($array, $searchTerms);
	}
	return $array;
}
function displayResults($results)
{
	echo '<h1>PMID Relational Search Results</h1>';
	echo '<small>I will pretty it up later. &#129304; &#128526; &#129305;</small><br><hr>';
	echo '<b>Example Request:</b> http://at-2.wucon.wustl.edu/api/abstract.php?account=Curtis&project=CTCF_Test&entity=cdk16+socs3&concept=cyclin<br>';
	echo '<b><u>Parameters</u></b><br><ul><li>account</li><li>project</li><li>entity (<small>can have multiple seperated by "+")</small></li><li>concept</li></ul><hr>';
	echo '<h2>Results</h2>';
	echo '<pre>';
	print_r($results);
	echo '</pre>';
}
?>