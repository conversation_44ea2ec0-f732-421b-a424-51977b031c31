<?php

// http://10.29.44.223:8000/legacy/api/globus/?id=<EMAIL>
// http://3.137.210.17/globus/validate/id/?id=<EMAIL>

// if(strcasecmp($_SERVER['REQUEST_METHOD'], 'POST') != 0){
//     throw new Exception('Request method must be POST!');
// }


// $contentType = isset($_SERVER["CONTENT_TYPE"]) ? trim($_SERVER["CONTENT_TYPE"]) : '';
// if(strcasecmp($contentType, 'application/json') != 0){
//     throw new Exception('Content type must be: application/json');
// }


// $content = trim(file_get_contents("php://input"));


// $decoded = json_decode($content, true);


// if(!is_array($decoded)){
//     throw new Exception('Received content contained invalid JSON!');
// }

// print_r($content);
// $data = json_decode(file_get_contents('php://input'));
// $data = file_get_contents('php://input');
// print_r($data);

// $output = submitCommand($data);
// print_r($output);

$id = $_GET['id'] ?? null;
$bol = getValidation($id);
print_r($bol);



function getValidation($id)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "http://3.137.210.17/globus/validate/id/?id={$id}");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

    $headers = array();
    $headers[] = 'Connection: keep-alive';
    $headers[] = 'Content-Type: text/plain;charset=UTF-8';
    $headers[] = 'Accept: */*';
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);


    $result = curl_exec($ch);
    if (curl_errno($ch)) 
    {
        return 'Error:' . curl_error($ch);
    }
    curl_close($ch);

    return $result;
}
