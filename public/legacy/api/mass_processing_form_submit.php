<?php
	session_start();
	include('mass_processing_functions.php');
	include('function.php');
	date_default_timezone_set('America/Chicago');
	ini_set('memory_limit','0');
	set_time_limit(0);
	$logDirectory = "/huge1/api_storage/log";
	$huge1Storage = "/huge1/api_storage/mass_processing_data";
	if(isset($_SESSION['Laboratory']) && in_array(strtoupper($_SESSION['Laboratory']), $authAccts))
	{
		if(isset($_POST['submit']))
		{
			$baseDir = (file_exists($huge1Storage)) ? $huge1Storage : getcwd();
			$assertName = $_POST['final-name'];
			$email = $_POST['email'];
			$assertDir = "{$baseDir}/input_data/{$assertName}";
			if(file_exists($assertDir))
			{
				$libPath = glob($assertDir.'/*',GLOB_ONLYDIR)[0];
				$libPathArray = explode('/',$libPath);
				$libName = end($libPathArray);
				$results = (object)[];
				$results->AssertionName = $assertName;
				$processingStart = date("Y-m-d h:i:s",time());
				$paths = createProcessingArray($assertDir);
				$cbNames = (array)[];
				foreach($paths as $path)
				{
					$pathArray = explode('/', $path);
					$cbName = str_replace('.txt','',end($pathArray));
					array_push($cbNames,$cbName);
				}
				$singleProjectPath = array_shift($paths);
				$singleProjectName = array_shift($cbNames);
				$processingDir = "{$baseDir}/tmp";
				mkdir("{$processingDir}/{$assertName}");
				chmod("{$processingDir}/{$assertName}", 0777);
				mkdir("{$processingDir}/{$assertName}/{$singleProjectName}");
				chmod("{$processingDir}/{$assertName}/{$singleProjectName}", 0777);
				copy($singleProjectPath,"{$processingDir}/{$assertName}/{$singleProjectName}/input.txt");
				$cmdPath1 = "{$processingDir}/{$assertName}/{$singleProjectName}/input.txt";
				$cmdPath2 = "{$processingDir}/{$assertName}/{$singleProjectName}/{$singleProjectName}";
				$command = '{"jsonrpc": "2.0","method": "LaunchBKGRun","id": "225","params": ["'.$cmdPath1.'","'.$cmdPath2.'"]}';
				$response = submitCbProject($command);
				$results->singleProjectName = $singleProjectName;
				$results->singleProject = $response;
				file_put_contents("{$processingDir}/{$assertName}/{$singleProjectName}/results.json",json_encode($results,JSON_PRETTY_PRINT));
				$results->libraryName = $libName;
				if(strpos($results->singleProject,'Success') !== false)
				{

					$results->libraryResults = (array)[];
					mkdir("{$processingDir}/{$assertName}/{$libName}");
					chmod("{$processingDir}/{$assertName}/{$libName}", 0777);
					$successFinished = 0;
					$failFinished = 0;
					$nameIndex = 0;
					foreach($cbNames as $name)
					{
						mkdir("{$processingDir}/{$assertName}/{$libName}/{$name}");
						chmod("{$processingDir}/{$assertName}/{$libName}/{$name}", 0777);
						$projectListPath = $paths[$nameIndex];
						copy($projectListPath,"{$processingDir}/{$assertName}/{$libName}/{$name}/input.txt");
						$cmdPath1 = "{$processingDir}/{$assertName}/{$libName}/{$name}/input.txt";
						$cmdPath2 = "{$processingDir}/{$assertName}/{$libName}/{$name}/{$name}";
						$command = '{"jsonrpc": "2.0","method": "LaunchBKGRun","id": "225","params": ["'.$cmdPath1.'","'.$cmdPath2.'"]}';
						$startTimeStamp = date("Y-m-d h:i:s",time());
						$response = submitCbProject($command);
						$endTimeStamp = date("Y-m-d h:i:s",time());
						$tmpProjectObj = (object)[];
						$tmpProjectObj->name = $name;
						$tmpProjectObj->startProcessing = $startTimeStamp;
						$tmpProjectObj->endTimeStamp = $endTimeStamp;
						$tmpProjectObj->result = $response;
						if(strpos($tmpProjectObj->result,'Success') === false)
						{
							deleteDirectory("{$processingDir}/{$assertName}/{$libName}/{$name}");
							$failFinished++;
						} else
						{
							$successFinished++;
						}
						array_push($results->libraryResults,$tmpProjectObj);
						$nameIndex++;
						file_put_contents("{$processingDir}/{$assertName}/results.json",json_encode($results,JSON_PRETTY_PRINT));
					}
					$processingEnd = date("Y-m-d h:i:s",time());
					$results->stats = (object)[];
					$results->stats->processingStart = $processingStart;
					$results->stats->processingEnd = $processingEnd;
					$results->stats->totalProcessed = $nameIndex + 1;
					$results->stats->success = $successFinished;
					$results->stats->failed = $failFinished;
					file_put_contents("{$processingDir}/{$assertName}/results.json",json_encode($results,JSON_PRETTY_PRINT));
					sendEmailSuccessNotification($email,$results);
					if(!file_exists("{$logDirectory}/{$assertName}"))
					{
						mkdir("{$logDirectory}/{$assertName}");
						chmod("{$logDirectory}/{$assertName}",0777);
						file_put_contents("{$logDirectory}/{$assertName}/results.json",json_encode($results,JSON_PRETTY_PRINT));
					} else
					{
						file_put_contents("{$logDirectory}/{$assertName}/results.json",json_encode($results,JSON_PRETTY_PRINT),FILE_APPEND);
					}
					echo 'Processing completed successfully.<br> Returned Output:<br><pre>';
					print_r($results);
					echo '</pre><br>Click the link below to submit another Mass Proccessing Run.<br>';
					echo '<a href="javascript:history.back()">Try Again</a>';
					exit;
				} else
				{
					sendEmailFailNotification($email,$assertName);
					deleteDirectory("{$processingDir}/{$assertName}");
					if(!file_exists("{$logDirectory}/{$assertName}"))
					{
						mkdir("{$logDirectory}/{$assertName}");
						chmod("{$logDirectory}/{$assertName}",0777);
						file_put_contents("{$logDirectory}/{$assertName}/results.json",json_encode($results,JSON_PRETTY_PRINT));
					} else
					{
						file_put_contents("{$logDirectory}/{$assertName}/results.json",json_encode($results,JSON_PRETTY_PRINT),FILE_APPEND);
					}
					echo 'Single project processing failed to complete successfully.<br> Returned Output:<br><pre>';
					print_r($results);
					echo '</pre><br>Please correct the input list and try again.<br>';
					echo '<a href="javascript:history.back()">Try Again</a>';
					exit;
				}
			} else
			{
				echo 'The Assertion Name Entered does not match any data directories. Please check the spelling and try again.<br>';
				echo '<a href="javascript:history.back()">Try Again</a>';
			}
		}
	} else
	{
		echo 'You must be logged in to use this service...';
	}
?>