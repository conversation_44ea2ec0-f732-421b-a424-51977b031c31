<?php
	$authAccts = (array)["C<PERSON><PERSON><PERSON>","STORER","RHEAD","RBAR<PERSON>","WHOXSIE"];

	function deleteDirectory($dir)
	{
	    if (!file_exists($dir))
	    {
	        return true;
	    }
	    if (!is_dir($dir))
	    {
	        return unlink($dir);
	    }
	    foreach (scandir($dir) as $item)
	    {
	        if ($item == '.' || $item == '..')
	        {
	            continue;
	        }
	        if (!deleteDirectory($dir . DIRECTORY_SEPARATOR . $item))
	        {
	            return false;
	        }
	    }
	    return rmdir($dir);
	}

	function createProcessingArray($path)
	{
		$listArray = (array)[];
		// $files = array_values(array_diff(scandir($path), ['.','..']));
		$files = glob($path.'/*',GLOB_BRACE);
		foreach ($files as $key => $value)
		{
			if(is_dir($value))
			{
				$libDir = $value;
			} else
			{
				array_push($listArray, $value);
			}
		}
		$libFiles = glob($libDir.'/*.txt',GLOB_BRACE);
		return array_merge($listArray,$libFiles);
	}

	function submitCbProject($cmd)
	{
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, 'localhost:8001/pcmmrpc');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $cmd);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');

        $headers = array();
        $headers[] = 'Connection: keep-alive';
        $headers[] = 'Content-Type: text/plain;charset=UTF-8';
        $headers[] = 'Accept: */*';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($ch);
        if (curl_errno($ch))
        {
            return 'Error:' . curl_error($ch);
        }
        curl_close($ch);

        return $result;
	}

	function sendEmailSuccessNotification($email,$data)
	{
		$to = $email;
		$subject = "CompBio Mass Proccessing - {$data->AssertionName}";

		$message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

	<head>
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>Mass Proccessing Results</title>
	    <!--[if gte mso 9]><xml>
		      <o:OfficeDocumentSettings>
			    <o:AllowPNG/>
			    <o:PixelsPerInch>96</o:PixelsPerInch>
			  </o:OfficeDocumentSettings>
			</xml><![endif]-->
	    <style>
	    #title-wrap {
	        margin: auto 0;
	        text-align: center;
	        font-variant: small-caps;
	        font-weight: bold
	    }

	    table {
	        margin: auto;
	        border-collapse: collapse;
	        width: 100%
	    }

	    td,
	    th {
	        border: 1px solid #ddd;
	        border-color: rgba(221,221,221,0.5);
	        text-align: left;
	        padding: 1px;
	    }

	    #list {
	        text-align: center
	    }
	    </style>
	</head>

	<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
	    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>

	    <div id="title-wrap">
	    	<h2 class="title">Mass Proccessing Project '.$data->AssertionName.' has finished.  Some Quick-Look processing details are listed below.</h2>
	    </div>
	    <table width="100%" border="0" cellpadding="0" cellspacing="0" style="min-width: 100%;" role="presentation">
	        <tr>
	            <th>Assertion Name:</th>
	            <td>' . $data->AssertionName . '</td>
	        </tr>
	        <tr>
	            <th>Started At:</th>
	            <td>' . $data->stats->processingStart . '</td>
	        </tr>
	        <tr>
	            <th>Finished At:</th>
	            <td>' . $data->stats->processingEnd . '</td>
	        </tr>
	        <tr>
	            <th>Total Processed:</th>
	            <td>' . $data->stats->totalProcessed . '</td>
	        </tr>
	        <tr>
	            <th>Successfully Completed:</th>
	            <td>' . $data->stats->success . '</td>
	        </tr>
	        <tr>
	            <th>Processing Failures:</th>
	            <td>' . $data->stats->failed . '</td>
	        </tr>
	    </table>
	</body>

	</html>';


		$headers = "MIME-Version: 1.0" . "\r\n";
		$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
		$headers .= 'From: <<EMAIL>>' . "\r\n";
		$headers .= 'CC: <EMAIL>';
		mail($to,$subject,$message,$headers);
	}

	function sendEmailFailNotification($email,$name)
	{
		$to = $email;
		$subject = "CompBio Mass Proccessing - {$name}";

		$message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

	<head>
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>Mass Proccessing Results</title>
	    <!--[if gte mso 9]><xml>
		      <o:OfficeDocumentSettings>
			    <o:AllowPNG/>
			    <o:PixelsPerInch>96</o:PixelsPerInch>
			  </o:OfficeDocumentSettings>
			</xml><![endif]-->
	    <style>
	    #title-wrap {
	        margin: auto 0;
	        text-align: center;
	        font-variant: small-caps;
	        font-weight: bold
	    }
	    #message-wrap {
	        margin: auto 0;
	        text-align: center;
		}
	    table {
	        margin: auto;
	        border-collapse: collapse;
	        width: 100%
	    }
	    td,
	    th {
	        border: 1px solid #ddd;
	        border-color: rgba(221,221,221,0.5);
	        text-align: left;
	        padding: 1px;
	    }

	    #list {
	        text-align: center
	    }
	    </style>
	</head>

	<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
	    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>

	    <div id="title-wrap">
	    	<h2 class="title">Mass Proccessing Project '.$name.' has finished in failure.</h2>
	    </div>
	    <div id="message-wrap">
	    	Please check the input_data directory project file structure and the associated lists.  Contact Curtis Marcum, <a href="mailto:<EMAIL>"><EMAIL></a>, if multiple processing runs end in failure and you need further assistance.
	    </div>
	</body>

	</html>';


		$headers = "MIME-Version: 1.0" . "\r\n";
		$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
		$headers .= 'From: <<EMAIL>>' . "\r\n";
		$headers .= 'CC: <EMAIL>';
		mail($to,$subject,$message,$headers);
	}
?>