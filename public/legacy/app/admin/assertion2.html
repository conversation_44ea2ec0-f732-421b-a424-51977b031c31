<!DOCTYPE html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="../assets/images/sphere.ico" type="image/ico" sizes="16x16">
    <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <title>Assertion Engine Results View</title>
    <style type="text/css">
    iframe#display {
        height: 99vh;
        width: 100%;
    }
    </style>
    <script></script>
</head>

<body>
    <iframe id="display" class="embed-responsive-item"></iframe>
    <script src="https://code.jquery.com/jquery-3.5.1.min.js" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js" integrity="sha256-VazP97ZCwtekAsvgPBSUwPFKdrwD3unUfSGVYrahUqU=" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl" crossorigin="anonymous"></script>
    <script type="text/javascript">
        window.onload = (event) => {
            let assertion = location.search.substring(1);
            // let account = sessionStorage.getItem('Laboratory');
            let account = window.opener.labname;
            // let url = `${window.location.origin}/legacy/data/${account.toUpperCase()}/account_info/info.json
            // let relSrc = `../../data/${account.toUpperCase()}/account_info/assertion2/${assertion}/index.html`;
            let relSrc = `${window.location.origin}/legacy/data/${account.toUpperCase()}/account_info/assertion2/${assertion}/index.html`;
            console.log(assertion);
            console.log(account);
            document.getElementById('display').src = relSrc;
        }
    </script>
</body>
</html>