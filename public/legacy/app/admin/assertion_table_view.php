<?php
include('../../config/config.php');
session_start();
if(isset($_SESSION['LoggedIn']))
{
    $acct = strtoupper($_SESSION['Laboratory']);
    $email = $_SESSION['Email'];
    include(TEMPLATES_ROOT.DIRECTORY_SEPARATOR.'header.php');
    echo '<title>GTAC - Assertion Batch Results</title>';
    if(file_exists(CSS_ROOT.DIRECTORY_SEPARATOR.'acct.min.css'))
    {
        echo '<link rel="stylesheet" type="text/css" href="'.CSS_WEBROOT.'/acct.min.css?ver='.filemtime(CSS_ROOT.DIRECTORY_SEPARATOR.'acct.min.css').'">';
    } else
    {
        echo '<link rel="stylesheet" type="text/css" href="'.CSS_WEBROOT.'/acct.css?ver='.filemtime(CSS_ROOT.DIRECTORY_SEPARATOR.'acct.css').'">';
    }
    $batchProject = (isset($_GET['batch'])) ? $_GET['batch'] : NULL;
    $projectPath = "../../data/{$acct}/account_info/assertion_w_libs/{$batchProject}";
    $projectInfoPath = "{$projectPath}/info.json";
?>
</head>
<body>
    <div class="container-fluid">
<?php if(file_exists($projectPath) && file_exists($projectInfoPath)) :
    $info = json_decode(file_get_contents($projectInfoPath));
    $runResults = $info->RunResults;
    $removed = (array)[];
    foreach($runResults as $key => $result)
    {
        if($result->similarity === 'n/a')
        {
            array_push($removed, $result);
            unset($runResults[$key]);
        }
    }
    function sortBySimilarity($a,$b)
    {
        if ($a->similarity == $b->similarity) return 0;
        return ($a->similarity > $b->similarity)?-1:1;
    }
    usort($runResults, 'sortBySimilarity');
    $index = 1;
?>
        <div class="row">
            <div class="col-12 ml-auto main-content-col">
                <main role="main">
                    <div class="row">
                        <div class="col">
                            <section id="tab-1" class="tab justify-content-center tabbed-content">
                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">Assertion Batch Results</h3>
                                <h3 class="page-title text-center d-md-none mb-2">Assertion Batch Results</h3>
                                <section class="tab-content assert-lib-table-page">
                                    <div class="tab-head text-center">
                                        <div class="row">
                                            <div class="col h6 font-weight-bold">Assertion Name: <span id="assertion-batch-name"><?php echo $info->name; ?></span></div>
                                            <div class="col h6 font-weight-bold">Processing Date: <?php echo $info->creation_date; ?></div>
                                            <div class="col h6 font-weight-bold">Assertion Engine Version: <?php echo $info->engine; ?></div>
                                        </div>
                                    </div>
                                    <div class="table-responsive">
                                      <table id="lib-results-table" class="table table-hover">
                                          <thead>
                                        <tr>
                                          <?php if(property_exists($info,'isLibToLibAssertion') && $info->isLibToLibAssertion === true) : ?>
                                          <th scope="col">#</th>
                                          <th scope="col">Similarity Score</th>
                                          <th scope="col">Name</th>
                                          <th scope="col">Base Library Project</th>
                                          <th scope="col">Secondary Library Project</th>
                                          <th scope="col">Exit Status</th>
                                          <th scope="col">Exit Error</th>
                                          <?php else : ?>
                                          <th scope="col">#</th>
                                          <th scope="col">Similarity Score</th>
                                          <th scope="col">Name</th>
                                          <th scope="col">Library Project</th>
                                          <th scope="col">Project</th>
                                          <th scope="col">Exit Status</th>
                                          <th scope="col">Exit Error</th>
                                          <?php endif; ?>
                                        </tr>
                                      </thead>
                                      <?php if(property_exists($info,'isLibToLibAssertion') && $info->isLibToLibAssertion === true) : ?>
                                      <tbody>
                                        <?php foreach($runResults as $result): ?>
                                        <tr class="valid-row">
                                          <th scope="row"><?php echo $index; ?></th>
                                          <td><?php echo $result->similarity; ?></td>
                                          <td><?php echo $result->Name; ?></td>
                                          <td><?php echo $result->Project1; ?></td>
                                          <td><?php echo $result->Project2; ?></td>
                                          <td><?php echo $result->Return; ?></td>
                                          <td><?php echo (isset($result->err)) ? $result->err : 'n/a'; ?></td>
                                        </tr>
                                        <?php $index++;
                                            endforeach;
                                            foreach($removed as $result): ?>
                                       <tr>
                                          <th scope="row"><?php echo $index; ?></th>
                                          <td><?php echo $result->similarity; ?></td>
                                          <td><?php echo $result->Name; ?></td>
                                          <td><?php echo $result->Project1; ?></td>
                                          <td><?php echo $result->Project2; ?></td>
                                          <td><?php echo $result->Return; ?></td>
                                          <td><?php echo (isset($result->err)) ? $result->err : ''; ?></td>
                                        </tr>
                                        <?php $index++;
                                            endforeach; ?>
                                      </tbody>
                                      <?php else : ?>
                                      <tbody>
                                        <?php foreach($runResults as $result): ?>
                                        <tr class="valid-row">
                                          <th scope="row"><?php echo $index; ?></th>
                                          <td><?php echo $result->similarity; ?></td>
                                          <td><?php echo $result->Name; ?></td>
                                          <td><?php echo $result->LibProject; ?></td>
                                          <td><?php echo $result->SingleProject; ?></td>
                                          <td><?php echo $result->Return; ?></td>
                                          <td><?php echo (isset($result->err)) ? $result->err : 'n/a'; ?></td>
                                        </tr>
                                        <?php $index++;
                                            endforeach;
                                            foreach($removed as $result): ?>
                                       <tr>
                                          <th scope="row"><?php echo $index; ?></th>
                                          <td><?php echo $result->similarity; ?></td>
                                          <td><?php echo $result->Name; ?></td>
                                          <td><?php echo $result->LibProject; ?></td>
                                          <td><?php echo $result->SingleProject; ?></td>
                                          <td><?php echo $result->Return; ?></td>
                                          <td><?php echo (isset($result->err)) ? $result->err : ''; ?></td>
                                        </tr>
                                        <?php $index++;
                                            endforeach; ?>
                                      </tbody>
                                    <?php endif; ?>
                                      </table>
                                    </div>
                                </section>
                            </section>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    <script src="<?php echo JS_WEBROOT.'/assertion_table_view.js'; ?>"></script>
<?php else : ?>
    <div class="row">
        <div class="col">
            <p>Cannot find file</p>
            <p>Path:</p>
            <p><?php echo realpath($projectInfoPath); ?></p>
        </div>
    </div>
<?php endif;
}
?>
