<?php
include('../../config/config.php');
session_start();
if (isset($_SESSION['LoggedIn'])) {
  $labname = $_SESSION['Laboratory'];
  $email = $_SESSION['Email'];
  include(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'header.php');
  echo '   <title>GTAC-CompBio Batch Builder Sheet</title>';
  echo '   <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/handsontable/dist/handsontable.full.js"></script>';
  echo '   <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable/dist/handsontable.full.css" />';
  // if (file_exists(CSS_ROOT . DIRECTORY_SEPARATOR . 'acct.min.css')) {
  //     echo '<link rel="stylesheet" type="text/css" href="' . CSS_WEBROOT . '/acct.min.css?ver=' . filemtime(CSS_ROOT . DIRECTORY_SEPARATOR . 'acct.min.css') . '">';
  // } else {
  //     echo '<link rel="stylesheet" type="text/css" href="' . CSS_WEBROOT . '/acct.css?ver=' . filemtime(CSS_ROOT . DIRECTORY_SEPARATOR . 'acct.css') . '">';
  // }
} else {
  session_destroy();
  header('Location: ' . WEBROOT);
  exit;
}

?>
<style>
  body {
    background: rgb(39, 33, 95);
  }

  div.header,
  div.footer {
    height: 60px;
  }

  #example table>tbody>tr:nth-child(1) td,
  #example table>tbody>tr:nth-child(1) th {
    border-bottom: 2px solid black;

  }

  #example table>tbody>tr:nth-child(1) td {
    font-weight: bold;
    background: lightcyan;
  }

  #btn-back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
  }
</style>
</head>

<body>
  <div class="container-fluid">
    <div class="row">
      <div class="col header">
        <input type="email" class="form-control my-2" id="contact-email" placeholder="Enter a Contact Email" value="<?php echo $email; ?>" readonly>
      </div>
      <div class="col header">
        <button type="button" class="btn btn-danger btn-standard my-2 ml-3 mr-1" onclick="window.close();">Cancel</button>
        <button type="button" id="reset-batch-btn" class="btn btn-warning btn-standard my-2 ml-3 mr-1">Reset</button>
        <button type="button" id="init-batch-btn" class="btn btn-primary btn-standard my-2 mx-1">Submit Batch Sheet</button>
      </div>
    </div>
    <div class="row">
      <div class="col main-content">
        <div id="example"></div>
      </div>
    </div>
    <div class="row">
      <div class="col footer">
      </div>
    </div>
    <div class="modal" tabindex="-1" role="dialog" id="batch-modal">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="batch-modal-title">Message</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="batch-modal-close-btn-2">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="batch-modal-message">
            <p>Modal body text goes here.</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal" id="batch-modal-close-btn-1">OK</button>
            <!-- <button type="button" class="btn btn-primary">Save changes</button> -->
          </div>
        </div>
      </div>
    </div>
  </div>
  <button type="button" class="btn btn-danger btn-floating btn-lg" id="btn-back-to-top">
    <span>TOP</span>
  </button>
  <!-- <script src="bb.js"></script> -->
  <script>
    const container = document.querySelector('#example');
    const hot = new Handsontable(container, {
      contextMenu: true,
      startRows: 2500,
      startCols: 50,
      // rowHeaders: true,
      rowHeaders: function(index) {
        if (index === 0) {
          return 'Project Name';
        } else {
          return index;
        }
      },
      rowHeaderWidth: 100,
      colHeaders: true,
      copyPaste: true,
      fixedRowsTop: 1,
      filters: true,
      licenseKey: 'non-commercial-and-evaluation' // for non-commercial use only
      // beforePaste: (data, coords) => {
      //   console.log(data);
      //   data -> [[1, 2, 3], [4, 5, 6]]
      //   data.splice(0, 1);
      //   data -> [[4, 5, 6]]
      //   coords -> [{startRow: 0, startCol: 0, endRow: 1, endCol: 2}]
      // }

    });
    // hot.addHook('beforePaste', function(data) {
    //   // console.log(this.getDataAtRow(0))
    //   // console.log(data);
    //   // data[0] = data[0].map((el) => cleanString(el.trim().replace(/ /g, '_')));
    //   // checkArrayForIrregularities(data);
    // });
    // hot.addHook('afterPaste', function(data) {
    //   // console.log(this.getDataAtRow(0))
    //   // console.log(data);
    //   // checkArrayForIrregularities(data);
    // });
    hot.addHook('beforeChange', function(changes, source) {
      // console.log(this.getDataAtRow(0))
      console.log(changes);
      changes.forEach(function(change) {
        if (change[0] === 0 && change[3] !== null && change[3] !== '') {
          console.log(change[3]);
          change[3] = cleanString(change[3].trim().replace(/ /g, '_'));
          console.log(change[3]);
        }
      });

      // hot.getData()[0].filter(el => el != null ).map((el) => cleanString(el.trim().replace(/ /g, '_')));
    });
    // hot.addHook('afterChange', function(changes) {
    //   // console.log(this.getDataAtRow(0))
    //   // console.log(changes);
    //   // hot.getData()[0].filter(el => el != null ).map((el) => cleanString(el.trim().replace(/ /g, '_')));
    // });
  </script>
  <script>
    //Get the button
    let mybutton = document.getElementById("btn-back-to-top");

    // When the user scrolls down 20px from the top of the document, show the button
    window.onscroll = function() {
      scrollFunction();
    };

    function scrollFunction() {
      if (
        document.body.scrollTop > 20 ||
        document.documentElement.scrollTop > 20
      ) {
        mybutton.style.display = "block";
      } else {
        mybutton.style.display = "none";
      }
    }
    // When the user clicks on the button, scroll to the top of the document
    mybutton.addEventListener("click", backToTop);

    function backToTop() {
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
    }
  </script>
  <script>
    $(function() {
      console.log("DOC Ready!");
      $('#init-batch-btn').click(function() {
        console.log('Submit Button Pressed!');
        if ($('#contact-email').val().trim().length > 0 && validateEmail($('#contact-email').val().trim())) {
          $('#contact-email').removeClass('is-invalid');
          collectSheetData();
        } else {
          $('#contact-email').addClass('is-invalid');
          openModalMessage('ERROR!', 'Email address is either incorrect format or empty.  Please enter a valid email address.')
        }
      });
      $('#reset-batch-btn').click(function() {
        console.log('Reset Button Pressed!');
        hot.clear();
      });
    });

    function validateEmail(inputText) {
      var mailformat = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
      if (inputText.match(mailformat)) {
        return true;
      } else {
        return false;
      }
    }

    function collectSheetData() {
      console.log('collecting data...');
      let firstRowNull = (hot.getData()[1].indexOf(null) < 0) ? hot.getData()[1].length : hot.getData()[1].indexOf(null);
      console.log(firstRowNull);
      // let data = hot.getData().map(subarray => subarray.filter(el => el != null)).filter(arr => arr.length > 0);
      let data = hot.getData()
        .map(row => row.filter((el, index) => index < firstRowNull))
        .filter(el => !([...new Set(el)].length === 1 && [...new Set(el)][0] === null))
        .map(row => row.map(el => (el === null) ? '' : el));
      console.log(data);
      if (data.length > 0) {
        // data = removeNullVals(data);
        console.log(data);
        if (checkArrayForIrregularities(data)) {
          console.log(data);
          let colNames = data.shift();
          console.log(colNames);
          let cols = data[0].length;
          let dataFinal = [];
          console.log(data);
          let dupProjectNames = [];
          colNames.forEach(function(name) {
            if (name != '') {
              let dupBool = opener.duplicateNameCheck(name);
              console.log(dupBool);
              if (dupBool === true) {
                dupProjectNames.push(name);
              }
            }
          });
          console.log(dupProjectNames);
          if (dupProjectNames.length > 0) {
            console.log('ERROR: Failed finaliztion checks. Stopping submission. Duplicate Project Names Found\n' + dupProjectNames);
            openModalMessage('WARNING!', `Project Name Overlap(s) Detected! You already have projects named:<br>${dupProjectNames.join(',')}<br>Change the listed name(s) in your Batch Sheet to proceed.`);
          } else {
            for (let i = 0; i < cols; i++) {
              if (colNames[i] != '') {
                let tmp = {
                  name: cleanString(colNames[i].replace(/ /g, '_').replace(/\./g, '_').replace(/[^a-z0-9_-]/gi, '')),
                  ents: data.map(c => c[i]).filter(v => v != ''),
                  expr: []
                };
                if (i < colNames.length - 1 && colNames[i + 1] == '') {
                  tmp.expr = data.map(c => c[i + 1]).filter(v => v != '');
                }
                dataFinal.push(tmp);
              }
            }
            let genInfo = {
              account: window.opener.labname,
              contactEmail: $('#contact-email').val()
            };
            dataFinal.unshift(genInfo);
            // Commented for debug testing
            // UNCOMMENT BEFORE SENDING GIT PUSH
            submitBatchProcessing(dataFinal);
          }
        } else {
          console.log('ERROR: Failed finaliztion checks.  Stopping submission');
        }
      } else {
        openModalMessage('ERROR!', 'The batch sheet seems to be empty.  Please make sure you start loading data on Column A.')
      }
    }

    function submitBatchProcessing(data) {
      console.log('submitting batch data...');
      data = `cb_batch_data=${JSON.stringify(data)}`;
      let url = `${Webroot}legacy/app/assets/php/cb_batch_sub.php`;
      let xhr = new XMLHttpRequest();
      xhr.open("POST", url, true);
      xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
      xhr.upload.onload = function() {
        console.log(`Upload finished successfully.`);
        hot.clear();
        hot.deselectCell();
        window.close();
      };

      xhr.upload.onerror = function() {
        console.log(`Error during the upload: ${xhr.status}`);
      };
      xhr.onload = function() {
        if (xhr.status == 200) {
          console.log('Sheet Upload Complete!');
          log(xhr.responseText);
        } else {
          console.log("error " + this.status);
        }
      };
      xhr.send(data);
    }

    function cleanString(input) {
      var output = "";
      for (var i = 0; i < input.length; i++) {
        if (input.charCodeAt(i) <= 127) {
          output += input.charAt(i);
        }
      }
      output = output.replace(/[[\]{}()*+!<=:?\/\\^$|#,&%@]/g, '');
      return output;
    }

    // function removeNullVals(array) {
    //   console.log(array);
    //   if (Array.isArray(array) && array.length > 0) {
    //     let defLength = array[1].length;
    //     let len1 = array.length;
    //     let retArray = [];
    //     for (let i = 0; i < len1; i++) {
    //       let intRetArray = [];
    //       if (Array.isArray(array[i]) && array[i].length > 0) {
    //         let array2 = [...array[i]];
    //         // let len2 = array2.length;
    //         for (let i2 = 0; i2 < defLength; i2++) {
    //           let clnVal;
    //           if (array2[i2] === null) {
    //             console.log(`Null value cleaned at index: [${i2}][${i}]`);
    //             clnVal = '';
    //           } else {
    //             clnVal = array2[i2];
    //           }
    //           intRetArray.push(clnVal);
    //         }
    //         retArray.push(intRetArray);
    //       } else {
    //         console.log('ERROR: Array failed condition 2 while removing NULL values');
    //         openModalMessage('ERROR', 'Array failed condition 2 while removing NULL values');
    //         return false;
    //       }
    //     }
    //     console.log('Array null values have been removed');
    //     return retArray;
    //   } else {
    //     console.log('ERROR: Array failed condition 1 while removing NULL values');
    //     openModalMessage('ERROR', 'Array failed condition 1 while removing NULL values');
    //     return false;
    //   }
    // }

    function checkArrayForIrregularities(array) {
      console.log(array);
      let flag = false,
        flagLog = [],
        flagLocation = [];
      if (Array.isArray(array) && array.length > 2) {
        let copy = [...array];
        let headers = copy.shift();
        console.log(headers);
        let duplicateHeaders = findDuplicates(headers);
        console.log(duplicateHeaders);
        if (duplicateHeaders.length > 0) {
          console.log('Irregularity detected: Column headers or project names have duplications');
          openModalMessage('WARNING!', `Duplicate column headers or project names detected.  Please check your batch spreadsheet and correct.<br>Flagged Column Headers: ${duplicateHeaders.toString()}`);
          return false;
        } else {
          if (Array.isArray(copy[0]) && copy[0].length > 0) {
            let colEndFlags = new Array(copy[1].length);
            let len1 = copy.length;
            for (let i = 0; i < len1; i++) {
              let array2 = copy[i];
              let len2 = array2.length;
              for (let i2 = 0; i2 < len2; i2++) {
                if (array2[i2].length > 0 && colEndFlags[i2]) {
                  console.log(`Irregularity detected at index: [${i2}][${i}]`);
                  flag = true;
                  if (flagLocation.indexOf(i2) < 0) {
                    flagLocation.push(i2);
                    flagLog.push(`Empty Cell: Column ${i2 + 1}, Row ${i}`);
                  }
                } else if (array2[i2].length === 0 && colEndFlags[i2] === undefined) {
                  console.log(`Column ${i2} ended at index: [${i2}][${i}]`);
                  colEndFlags[i2] = true;
                }
              }
            }
          } else {
            console.log('ERROR: Array failed condition 2');
            openModalMessage('ERROR', 'Array failed condition 2 while checking for irregularities');
            return false;
          }
        }
      } else {
        console.log('ERROR: Array failed condition 1');
        openModalMessage('ERROR', 'Array failed condition 1 while checking for irregularities');
        return false;
      }
      if (flag === true) {
        console.log('FLAG THROWN! Data Problems found!')
        console.log(flag);
        console.log(flagLog);
        openModalMessage('WARNING!', `Data irregularity detected at:<br>${flagLog.join(',<br>')}`);
        return false;
      } else {
        console.log('👍 no irregularities detected');
        console.log(flag);
        console.log(flagLog);
        return true;
      }
    }

    function findDuplicates(arr) {
      let filtered = arr.filter((el) => el != '');
      return filtered.filter((currentValue, currentIndex) => filtered.indexOf(currentValue) !== currentIndex);
    }

    function openModalMessage(title, msg) {
      $("#batch-modal-title").text(title);
      $("#batch-modal-message").html(msg);
      $("#batch-modal").modal("show");
    }
  </script>
</body>

</html>