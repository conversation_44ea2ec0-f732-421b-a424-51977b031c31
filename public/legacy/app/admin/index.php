<?php
include('../../config/config.php');
session_start();
if (isset($_SESSION['LoggedIn'])) {
    $labname = $_SESSION['Laboratory'];
    $id = $_SESSION['Id'];
    $email = $_SESSION['Email'];
    $token = $_SESSION['_token'];
    include(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'header.php');
    echo '<title>GTAC-CompBio Administration Panel</title>';
    if (file_exists(CSS_ROOT . DIRECTORY_SEPARATOR . 'acct.min.css')) {
        echo '<link rel="stylesheet" type="text/css" href="' . CSS_WEBROOT . '/acct.min.css?ver=' . filemtime(CSS_ROOT . DIRECTORY_SEPARATOR . 'acct.min.css') . '">';
    } else {
        echo '<link rel="stylesheet" type="text/css" href="' . CSS_WEBROOT . '/acct.css?ver=' . filemtime(CSS_ROOT . DIRECTORY_SEPARATOR . 'acct.css') . '">';
    }
} else {
    session_destroy();
    header('Location: ' . WEBROOT);
    exit;
}
?>
</head>

<body>
    <script>
        /* Get laravel CRSF token for legacy app AJAX calls */
        document.addEventListener('DOMContentLoaded', () => {
            // Create a <meta> element
            const metaElement = document.createElement('meta');
            metaElement.setAttribute('name', 'csrf-token');
            metaElement.setAttribute('content', '<?php echo $token; ?>');

            // Append the <meta> element to the <head>
            document.head.appendChild(metaElement);
            console.log('CSRF meta tag added successfully.');

        });
    </script>
    <script>
        var labname = '<?php echo $labname; ?>';
        var id = '<?php echo $id; ?>';
    </script>
    <nav class="navbar navbar-dark d-block d-lg-none d-xl-none">
        <div class="navbar-brand">Account Administration</div>
        <button class="navbar-toggler collapsed float-right" type="button" data-toggle="collapse" data-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="navbar-collapse collapse" id="navbarCollapse">
            <div class="account-name-mobile">
                <?php
                if ($_SESSION['LoggedIn']) {
                    echo $labname;
                } else {
                    echo 'Not Loggedin';
                }
                ?>
            </div>
            <ul class="navbar-nav mr-auto">
                <li class="navbar-text mobile-nav-section-title">
                    CompBio <i class="fa fa-chevron-right"></i>
                </li>
                <li class="nav-item mobile-nav-link active" data-tab-request="1">
                    Completed Projects <span class="sr-only">(current)</span>
                </li>
                <li class="nav-item mobile-nav-link" data-tab-request="2">
                    Submit New Project
                </li>
                <li class="navbar-text mobile-nav-section-title">
                    BioExplorer <i class="fa fa-chevron-right"></i>
                </li>
                <li class="nav-item mobile-nav-link active" data-tab-request="7">
                    Completed Projects <span class="sr-only">(current)</span>
                </li>
                <li class="nav-item mobile-nav-link" data-tab-request="8">
                    Submit New Project
                </li>
                <?php /* if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'aers_tab.html')) : */ ?>
                <!-- <li class="navbar-text mobile-nav-section-title">
                        AERS <i class="fa fa-chevron-right"></i>
                    </li>
                    <li class="nav-item mobile-nav-link active" data-tab-request="10">
                        Completed Projects <span class="sr-only">(current)</span>
                    </li>
                    <li class="nav-item mobile-nav-link" data-tab-request="11">
                        Submit New Project
                    </li> -->
                <?php /* endif; */ ?>
                <li class="navbar-text mobile-nav-section-title">
                    Comparison <i class="fa fa-chevron-right"></i>
                </li>
                <li class="nav-item mobile-nav-link" data-tab-request="6">
                    Project & Filter
                </li>
                <li class="nav-item mobile-nav-link" data-tab-request="9">
                    Assertion Generation
                </li>
                <li class="navbar-text mobile-nav-section-title">
                    Account <i class="fa fa-chevron-right"></i>
                </li>
                <a href="/account">
                    <li class="nav-link mobile-nav-link" href="/lab">
                        Maintenance
                    </li>
                </a>
                <a href="/lab">
                    <li class="nav-link mobile-nav-link" href="/lab">
                        Lab
                    </li>
                </a>
                <?php
                if (isset($_SESSION['Sudo'])) {
                    echo '<li class="navbar-text mobile-nav-section-title">Super User <i class="fa fa-chevron-right"></i></li>
                                <a href="/admin/account-requests">
                    <li class="nav-link mobile-nav-link">Create New Account</li>
                    </a>
                    <a href="/admin/users">
                <li class="nav-link mobile-nav-link">Accounts Management</li>
                </a>
                <li class="nav-item mobile-nav-link" data-tab-request="10">Email Notification Tool</li>';
                }
                ?>
                <li class="mobile-nav-link mt-5 d-flex">
                    <div id="mobile-log-out">
                        Log Out
                    </div>
                </li>
            </ul>

        </div>
    </nav>
    <div class="container-fluid">
        <div class="row">
            <div class="col-lg-2 d-none d-lg-block sidebar no-gutter-left">
                <section class="nav-panel">
                    <nav class="sidebar-sticky">
                        <p class="panel-title">Account Administration</p>
                        <p class="acct-name">
                            <?php
                            if ($_SESSION['LoggedIn']) {
                                echo $labname;
                            } else {
                                echo 'Not Loggedin';
                            }
                            ?>
                        </p>
                        <section class="nav-sub">
                            <span class="navbar-text ">CompBio <i class="fa fa-chevron-right"></i></span>
                            <ul class="nav flex-column">
                                <li class="nav-item active-nav-item" data-tab-request="1">Completed Projects</li>
                                <li class="nav-item" data-tab-request="2">Submit New Project</li>
                                <li class="nav-item-exception" id="open-batch-btn">Create Batch Submission</li>
                                <li class="nav-item-exception" id="batch-status-btn">Batch Processing Status</li>
                            </ul>
                        </section>
                        <section class="nav-sub">
                            <span class="navbar-text ">BioExplorer <i class="fa fa-chevron-right"></i></span>
                            <ul class="nav flex-column">
                                <li class="nav-item" data-tab-request="7">Completed Projects</li>
                                <li class="nav-item" data-tab-request="8">Submit New Project</li>
                            </ul>
                        </section>
                        <?php /* if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'aers_tab.html')) : */ ?>
                        <!-- <section class="nav-sub">
                                <span class="navbar-text ">AERS <i class="fa fa-chevron-right"></i></span>
                                <ul class="nav flex-column">
                                    <li class="nav-item" data-tab-request="10">Completed Projects</li>
                                    <li class="nav-item" data-tab-request="11">Submit New Project</li>
                                </ul>
                            </section> -->
                        <?php /* endif; */ ?>
                        <section class="nav-sub">
                            <span class="navbar-text">Comparison <i class="fa fa-chevron-right"></i></span>
                            <ul class="nav flex-column">
                                <li class="nav-item" data-tab-request="6">Project & Filter</li>
                                <li class="nav-item" data-tab-request="9">Assertion Generation</li>
                                <li class="nav-item" data-tab-request="12">ThemeLink</li>
                            </ul>
                        </section>
                        <section class="nav-sub">
                            <span class="navbar-text">Account <i class="fa fa-chevron-right"></i></span>
                            <ul class="nav flex-column">
                                <a href="/account" class="nav-link-anchor">
                                    <li class="nav-link">Maintenance</li>
                                </a>
                                <a href="/lab" class="nav-link-anchor">
                                    <li class="nav-link">Lab</li>
                                </a>
                            </ul>
                        </section>
                        <?php
                        if (isset($_SESSION['Sudo'])) {
                            echo '
                            <section class="nav-sub">
                                <span class="navbar-text">Administrator <i class="fa fa-chevron-right"></i></span>
                                <ul class="nav flex-column">
                                    <a href="/admin/account-requests" class="nav-link-anchor">
                                        <li class="nav-link">Create New Account</li>
                                    </a>
                                    <a href="/admin/users" class="nav-link-anchor">
                                        <li class="nav-link">Accounts Management</li>
                                    </a>
                                    <li class="nav-item" data-tab-request="10">Email Notification Tool</li>
                                </ul>
                            </section>';
                        }
                        ?>
                        <button type="button" id="log-out-btn" class="btn btn-primary mt-5 mb-5">Log Out</button>
                    </nav>
                </section>
            </div>
            <div class="col-12 col-lg-10 ml-auto main-content-col">
                <!-- <main role="main" class="col-md-9 ml-sm-auto col-lg-10 pt-3 px-4"> -->
                <main role="main">
                    <div class="row">
                        <div class="col">
                            <section id="tab-1" class="tab justify-content-center tabbed-content">
                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">Project Management</h3>
                                <h3 class="page-title text-center d-md-none mb-2">Project Management</h3>
                                <section class="tab-content tab-content d-flex flex-column justify-content-between">
                                    <div class="row tab-content-row">
                                        <div class="col-md-12">
                                            <div class="row">
                                                <div class="col-xs-12 col-md-8">
                                                    <!-- Table Head -->
                                                    <h5 class="content-title col-xs-12 col-6">Completed Projects</h5>
                                                    <button type="button" id="cb-shared-projects" class="col-xs-10 col-3 btn btn-primary btn-block btn-standard">Lab Projects</button>
                                                </div>
                                                <div class="col-xs-12 col-md-4 my-1">
                                                    <div class="input-group">
                                                        <input type="text" id="pro-name-search" class="form-control" placeholder="Search Name..." autocomplete="off" name="pro-name-search">
                                                        <div class="input-group-append">
                                                            <button class="btn btn-outline-secondary" type="button" id="clear-pro-search-btn">Clear</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row px-4">
                                                <div class="col-5 px-2" id="comp-pro-names-header">Project Name</div>
                                                <div class="col-3 px-2" id="comp-pro-date-header">Date/Time Created</div>
                                                <div class="col text-left px-2" id="comp-pro-ent-header">Number Entities</div>
                                                <div class="col text-right px-2" id="comp-pro-profile-header">Profile Data</div>
                                                <div class="col text-right px-2" id="comp-pro-shared-header">Shared</div>
                                            </div>
                                            <div class="box-outline-wrap tall-box mb-2">
                                                <ul id="projects-list">
                                                    <i class="fa fa-spinner fa-pulse fa-5x fa-fw" style="position: absolute;top: 35%;left: 40%"></i>
                                                    <span class="sr-only">Loading...</span>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row tab-content-row short-row">
                                        <div class="col-md-6 justify-content-start btn-panel-group">
                                            <div class="row mt-1 mb-3 justify-content-center" id="optimization-check-wrap">
                                                <div class="form-check-inline">
                                                    <label class="form-check-label">
                                                        <input type="checkbox" class="form-check-input" id="optimization-check" checked>CompBio Visualizer Optimization Mode ( top 50 themes only )
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <button type="button" id="visualize-project-btn" class="btn btn-primary btn-block btn-standard">Visualize Project</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="toggle-share-btn" class="btn btn-primary btn-block btn-standard">Share/Unshare</button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <button type="button" id="delete-project-btn" class="btn btn-primary btn-block btn-standard">Delete Project</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="display-input-btn" class="btn btn-primary btn-block btn-standard">Display Input List</button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <button type="button" id="create-folder-btn" class="btn btn-primary btn-block btn-standard">Create Project Folder</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="move-folder-btn" class="btn btn-primary btn-block btn-standard">Move To Folder</button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <button type="button" id="reanalyze-btn" class="btn btn-primary btn-block btn-standard">Reanalyze Project</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="announcements-btn" class="btn btn-primary btn-block btn-standard">Announcements</button>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="col-md-6 justify-content-start btn-panel-group">
                                            <div class="row mt-1 mb-3 justify-content-center">
                                                <div class="form-check-inline" style="visibility: hidden;">
                                                    <label class="form-check-label">
                                                        <input type="checkbox" class="form-check-input" checked>CompBio Visualizer Optimization Mode ( top 50 themes only )
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <a href="../../help/CompBio_Manual.pdf" target="_blank" id="cb_manual" class="btn btn-primary btn-block btn-standard btn-large" role="button" aria-pressed="true">Compbio User Manual</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </section>
                            <section id="tab-2" class="tab justify-content-center tabbed-content" style="display:none;">
                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">Project Submission</h3>
                                <h3 class="page-title text-center d-md-none mb-2">Project Submission</h3>
                                <section class="tab-content">
                                    <div class="row tab-content-row">
                                        <div class="col-lg-6">
                                            <h5>Project Information</h5>
                                            <hr class="section-seperate">
                                            <div id="info-wrap" class="input-form">
                                                <div class="form-group">
                                                    <label for="pname">Project Name: <i class="fa fa-question-circle-o" aria-hidden="true" onclick="displayModalHelp(1)"></i></label>
                                                    <input type="text" class="form-control" id="pname" tabindex="1" required>
                                                </div>
                                                <div class="form-group">
                                                    <label for="contact">Email: <i class="fa fa-question-circle-o" aria-hidden="true" onclick="displayModalHelp(2)"></i></label>
                                                    <input type="email" class="form-control" id="contact" tabindex="2" required>
                                                </div>
                                                <div class="form-row">
                                                    <div class="form-group col-md-6" hidden>
                                                        <label for="concept">Processing Type: <i class="fa fa-question-circle-o" aria-hidden="true" onclick="displayModalHelp(4)"></i></label>
                                                        <select id="concept" class="form-control" required>
                                                            <option value="500" selected>Standard Run ( 500 Concepts )</option>
                                                            <option value="250">Rapid Run ( 250 Concepts )</option>
                                                            <option value="1000">Comprehensive Run ( 1000 Concepts )</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="form-submit">Initiate Processing:</label>
                                                        <button type="submit" id="form-submit" class="form-control btn btn-primary btn-standard" style="margin-top:0" tabindex="7">Submit Project</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-6">
                                            <h5>Entity Input List <i id="input-help" class="fa fa-question-circle aria-hidden=" true"></i></h5>
                                            <hr class="section-seperate">
                                            <div class="input-form">
                                                <div class="form-row">
                                                    <div class="form-group col-md-6 mb-5">
                                                        <label for="copypaste">Entity List: </label>
                                                        <br>
                                                        <textarea id="copypaste" name="cplist" class="entity-textarea" placeholder="Copy and Paste the Entity List Here..." tabindex="4"></textarea>
                                                    </div>
                                                    <div class="form-group col-md-6 mb-5">
                                                        <label>Expression Values: </label><br>
                                                        <textarea id="exp-list" name="explist" class="entity-textarea" placeholder="( Optional )" tabindex="5"></textarea>
                                                    </div>
                                                </div>
                                                <!-- <div class="form-row">
                                                    <div class="col-12 mb-5">
                                                        <div id="file-upload-innerwrap">
                                                            <label>Text File Upload:</label>
                                                            <br>
                                                            <input type="file" accept="text/plain,.csv" onchange="openFile(event)" id="uploadfiles" name="inputlist" tabindex="6" multiple>
                                                            <div id="filetype-error"></div>
                                                        </div>
                                                    </div>
                                                </div> -->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row tab-content-row notes-row">
                                        <div class="col-12">
                                            <div id="tips" class="p-2">
                                                <h5>Notes:</h5>
                                                <div class="row">
                                                    <div class="col-md-6 note-items"><span>-</span>All spaces in the project name are converted to underscores</div>
                                                    <div class="col-md-6 note-items"><span>-</span>All entity input lists must be newline delimited</div>
                                                    <div class="col-md-6 note-items"><span>-</span>Seperate and paste entities/expression data within corresponding boxes</div>
                                                    <div class="col-md-6 note-items"><span>-</span>Input lists length, 20 entities minimum and 1500 entities maximum</div>
                                                    <div class="col-md-6 note-items"><span>-</span>The file upload currently only supports txt and csv filetypes</div>
                                                    <div class="col-md-6 note-items"><span>-</span>File upload currently excepts entities only, not entity/expression combined</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </section>
                            <section id="tab-3" class="tab justify-content-center tabbed-content" style="display:none;">
                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">Account Maintenance</h3>
                                <h3 class="page-title text-center d-md-none mb-2">Account Maintenane</h3>
                                <section class="tab-content">
                                    <div class="row tab-content-row">
                                        <div class="col-md-6">
                                            <h5 class="">Email</h5>
                                            <hr class="section-seperate">
                                            <div>
                                                <div class="form-group">
                                                    <label for="current-email">Current Email Address:</label>
                                                    <input type="email" class="form-control" id="current-email" value="<?php echo $email; ?>" readonly>
                                                </div>
                                                <div class="form-group">
                                                    <span class="error-message" id="email-error"></span>
                                                    <label for="maintenance-contact">New Email Address:</label>
                                                    <input type="email" class="form-control" id="maintenance-contact" placeholder="Enter New Email Address...">
                                                </div>
                                                <button type="submit" id="email-new" class="btn btn-primary btn-standard ">Submit New Email</button>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h5 class="">Password</h5>
                                            <hr class="section-seperate">
                                            <div>
                                                <div class="form-group">
                                                    <label for="password">New Password:</label>
                                                    <input type="password" class="form-control" id="password" placeholder="Enter New Password..." autocomplete="off" auto-fill="false">
                                                </div>
                                                <div class="form-group">
                                                    <label for="password-confirm">Confirm New Password:</label>
                                                    <input type="password" class="form-control" id="password-confirm" placeholder="Re-Enter New Password..." autocomplete="off" auto-fill="false">
                                                </div>
                                                <button type="submit" id="pass-new" class="btn btn-primary btn-standard ">Submit New Password</button>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </section>
                            <?php
                            if (isset($_SESSION['Sudo'])) {
                                echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'sudo_admin_tabs.html');
                            }
                            ?>
                            <section id="tab-6" class="tab justify-content-center tabbed-content">
                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">Compare Projects</h3>
                                <h3 class="page-title text-center d-md-none mb-2">Compare Projects</h3>
                                <section class="tab-content">
                                    <div class="row tab-content-row">
                                        <div class="col-md-6">
                                            <h5 class="content-title col-12">Existsing Comparison Lists:</h5>
                                            <div class="box-outline-wrap">
                                                <ul id="filters-list">
                                                    <i class="fa fa-spinner fa-pulse fa-5x fa-fw" style="position: absolute;top: 35%;left: 40%"></i>
                                                    <span class="sr-only">Loading...</span>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="col-md-6 justify-content-start btn-panel-group">
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <div class="special-btn-wrap">
                                                        <p><small>Concept Level</small></p>
                                                        <button type="button" id="create-filter-btn" class="btn btn-primary btn-block btn-standard">New Comparison Filter</button>
                                                    </div>
                                                </div>
                                                <div class="col-lg-6">
                                                    <div class="special-btn-wrap">
                                                        <p><small>Gene Level</small></p>
                                                        <button type="button" id="create-filter-project-btn" class="btn btn-primary btn-block btn-standard">New Comparison Project</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <button type="button" id="display-filter-btn" class="btn btn-primary btn-block btn-standard">Display Overlap List</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="delete-filter-btn" class="btn btn-primary btn-block btn-standard">Delete Overlap Filter</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row tab-content-row">
                                        <div id="filter-info" class="col-6 d-none">
                                            <div class="box-outline-wrap table-responsive" style="display: block;">
                                                <table class="table info-table">
                                                    <tr>
                                                        <th>Filter Name:</th>
                                                        <td id="filterinfo-name"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Creation Date:</th>
                                                        <td id="filterinfo-creationdate"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Base Project:</th>
                                                        <td id="filterinfo-basename"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Overlap Project:</th>
                                                        <td id="filterinfo-overlapname"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Total Concepts:</th>
                                                        <td id="filterinfo-numofcons"></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                        <div id="create-filter-form" class="col-6 d-none">
                                            <h5 class="content-title col-12" id="filter-form-title">Create An Overlap Filter</h5>
                                            <hr class="section-seperate">
                                            <div id="info-wrap" class="input-form">
                                                <div class="form-group">
                                                    <label for="filter-name" id="filter-name-label">Filter Name: </label>
                                                    <input type="text" class="form-control" id="filter-name" required>
                                                </div>
                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="base-project-select">Base Project: </label>
                                                        <select id="base-project-select" class="form-control" required>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="project2-select">Compare To Project: </label>
                                                        <select id="project2-select" class="form-control" required>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <label for="init-create-filter" id="create-filter-label">Create Filter</label>
                                                        <button type="submit" id="init-create-filter" class="form-control btn btn-primary btn-standard" style="margin-top:0">Create Filter</button>
                                                        <label for="init-create-filter-project" id="filter-project-label">Create Project</label>
                                                        <button type="submit" id="init-create-filter-project" class="form-control btn btn-primary btn-standard" style="margin-top:0">Create Project</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </section>
                            <section id="tab-7" class="tab justify-content-center tabbed-content">
                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">BioExplorer Project Management</h3>
                                <h3 class="page-title text-center d-md-none mb-2">BioExplorer Project Management</h3>
                                <section class="tab-content">
                                    <div class="row tab-content-row">
                                        <div class="col-md-6">
                                            <div class="row">
                                                <h5 class="content-title col-6">Completed Projects</h5>
                                                <h6 class="col-3">Profile Data</h6>
                                                <h6 class="col-3 text-right">Shared</h6>
                                            </div>

                                            <div class="box-outline-wrap d-flex justify-content-center align-items-center" id="explorer-projects">
                                                <ul id="explorer-list">
                                                    <div>Nothing To Display Yet...</div>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="col-md-6 justify-content-start btn-panel-group">
                                            <div class="row mt-1 mb-3" id="optimization-check-wrap">
                                                <div class="form-check-inline">
                                                    <label class="form-check-label">
                                                        <input type="checkbox" class="form-check-input" id="bio-optimization-check" checked>BioExplorer Visualizer Optimization Mode ( top 50 themes only )
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <button type="button" id="visualize-explorer-btn" class="btn btn-primary btn-block btn-standard">Visualize Project</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="delete-bio-project-btn" class="btn btn-primary btn-block btn-standard">Delete Project</button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                    <button type="button" id="be-shared-projects" class="btn btn-primary btn-block btn-standard">Lab Projects</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="toggle-bio-share-btn" class="btn btn-primary btn-block btn-standard">Share/Unshare</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row tab-content-row" id="bio-project-info" hidden>
                                        <div class="col-12">
                                            <h5 id="bio-info-heading" class="content-title col-12">Project Information</h5>
                                            <div class="box-outline-wrap table-responsive">
                                                <table class="table info-table">
                                                    <tr>
                                                        <th>Name:</th>
                                                        <td id="bio-name" class="bio-info-cell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Submission Date:</th>
                                                        <td id="bio-sub-date" class="bio-info-cell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Submitted Value:</th>
                                                        <td id="bio-val" class="bio-info-cell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Value Class:</th>
                                                        <td id="bio-class" class="bio-info-cell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Included Terms:</th>
                                                        <td id="bio-terms" class="bio-info-cell"></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </section>
                            <section id="tab-8" class="tab justify-content-center tabbed-content" style="display:none;">
                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">BioExplorer Project Submission</h3>
                                <h3 class="page-title text-center d-md-none mb-2">BioExplorer Project Submission</h3>
                                <section class="tab-content">
                                    <div class="row tab-content-row">
                                        <div class="col">
                                            <div id="info-wrap" class="input-form">
                                                <?php
                                                if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'explorer_form.html')) {
                                                    echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'explorer_form.html');
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </section>
                            <section id="tab-9" class="tab justify-content-center tabbed-content">
                                <?php
                                if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'assertion_tab.html')) {
                                    echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'assertion_tab.html');
                                    
                                }
                                ?>
                            </section>
                            <section id="tab-12" class="tab justify-content-center tabbed-content">
                                <?php
                                if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'themelink_tab.html')) {
                                    echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'themelink_tab.html');
                                }
                                ?>
                            </section>
                            <?php
                            /*
                            if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'aers_tab.html')) {
                                echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'aers_tab.html');
                            }
                            */
                            ?>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    <div id="version-display">Compbio Visualizer: V2.6.0_NA</div>
    <style type="text/css">
        #version-display {
            color: #27215f;
            text-align: center;
            font-size: 12px;
            position: absolute;
            bottom: 5px;
            margin: auto;
            left: 0;
            right: 0;
            font-weight: bold;
            text-shadow: 1px 1px 5px #FFFFFF,
                -1px 1px 5px #FFFFFF,
                1px -1px 5px #FFFFFF,
                -1px -1px 5px #FFFFFF;
            /* font-family: monospace; */
        }
    </style>
    <div id="dialog-message" title="Project Submitted..." class="dialog-load-hidden" style="display:none;">
        <p id="alert-message">Congratulations! Your project has been submitted.<br>
            In most cases the submission will take approximately 1-5 minutes to process. Upon successful processing, an email will be sent to the provided email address, notifying of the project processing completion. At this time, your project will be available from the "Completed Submissions" tab, within the "Successfully Completed Projects" list.</p>
    </div>
    <div id="alert" title="Alert!" class="dialog-load-hidden" style="display:none;">
        <p id="alert-msg"></p>
    </div>
    <div id="dialog-list" title="List Display" class="dialog-load-hidden" style="display:none;">
        <div id="list-display"></div>
    </div>
    <div id="dialog-alert" title="Alert!" class="dialog-load-hidden" style="display:none;">
        <p id="alert-confirm"></p>
    </div>
    <div id="dialog-confirm" title="!!Delete Confirmation!!" class="dialog-load-hidden" style="display:none;">
        <p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>This project will be permanently deleted and cannot be recovered. Are you sure you want to delete this project and all of its contents?</p>
    </div>
    <div id="bio-delete-confirm" title="!!Delete Confirmation!!" class="dialog-load-hidden" style="display:none;">
        <p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>This project will be permanently deleted and cannot be recovered. Are you sure you want to delete this project and all of its contents?</p>
    </div>
    <!-- <div id="aers-delete-confirm" title="!!Delete Confirmation!!" class="dialog-load-hidden" style="display:none;">
        <p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>This project will be permanently deleted and cannot be recovered. Are you sure you want to delete this project and all of its contents?</p>
    </div> -->
    <div id="request-delete-confirm" title="!!Delete Confirmation!!" class="dialog-load-hidden" style="display:none;">
        <p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>On confirmation, this entry and all of the data associated with it will be deleted. Are you sure you want to delete this "Account Request" entry?</p>
    </div>
    <div id="dialog-reanalyze" title="Reanalyze Project" class="dialog-load-hidden" style="display:none;">
        <div class="input-form">
            <div class="row">
                <div class="reanalyze-warning"></div>
                <div class="form-group col-12">
                    <label for="reanalyze-name">Reanalyzed Project Name:</label><br>
                    <input type="text" id="reanalyze-name" class="form-control" required>
                </div>
                <div class="form-group col-12">
                    <label for="reanalyze-email">Email:</label><br>
                    <input type="text" id="reanalyze-email" class="form-control" requied>
                </div>
                <div class="form-group col-12" hidden>
                    <label for="reanalyze-concept">Number Of Concepts:</label><br>
                    <input type="number" id="reanalyze-concept" step="1" min="50" max="1000" class="form-control" required>
                </div>
                <!--Remove Context-->
                <!--                 <div class="form-group col-12">
                    <label for="reanalyze-context">Context:</label><br>
                    <input type="text" id="reanalyze-context"class="form-control">
                </div> -->
            </div>
        </div>
    </div>
    <div id="dialog-syn-alert" title="Alert!" class="dialog-load-hidden" style="display:none;">
        <p id="syn-confirm">CompBio recognizes less than half of the entities in the inputted list. This usually is due to a data formatting error within the input list. It is advised that you read the above notes or the "help" documents and make sure that the entity list being submitted follows the CompBio list submission guidelines.<br><br>If you are certain that the submitted list matches the recommended guidelines you may proceed with submission.</p>
    </div>
    <div id="dialog-modal-help" title="Project Submission Help" class="dialog-load-hidden" style="display:none;">
        <div id="modal-help-content"></div>
    </div>
    <div id="dialog-create-folder" title="Create Projects Folder" class="dialog-load-hidden" style="display:none;">
        <div class="input-form">
            <div class="row">
                <div id="new-folder-error" class="reanalyze-warning"></div>
                <div class="form-group col-12">
                    <label for="new-folder-name">New Folder Name:</label><br>
                    <input type="text" id="new-folder-name" class="form-control" placeholder="Enter Folder Name" required>
                </div>
                <div class="form-group col-12">
                    <label for="create-folder-projects-list">Add Projects To Folder: (Optional)</label>
                    <select id="create-folder-projects-list" class="form-control" multiple>

                    </select>
                    <p style="font-size: small;">-Select multiple projects by holding "control" for Windows or "command" for Mac when selecting</p>
                </div>
            </div>
        </div>
    </div>
    <div id="dialog-moveto-folder" title="Move Project To Folder" class="dialog-load-hidden" style="display:none;">
        <div class="input-form">
            <div class="row">
                <div class="form-group col-12">
                    <p>Select Target Folder: <span id="project-moveto-name"></span></p>
                    <label for="folders-move-select">To Folder:</label>
                    <select id="folders-move-select">

                    </select>
                </div>
                <div class="form-group col-12">
                    <label for="move-folder-projects-list">Add Projects To Folder: (Optional)</label>
                    <select id="move-folder-projects-list" class="form-control" multiple>

                    </select>
                    <p style="font-size: small;">-Select multiple projects by holding "control" for Windows or "command" for Mac when selecting</p>
                    <p style="font-size: small;">-Select "No Folder" to remove project from folder</p>
                    <p style="font-size: small;">-Projects with shaded backgrounds are currently within a folder</p>
                    <p style="font-size: small;">-Selecting a project in a folder will automatically remove it from the current folder and move to the newly selected folder</p>
                </div>
            </div>
        </div>
    </div>
    <div id="assertion-delete-confirm" title="Are You Sure You Want To Delete This Assertion?">
        <p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>These items will be permanently deleted and cannot be recovered. Are you sure?</p>
    </div>
    <div id="assertionlib-delete-confirm" title="Are You Sure You Want To Delete This Assertion Library?">
        <p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>These items will be permanently deleted and cannot be recovered. Are you sure?</p>
    </div>
    <div class="modal" tabindex="-1" role="dialog" id="batch-status-modal">
        <div class="modal-dialog modal-lg " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-dark text-bold" id="batch-modal-title">Batch Submissions Status</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="batch-modal-close-btn-2">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <!-- Batch Status Content -->
                <div class="modal-body">
                    <div class="container-fluid" id="batch-modal-status-message">

                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" id="batch-modal-close-btn-1">CLOSE</button>
                    <!-- <button type="button" class="btn btn-primary">Save changes</button> -->
                </div>
            </div>
        </div>
    </div>
    <!-- Loading Native Modal -->
    <dialog id="loading">
        <img src="<?php echo IMAGES_WEBROOT; ?>/loading.gif">
        <h4>Generating New Annotation Files...</h4>
        <p>The system is generating additional project files that are needed for new features. The process should be completed witin 1-2 minutes, depending on system congestion.</p>
        <p>On completion of the process, the additional files will be saved to the project and future visualization should open normally as before.</p>
    </dialog>
    <?php
    if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'reauthenticate.html')) {
        echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'reauthenticate.html');
    }

    if (isset($_SESSION['Sudo'])) {
        echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'sudo_popups.html');
    }
    ?>
    <script>

    </script>


    <?php
    if (isset($_SESSION['Email'])) {
        echo '<script>var currentAccountEmail = "' . $_SESSION['Email'] . '";</script>';
    } else {
        echo '<script>var currentAccountEmail = "";</script>';
    }
    if (file_exists(JS_ROOT . DIRECTORY_SEPARATOR . 'acct.min.js')) {
        echo '<script src="' . JS_WEBROOT . '/acct.min.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'acct.min.js') . '"></script>';
    } else {
        echo '<script src="' . JS_WEBROOT . '/acct.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'acct.js') . '"></script>';
        echo '<script src="' . JS_WEBROOT . '/submit.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'submit.js') . '"></script>';
    }
    if (isset($_SESSION['Sudo'])) {
        if (file_exists(JS_ROOT . DIRECTORY_SEPARATOR . 'sudo.min.js')) {
            echo '<script src="' . JS_WEBROOT . '/sudo.min.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'sudo.min.js') . '"></script>';
        } else {
            echo '<script src="' . JS_WEBROOT . '/sudo.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'sudo.js') . '"></script>';
        }
    }
    if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'explorer_form.html')) {
        echo '<script src="' . JS_WEBROOT . '/explorer-form.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'explorer-form.js') . '"></script>';
    }
    if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'assertion_tab.html')) {
        echo '<script src="' . JS_WEBROOT . '/assertion.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'assertion.js') . '"></script>';
    }
    if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'themelink_tab.html')) {
        echo '<script src="' . JS_WEBROOT . '/themelink.js?ver=' . filemtime(JS_ROOT . DIRECTORY_SEPARATOR . 'themelink.js') . '"></script>';
    }
    echo file_get_contents(ABS_ROOT . DIRECTORY_SEPARATOR . 'app' . DIRECTORY_SEPARATOR . 'templates' . DIRECTORY_SEPARATOR . 'help_tab.html');
    echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'footer.html');
    ?>