/* Remove UserSnap Btn */
div#us_report_button {
    display: none !important;
}
/* End */
/*Bootstrap Overrides*/
div.no-gutter-left {
    padding-left: 0;
}

button.btn-primary:hover {
    background-color: #82be42;
}
button.btn-primary:focus {
    /* box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5); */
}
/*.form-control:focus {
    border-color: #820606;
    box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5);
}*/
table.table {
    color: #ffffff;
    height: 99.9%;
/*    height: fit-content;
    height: -webkit-fill-available;*/
    margin-bottom: 0;
}
table.table.info-table th {
    width: 25%;
    font-size: 75%;
}
table.table th, table.table td {
    border: 1px solid #ffffff;
    text-align: left;
    padding: 0;
    padding-left: 8px;
    vertical-align: middle;
}
table#lib-results-table {
    font-size: 85%;
}
table#lib-results-table th, table#lib-results-table td {
    padding: 0.5em;
}
#lib-results-table > tbody > tr > td {
    max-width: 25vw;
    word-wrap: break-word;
    word-break: break-word;
}
#lib-results-table > tbody > tr:hover {
    color: #820606;
}
/*Overrides End*/

/*Global Scrollbars*/
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-thumb {
    background: #fafafa;
    box-shadow: inset 0 0 2px #27215f;
}
::-webkit-scrollbar-track {
    /* box-shadow: inset 0 0 5px #27215f; */
    background-color: #27215f;
    box-shadow: inset 0 0 2px #e4deef;
}
/*Scrollbars End*/

html {
    /*    background: linear-gradient(135deg, #212121, #540a0a) !important;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;*/
}

body {
/*    background: linear-gradient(135deg, #212121, #540a0a) !important;*/
    /* background: #3c0808 !important; */
    background: #e4deef;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    color: #ffffff;
    font-family: Raleway;
    cursor: pointer;
    overflow: hidden;
    height: 100vh;
}

p#auth-msg {
    color: red;
}

nav.navbar-dark {
    background: #191c21;
}

section.nav-panel {
    /*    position: fixed;
    top: 0;
    left: 0;*/
    height: 100vh;
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

nav.sidebar-sticky {
    background: #e4deef;
    height: 100%;
    width: 100%;
    padding: 15px;
    overflow-y: auto;
    color: #27215f;
    font-weight: bold;
}

p.panel-title {
    position: absolute;
    width: 100%;
    background: #27215f;
    top: 0;
    left: 0;
    padding: 10px 0;
    color: var(--light);
    text-align: center;
    font-weight: bold;
}

p.acct-name {
    /* text-align: center; */
    margin: 15px;
    margin-top: 50px;
    font-size: 1.5em;
}

section.nav-sub {
    border-top: 1px solid #fff;
    padding-top: 1em;
    padding-bottom: 1em;
}

ul.nav {
    margin-left: 15px;
}

li.nav-item,li.nav-item-exception,li.nav-link {
    font-size: 0.9em;
    padding: 10px;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}

li.nav-item:hover,li.nav-item-exception:hover,li.nav-link:hover {
    font-size: 1em;
    background-color: #82be42;
    border: 1px groove #ffffff;
    margin-right: -10px;
}

a.nav-link-anchor {
    color: inherit;
}

a.nav-link-anchor:hover {
    color: inherit;
    text-decoration: none;
}

button.indv-del-btn.bg-danger {
    color: white !important;
}

/* Quickfix Remove Assertion 1.0 Button */
button#open-assertform-btn {
    display: none !important;
}
/* Quickfix End */

/* li.active-nav-item, li.active-project, li.active-assertion, li.active-assertion-lib {
    border: 1px solid #540a0a !important;
    background-color: #0d0e11;
    margin-right: -10px;
} */

li.active-nav-item, li.active-project, li.active-assertion, li.active-assertion-lib {
    background-color: #82be42 !important;
    color: #ffffff !important;
    margin-right: -10px;

}



span.dot {
    height: 5px;
    width: 5px;
    background-color: #00ff0c;
    border-radius: 50%;
    margin-right: 5px;
    margin-top: 10px;
}
span.dot.dot-black {
    height: 5px;
    width: 5px;
    background-color: #000000;
    border-radius: 50%;
    margin-right: 5px;
    margin-top: 10px;
}

.col.cb-project-profile-data > span.pull-right.dot {
    margin-right: 15%;
}
.col.cb-project-profile-data.nested-folder-item {
    padding-right: 10px;
}
.col.cb-project-profile-data.nested-folder-item > span.pull-right.dot {
    margin-right: 0%;
}

button#log-out-btn {
    width: 100%;
    /* background: #540a0a;
    border: 3px outset #fff; */
}

input#current-email {
    color: #000;
    font-weight: bold;
}

.invalid-input {
    outline: 2px solid #f00 !important;
}


/*Mobile Small Screen Menu*/

div.account-name-mobile {
    margin: 15px 0;
    padding: 10px 0;
    border-color: #fff;
    border-top: 1px groove rgba(255,255,255,0.5);
    border-bottom: 1px groove rgba(255,255,255,0.5);
    font-size: 1.2em;
}

li.mobile-nav-section-title {
    color: #fff !important;
    border-bottom: 1px groove rgba(255,255,255,0.5);
}

li.mobile-nav-link {
    margin-left: 15px;
}

/*mobile end*/

.page-title {
    color: #27215f;
    /* font-family: 'Audiowide', cursive; */
    /* text-shadow: 0 0 15px #ffffff, 0 0 5px #d80000; */
    font-variant: small-caps;
    font-weight: bold;
}

section.tab {
    /*    margin: 15px;
    padding: 15px;*/
}
#clear-pro-search-btn {
    color: #ffffff;
    background-color: #1b80f3;
}

section.tab-content {
    background: #27215f;
    width: 100%;
    min-height: 80vh;
    padding: 15px;
    border: 1px solid #27215f;
    overflow-y: auto;
    /* box-shadow: 0 0 25px #fff, 0 0 5px #d80000; */
    margin-bottom: 25px;
}

section.assert-lib-table-page {
    max-height: 90vh;
}
div.tab-content-row {
    min-height: 35vh;
}

div.tab-content-row.short-row {
    min-height: 25vh;
    padding-top: 10px;
}

/*Notes Row Height Exception*/
div.notes-row {
    min-height: 28vh;
}
/*Exception End*/
div.btn-panel-group {}

button.btn-standard {
    background: #fff;
    border: none;
    color: #27215f;
    font-weight: bold;
    font-variant: small-caps;
    margin-top: 5px;
    margin-bottom: 5px;
}

div.box-outline-wrap {
    border: 2px solid #ffffff;
    height: 85%;
    max-height: 275px;
    overflow-x: hidden;
    overflow-y: auto;
}

div.box-outline-wrap.tall-box {
    height: 100%;
    max-height: 350px;
}

ul#projects-list, ul#filters-list, ul#explorer-list {
    margin-bottom: 5px;
}

div#announcements {
    padding: 15px;
}

tr:nth-child(even) {
    background-color: #8484848a;
}

hr.section-seperate {
    border: 1px solid #ffffff;
}

form.input-form {
    height: 100%;
}

.form-notice {
    font-size: 75%;
}

textarea.entity-textarea {
    width: 90%;
    height: 100%;
    resize: none;
    margin-bottom: 10em;
}
ul {
    padding: 0;
}
li.list-item {
    font-size: 0.9em;
    padding: 15px;
    border-bottom: 1px dotted #4a4a4a;
    list-style: none;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
    color: #27215f;
    background:white;
    font-weight:bold;
}

li.list-item:hover {
    font-size: 1em;
    /* background-color: #0d0e11; */
    background-color:#82be42;
    border: 1px groove #ffffff;
    padding: 17px;
    color:white;
}
 li.active-project {
    /* font-size: 1em; */
    background-color:#82be42;
    border: 1px groove #ffffff;
    /* padding: 17px; */
    color:white;
 }
 li.list-item.explorer-list-item {
    display: flex;
    justify-content: space-between;
 }
li.assert2-project {
    color: orange;
}

div#new-folder-error {
    color: red;
}

i.folder-icon {
    margin-right: 15px;
}

ul.folder-list {
    /*padding-left: 15px;*/
}

li.folder-list-item {
    font-size: 0.8em;
}

li.folder-list-item:hover {
    font-size: 0.9em;
}

li.folder-list-item::before {
    content: '\2BA1';
    color: #1b80f3;
    font-size: 1em;
    margin-right: 10px;
    margin-left: 15px;
}

/*Dialog Popups*/
div#list-display {
    font-family: monospace;
    line-height: 1em;
}
span.ui-dialog-title, span.ui-button-icon , button.ui-button {
    font-size: 1.3em;
}

div#modal-help-content {
    margin: 25px 15px;
}

div#modal-help-content h3 {
    text-decoration: underline;
}

/*Sudo Tabs*/

button.account-create-btn-group {
    border: 1px solid #191c21;
}

textarea#anon-edit {
    width: 100%;
    width: -webkit-fill-available;
    height: 75%;
}
/*sudo end*/
div#mobile-log-out {
    font-size: 1.5em;
    font-weight: bold;
}

div#mobile-log-out::before, div#mobile-log-out::after {
    content: '-';
}

/*Dialog End*/
/*Comparison Btns Wrap*/
div.special-btn-wrap {
    background: #1b80f3;
    padding: 5px;
    border-radius: 10px;
    text-align: center;
    transition: 0.15s;
}
div.special-btn-wrap > p {
    margin-bottom: 0;
}
/*Compare Btn End*/
/*Optimization Check*/
div#optimization-check-wrap {
/*    position: absolute;
    bottom: 0;*/
}
/*Optimization End*/

button[disabled] {
    cursor: not-allowed;
    background-color: #6c757d9c !important;
}
/*AERS Tab*/
#tab-10 legend, #tab-10 label, #tab-11 legend, #tab-11 label {
    user-select: none;
}
/* Admin Email Tool */
ul#custom_email_select {
    padding: 0px;
}
ul#custom_email_select::-webkit-scrollbar-thumb {
    /* background: darkred; */
}
#custom_email_select > li.list-group-item {
    color: #27215f;
    /* background-color: #fff; */
    border: 1px solid rgba(0,0,0,0.125);
}
/* Compbio User Manual Btn */
#cb_manual {
   /* background-color: #820606; */
   color: #ffffff;
    font-weight: bold;
    font-variant: small-caps;
    margin-top: 5px;
    margin-bottom: 5px;
}
#cb_manual:hover {
   background-color: #ffffff;
   color: #000000;
   font-weight: bold;
}

div#biological_concepts-autocomplete-list {
    overflow-y: scroll;
    max-height: 50vh;
}
form#explorer-form {
	overflow-x: hidden;
    overflow-y: auto;
    max-height: 75vh;
}
/*the container must be positioned relative:*/
.autocomplete {
	position: relative;
	display: inline-block;
}

form[name="explorer-form"] input {
	border: 1px solid transparent;
	background-color: #f1f1f1;
	padding: 10px;
	font-size: 16px;
}

form[name="explorer-form"] input[type="text"] {
	background-color: #f1f1f1;
	width: 100%;
}
.form-check-label {
	font-size: 0.75em;
}
/* form[name="explorer-form"] input[type="submit"] {
	background-color: DodgerBlue;
	color: #fff;
	cursor: pointer;
} */
form[name="explorer-form"] input.invalid, form[name="assertlib-generation-form"] input.invalid {
	border-color: #dc3545 !important;
}
.autocomplete-items {
    color: initial;
	position: absolute;
	border: 1px solid #d4d4d4;
	border-bottom: none;
	border-top: none;
	z-index: 99;
	/*position the autocomplete items to be the same width as the container:*/
	top: 100%;
	left: 0;
	right: 0;
	/*add height max and scrollable for overflow*/
	max-height: 50vh;
	overflow-y: auto;
	/* prevent horizontal scrollbar */
	overflow-x: hidden;
	/* add padding to account for vertical scrollbar */
}

.autocomplete-items div {
	padding: 10px;
	cursor: pointer;
	background-color: #fff;
	border-bottom: 1px solid #d4d4d4;
}

/*when hovering an item:*/
.autocomplete-items div:hover {
    background-color: #820606;
    color: #ffffff;
}

/*when navigating through the items using the arrow keys:*/
.autocomplete-active {
    /* background-color: DodgerBlue !important; */
    background-color: #820606 !important;
	color: #ffffff;
}

.autocomplete-items > div::first-letter {
	text-transform: capitalize;
}
.invalid-message {
    width: 100%;
    margin-bottom: .25rem;
    font-size: 80%;
    color: #dc3545;
}
div.sub-build-btns {
	width: 100%;
}
div.sub-build-btns > button {
	margin-top: 0;
}
#submission-build-list {
	padding-left: 20px;
}
li.submission-list-item {
	font-size: 0.7rem;
}
li.submission-list-item-active {
    background-color: #3d4551;
    padding: 2px;
}
/*span.term-wrap:hover {
    background-color: #3d4551;
    padding: 2px;
}*/
/*2nd item is for the assertion library form*/
span.remove-term-btn, span.libform-remove-assertion {
	margin-left: 10px;
	font-size: 125%;
	color: red;
}
/*span.remove-term-btn:hover, span.libform-remove-assertion:hover {
	font-size: 125%;
	margin-left: 10px;
}*/
div#lib-list-parent {
	max-height: 100%;
}
ol#lib-assertion-list {
	max-height: 100%;
	overflow: auto;
	list-style-type: inherit;
	columns: 2;
	-webkit-columns: 2;
	-moz-columns: 2;
}
span#lib-count-warn {
	color: red;
	font-size: 75%;
}
div#assert-date-colhead {
    text-align: center;
    padding-right: 7%;
}
/* Loading Native Modal */
dialog::backdrop {
    background: rgba(0,0,0,0.5);
}
dialog#loading {
    max-width: 50vw;
    border-color: #27215f;
    border-radius: 5px;
}
#lib-assertion-select-wrapper {
    display: block;
    height: 20vh;
    resize: vertical;
    overflow: auto;
}
#lib-assertion-select {
    color: #27215f;
    resize:vertical;
}
#lib-assertion-select > tr {
    background-color: white;
    user-select: none;
    -moz-user-select: none;
    -webkit-text-select: none;
    -webkit-user-select: none;
}
#lib-assertion-select > tr:last-child {
    text-align: right !important;
}
tr.lib-assert-select-row:hover {
    background-color: #82be42 !important;
    color: white;
}
tr.lib-assert-select-row-selected {
    background-color: #82be42 !important;
    color: white;
}
.foreign-shared {
    background-color: #f67885 !important;
}

@media screen and (min-width: 992px) {

}

@media screen and (max-width: 991px) {
    body {
        overflow-y: auto;
    }
    section.tab-content {
        height: 100%;
        width: 100%;
        overflow: unset;
    }
}

@media screen and (max-width: 769px) {
    textarea.entity-textarea {
        width: 100%;
    }
}

@media screen and (max-width: 575.98px) {
    body {
        background-position: center top;
    }
    div.form-dark {
        padding: 10px;
    }
    div.box-outline-wrap {
        overflow-x: auto;
    }
    iframe {
        position: absolute;
        left: 0 !important;
    }
    div#help-tab {
        position: absolute;
        /*right: 0 !important;*/
    }
    div.main-content-col {
        padding-left: 1em !important;
        padding-right: 1em !important;
    }
}
