body {
    /* background: #3c0808 !important; */
    /* background: #fafafa; */
    background: rgb(39,33,95);
    background: -moz-linear-gradient(30deg, rgba(39,33,95,1) 0%, rgba(250,250,250,1) 30%, rgba(250,250,250,1) 70%, rgba(39,33,95,1) 100%);
    background: -webkit-linear-gradient(30deg, rgba(39,33,95,1) 0%, rgba(250,250,250,1) 30%, rgba(250,250,250,1) 70%, rgba(39,33,95,1) 100%);
    background: linear-gradient(30deg, rgba(39,33,95,1) 0%, rgba(250,250,250,1) 30%, rgba(250,250,250,1) 70%, rgba(39,33,95,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#27215f",endColorstr="#27215f",GradientType=1);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    height: 100vh;
    color: #ffffff;
    font-family: Raleway;
}

.background-model-wrapper {
    position: absolute;
}

.page-title {
    color: #27215f;
    /* font-family: 'Audiowide', cursive; */
    /* text-shadow: 0 0 15px #ffffff, 0 0 5px #d80000; */
    font-variant: small-caps;
    font-weight: bold;
}

div.form-dark {
    background: #27215f;
    padding: 25px 50px;
    border: 1px solid #ffffff;
    /* box-shadow: 0 0 25px #fff, 0 0 5px #d80000; */
}

.login-header-img {
    max-height: 100px;
    max-width: 100%;
}

button.btn-standard {
    background: #fff;
    border: none;
    color: #000;
    font-weight: bold;
    font-variant: small-caps;
    margin-top: 5px;
    margin-bottom: 5px;
}

button.btn-primary:hover {
    /* background-color: #820606; */
}
button.btn-primary:focus {
    /* box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5); */
}
.form-control:focus {
    /* border-color: #820606; */
    /* box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5); */
}

button#form-submit, button#new-account-submit {
    font-family: 'Exo 2',sans-serif;
    font-variant-caps: petite-caps;
    font-weight: bold;
}
.alt-links {
    color: #ffffff !important;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.alt-links:hover {
    color: #82BE42 !important;
    text-decoration: none;
}
.resource-div {
    /* background:#68c2f3; */
    /* background: rgb(250,250,250);
    background: radial-gradient(circle, rgba(250,250,250,0) 0%, rgba(27,128,243,1) 50%); */
    background: rgb(27,128,243);
    background: linear-gradient(30deg, rgba(27,128,243,1) 0%, rgba(250,250,250,1) 95%);
    border:1px solid #27215f;
}
h4.resource-title {
    font-size:2em;
}
h6.resource-title {
    font-size:1.5em;
}
.resource-title > a {
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
    /* -webkit-text-stroke: 1px #27215f; */
    text-decoration: underline;
}
.resource-title > a:hover {
    font-size: 105%;
}
#alert-message {
    color: red;
    text-align: center;
}
.alert-hidden {
    visibility: hidden;
}

.invalid-input {
    outline: 1px solid #f00;
}

hr.section-seperate {
    border: 1px solid #ffffff;
}

/*Dialog Popups*/
span.ui-dialog-title, span.ui-button-icon , button.ui-button {
    font-size: 1.3em;
}
/*dialog end*/

@media screen and (max-width: 575.98px) {
    body {
        background-position: center top;
    }
    div.form-dark { 
        padding: 10px;
    }
    iframe {
        position: absolute;
        left: 0 !important;
    }
    div#help-tab {
            position: absolute;
            right: 0 !important;
    }
}

