body {
	text-align: center;
	background-color: #333333;
}
#content-all-wrap {
	height: 100%;
}
#page-header {
	margin: 0;
	padding: 25px;
}
#results {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
}
.img-container {
	width: 300px;
    height: 300px;
    margin: 10px 5px;
    border: 1px solid black;
}
#results-wrap img {
	object-fit: fill;
    width: 300px;
    height: 300px;
}
/*Modal Content Focus Info Item*/
#outer-item-modal {
	display: none;
}
#item-modal {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 98%;
	height: 85%;
	background-color: grey;
	display: inline-flex;
	flex-flow: row wrap;
	justify-content: space-between;
    padding: 25px;
}
#panel-close {
	position: absolute;
	top: 25px;
	left: 95%;
}
#selectedImg-wrapper {
	width: 30%;
	margin: auto;
}
#selectedImg-wrapper img {
	width: 100%;
	height: 100%;
	object-fit: contain;
}
#info-wrapper {
	text-align: left;
	word-wrap: break-word;
	overflow-y: auto;
	width: 55%;
}
#info-wrapper p {
	margin-right: 5px;
	padding-right: 5px;
}
#info-wrapper p span {
	font-weight: bold;
	font-size: 1.2em;
}
#info-wrapper p b {
	color: darkred;
}
.link-btn {
    background-color: #333333;
    color: white;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
}
.link-btn a:link, .link-btn a:visited {
	text-decoration: none;
	color: white;
	cursor: pointer;
}
