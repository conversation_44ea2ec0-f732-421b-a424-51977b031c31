'use strict';
console.log('JS loaded');
if(sessionStorage.hasOwnProperty('TabData') == false) {
    sessionStorage.setItem('TabData', window.opener.localStorage.getItem('abstractData'));
    window.opener.localStorage.removeItem('abstractData');    
}
let abstractData = JSON.parse(sessionStorage.getItem('TabData'));
log(abstractData);
let key = abstractData.Key;
let a = abstractData.a;
let b = abstractData.b;
let queryCall;
if(abstractData.hasOwnProperty('c')) {
    let c = abstractData.c;
    let colorKeys = [].slice.call(document.getElementsByTagName('ul'))[0].children;
    colorKeys[0].style.display = 'none';
    colorKeys[1].innerHTML = 'Selected Concepts: <span style="color:#0000ff;">Blue</span>';
    queryCall = a + ',' + b + ',' + c;
} else {
    queryCall = a + ',' + b;
}
let data3;
let outputHtml = document.getElementById('content');
let len;
let themeConcepts = abstractData.ThemeConcepts;
let allConcepts = [];
window.opener.data.forEach(function(item) {
    let itemArr = Object.keys(item.Concepts);
    allConcepts = allConcepts.concat(itemArr);
    // itemArr.forEach(function(concept) {
    //     allConcepts.push(concept);
    // });
});
let terms = {
    'a': abstractData.a,
    'b': abstractData.b,
    'all': allConcepts
};
if(abstractData.hasOwnProperty('ThemeConcepts') == true) {
    terms.theme = abstractData.ThemeConcepts;
}

function getQuery() {
    console.log(queryCall);

    let data = {'function':'abstractQueryPHP','parameters': {'Key':key, 'Query':queryCall, 'Terms':terms}};
    data = JSON.stringify(data);
    httpRequest(data);
}

function httpRequest(data) {
    let header = 'PostRequest=';
    let request = header + encodeURIComponent(data);
    let url = '../assets/php/cbv_functions.php';
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            console.log('success');
            data3 = xhr.responseText;
            if(data3 == 'Error'){
                console.log(data3);
            } else if(data3 == "No Matches Found...") {
                noAbstractData(data3);
            } else {
                console.log(data3);
                data3 = JSON.parse(data3);
                data3Prep(data3);
            }
        }
    }
    xhr.send(request);
}

function data3Prep(data3) {
    let gene = data3.GeneTerms;
    let concept = data3.ConceptTerms;
    let allConcepts = data3.AllConcepts;
    let themeConcepts = data3.ThemeTerms;
    let data = data3.Publications;
    data.forEach(function(item) {
        if(abstractData.hasOwnProperty('c')) {
            for(let i=0;i<gene.length;i++) {        
                item.Title = item.Title.split(' ' + gene[i] + ' ').join(' <span style="color:#0000ff;">' + gene[i] + '</span> ');
                item.Abstract = item.Abstract.split(' ' + gene[i] + ' ').join(' <span style="color:#0000ff;">'+ gene[i] + '</span> ');
            }
        } else {
            for(let i=0;i<gene.length;i++) {        
                item.Title = item.Title.split(' ' + gene[i] + ' ').join(' <span style="color:#ff0000;">' + gene[i] + '</span> ');
                item.Abstract = item.Abstract.split(' ' + gene[i] + ' ').join(' <span style="color:#ff0000;">'+ gene[i] + '</span> ');
            }            
        }
        for(let i=0;i<concept.length;i++) {
            item.Title = item.Title.split(' ' + concept[i] + ' ').join(' <span style="color:#0000ff;">' + concept[i] + '</span> ');
            item.Abstract = item.Abstract.split(' ' + concept[i] + ' ').join(' <span style="color:#0000ff;">'+ concept[i] + '</span> ');    
        }
        for(let i=0;i<themeConcepts.length;i++) {
            item.Title = item.Title.split(' ' + themeConcepts[i] + ' ').join(' <span style="color:#cc00cc;">'+ themeConcepts[i] + '</span> ');
            item.Abstract = item.Abstract.split(' ' + themeConcepts[i] + ' ').join(' <span style="color:#cc00cc;">'+ themeConcepts[i] + '</span> ');             
        }
        for(let i=0;i<allConcepts.length;i++) {
            item.Title = item.Title.split(' ' + allConcepts[i] + ' ').join(' <span style="color:#00aa00;">'+ allConcepts[i] + '</span> ');
            item.Abstract = item.Abstract.split(' ' + allConcepts[i] + ' ').join(' <span style="color:#00aa00;">'+ allConcepts[i] + '</span> ');       
        }

        let output = '';
        output += '<h2>'+ item.Title +'</h2>';
        output += '<p>' + item.Abstract +'</p>';
        output += '<div class="links"><a href="https://www.ncbi.nlm.nih.gov/pubmed/?term='+ item.PMID +'" target="_blank">See Original Document</a></div>'; 
        document.getElementById('content').innerHTML += output;
    });
}

function noAbstractData(msg) {
    console.log(msg);
    document.getElementById('content').innerHTML = '<h2>Sorry...no matches have been found.</h2>';
}