"use strict";
/*DebugVar*/
var debug = [];
/*End*/
var tableData = {};
// var labname = sessionStorage.getItem('Laboratory');
var activeExpName = '';
var activeExpId = '';
var activeExpFolderName = '';
var activeExpFolderId = '';
var activeFilter = '';
var activeFilterId = '';
var activeExplorerProject = '';
var activeExplorerProjectId = '';
var activeAersProject = '';
var flag = '';
var msg = '';
var requestObjs = [];
var tabArray = [].slice.call(document.getElementsByClassName('nav-item'));
var newProjectWindow,prevListUpdate;
document.getElementById('contact').value = currentAccountEmail;
tabArray.forEach(function(element) {
    element.addEventListener('click', function() {
        tabToggle(event.target, event.target.getAttribute('data-tab-request'));
    });
});

var tabContentArray = [].slice.call(document.getElementsByClassName('tabbed-content'));

function tabToggle(target, tabNum) {
    // log(tabNum);
    for (let i = 0; i < tabArray.length; i++) {
        tabArray[i].classList.remove('active-nav-item');
    }
    let navItemsArray = [].slice.call(document.querySelectorAll('[data-tab-request="' + target.getAttribute('data-tab-request') + '"]'));
    navItemsArray.forEach(function(element) {
        element.classList.add('active-nav-item');
    });
    tabContentArray.forEach(function(element){
        element.style.display = 'none';
    });
    document.getElementById('tab-' + tabNum.toString()).style.display = 'block';
    updateLists();

    /*Hide FilterInfo*/
    document.getElementById('filter-info').classList.add('d-none');
    /*hide end*/
    /*clear browser autofill*/

    let eleArray = ['maintenance-contact', 'password', 'pass-new'];
    eleArray.forEach(function(item) {
        document.getElementById(item).value = '';
    });
    /*clear end*/
}

let sharedActive = false;
let sharedProjectsBtn = document.getElementById('cb-shared-projects');
sharedProjectsBtn.addEventListener('click', function() {
    if(sharedActive === false) {
        sharedActive = true;
        updateLists();
    } else {
        sharedActive = false;
        updateLists();
    }
    sharedProjectsBtn.classList.toggle('active');
});

let btnWraps = [].slice.call(document.getElementsByClassName('special-btn-wrap'));
// btnWraps.forEach(function(wrap) {
//     let wrapChild = wrap.children[1];
//     wrapChild.addEventListener('mouseover', function(e) {
//         wrap.style.background = '#191c21';
//     });
//     wrapChild.addEventListener('mouseout', function(e) {
//         wrap.style.background = '#820606';
//     });
// });

$(function() {
    $("#alert").dialog({
        autoOpen: false,
        modal: true,
        width: 400,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            "Ok": {
                text: "Ok",
                id: "alert-ok-btn",
                click: function() {
                    event.preventDefault();
                    $(this).dialog('close');
                    document.getElementById('alert-msg').innerHTML = '';
                }
            }
        }
    });
});
$(function() {
    $("#dialog-list").dialog({
        autoOpen: false,
        modal: true,
        width: 400,
        height: window.innerHeight-100,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            Close: function() {
                $(this).dialog('close');
                document.getElementById('list-display').innerHTML = '';
            }
        }
    });
});
$(function() {
    $("#dialog-alert").dialog({
        autoOpen: false,
        modal: true,
        width: 400,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            Close: function() {
                if(tabArray[4].classList.contains('active-tab-btn')) {
                    let form = [].slice.call(document.getElementById('new-form-wrap').querySelectorAll('input'));
                    for(let i=0;i<form.length;i++) {
                        if(form[i].type == 'checkbox') {
                            form[i].checked = false;
                        } else {
                            form[i].value = '';
                        }
                    }
                }
                $(this).dialog('close');
                document.getElementById('alert-confirm').innerHTML = '';
            }
        }
    });
});
$( function() {
    $( "#dialog-confirm" ).dialog({
      autoOpen: false,
      resizable: false,
      height: "auto",
      width: 600,
      modal: true,
      buttons: {
        "Delete Project": function() {
            if (activeExpName != '') {
                let data = {'function':'deleteProject','parameters': {
                    'AcctId': id,
                    'Account':labname,
                    'Experiment':activeExpName,
                    'ExpId': activeExpId,
                    'Folder':activeExpFolderName,
                    'FolderId': activeExpFolderId
                }};
                data = JSON.stringify(data);
                // log(data);
                httpRequest(data);
            }
          $( this ).dialog( "close" );
        },
        Cancel: function() {
          $( this ).dialog( "close" );
        }
      }
    });
});
$( function() {
    $( "#dialog-syn-alert" ).dialog({
      autoOpen: false,
      resizable: false,
      height: "auto",
      width: 600,
      modal: true,
      buttons: {
        "Continue Submit": function() {
                projectInfo.Num_Entities = projectInfo.Entity_List.length;
                projectInfo.Entity_List = projectInfo.Entity_List.join('\n');
                // log(projectInfo);
                log('Submitting Test!');
                // submitProjectInfo('NewProject=' + JSON.stringify(projectInfo));
                // runPyScript('NewProject=' + JSON.stringify(projectInfo));
                $('#dialog-message').dialog('open');
                let inputs = document.getElementById('info-wrap').querySelectorAll('input');
                inputs[0].value = '';
                inputs[1].value = '';
                inputs[2].value = '';
                document.getElementById('concept').value = 500;
                document.getElementById('copypaste').value = '';
                document.getElementById('exp-list').value = '';
                document.getElementById('uploadfiles').value = '';
                projectInfo = {};
                updateLists();
          $( this ).dialog( "close" );
        },
        Cancel: function() {
            synonymValidation = false;
          $( this ).dialog( "close" );
          document.getElementById('pname').focus();
        }
      }
    });
});
$( function() {
    $( "#request-delete-confirm" ).dialog({
      autoOpen: false,
      resizable: false,
      height: "auto",
      width: 600,
      modal: true,
      buttons: {
        "Delete Request Entry": function() {
            let data = {
                'function':'deleteRequestItem',
                'parameters':{
                    'RequestName':document.getElementById('pi').innerHTML.trim(),
                    'RequestDate':document.getElementById('req-date').innerHTML.trim(),
                    'RequestOrg': document.getElementById('org').innerHTML.trim(),
                    'RequestEmail': document.getElementById('req-email').innerHTML.trim()
                }
            };
            data = JSON.stringify(data);
            // log(data);
            httpRequest(data);
            $( this ).dialog( "close" );
        },
        Cancel: function() {
            $( this ).dialog( "close" );
        }
      }
    });
});
$(function() {
    $("#dialog-message").dialog({
        autoOpen: false,
        modal: true,
        width: 600,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            Ok: function() {
                $(this).dialog('close');
            }
        }
    });
});
$( function() {
    $( "#dialog-reanalyze" ).dialog({
      autoOpen: false,
      resizable: false,
      height: "auto",
      width: 600,
      modal: true,
      buttons: {
        "Submit": function() {
            if(document.getElementById('reanalyze-name').value.trim() == '' ) {
                document.getElementById('reanalyze-name').style.border = '1px solid red';
            } else {

                if(!(emailValidation(document.getElementById('reanalyze-email').value.trim()))) {
                    document.getElementById('reanalyze-email').style.border = '1px solid red';
                } else {

                    log('Reanalyze Checks Comlete');
                    projectInfo.Project_Name = document.getElementById('reanalyze-name').value.trim().replace(/ /g,'_');
                    projectInfo.Contact_Email = document.getElementById('reanalyze-email').value.trim();
                    projectInfo.Num_Concepts = Math.trunc(document.getElementById('reanalyze-concept').value);
                    // Context Remove
                    // let context = document.getElementById('reanalyze-context').value.trim();
                    // if(context == ''){
                    //     context = [];
                    // } else {
                    //     context = context.split(',');
                    // }
                    // projectInfo.Num_Context = context.length;
                    // projectInfo.Context = context.toString();
                    submitProjectInfo('NewProject=' + JSON.stringify(projectInfo));
                    /* Flagged for removal in future versions */
                    /* Switched to rpc call now from run_py */
                    // runPyScript('NewProject=' + JSON.stringify(projectInfo));
                    submitCompBioProject(`CompBioProject=${JSON.stringify(projectInfo)}`);
                    projectInfo = {};
                    $( this ).dialog( "close" );

                    let reanalyzeInputs = [].slice.call(document.getElementById('dialog-reanalyze').querySelectorAll('input'));
                    reanalyzeInputs.forEach(function(item) {
                        item.value = '';
                    });

                    updateLists();
                }
            }
        },
        Cancel: function() {
            projectInfo = {};
            let reanalyzeInputs = [].slice.call(document.getElementById('dialog-reanalyze').querySelectorAll('input'));
            reanalyzeInputs.forEach(function(item) {
                item.value = '';
            });
            $( this ).dialog( "close" );
        }
      }
    });
});
$(function() {
    $("#sudo-tools").dialog({
        autoOpen: false,
        modal: true,
        width: 600,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            Ok: function() {
                $(this).dialog('close');

            }
        }
    });
});
$(function() {
    $("#sudo-secondary-copy").dialog({
        autoOpen: false,
        modal: true,
        width: 600,
        title: 'Copy Project To Account',
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            Copy: function() {
                // log('from: ' + sudoSelectedAcct + ' project: ' + sudoSelectedExp + ' To-Account: ' + document.getElementById('cp-target-account').value);

                let data = { 'function': 'sudoCpProject', 'parameters': { 'SrcAccount':sudoSelectedAcct, 'SrcProject':sudoSelectedExp, 'DestAccount':document.getElementById('cp-target-account').value}};
                data = JSON.stringify(data);
                // log(data);
                httpRequest(data);
                $(this).dialog('close');
            }
        }
    });
});
var logCount = 0;
$(function() {
    $("#dialog-session-confirm").dialog({
        dialogClass: "no-close",
        draggable: false,
        autoOpen: false,
        modal: true,
        width: 400,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            "Login": {
                id: "session-login-btn",
                text: "Login",
                click: function() {
                    let acctCredentials = {
                        account: document.getElementById('inactive-acct-name').value.trim(),
                        password: document.getElementById('inactive-password').value.trim(),
                        session: labname
                    };
                    if(acctCredentials.account!='' && acctCredentials.password!='') {
                        if(logCount<4) {
                            if(acctCredentials.account == acctCredentials.session.trim()) {
                                let data = { 'function': 'reauthSession', 'parameters': {
                                        'Laboratory':acctCredentials.account,
                                        'Password':acctCredentials.password
                                    }
                                };
                                data = JSON.stringify(data);
                                // log(data);
                                httpRequest(data);
                            } else {
                                logCount++;
                                document.getElementById('auth-msg').style.visibility = 'visible';
                            }
                        } else {
                            let data = {'function':'logout'};
                            data = JSON.stringify(data);
                            // log(data);
                            httpRequest(data);
                        }
                    }
                }
            },
            "Logout": function() {
                let data = {'function':'logout'};
                data = JSON.stringify(data);
                // log(data);
                httpRequest(data);
            }
        }
    });
});
$(function() {
    $("#dialog-modal-help").dialog({
        draggable: false,
        autoOpen: false,
        height: window.innerHeight/2,
        width: window.innerWidth - (window.innerWidth/3),
        modal: true,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        }
    });
});
$(function() {
    $("#dialog-create-folder").dialog({
        draggable: false,
        autoOpen: false,
        modal: true,
        minWidth: 600,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            "Create": function() {
                if ($('#new-folder-name').val() != '' && $('#new-folder-name').val() != 'root') {
                    if ($('#create-folder-projects-list').val().length > 0) {
                        let newProjectFolder = {
                            folderName: $('#new-folder-name').val().replace(/ /g, '_').replace(/\./g, '_'),
                            addProjects: $('#create-folder-projects-list').val()
                        }
                        log('creating folder');
                        let data = {'function':'createProjectFolder', 'parameters':{
                            'AcctId': id, 
                            'Account':labname, 
                            'NewFolderData':newProjectFolder
                        }};
                        data = JSON.stringify(data);
                        httpRequest(data);
                        $('#dialog-create-folder').dialog('close');
                        $('#new-folder-name').val('');
                        $('#create-folder-projects-list').val([]);
                        document.getElementById('new-folder-error').innerText = '';
                    } else {
                        document.getElementById('new-folder-error').innerText = 'Please select at least one project to add to the folder';
                    }
                } else {
                    document.getElementById('new-folder-error').innerText = 'Invalid or protected folder name';
                }
            },
            "Cancel": function() {
                $('#dialog-create-folder').dialog('close');
                $('#new-folder-name').val('');
                $('#create-folder-projects-list').val([]);
            }
        }
    });
});
$(function() {
    $("#dialog-moveto-folder").dialog({
        draggable: false,
        autoOpen: false,
        modal: true,
        minWidth: 600,
        show: {
            effect: "scale"
        },
        hide: {
            effect: "scale"
        },
        buttons: {
            "Move": function() {
                let data = {'function':'moveProjectToFolder', 'parameters':{
                    'AcctId': id,
                    'Account': labname,
                    'FolderId': $('#folders-move-select').val(),
                    'Projects': $('#move-folder-projects-list').val()
                }};
                data = JSON.stringify(data);
                log(data);
                httpRequest(data);
                $('#dialog-moveto-folder').dialog('close');
                document.getElementById('folders-move-select').innerHTML = '';
                document.getElementById('move-folder-projects-list').innerHTML = '';
            },
            "Cancel": function() {
                $('#dialog-moveto-folder').dialog('close');
                document.getElementById('folders-move-select').innerHTML = '';
                document.getElementById('move-folder-projects-list').innerHTML = '';
            }
        }
    });
});
/* Batch Status Button Modal Event Listener */
$(function() {
    $("#batch-status-btn").click(function(){
        updateLists();
        $("#batch-status-modal").modal("show");
    });
});
// var inactivityTime = (function () {
//     var t;
//     window.addEventListener('beforeunload', function() {
//         if($('#dialog-session-confirm').hasClass('ui-dialog-content')) {
//             let isOpen = $('#dialog-session-confirm').dialog('isOpen');
//             if(isOpen==true) {
//                 let data = {'function':'logout'};
//                 data = JSON.stringify(data);
//                 // log(data);
//                 httpRequest(data);
//             }
//         } else {
//             log('session-confirm has not initialized yet -> 386');
//         }
//     });
//     window.onload = resetTimer;
//     document.onmousemove = resetTimer;
//     document.onkeypress = resetTimer;
//     document.onfocus = resetTimer;
//     document.onmousewheel = resetTimer;
//     document.onwheel = resetTimer;

//     function sessionPrompt() {
//         log('sessionPrompt');
//         if($('#dialog-session-confirm').hasClass('ui-dialog-content')) {
//             let isOpen = $('#dialog-session-confirm').dialog('isOpen');
//             if(isOpen!=true) {
//                 log('dialog closed')
//                 let data = {'function':'checkSessionStatus'};
//                 data = JSON.stringify(data);
//                 // log(data);
//                 httpRequest(data);
//                 resetTimer();
//             }
//         } else {
//             log('session-confirm has not initialized yet -> 409');
//         }
//     }
//     function resetTimer() {
//         clearTimeout(t);
//         t = setTimeout(sessionPrompt, 1000 * 60 * 15);
//     }
// })();
// var refocusSessionCheck = (function () {
//     window.addEventListener('focus', function() {
//         log('Refocus')
//         let data = {'function':'checkSessionStatus'};
//         data = JSON.stringify(data);
//         // log(data);
//         httpRequest(data);
//     });
// })();
document.getElementById('input-help').addEventListener('click', function() {
    window.open(Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=5');
});
window.addEventListener('load', function() {
    // console.log('setting Auth event');
    // document.getElementById('inactive-acct-name').addEventListener('keyup', (event) => {
    //     console.log('auth 1');
    //     if($("#dialog-session-confirm").dialog("isOpen")) {
    //         console.log('auth 2');
    //         if(event.keyCode === 13) {
    //             console.log('auth 3');
    //             event.preventDefault();
    //             document.getElementById('session-login-btn').click();
    //         }
    //     }
    // });
    // document.getElementById('inactive-password').addEventListener('keyup', (event) => {
    //     console.log('auth 1');
    //     if($("#dialog-session-confirm").dialog("isOpen")) {
    //         console.log('auth 2');
    //         if(event.keyCode === 13) {
    //             console.log('auth 3');
    //             event.preventDefault();
    //             document.getElementById('session-login-btn').click();
    //         }
    //     }
    // });
    tabContentArray.forEach( function(element) {
        element.style.display = 'none';
    });

    document.getElementById('help-tab').style.height = '34px';

    let dialogArray = [].slice.call(document.getElementsByClassName('dialog-load-hidden'));
    dialogArray.forEach(function(item) {
        item.style.display = 'block';
    });

    document.getElementById('tab-1').style.display = 'block';
    // document.getElementById('project-info').style.display = 'none';

    // getAnnouncements();
    updateLists();
});
// var liArray = document.getElementById('projects-list').children;
var liArray = document.getElementsByClassName('project-list-item');
document.getElementById('visualize-project-btn').addEventListener('click', function() {
    if (activeExpName != '') {
        // newProjectWindow = window.open();
        // newProjectWindow.opener.focus();
        let data = {'function':'setSessionExp', 'parameters':{'Account':labname, 'Experiment':activeExpName, 'ExpId': activeExpId, 'Id': id}};
        data = JSON.stringify(data);
        log(data);
        loadingModal.showModal();
        httpRequest(data);
    }
});

var liExplorerArray = document.getElementsByClassName('explorer-list-item');
document.getElementById('visualize-explorer-btn').addEventListener('click', function() {
    if (activeExplorerProject != '') {
        // newProjectWindow = window.open();
        // newProjectWindow.opener.focus();
        let data = { 'function': 'setSessionExp', 'parameters': { 'Account': labname, 'Experiment': activeExplorerProject, 'ExpId': activeExplorerProjectId, 'Id': id }};
        data = JSON.stringify(data);
        log(data);
        loadingModal.showModal();
        httpRequest(data);
    }
});

// var liAersArray = document.getElementsByClassName('aers-list-item');
// document.getElementById('visualize-aers-btn').addEventListener('click', function() {
//     if (activeAersProject != '') {
//         newProjectWindow = window.open();
//         newProjectWindow.opener.focus();
//         let data = {'function':'setSessionExp', 'parameters':{'Account':labname, 'Experiment':activeAersProject}};
//         data = JSON.stringify(data);
//         log(data);
//         httpRequest(data);
//     }
// });

var filterArray = document.getElementsByClassName('filter-list-item');


document.getElementById('display-input-btn').addEventListener('click', function() {
    if (activeExpName != '') {
        let data = {'function':'returnInputList','parameters': {'Account':labname,'Experiment':activeExpName, 'ExpId': activeExpId}};
        data = JSON.stringify(data);
        // log(data);
        httpRequest(data);
    }
});
document.getElementById('delete-project-btn').addEventListener('click', function() {
    $('#dialog-confirm').dialog('open');
});
document.getElementById('announcements-btn').addEventListener('click', function() {
    log('announcements button clicked');
    log('Deprecated: announcements needs to be moved to new location/call');
    // getAnnouncements();
    // document.getElementById('project-info').style.display = 'none';
    // document.getElementById('announcements-wrapper').style.display = 'block';
    // document.getElementById('secondary-heading').innerText = 'Announcements';
    for (let i = 0; i < liArray.length; i++) {
        liArray[i].classList.remove('active-project');
    }
    activeExpName = '';
    activeExpId = '';
    activeExpFolderName = '';
    activeExpFolderId = '';
});
document.getElementById('log-out-btn').addEventListener('click', function() {
    let data = {'function':'logout'};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
    log('logout');
});
document.getElementById('mobile-log-out').addEventListener('click', function() {
    let data = {'function':'logout'};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
    log('logout');
});
document.getElementById('email-new').addEventListener('click', function() {
    let newInput = document.getElementById('maintenance-contact').value.trim().toLowerCase();
    if (emailValidation(newInput)) {
        document.getElementById('maintenance-contact').value = '';
        document.getElementById('maintenance-contact').classList.remove('invalid-input');
        document.getElementById('email-error').innerHTML = '';
        let data = {'function':'emailChange','parameters': {'Account':labname, 'PrevEmail': document.getElementById('current-email').value, 'Email':newInput}};
        data = JSON.stringify(data);
        // log(data);
        httpRequest(data);
    } else {
        document.getElementById('maintenance-contact').classList.add('invalid-input');
        if(text!=''){
            document.getElementById('email-error').innerHTML = '*Email format error';
        } else {
            document.getElementById('email-error').innerHTML = '*Must enter email';
        }
    }
});
document.getElementById('pass-new').addEventListener('click', function() {
    let newInput = document.getElementById('password').value.trim();
    let newInput2 = document.getElementById('password-confirm').value.trim();
    document.getElementById('password').value = '';
    document.getElementById('password-confirm').value = '';
    if (passMatch(newInput, newInput2)) {
        let data = {'function':'passwordChange','parameters': {'Account':labname,'Password':newInput}};
        data = JSON.stringify(data);
        httpRequest(data);
    }
});
document.getElementById('reanalyze-btn').addEventListener('click', function() {
    if( activeExpName != '') {
        let data = {'function':'returnInputList',
                        'parameters': {
                            'Reanalyze':true,
                            'Account':labname,
                            'Experiment':$('#projects-list li.active-project div.cb-project-names').text(),
                            'ExpId': activeExpId,
                            'Project_Details': {
                                'Creation_Date':$('#projects-list li.active-project div.cb-project-timestamps').text(),
                                'Num_Entities':$('#projects-list li.active-project div.cb-project-num-ents').text()
                            }
                        }
                    };
        data = JSON.stringify(data);
        httpRequest(data);
    }
});
document.getElementById('create-folder-btn').addEventListener('click', function() {
    document.getElementById('create-folder-projects-list').innerHTML = '';
    let proList = [].slice.call(document.getElementsByClassName('project-list-item'));
    proList.forEach(function(item) {
        if(!(item.classList.contains('folder-list-item'))) {
            const onClickAttr = item.getAttribute('onclick');
            const match = onClickAttr.match(/selectExperiment\('.*?','(.*?)'\)/);
            if (match) {
                console.log("Second Parameter:", match[1]); // Output: 01jq7hpjm9qn6fa0kjrpcjpv6s
                document.getElementById('create-folder-projects-list').innerHTML += '<option value="' + match[1] + '">' + item.children[0].innerText + '</option>';
            } else {
                console.log("No match found");
            }
        }
    });
    $('#dialog-create-folder').dialog('open');
});
document.getElementById('create-filter-btn').addEventListener('click', function() {

    document.getElementById('create-filter-label').classList.remove('d-none');
    document.getElementById('init-create-filter').classList.remove('d-none');
    document.getElementById('filter-project-label').classList.add('d-none');
    document.getElementById('init-create-filter-project').classList.add('d-none');

    document.getElementById('filter-form-title').innerText = 'Create An Overlap Filter';
    document.getElementById('filter-name-label').innerText = 'Filter Name: ';

    document.getElementById('create-filter-form').classList.remove('d-none');
    document.getElementById('base-project-select').innerHTML = '';
    document.getElementById('project2-select').innerHTML = '';

    // let proList = [].slice.call(document.getElementsByClassName('project-list-item')).concat([].slice.call(document.getElementsByClassName('explorer-list-item')));
    let proList = [].slice.call(document.getElementsByClassName('project-list-item'));
    proList.forEach(function(item) {
        document.getElementById('base-project-select').innerHTML += '<option value="' + item.children[0].innerText + '">' + item.children[0].innerText + '</option>';
        document.getElementById('project2-select').innerHTML += '<option value="' + item.children[0].innerText + '">' + item.children[0].innerText + '</option>';
    });
    proList = [].slice.call(document.getElementsByClassName('explorer-list-item'));
    proList.forEach(function(item) {
        document.getElementById('base-project-select').innerHTML += '<option value="' + item.innerText + '">' + item.innerText + '</option>';
        document.getElementById('project2-select').innerHTML += '<option value="' + item.innerText + '">' + item.innerText + '</option>';
    });
});
document.getElementById('create-filter-project-btn').addEventListener('click', function() {

    document.getElementById('create-filter-label').classList.add('d-none');
    document.getElementById('init-create-filter').classList.add('d-none');
    document.getElementById('filter-project-label').classList.remove('d-none');
    document.getElementById('init-create-filter-project').classList.remove('d-none');

    document.getElementById('filter-form-title').innerText = 'Create An Overlap Project';
    document.getElementById('filter-name-label').innerText = 'Project Name: ';

    document.getElementById('create-filter-form').classList.remove('d-none');
    document.getElementById('base-project-select').innerHTML = '';
    document.getElementById('project2-select').innerHTML = '';

    let proList = [].slice.call(document.getElementsByClassName('project-list-item'));
    proList.forEach(function(item) {
        // Get the onclick attribute value
        const onClickAttr = item.getAttribute('onclick');
        const match = onClickAttr.match(/selectExperiment\('.*?','(.*?)'\)/);
        if (match) {
            console.log("Second Parameter:", match[1]); // Output: 01jq7hpjm9qn6fa0kjrpcjpv6s
            document.getElementById('base-project-select').innerHTML += '<option value="' + match[1] + '">' + item.children[0].innerText + '</option>';
            document.getElementById('project2-select').innerHTML += '<option value="' + match[1] + '">' + item.children[0].innerText + '</option>';
        } else {
            console.log("No match found");
        }
    });
});
function filterDuplicateNameCheck(userId, filterName) {
    const url = '../assets/php/filterDuplicateNameCheck.php';

    return fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ AcctId: userId, Name: filterName })
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error("Fetch error:", error);
            return { error: error.message }; // Return an error object instead of a raw error
        });
}
document.getElementById('init-create-filter').addEventListener('click', async function() {
     let filterList = [].slice.call(document.getElementsByClassName('filter-list-item'));
     filterList.forEach(function(item, index) {
        filterList[index] = item.innerText;
     });

     let filterName = document.getElementById('filter-name').value.trim();
     const duplicateNameResponse = await filterDuplicateNameCheck(id, filterName);
     if(filterName != '' && filterList.indexOf(filterName) < 0 && duplicateNameResponse.error == null && duplicateNameResponse.exists == false) {

        filterName = filterName.replace(/ /g, '_');

        let baseProject = document.getElementById('base-project-select').value;
        let project2 = document.getElementById('project2-select').value;

        if(baseProject !== project2) {

            let data = {'function':'createNewFilter','parameters': {'AcctId': id, 'Account':labname, 'FilterName': filterName, 'Base':baseProject, 'Project2': project2}};
            data = JSON.stringify(data);
            // log(data);
            httpRequest(data);

        } else {

            log('base and project2 are the same project');
            document.getElementById('alert-confirm').innerHTML = '<p>The base project and comparison project are the same.  Change one of the project selctions to proceed.</p>';
            $('#dialog-alert').dialog('open');

        }

     } else {

        let message = '';

        if(filterName == '') {

            log('blank name');
            message = '<p>You must enter a name</p>';

        } else if(duplicateNameResponse.error != null || duplicateNameResponse.exists == true) {
            log('duplicate name');
            message = '<p>Duplicate name!  This name has already been used.  Enter another name to proceed</p>';
        } else {

            log('duplicate name');
            message = '<p>Duplicate name!  This name has already been used.  Enter another name to proceed</p>';

        }

        document.getElementById('alert-confirm').innerHTML = message;
        $('#dialog-alert').dialog('open');
     }
});
document.getElementById('init-create-filter-project').addEventListener('click', function() {
     let filterList = [].slice.call(document.getElementsByClassName('filter-list-item'));
     filterList.forEach(function(item, index) {
        filterList[index] = item.innerText;
     });
     let filterName = document.getElementById('filter-name').value.trim();
     if(filterName != '' && filterList.indexOf(filterName) < 0) {
         filterName = filterName.replace(/ /g, '_');
         let baseProjectEl = document.getElementById('base-project-select');
         let baseProject = baseProjectEl.options[baseProjectEl.selectedIndex].text;
         let baseProjectId = baseProjectEl.value;
         let project2El = document.getElementById('project2-select');
         let project2 = project2El.options[project2El.selectedIndex].text;
         let project2Id = document.getElementById('project2-select').value;
        if(baseProject !== project2) {
            let data = {'function':'createFilterProject','parameters': {
                'AcctId': id, 
                'Account':labname, 
                'FilterName': filterName, 
                'Base':baseProject,
                'BaseId': baseProjectId,
                'Project2': project2,
                'Project2Id': project2Id
            }};
            data = JSON.stringify(data);
            httpRequest(data);
        } else {
            log('base and project2 are the same project');
            document.getElementById('alert-confirm').innerHTML = '<p>The base project and comparison project are the same.  Change one of the project selctions to proceed.</p>';
            $('#dialog-alert').dialog('open');
        }

     } else {

        let message = '';

        if(filterName == '') {

            log('blank name');
            message = '<p>You must enter a name</p>';

        } else {

            log('duplicate name');
            message = '<p>Duplicate name!  This name has already been used.  Enter another name to proceed</p>';

        }

        document.getElementById('alert-confirm').innerHTML = message;
        $('#dialog-alert').dialog('open');
     }
});
document.getElementById('delete-filter-btn').addEventListener('click', function() {

    if(activeFilter != undefined || activeFilter != '') {
        let data = {'function':'deletefilter','parameters': {'AcctId': id, 'Account':labname, 'FilterName': activeFilter}};
        data = JSON.stringify(data);
        httpRequest(data);
    } else {
        log('Select a filter');
    }
});
document.getElementById('display-filter-btn').addEventListener('click', function() {
    getFilterList();
});
document.getElementById('move-folder-btn').addEventListener('click', function() {
    document.getElementById('folders-move-select').innerHTML = '';
    document.getElementById('folders-move-select').innerHTML = `<option value="root">No Folder</option>`;
    let folderElementsArray = [].slice.call(document.getElementsByClassName('folder-list-btn'));
    folderElementsArray.forEach(function(item) {
        let folderName = item.innerText.trim();
        let folderId = item.dataset.target.replace('#folder-','');
        document.getElementById('folders-move-select').innerHTML += `<option value="` + folderId + `">` + folderName + `</option>`;
    });
    let proList = [].slice.call(document.getElementsByClassName('project-list-item'));
    document.getElementById('move-folder-projects-list').innerHTML = '';
    proList.forEach(function (item) {
        const onclickAttr = item.getAttribute('onclick');
        const match = onclickAttr.match(/selectExperiment\('([^']+)','([^']+)'/);
        if (match) {
            console.log("Second Parameter:", match[1]); // Output: 01jq7hpjm9qn6fa0kjrpcjpv6s
            if (item.classList.contains('folder-list-item')) {
                document.getElementById('move-folder-projects-list').innerHTML += '<option style="background-color: #f5b3be;" value="' + match[2] + '">' + item.children[0].innerText + '</option>';
            } else {
                document.getElementById('move-folder-projects-list').innerHTML += '<option value="' + match[2] + '">' + item.children[0].innerText + '</option>';
            }
        } else {
            console.log("No match found");
        }
    });
    $('#dialog-moveto-folder').dialog('open');
});
function getAnnouncements() {
    let data = {'function':'returnAnnouncements'};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}

function selectExperiment(project, projectId, folder = '', folderId = '') {
    for (let i = 0; i < liArray.length; i++) {
        liArray[i].classList.remove('active-project');
    }
    if( event.target.tagName === 'DIV' ) {
        log('ExpSelect 1');
        event.target.parentElement.classList.add('active-project');
    } else if( event.target.tagName === 'SPAN' ) {
        log('ExpSelect 2');
        event.target.parentElement.parentElement.classList.add('active-project');
    } else {
        log('ExpSelect 3');
        event.target.classList.add('active-project');
    }
    activeExpFolderName = folder;
    activeExpFolderId = folderId;
    activeExpName = project;
    activeExpId = projectId;
    // target.classList.add('active-project');
    // console.log({html: target.innerHTML, text: target.innerText, project: project});
    // if(activeExpName!='') {
        // tableData = {
        //     "Creation_Date": "No Data",
        //     "Submitted_By": "No Data",
        //     // REmove Context
        //     // "Context": "No Data",
        //     // "Num_Context": "No Data",
        //     "Num_Concepts": "No Data",
        //     "Num_Entities": "No Data"
        // }
        // getExpInfo();
    // } else {
        // document.getElementById('project-info').style.display = 'none';
        // document.getElementById('announcements-wrapper').style.display = 'block';
    // }
}

function selectFilter(target) {
    document.getElementById('create-filter-form').classList.add('d-none');

    activeFilter = target.innerText;
    for(let i=0;i<filterArray.length;i++) {

        filterArray[i].classList.remove('active-project');

    }
    target.classList.add('active-project');

    let data = {'function':'getFilterInfo','parameters': {'Account':labname,'FilterName':activeFilter}};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);

}

function getFilterList() {
    if(activeFilter != '') {
        let data = {'function':'getFilterList','parameters': {'AcctId': id, 'Account':labname,'FilterName':activeFilter}};
        data = JSON.stringify(data);
        httpRequest(data);

    } else {
        log('no active filter selected');
    }

}

function updateLists() {
    let data = {'function':'updateLists','parameters': {
        'AcctId': id,
        'Shared': sharedActive, 
        'SharedBe': sharedBeActive, 
        'Account':labname,
        'complete':[], 
        'failed': [], 
        'folders': [], 
        'filters': [], 
        'explorer': [], 
        'aers': []
    }};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}

function selectFolder(index) {
    console.log(index);
    let id = '#folder-icon-' + index;
    $(id).toggleClass('fa-folder-o').toggleClass('fa-folder-open-o');
}

function selectFailedExperiment(target) {
    // log(target);
}


function getExpInfo() {
    let data = {'function':'returnExpData','parameters': {'Account':labname,'Experiment':activeExpName,'ExpId':activeExpId}};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}
// Go back and fix 12/08/2022
function tableInfo(data) {
    log('loading table');
    log('Deprecated: function tableInfo needs to be removed');
    // if(document.getElementById('reanalyze-date-row') != null) {
    //     document.getElementById('reanalyze-date-row').remove();
    // }
    // document.getElementById('announcements-wrapper').style.display = 'none';
    // document.getElementById('secondary-heading').innerText = 'Project Information';
    // document.getElementById('project-info').style.display = 'block';
    // document.getElementsByClassName('active-project')[0].innerHTML = activeExpName;
    // document.getElementById('sub-date').innerHTML = (data['Creation_Date']==null)?'No Data...':data['Creation_Date'];
    // document.getElementById('sub-email').innerHTML = (data['Submitted_By']==null)?'No Data...':data['Submitted_By'];
    // document.getElementById('num-list').innerHTML = (data['Num_Entities']==null)?'No Data...':data['Num_Entities'];
    //Remove Context
    // document.getElementById('sub-context').innerHTML = (data['Context']==null)?'No Data...':data['Context'];
    // document.getElementById('num-concepts').innerHTML = (data['Num_Concepts']==null)?'No Data...':data['Num_Concepts'];
    // if(data['Reanalyze_Date'] != undefined) {
    //     let projectTable = document.getElementById('project-info').getElementsByTagName('tbody')[0];
    //     let reanalyzeDateRow = projectTable.insertRow(2);
    //     reanalyzeDateRow.innerHTML = '<th>Reanalyze Date:</th><td>' + data['Reanalyze_Date'] + '</td>';
    //     reanalyzeDateRow.id = 'reanalyze-date-row';
    // }
};

function passMatch(pass, passConfirm) {
    if(pass=='' && passConfirm=='') {
        document.getElementById('password').classList.add('invalid-input');
        document.getElementById('password-confirm').classList.add('invalid-input');
        log('empty field');
        return false;
    } else if(pass=='') {
        document.getElementById('password').classList.add('invalid-input');
        document.getElementById('password-confirm').classList.remove('invalid-input');
        log('empty input');
        return false;
    } else if(passConfirm=='') {
        document.getElementById('password').classList.remove('invalid-input');
        document.getElementById('password-confirm').classList.add('invalid-input');
        log('empty field');
        return false;
    } else if (pass != passConfirm) {
        document.getElementById('password').classList.add('invalid-input');
        document.getElementById('password-confirm').classList.add('invalid-input');
        log('passwords do not match');
        return false;
    } else {
        document.getElementById('password').classList.remove('invalid-input');
        document.getElementById('password-confirm').classList.remove('invalid-input');
        return true;
    }
}

function emailValidation(text) {
    var mailformat = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    // log(text.match(mailformat));
    if (text!='' && text.match(mailformat)) {
        log('Valid Email...');
        return true;
    } else {
        log('You have entered an invalid email address!');
        return false;
    }
}

function displayModalHelp(num) {
    let msg = '';
    switch(num) {
        case 1:
            msg = `<h3>Project Name</h3>
                   <p>This should be a short, one to a few words, descriptive title that typically relates to the experiment or project the entity list has been generated from.
                   The project name can be anything that allows your team to easily identify the project.</p>
                   <br />
                   <p><i class="fa fa-exclamation-circle" aria-hidden="true"></i> Note that spaces in the project name will be converted to underscores.</p>`;
            break;

        case 2:
            msg = `<h3>Email</h3>
                   <p>The address will be notified when the project has been completed. If there is a problem with the project processing,
                   we also will have a direct point of contact to the person submitting the project,
                   so as we may be able to get the data corrected and the project processed as quickly as possible.</p>`;
            break;

        case 3:
            msg = `<h3>Context</h3>
                   <p>The framework, setting, or environment relating to the entered entity list, i.e., an entity list of genes returned from an experiment investigating Crohn's
                   disease may have context terms of gut, ileum and enteritis. Though entering context terms may return more contextually relevant results, it is not required.</p>
                   <br />
                   <p><i class="fa fa-exclamation-circle" aria-hidden="true"></i> Note that context terms need to be comma separated and should generally stay between one to ten terms.</p>`;
            break;

        case 4:
            msg = `<h3>Processing Type</h3>
                   <p>This value represents the “processing time vs level of analysis” balance within the CompBio processing engine.
                   “Rapid Run” being the fastest in processing time, yet the lowest in detail analysis, “Standard Run” as the balanced middle between time and level of detail,
                   and “Comprehensive Run” being the longest processing time yet the highest level of detail returned during the analysis.
                   Behind the scenes, what this controls is the allowance of concept, contextually related terms that are generated and assembled into spatially related themes,
                   generation capacity during data processing.</p>`;
            break;
    }

    document.getElementById('modal-help-content').innerHTML = msg;
    $('#dialog-modal-help').dialog('open');

}

function httpRequest(data) {
    let header = 'PostRequest=';
    let request = header + data;
    let url = `${Webroot}legacy/app/assets/php/admin_functions.php`;
    let xhr = new XMLHttpRequest();
    // xhr.timeout = 4000;
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    // xhr.ontimeout = function() {
    //     log('request-error -> ' + xhr.statusText);
    // }
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            // log(response);
            if(response!='request complete') {
                httpResponse(response);
            } else {
                log('hello from js');
            }
        }
    }
    xhr.send(request);
}

function httpResponse(response) {
    let data = 0;
    try {
        data = JSON.parse(response);
    } catch(e) {
        console.log(e);
        log('parse error response');
        log(response);
    }
    if(data==0) {
        data = response;
    }
    if(data.hasOwnProperty('switch_cmd')) {
        let cmd = data['switch_cmd'];
        switch (cmd) {
            case 'ERROR!':
                log('!!!Error!!!');
                log(data);
                break;

            case 'ReturnRedirectLogin':
                console.log('SESSION_EXPIRED: Redirecting to Login!');
                window.location.href = Webroot + 'login';
                break;

            case 'ReturnContactData':
                log('ReturnContactData!!!!!!!!!!!!!!!!!!!!!');
                // log( data );
                contactData = data['contactData'];
                populateEmailSelect();
                // log(contactData);
                break;

            case 'ReturnEmailToolResp':
                // log( data['emailToolResp'] );
                document.getElementById('email_notification_tool_form').reset();
                break;

            case 'CompBioSubmissionResponse':
                console.log(data);
                updateLists();
                break;

            case 'MoveToFolder':
                // log(data);
                updateLists();
                break;

            case 'NewProjectFolder':
                // log(data);
                updateLists();
                break;

            case 'ProjectsMove':
                // log(data);
                updateLists();
                break;

            case 'SessionStatus':
                log('session-status');
                if(data['Prompt'] == true) {
                    log('Reauthenticate!');
                    // location = AppWebroot;
                    // $("#dialog-session-confirm").dialog('open');
                } else {
                    log('data-prompt is false');
                    updateLists();
                    if($('#dialog-session-confirm').hasClass('ui-dialog-content')) {
                        let isOpen = $("#dialog-session-confirm").dialog('isOpen');
                        if(data['Prompt'] != true && isOpen == true) {
                            log('prompt is false and dialog is open');
                            $("#dialog-session-confirm").dialog('close');
                        }
                    } else {
                        log('session-confirm has not initialized yet -> 733');
                    }
                }
                break;

            case 'Reauth':
                // log(data);
                if(data['Status']==true) {
                    log('Reauth Success');
                    logCount = 0;
                    $('#dialog-session-confirm').dialog('close');
                    document.getElementById('inactive-acct-name').value = '';
                    document.getElementById('inactive-password').value = '',
                    document.getElementById('auth-msg').style.visibility = 'hidden';
                    let data = {'function':'reauthSuccess'};
                    data = JSON.stringify(data);
                    // log(data);
                    httpRequest(data);
                } else {
                    log('Reauth Failed');
                    logCount++;
                    // log(logCount);
                    document.getElementById('auth-msg').style.visibility = 'visible';
                    if(logCount>=4) {
                        let data = {'function':'logout'};
                        data = JSON.stringify(data);
                        // log(data);
                        httpRequest(data);
                    }
                }
                break;

            case 'ReanalyzeInputList':
                // log(data);
                if(data['List']=='No Data...'){
                    document.getElementById('alert-confirm').innerHTML = '<h2><i class="fa fa-exclamation-circle" aria-hidden="true"></i>Unable To Reanalyze!</h2>' +
                                                                '<p>Projects processed in early versions of CompBio sometimes cannot be reanalyzed automatically.<br>' +
                                                                'In these cases the data can be reanalyzed via the standard submission process which can be accessed with the "Submit New" tab.</p>';
                    $('#dialog-alert').dialog('open');
                } else {

                    let date = new Date();
                    let year = date.getFullYear();
                    let month = date.getMonth();
                    let day = date.getDate();
                    let milliSeconds = date.getMilliseconds();
                    let dateArray = [year, month + 1, day, milliSeconds];
                    let fullDate = dateArray.join('-');
                    document.getElementById('reanalyze-name').value = data['Experiment'] + '-' + fullDate;
                    // document.getElementById('reanalyze-email').value = data['Project_Details']['Email'];
                    document.getElementById('reanalyze-concept').value = data['Project_Details']['Num_Concepts'];
                    // Remove Context
                    // let context = data['Project_Details']['Context'];
                    // context = context.replace(/cells,/g, '');
                    // context = context.replace(/cells/g, '');
                    // document.getElementById('reanalyze-context').placeholder = context;
                    // projectInfo.Project_Name = document.getElementById('reanalyze-name').value;
                    projectInfo.Laboratory_Name = labname;
                    projectInfo.Entity_List = data['List'];
                    projectInfo.Num_Entities = data['List'].trim().split('\n').length;
                    projectInfo.Creation_Date = data['Project_Details']['Creation_Date'];
                    if( data['ExpressionData'] !== null ) {
                        projectInfo.Expression_List = data['ExpressionData'];
                    }
                    $('#dialog-reanalyze').dialog('open');
                }
                break;

            case 'CopyProject':
                // log(data['WriteResp']);
                break;

            case 'AnonInfo':
                // Go back and fix 12/08/2022
                // document.getElementById('announcements').innerText = data['Announcements'];
                break;

            case 'AnonUpdate':
                    // log(data['WriteResp']);
                    // getAnnouncements();
                break;

            case 'UpdateLists':
                console.log(data);
                console.log(prevListUpdate);
                console.log(objectsEqual(data,prevListUpdate));
                if( labname === null ) {
                    labname = data['Labname'];
                    window.sessionStorage.setItem( 'Laboratory', labname );
                }
                if(!objectsEqual(data,prevListUpdate)) {
                    prevListUpdate = structuredClone(data);
                    document.getElementById('projects-list').innerHTML = '';
                    if(data['Folders'].length > 0) {
                        document.getElementById('projects-list').innerHTML += data['Folders'][0];
                    }
                    for(let i=0;i<data['Completed'].length;i++) {
                        document.getElementById('projects-list').innerHTML += data['Completed'][i];
                    }
                    document.getElementById('filters-list').innerHTML = '';
                    for(let i=0;i<data['Filters'].length;i++) {
                        document.getElementById('filters-list').innerHTML += data['Filters'][i];
                    }
                    if(data['explorer_projects'].length > 0) {
                        // console.log(data['explorer_projects'])
                        document.getElementById('explorer-list').innerHTML = '';
                        if(document.getElementById('explorer-projects').classList.contains('d-flex')) {
                            document.getElementById('explorer-projects').classList.remove('d-flex');
                            document.getElementById('explorer-projects').classList.remove('justify-content-center');
                            document.getElementById('explorer-projects').classList.remove('align-items-center');
                        }
                        for(let i=0; i<data['explorer_projects'].length; i++) {
                            document.getElementById('explorer-list').innerHTML += data['explorer_projects'][i];
                        }
                    }
                    if(data['BatchJobs'] !== undefined) {
                        $('#batch-status-btn').show();
                        let fIndex = 0;
                        let batchStatusContent = '';
                        data['BatchJobs'].forEach(function(job) {
                            let fileName = data['BatchFiles'][fIndex];
                            let currentJobIndex = job.shift();
                            let jobCount = job.length;
                            let compPercent = Math.round((currentJobIndex/jobCount)*100);
                            batchStatusContent +=
                            `<div class="batch-job-container">
                                <div class="row">
                                    <div class="col-10">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="${compPercent}" aria-valuemin="0" aria-valuemax="100" style="width: ${compPercent}%">${compPercent}%</div>
                                        </div>
                                        <p class="text-body mt-2">`;
                            let tmpJobArr = [];
                            job.forEach(function(j,jIndex) {
                                if((currentJobIndex-1) > jIndex ) {
                                    tmpJobArr.push(`<span class="text-success">${j}</span>`);
                                } else if((currentJobIndex-1) == jIndex ) {
                                    tmpJobArr.push(`<span class="text-danger">${j}</span>`);
                                } else {
                                    tmpJobArr.push(`<span class="text-primary">${j}</span>`);
                                }
                            });
                            batchStatusContent += tmpJobArr.join(', ');
                            batchStatusContent +=
                                        `</p>
                                    </div>
                                    <div class="col-2">
                                        <button type="button" class="btn btn-danger batch-cancel-btn" id="${fileName}" onclick="cancelBatchProcess(this.id)">Cancel</button>
                                    </div>
                                </div>
                            </div>`;
                        });
                        $('#batch-modal-status-message').html(batchStatusContent);
                    } else {
                        $('#batch-status-btn').hide();
                    }
                } else {
                    console.log("INFO: UpdateLists- No Status Changes...");
                }
                updateCompletedAssertion();
                updateAssertionLibs();
                break;

            case 'EmailChanged':
                document.getElementById('current-email').value = data['Email'];
                document.getElementById('alert-msg').innerHTML = 'Email has been changed!';
                $('#alert').dialog('open');
                break;

            case 'PasswordChanged':
                document.getElementById('alert-msg').innerHTML = 'Password has been changed!';
                $('#alert').dialog('open');
                break;

            case 'InputList':
                let list = data['List'].replace(/\n/g, "<br>");
                document.getElementById('list-display').innerHTML = list;
                $('#dialog-list').dialog('open');
                break;

            case 'LoggedOut':
                sessionStorage.removeItem('Laboratory');
                // window.location.replace(`${AppWebroot}logout`);
                $.post(`${AppWebroot}logout`,null,(data, status) => {
                    console.log(data);
                });
                window.location.replace(`${AppWebroot}`);
                break;

            case 'ExpInfo':
                if(data['Information']!='No Data'){
                    tableData = data['Information'];
                }
                tableInfo(tableData);
                break;

            case 'BioProjectInfo':
                console.log(data['debug']);
                console.log(data['Data']);
                populateBioInfoTable(data['Data']);
                break;

            case 'AersProjectInfo':
                // console.log(data['Data']);
                populateAersInfoTable(data['Data']);
                break;

            case 'SudoExpInfo': {
                document.getElementById('sudo-table-info-heading').innerText = 'Selected Project Information';
                document.getElementById('sudo-table-info-heading').style.visibility = 'visible';
                let name = data['Information']['name'] || 'No Data...';
                let date = data['Information']['created_at'] || 'No Data...';
                let numEntities = data['Information']['entity_count'] || 'No Data...';
                // Remove Context
                // let context = data['Information']['Context'] || 'No Data...';
                let numConcepts = data['Information']['concept_count'] || 'No Data...';
                let status = data['Information']['status'] || 'No Data...';
                let sudoTable = '<table class="table">' +
                                    '<tr><th>Name:</th><td>' + name + '</td></tr>' +
                                    '<tr><th>Creation Date:</th><td>' + date + '</td></tr>' +
                                    '<tr><th>Status:</th><td>' + status + '</td></tr>' +
                                    '<tr><th>Number Of Entities:</th><td>' + numEntities + '</td></tr>' +
                                    '<tr><th>Number Of Concepts:</th><td>' + numConcepts + '</td></tr>' +
                                '</table>';

                document.getElementById('sudo-table-info').innerHTML = sudoTable;
                document.getElementById('sudo-table-info').style.visibility = 'visible';
                }
                break;

            case 'SudoProjects': {
                let projectList = data['ProjectsList'];
                let numOfProjects = projectList.length;
                if(projectList.length<1) {
                    projectList = 'No Projects To Display...';
                } else {
                    projectList = projectList.join('');
                }
                document.getElementById('sudo-projects-list').innerHTML = projectList;
                document.getElementById('sudo-projects-list').style.visibility = 'visible';
                document.getElementById('sudo-table-info-heading').innerText = 'Selected Account Information';
                document.getElementById('sudo-table-info-heading').style.visibility = 'visible';

                let name = data['Info']['LabName'] || 'No Data...';
                let date = data['Info']['created_at'] || 'No Data...';
                let email = data['Info']['email'] || 'No Data...';

                let sudoTable = '<table class="table">' +
                                    '<tr><th>Lab Name:</th><td>' + name + '</td></tr>' +
                                    '<tr><th>Creation Date:</th><td>' + date + '</td></tr>' +
                                    '<tr><th>Email:</th><td>' + email + '</td></tr>' +
                                    '<tr><th>Number Of Projects:</th><td>' + numOfProjects + '</td></tr>' +
                                '</table>';

                document.getElementById('sudo-table-info').innerHTML = sudoTable;
                document.getElementById('sudo-hidden-1').style.visibility = 'visible';
                document.getElementById('sudo-table-info').style.visibility = 'visible';
                }
                break;

            case 'Delete':
                updateLists();
                break;

            case 'SetExp':
                loadingModal.close();
                log(data);
                let optiCheck = (data['BioExplorer']) ? document.getElementById('bio-optimization-check').checked : document.getElementById('optimization-check').checked;
                newProjectWindow = window.open();
        // newProjectWindow.opener.focus();
                newProjectWindow.sessionStorage.setItem('ProjectKey', data['Key']);
                newProjectWindow.sessionStorage.setItem('ProjectAcct', data['ProjectAcct']);
                newProjectWindow.sessionStorage.setItem('Project', data['Project']);
                newProjectWindow.sessionStorage.setItem('Entity_List', data['Entity_List']);
                newProjectWindow.sessionStorage.setItem('Expression_List', data['Expression_List']);
                newProjectWindow.sessionStorage.setItem('Optimized', optiCheck);
                newProjectWindow.sessionStorage.setItem('BioExplorerProject', data['BioExplorer']);
                newProjectWindow.sessionStorage.setItem('AersProject', data['AERS']);
                newProjectWindow.sessionStorage.setItem('AcctId', data['Id']);
                newProjectWindow.sessionStorage.setItem('ProjectId', data['ProjectId']);
                let filterLi = [].slice.call(document.getElementById('filters-list').children);
                let filterList = [];
                filterLi.forEach(function(item) {
                    filterList.push(item.innerText);
                });
                newProjectWindow.sessionStorage.setItem('FilterList', JSON.stringify(filterList));
                log('newTab');
                newProjectWindow.location = data['URL'];
                log('focusing newTab');
                // newProjectWindow.focus();
                newProjectWindow = null;
                break;

            case 'AccountsList':
                let accountLists = [].slice.call(document.getElementsByClassName('account-list'));
                accountLists.forEach(function(element) {
                    element.innerHTML = data['List'].join('');
                });
                let accountListItems = [].slice.call(accountLists[0].children);
                accountListItems.forEach(function(item) {
                    // item.id = item.innerText;
                    item.addEventListener('click', function() {
                        let parentListArr = [].slice.call(item.parentElement.children);
                        parentListArr.forEach(function(el) {
                            el.classList.remove('active-project');
                        });
                        item.classList.add('active-project');
                        sudoAccountSelect(item.id,item.innerText);
                    });
                });
                break;

            case 'RequestsList':
                // log(data);
                document.getElementById('request-list').innerHTML = data['List'].join('');
                requestObjs = data['RequestObjs'];
                updateRequestItemsArray();
                break;

            case 'AccountCreated':
                document.getElementById('alert-confirm').innerHTML = 'New Account Created<br>Account:  ' + data['NewAccount'] + '<br>Email:  ' + data['Email'];
                let form = [].slice.call(document.getElementById('new-form-wrap').querySelectorAll('input'));
                for(let i=0;i<3;i++) {
                    form[i].value = '';
                }
                form[3].checked = false;
                $('#dialog-alert').dialog('open');
                updateAccountLists();
                break;

            case 'RequestDeleted':
                let node = document.getElementsByClassName('requests-action-container');
                node[0].style.visibility = 'hidden';
                updateRequestsList();
                break;

            case 'EditAccount':
                // log('Name Changed -->  ' + data['NewName']);
                updateAccountLists();
                break;

            case 'FilterCreated':
                log('FilterCreated/Deleted');
                updateLists();

                for(let i=0;i<filterArray.length;i++) {
                    filterArray[i].classList.remove('active-project');
                }
                activeFilter = '';
                document.getElementById('filter-name').value = '';
                if(document.getElementById('create-filter-form').classList.contains('d-none') === false) {
                    document.getElementById('create-filter-form').classList.add('d-none');
                }
                if(document.getElementById('filter-info').classList.contains('d-none') === false) {
                    document.getElementById('filter-info').classList.add('d-none');
                }
                break;

            case 'FilterProjectReturn':
                log('FilteredInputList');
                if(data['status'] == 'Success'){
                    for(let i=0;i<filterArray.length;i++) {
                        filterArray[i].classList.remove('active-project');
                    }
                    activeFilter = '';
                    document.getElementById('filter-name').value = '';
                    if(document.getElementById('create-filter-form').classList.contains('d-none') === false) {
                        document.getElementById('create-filter-form').classList.add('d-none');
                    }
                    if(document.getElementById('filter-info').classList.contains('d-none') === false) {
                        document.getElementById('filter-info').classList.add('d-none');
                    }
                    document.querySelectorAll('[data-tab-request="2"]')[1].click();

                    document.getElementById('pname').value = data['project'];
                    document.getElementById('copypaste').value = data['list'].join("\n");
                    document.getElementById('contact').value = currentAccountEmail;
                    cpInsert(document.getElementById('copypaste').value);
                    setTimeout(() => {
                        document.getElementById('form-submit').click();
                    }, 500);
                } else {
                    let message = '';

                    switch(data['status']) {

                        case 'OverlapQuantity':
                            message = '<p>There was not enough overlap between the selected projects to successfully create a new project.</p>';
                            break;

                        case 'NullInputList':
                            message = '<p>We apologize but there was an unknown error that occurred during overlap processing.</p>';
                            message += '<p>Please report this problem to a CompBio team member if this persists.</p>';
                            break;

                        case 'Project2InputList':
                            message = '<p>Unfortunately one of the projects seems to be an older format that we no longer can use.</p>';
                            message += '<p>We suggest reanalyzing this older project and then running the comparison on the newer version';
                            break;

                        case 'BaseInputList':
                            message = '<p>Unfortunately one of the projects seems to be an older format that we no longer can use.</p>';
                            message += '<p>We suggest reanalyzing this older project and then running the comparison on the newer version';
                            break;

                        case 'DuplicateProjectName':
                            message = '<p>The entered project name is already in use by you or someone whithin your lab.</p>';
                            message += '<p>Rerun the comparison with a different name.</p>';
                            break;

                        default:
                            message = '<p>We apologize but there was an unknown error that occurred during overlap processing.</p>';
                            message += '<p>Please report this problem to a CompBio team member if this persists.</p>';
                            // log(data['status']);

                    }

                    document.getElementById('alert-confirm').innerHTML = message;
                    $('#dialog-alert').dialog('open');
                }

                break;

            case 'OverlapQuantity':
                log('Insufficient Overlap');
                break;

            case 'FilterNoMatches':
                log('FilterFail--> No Matches');
                // log(data['debug']);
                break;

            case 'NoList':
                log('One of the Selected projects does not have a list');
                break;

            case 'ReturnFilterInfo':
                // log('FLAG!!!--> Return filter info');
                document.getElementById('filterinfo-name').innerText = data['FilterName'];
                document.getElementById('filterinfo-creationdate').innerText = data['CreationDate'];
                document.getElementById('filterinfo-basename').innerText = data['Base'];
                document.getElementById('filterinfo-overlapname').innerText = data['Project2'];
                document.getElementById('filterinfo-numofcons').innerText = data['ListLength'];
                document.getElementById('filter-info').classList.remove('d-none');
                break;

            case 'ReturnAssertion':
                insertAssertionList(data['Assertion'], data['AssertWithLibs'],data['Assertion2']);
                break;

            case 'ReturnAssertionLibs':
                console.log(data['AssertionLibs']);
                insertAssertionLibsList(data['AssertionLibs']);
                break;

            case 'ReturnAssertionSubmission':
                // console.log(data);
                updateCompletedAssertion();
                    console.log('AssertionSubmitted!');
                    document.getElementById('assertion-message').innerHTML = 'Assertion Generation Submitted Successfully!<br>Upon Generation Completion (Approximately 1-5 minutes), The newly created assertion shall be available in the "Completed Assertion Generation(s)" list for selection and viewing.';
                    $('#dialog-assertion').dialog('open');
                // if(data.ReturnStatus !== 0 && data.hasOwnProperty('err')) {
                //     console.log('AssertionFailure!');
                //     document.getElementById('assertion-message').innerHTML = `Assertion Generation Failure!<br>Error Message:<br>${data.err}`;
                //     $('#dialog-assertion').dialog('open');
                // } else if(data.ReturnStatus !== 0 && !data.hasOwnProperty('err')) {
                //     console.log('AssertionFailureWithoutErrorMessage!');
                //     document.getElementById('assertion-message').innerHTML = 'Assertion Generation Failure Without Message!<br>This type of failure, indicates a deeper system problem. It is suggested that you try a "forced refresh" of the browser (Windows: ctrl + F5, Mac: cmd + shift + r), then try to resubmit.  If the problem still persists, log out, log back in and then try to resubmit.  If submission is still not possible after trying the previous troubleshooting steps, notify a CompBio Team Member to further disgnose the problem.';
                //     $('#dialog-assertion').dialog('open');
                // } else {
                //     console.log('AssertionSuccess!');
                //     document.getElementById('assertion-message').innerHTML = 'Assertion Generation Complete!<br>The newly created assertion is now available in the "Completed Assertion Generation(s)" list for selection and viewing.';
                //     $('#dialog-assertion').dialog('open');
                // }
                break;

            case 'AssertionRequirementsFailure':
                console.log('!ERROR! No matrix_file for one of the projects...');
                // console.log(data);
                break;

            case 'AssertionDeleted':
                // console.log(data);
                updateCompletedAssertion();
                break;

            case 'ConsoleLog':
                console.log(data['output']);
                break;

            case 'BatchCanceled':
                console.log(data['Messasge']);
                break;

            case 'AssertionMatrixFileCheck':
                console.log(data);
                significantCheckboxSwitch(data.parameters['Result']);
                break;

            default:
                log('Default Case');
                log(data);
                break;
        }
    } else {
        log(response);
    }
}

function cleanString(input) {
    var output = "";
    for (var i=0; i<input.length; i++) {
        if (input.charCodeAt(i) <= 127) {
            output += input.charAt(i);
        }
    }
    output = output.replace(/[[\]{}()*+!<=:?\/\\^$|#,&%@]/g, '');
    return output;
}

/*Filter Management*/
function createFilter(baseList, list2) {

    // log(baseList);
    // log(list2);

}
/*Filter End*/

/* Log Default Assertion Engine Version To Console */
function getEngineVersion() {
    let data = {'function':'returnDefaultEngine'};
    data = JSON.stringify(data);
    log(data);
    httpRequest(data);
    return 'Getting Value...';
}

// Project Table Sorting
/*
* cb-project-names
* cb-project=timestamps
* cb-project-num-ents
* cb-project-profile-data
*/
let descName = true;
let descDate = true;
let descEnty = true;
let descPrfl = true;
$(function() {
    console.log( "Headers Ready!" );
    $('#comp-pro-names-header').click(function() {
        // console.log('Name Header Sort');
        descName = cbProjectsSort('.cb-project-names', descName);
    });
    $('#comp-pro-date-header').click(function() {
        // console.log('Date Header sort');
        descDate = cbProjectsSort('.cb-project-timestamps', descDate);
    });

    function cbProjectsSort( col,desc ) {
        let parent = $('#projects-list')[0];
        // Folder Parents
        let folderRows = Array.from( $('.folder-list-btn') );
        if( col === '.cb-project-names' ) {
            if( desc === true ) {
                folderRows.sort(function(a, b) {
                    return $(a).text().toUpperCase().localeCompare($(b).text().toUpperCase());
                });
            } else {
                folderRows.sort(function(b, a) {
                    return $(a).text().toUpperCase().localeCompare($(b).text().toUpperCase());
                });
            }
        }
        $.each(folderRows, function(idx, itm) {
            parent.append(itm);
            // Folder Children Items
            let childListId = $(itm).data('target');
            if( $(childListId).length > 0 ) {
                let childList = $(childListId).get(0);
                let childListRows = Array.from( childList.children );
                if( desc === true ) {
                    childListRows.sort(function(a, b) {
                        if( $(col, a).text() == 'N/A' ) {
                            return 1;
                        } else {
                            return $(col, a).text().toUpperCase().localeCompare($(col, b).text().toUpperCase());
                        }
                    });
                } else {
                    childListRows.sort(function(b, a) {
                        if( $(col, b).text() == 'N/A' ) {
                            return 1;
                        } else {
                            return $(col, a).text().toUpperCase().localeCompare($(col, b).text().toUpperCase());
                        }
                    });
                }
                $.each(childListRows, function(idx, itm) { childList.append(itm); });
                parent.append(childList);
            }
        });
        // Standard Projects
        let rows = Array.from( $('.standard-project-item') );
        if( desc === true ) {
            rows.sort(function(a, b) {
                if( $(col, a).text() == 'N/A' ) {
                    return 1;
                } else {
                    return $(col, a).text().toUpperCase().localeCompare($(col, b).text().toUpperCase());
                }
            });
        } else {
            rows.sort(function(b, a) {
                if( $(col, b).text() == 'N/A' ) {
                    return 1;
                } else {
                    return $(col, a).text().toUpperCase().localeCompare($(col, b).text().toUpperCase());
                }
            });
        }
        $.each(rows, function(idx, itm) { parent.append(itm); });
        return ( desc === true ) ? false : true;
    }
    // Initial Load Sort by Date ( Newest First )
    document.onreadystatechange = () => {
        if (document.readyState === "complete") {
            setTimeout( setInitialSort,1000 );
        }
    };
    // setTimeout( setInitialSort,3000 );
    function setInitialSort() {
        if(document.getElementById('projects-list').innerText === 'Loading...') {
            // console.log('Looping!!!');
            setTimeout( setInitialSort,1000 );
        } else {
            console.log('Breaking Loop!!!');
            searchFilterClear();
            $('#comp-pro-date-header').click();
            $('#comp-pro-date-header').click();
        }
    }
    $('#pro-name-search').on('keyup', searchProNames);
    $('#clear-pro-search-btn').click(function(e) {
        searchFilterClear();
    });
    function searchProNames() {
        let txtValue;
        let folderRows = Array.from( $('.folder-list-btn') );
        let rows = Array.from( $('.standard-project-item') );
        let filter = $('#pro-name-search').val().toUpperCase();
        if(filter == '') {
            searchFilterClear();
        }
        for(let i=0;i<folderRows.length;i++) {
            txtValue = $(folderRows[i]).text().trim().toUpperCase();
            if(txtValue.indexOf(filter) > -1) {
                folderRows[i].style.display = "";
            } else {
                folderRows[i].style.display = "none";
            }
        }
        for(let i=0;i<rows.length;i++) {
            txtValue = $(rows[i].firstElementChild).text().trim().toUpperCase();
            if(txtValue.indexOf(filter) > -1) {
                rows[i].style.display = "";
                // console.log('row text found');
            } else {
                rows[i].style.display = "none";
            }
        }
    }
    // setTimeout(function (){
    //     searchFilterClear();
    // }, 1000);
});
function searchFilterClear() {
    let folderRows = Array.from( $('.folder-list-btn') );
    let rows = Array.from( $('.standard-project-item') );
    $('#pro-name-search').val('');
    for(let i=0;i<folderRows.length;i++) {
        folderRows[i].style.display = "";
    }
    for(let i=0;i<rows.length;i++) {
        rows[i].style.display = "";
    }
}
// Batch Processing
$(function() {
    $('#open-batch-btn').click(function() {
        // let batchBuildTab = window.open('batch_builder_sheet.php','_blank')
        let batchBuildTab = window.open(`${Webroot}legacy/app/admin/batch_builder_sheet.php`,'_blank')
    });
    console.log('Batch Button Event Listener Added');
});
function cancelBatchProcess(filename) {
    console.log('Canceling batch job' + filename);
    let data = {'function':'cancelBatchProcess','parameters': { 'Account':labname, 'Filename':filename}};
    data = JSON.stringify(data);
    log(data);
    httpRequest(data);
    $('#batch-status-btn').hide();
}
// Objects Equality Check
function objectsEqual(object1, object2) {
	if(isObject(object1) && isObject(object2)) {
    const keys1 = Object.keys(object1);
    const keys2 = Object.keys(object2);

    if (keys1.length !== keys2.length) {
      return false;
    }

    for (const key of keys1) {
      const val1 = object1[key];
      const val2 = object2[key];
      const areObjects = isObject(val1) && isObject(val2);
      if (
        areObjects && !objectsEqual(val1, val2) ||
        !areObjects && val1 !== val2
      ) {
        return false;
      }
    }

    return true;
	} else {
  	return false;
  }
}
function isObject(object) {
  return object != null && typeof object === 'object';
}
/* Loading Native Modal */
const loadingModal = document.getElementById('loading');

/* Toggle CbProject Shared Status */
document.getElementById('toggle-share-btn').addEventListener('click', function () {
    const projectName = activeExpName;
    const projectId = activeExpId;
    if(projectName !== '' && projectName !== null && projectId !== '' && projectId !== null) {
        const url = `${AppWebroot}cbprojects/${projectId}/toggle-shared`;
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log(data);
            if (data.success) {
                console.log(`Shared status toggled to: ${data.shared}`);
                updateLists();
            } else {
                console.log('Error toggling shared status');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            console.log('An error occurred while toggling shared status.');
        });
    } else {
        console.error('Project name is empty or null.');
    }
});

function urlDebug(){
    let t = `${Webroot}legacy/app/assets/php/debug.php`;
    fetch(t)
   .then(response => response.text())
   .then((response) => {
       console.log(response)
   })
   .catch(err => console.log(err))
}