$( function() {
    $( "#aers-response" ).dialog({
      autoOpen: false,
      resizable: false,
      height: "auto",
      width: 600,
      modal: true,
      buttons: {
      	Ok: function() {
      		updateLists();
      		$(this).dialog("close");
      		document.getElementById('aers-response-message').innerHTML = '';
      	}
      }
    });
});
$( function() {
    $( "#aers-delete-confirm" ).dialog({
      autoOpen: false,
      resizable: false,
      height: "auto",
      width: 600,
      modal: true,
      buttons: {
        "Delete Project": function() {
            if (activeAersProject != '') {
                let data = {'function':'deleteAersProject','parameters': {'Account':labname,'Project':activeAersProject}};
                data = JSON.stringify(data);
                log(data);
                httpRequest(data);
            }
          $( this ).dialog( "close" );
        },
        Cancel: function() {
          $( this ).dialog( "close" );
        }
      }
    });
});
var aersExposed = {};
var currentArrValue;
window.addEventListener('DOMContentLoaded', event => {
	document.getElementById('aers-form').addEventListener('submit', aersSubmitFormCheck);
	document.getElementById('aers-form-account-name').value = labname;
    document.getElementById('delete-aers-project-btn').addEventListener('click', (event) => {
    	$('#aers-delete-confirm').dialog('open');
    });
	// const url = 'http://sandbox7.wucon.wustl.edu:8001/pcmmrpc';
	// app\assets\aers\AERS_Form_Data.json
	const url = '../assets/aers/AERS_Form_Data.json';
	const opts = {"body":"{\n\"jsonrpc\": \"2.0\",\n\"method\": \"GetAllAERSIdeaNames\",\n\"id\": \"99\",\n\"params\": null\n}","method": "POST","mode": "cors"};
	fetch(url,opts)
	.then(resp => resp.json())
	.then(json => json.result)
	.then((array) => {
		/* Codes are index 0 and String Names are index 1 */
		aersExposed.ndc = array[0][1].map(item => Object.entries(item)[0]);
		aersExposed.meddra = array[1][1].map(item => Object.entries(item)[0]);
		return document.getElementsByClassName('aers-input');
	})
	.then((collection) => {
		for(let el of collection) {
            el.addEventListener('focus', clearUnfocusedInputs);
            aersInputAutoComplete(el, aersExposed[el.id]);
		}
		return document.getElementsByClassName('aers-form-check-input');
	})
	.then((checks) => {
		for(let check of checks) {
			check.addEventListener('change', aersTermCheckSwitch);
		}
	})
	.catch( error => console.log(error));
});

function aersSubmitFormCheck() {
	event.preventDefault();
	const projectName = cleanString(document.getElementById('aers-submit-name').value.trim().replace(/ /g, "_").replace(/&/g, "").replace(/;/g, ""));
	console.log(projectName)
	if(aersSubmitNameCheck(projectName) && aersSubmitCheckboxCheck() && aersSubmitInputCheck()) {
		console.log('AERS Submission Checks Passed');
		console.log(event.target);
		const formData = {
			"account-name": labname,
			"aers-submit-name": cleanString(document.getElementById('aers-submit-name').value.trim().replace(/ /g, "_").replace(/&/g, "").replace(/;/g, "")).replace(/[^a-z0-9_-]/gi,''),
			"queryArray": [(document.getElementById('ndc').value.trim() === '') ? document.getElementById('meddra').getAttribute('data-code') : document.getElementById('ndc').getAttribute('data-code')],
			"includedTerms": Array.from(document.getElementsByClassName('aers-form-check-input')).filter(el => el.checked).map(el => el.value).join()
		}
		console.log(formData);
		const dataString = 'aers=' + JSON.stringify(formData);
		console.log(dataString);
		aersSubmitProjectData(dataString);
		clearAllAersFormInputs();
		document.getElementById('alert-msg').innerHTML = 'The AERS Data project has been submitted.' +
	    '  The project Should be ready for viewing within the next few minutes via the left side screen menu, AERS > Completed Projects';
	    $('#alert').dialog('open');
	    document.getElementById('aers-submit-name').value = '';
	} else {
		console.log('aers form submit: ELSE')
	}
}

function aersSubmitProjectData(string) {
	const xhr = new XMLHttpRequest();
	xhr.open("POST","../assets/php/aers-submit.php");
	xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xhr.onreadystatechange = function() {
		if(xhr.readyState == 4 && xhr.status == 200) {
			console.log(xhr.responseText);
			let response = xhr.responseText;
			if(response.includes('Success')) {
            	let jsonResp = JSON.parse(xhr.responseText);
            	document.getElementById('aers-response-message').innerHTML = `<p>AERS Submission: ${jsonResp.Project}</p><p>The submission has completed successfully and
            	 the project is now available in the Completed Projects list</p>`;
				if($("#alert").dialog("isOpen")) {
					document.getElementById('alert-ok-btn').click();
				}
            	$("#aers-response").dialog("open");
			} else if(response.includes('Failure')) {
            	document.getElementById('aers-response-message').innerHTML = `<p>The AERS Project that has been submitted has finished processing with a result of failure.
            	  This typically happens due to not enough ideas being generated during the processing phase.  If this error regularly occurs, please contact a member of the
            	   CompBio Team and let us know so that we may look closer into the situation.</p>`;
				if($("#alert").dialog("isOpen")) {
					document.getElementById('alert-ok-btn').click();
				}
				$("#aers-response").dialog("open");
			} else {
				document.getElementById('aers-response-message').innerHTML = `<p>The AERS Project that has been submitted has suffered some type of unexpected error causing
				 the processing of the project to be terminated.  If this error continues to occur, please contact a member of the CompBio Team and let us know so that we
				 may look closer into the situation.</p>`;
				if($("#alert").dialog("isOpen")) {
					document.getElementById('alert-ok-btn').click();
				}
				$("#aers-response").dialog("open");
			}
		}
	};
	xhr.send(string);
}

function aersSubmitNameCheck(string) {
	document.getElementById('aers-submit-name').classList.remove('invalid-input');
	console.log('name check')
	console.log(string)
	if((/\d/.test(string) || /[a-zA-Z]/.test(string))) {
		return true;
	} else {
		document.getElementById('aers-submit-name').classList.add('invalid-input');
		return false;
	}
}

function aersSubmitInputCheck() {
	const inputs = document.getElementsByClassName('aers-input');
	let targetInput, inputCount = 0, matched = false;
	for(let input of inputs) {
		if(input.value.trim() != '') {
			targetInput = input;
			inputCount++;
			for(let i=0;i<aersExposed[input.id].length;i++) {
				if(input.value === aersExposed[input.id][i][1]) {
					matched = true;
					break;
				}
			}
		}
	}
	console.log(inputCount)
	if(inputCount > 0 && matched === true) {
		console.log('aersSubmitInputCheck PASSED CHECK')
		return true;
	} else if(inputCount < 1) {
		for(let input of inputs) {
			input.classList.add('invalid-input');
		}
		console.log('aersSubmitInputCheck FAILED CHECK FLAG 1')
		return false;
	} else if(matched === false) {
		targetInput.classList.add('invalid-input');
		console.log('aersSubmitInputCheck FAILED CHECK FLAG 2')
		return false;
	} else {
		console.log('!! AERS Input Value Check Error: Dumping Variable Values... !!')
		console.log({"inputs":inputs,"targetInput":targetInput,"inputCount":inputCount,"matched":matched})
		return false;
	}
}

function aersSubmitCheckboxCheck() {
	const checkboxes = document.getElementsByClassName('aers-form-check-input');
	let checkCount = 0;
	for(let checkbox of checkboxes) {
		if(checkbox.checked) {
			checkCount++;
		}
	}
	if(checkCount < 1) {
		document.getElementById('aers-checkbox-validation-message').classList.remove('d-none');
		console.log('aersSubmitCheckboxCheck FAILED CHECK')
		return false;
	} else {
		console.log('aersSubmitCheckboxCheck PASSED CHECK')
		return true;
	}
}

function aersTermCheckSwitch(event) {
	document.getElementById('aers-checkbox-validation-message').classList.add('d-none');
	const checkEl = event.target;
	if(checkEl.value.includes('meddra') && checkEl.checked) {
		const meddraChecks = document.getElementsByClassName('meddra-checks');
		for(let check of meddraChecks) {
			if(check !== checkEl) {
				check.checked = false;
			}
		}
	}
}

function aersInputAutoComplete(inp, arr) {
	var currentFocus;
	inp.addEventListener("input", function(e) {
		// arr = (submissionType === 'single-multi') ? arr.filter((term) => !term.includes(' ')) : arr;
		var a,
			b,
			i,
			val = this.value;
		closeAllLists();
		if (!val) {
			return false;
		}
		currentFocus = -1;
		a = document.createElement("DIV");
		a.setAttribute("id", this.id + "-autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
		this.parentNode.appendChild(a);
		/* RegEx Sanitizer string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') */
		const expressionNoSpace = new RegExp(`^${val.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, "ig");
        const expressionWithSpace = new RegExp(` ${val.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, "ig");
        const expressionWithHyphen = new RegExp(`\-${val.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, "ig");
		// const expressionNoSpace = new RegExp(`^${val.replace(/[()]/g, '\\$&')}`, "ig");
  		// const expressionWithSpace = new RegExp(` ${val.replace(/[()]/g, '\\$&')}`, "ig");
  		// const expressionWithHyphen = new RegExp(`\-${val.replace(/[()]/g, '\\$&')}`, "ig");
		for (i = 0; i < arr.length; i++) {
			if (
				expressionNoSpace.test(arr[i][1]) ||
                expressionWithSpace.test(arr[i][1]) ||
                expressionWithHyphen.test(arr[i][1])
			) {
				b = document.createElement("DIV");
				let tempString = arr[i][1].replace(
					expressionNoSpace,
					`<strong>${val}</strong>`,
				);
				tempString = tempString.replace(
					expressionWithSpace,
					` <strong>${val}</strong>`,
                );
                b.innerHTML = tempString.replace(
                    expressionWithHyphen,
                    `-<strong>${val}</strong>`,
                );
				b.innerHTML += `<input type="hidden" data-code="${arr[i][0]}" value="${arr[i][1]}">`;
				b.addEventListener("click", function(e) {
					inp.value = this.getElementsByTagName("input")[0].value;
					inp.setAttribute('data-code', this.getElementsByTagName("input")[0].getAttribute('data-code'));
					closeAllLists();
				});
				a.appendChild(b);
			}
        }
        if (a && a.childElementCount < 1) {
            inp.classList.add('invalid-input');
        }
	});
	inp.addEventListener("keydown", function(e) {
        this.classList.remove('invalid-input');
        var x = document.getElementById(this.id + "-autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
		if (e.keyCode == 40) {
			currentFocus++;
			addActive(x);
		} else if (e.keyCode == 38) {
			currentFocus--;
			addActive(x);
		} else if (e.keyCode == 13) {
			e.preventDefault();
			if (currentFocus > -1) {
				if (x) x[currentFocus].click();
			}
		}
	});
	function addActive(x) {
		if (!x) return false;
		removeActive(x);
		if (currentFocus >= x.length) currentFocus = 0;
		if (currentFocus < 0) currentFocus = x.length - 1;
		x[currentFocus].classList.add("autocomplete-active");
	}
	function removeActive(x) {
		for (var i = 0; i < x.length; i++) {
			x[i].classList.remove("autocomplete-active");
		}
	}
	function closeAllLists(elmnt) {
		var x = document.getElementsByClassName("autocomplete-items");
		for (var i = 0; i < x.length; i++) {
			if (elmnt != x[i] && elmnt != inp) {
				x[i].parentNode.removeChild(x[i]);
			}
		}
	}
	document.addEventListener("click", function(e) {
		closeAllLists(e.target);
	});
}

function selectAersProject(project) {
	target = event.target;
	console.log({project:project, target:target});
	for(let i=0; i<liAersArray.length; i++) {
		liAersArray[i].classList.remove('active-project');
	}
	target.classList.add('active-project');
	activeAersProject = project;
	clearAersProjectInfo();
	getAersProjectInfo();
}

function showHideAersProjectInfo() {
	if(activeAersProject !== '') {
		document.getElementById('aers-project-info').removeAttribute('hidden');
	} else {
		document.getElementById('aers-project-info').setAttribute('hidden', '');
	}
}

function clearAersProjectInfo() {
	const aersTableCells = Array.from(document.getElementsByClassName('aers-info-cell'));
	aersTableCells.forEach((cell) => {
		cell.innerText = '';
	});
}

function getAersProjectInfo() {
	let data = {'function':'returnAersProjectInfo','parameters': {'Account':labname,'Project':activeAersProject}};
    data = JSON.stringify(data);
    log(data);
    httpRequest(data);
}

function populateAersInfoTable(info) {
	document.getElementById('aers-name').innerText = activeAersProject;
	document.getElementById('aers-sub-date').innerText = (info.hasOwnProperty('Creation_Date') && info.Creation_Date !== null) ? info.Creation_Date : 'No Data...';
	document.getElementById('aers-val').innerText = returnTableValueSubmitted(info);
	document.getElementById('aers-text').innerText = (document.getElementById('aers-val').innerText !== 'No Data...') ? returnValueReference(document.getElementById('aers-val').innerText) : 'No Data...';
	document.getElementById('aers-terms').innerText = (info.hasOwnProperty('Terms') && info.Terms !== null) ? info.Terms : 'No Data...';
	showHideAersProjectInfo();
}

function returnTableValueSubmitted(object) {
	if(object.hasOwnProperty('Value_Submitted') && object.Value_Submitted !== null) {
		return (Array.isArray(object.Value_Submitted)) ? object.Value_Submitted.join() : object.Value_Submitted.replace(/^'|'$/g, '').replace(/\'\\'\'/g, "'");
	} else {
		return 'No Data...';
	}
	return (info.hasOwnProperty('Value_Submitted') && info.Value_Submitted !== null) ? info.Value_Submitted.replace(/^'|'$/g, '').replace(/\'\\'\'/g, "'") : 'No Data...';
}

function returnValueReference(string) {
	const allAersData = aersExposed.ndc.flat().concat(aersExposed.meddra.flat());
	let index = allAersData.indexOf(string);
	return (allAersData[index + 1] !== undefined) ? allAersData[index + 1] : 'No Data...';
}

function clearAllAersFormInputs() {
	const inputs = document.getElementsByClassName('aers-input');
    for(let input of inputs ) {
        input.value = '';
	    input.classList.remove('invalid');
    }
    const checkBoxes = document.getElementsByClassName('aers-form-check-input');
    for(let input of checkBoxes) {
    	input.checked = false;
    }
}