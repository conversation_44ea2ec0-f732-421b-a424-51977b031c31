"use strict";
console.log('js loading');
window.addEventListener("DOMContentLoaded", (event) => {
	console.log('js loaded');
	const assertionBatchName = document.getElementById('assertion-batch-name').innerText;
	console.log(`batchname: ${assertionBatchName}`);
	Array.from(document.querySelectorAll("#lib-results-table > tbody > tr.valid-row")).forEach((row) => {
		row.addEventListener('click', (event) => {
			const assertionName = row.children[2].innerText;
			console.log(`name: ${assertionName}`);
			let tempURL = window.location.href.split('/');
			tempURL.pop();
			const url = `${tempURL.join('/')}/assertion.html`;
			const assertionTab = window.open(url, "_blank");
			assertionTab.assertionName = assertionName;
			assertionTab.assertionBatchName = assertionBatchName;
		});
	});
});
