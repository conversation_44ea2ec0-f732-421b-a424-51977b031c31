"use strict";
/*
As of AsertionEngineVersion3.0 Score property has been replaced with overall_rank
and a new key has been added, internal_score, an absolute score given for a node
that can be used to compare across experiments
*/
window.addEventListener("DOMContentLoaded", (event) => {
    if(typeof assertionName === "undefined" && sessionStorage.getItem('AssertionReload') !== null) {
        window.assertionName = sessionStorage.getItem('AssertionReload');
        sessionStorage.removeItem('AssertionReload');
    }
    if(typeof assertionBatchName === "undefined" && sessionStorage.getItem('AssertionReloadBatchName') !== null) {
        window.assertionBatchName = sessionStorage.getItem('AssertionReloadBatchName');
        sessionStorage.removeItem('AssertionReloadBatchName');
    } else if(typeof assertionBatchName === "undefined" && sessionStorage.getItem('AssertionReloadBatchName') === null) {
        window.assertionBatchName = undefined;
    }
    window.addEventListener('beforeunload', (event) => {
        sessionStorage.setItem('AssertionReload', assertionName);
        if(typeof window.assertionBatchName !== "undefined") {
            sessionStorage.setItem('AssertionReloadBatchName', window.assertionBatchName);
        }
    });
    const account = sessionStorage.getItem('Laboratory');
    document.getElementById('sidebar-heading').innerHTML = `<h3>${assertionName}</h3>`;
    const getAssertionData = () => {
        $.post("../assets/php/returnAssertionData.php", {
                account: account,
                assertion: assertionName,
                assertionBatchName: (typeof assertionBatchName === "undefined") ? null : assertionBatchName
            },
            (resp, status) => {
                resp = JSON.parse(resp);
                console.log(resp);
                const data = (resp.hasOwnProperty('custom')) ? resp.custom : resp;
                console.log(data);
                if(data.hasOwnProperty('similarity')) {
                    document.getElementById('sidebar-heading').innerHTML += `Total Groups: ${data.subgraphs.length}<br>Similarity: <span id="similarity-score">${data.similarity}</span><hr><div id="save-message" style="display:none"></div>`;
                } else {
                    document.getElementById('sidebar-heading').innerHTML += `Total Groups: ${data.subgraphs.length}<hr><div id="save-message" style="display:none"></div>`;
                }
                const sideBarContent = document.getElementById('sidebar-content');
                data.subgraphs.forEach((group, index) => {
                    const groupName = (group.hasOwnProperty('name')) ? group.name : `Group ${index + 1}`;
                    const div = document.createElement('DIV');
                    div.classList.add('sidebar-group');
                    div.classList.add('ui-state-default');
                    div.innerHTML = `<div class="group-name" contentEditable="true">${groupName}</div><span class="ui-icon ui-icon-pencil"></span><span class="ui-icon ui-icon-arrowthick-2-n-s d-none"></span>`;
                    if(group.hasOwnProperty('score')) {
                        div.innerHTML += (group.score >= 0) ? `<div>Score: <span class="group-score">${group.score}</span></div>` : `<div>No Score<span class="group-score d-none">${group.score}</span></div>`;
                    } else if(group.hasOwnProperty('overall_rank')) {
                        div.innerHTML += (group.overall_rank >= 0) ? `<div>Score: <span class="group-score">${group.overall_rank}</span></div>` : `<div>No Score<span class="group-score d-none">${group.overall_rank}</span></div>`;
                    }
                    div.innerHTML += `<div>Items: ${group.nodes.length}</div><hr><div class="group-nodes">${group.nodes.join(', ')}</div>`;
                    sideBarContent.appendChild(div);
                });
            })
            .then(() => {
                $('#sidebar-content').sortable();
                $("#sidebar-content").sortable('disable');
                const saveBtn = document.createElement('BUTTON');
                saveBtn.id = 'save-btn';
                saveBtn.type = 'button';
                saveBtn.classList.add('btn');
                saveBtn.classList.add('btn-outline-secondary');
                saveBtn.innerText = 'Save Changes';
                saveBtn.addEventListener('click', saveData);
                const uiArrows = Array.from(document.getElementsByClassName('ui-icon-arrowthick-2-n-s'));
                const uiPencils = Array.from(document.getElementsByClassName('ui-icon-pencil'));
                const groupNames = Array.from(document.getElementsByClassName('group-name'));
                const customizeBtn = document.createElement('BUTTON');
                customizeBtn.id = 'customize-btn';
                customizeBtn.type = 'button';
                customizeBtn.classList.add('btn');
                customizeBtn.classList.add('btn-outline-secondary');
                customizeBtn.innerText = 'Change Order';
                customizeBtn.addEventListener('click', (event) => {
                    if(customizeBtn.innerText === 'Change Order') {
                        customizeBtn.innerText = 'Lock Order';
                        uiArrows.forEach(arrow => arrow.classList.remove('d-none'));
                        uiPencils.forEach(pencil => pencil.classList.add('d-none'));
                        groupNames.forEach(name => name.removeAttribute('contentEditable'));
                        $('#sidebar-content').sortable('enable');
                        $("#sidebar-content").disableSelection();
                    } else {
                        customizeBtn.innerText = 'Change Order';
                        uiArrows.forEach(arrow => arrow.classList.add('d-none'));
                        uiPencils.forEach(pencil => pencil.classList.remove('d-none'));
                        groupNames.forEach(name => name.setAttribute('contentEditable', true));
                        $('#sidebar-content').sortable('disable');
                        $("#sidebar-content").enableSelection();
                    }
                });
                const btnGroup = document.createElement('DIV');
                btnGroup.classList.add('btn-group');
                document.getElementById('sidebar-heading').appendChild(btnGroup);
                btnGroup.appendChild(customizeBtn);
                btnGroup.appendChild(saveBtn);
            });
    }
    getAssertionData();
    const pdfURL = (typeof assertionBatchName === "undefined") ? `../../data/${account.toUpperCase()}/account_info/assertion/${assertionName}/${assertionName}.pdf#toolbar=1` :  `../../data/${account.toUpperCase()}/account_info/assertion_w_libs/${assertionBatchName}/${assertionName}/${assertionName}.pdf#toolbar=1`;
    document.getElementById('pdf-container').src = pdfURL;
});

function saveData() {
    const data = {};
    data.subgraphs = [];
    const groupNames = Array.from(document.getElementsByClassName('group-name'));
    const groupScores = Array.from(document.getElementsByClassName('group-score'));
    const groupNodes = Array.from(document.getElementsByClassName('group-nodes'));
    groupNames.forEach((name, index) => {
        const tempObj = {
            "name": name.innerText,
            "score": parseFloat(groupScores[index].innerText),
            "nodes": groupNodes[index].innerText.split(',').map(node => node.trim())
        }
        data.subgraphs.push(tempObj);
    });
    if(document.getElementById('similarity-score') !== null) {
        data.similarity = parseFloat(document.getElementById('similarity-score').innerText);
    }
    sendCustomData(data);
}

function sendCustomData(data) {
    const account = sessionStorage.getItem('Laboratory');
    $.post("../assets/php/saveCustomAssertionData.php", {
            account: account,
            assertion: assertionName,
            assertionBatchName: (typeof assertionBatchName === "undefined") ? null : assertionBatchName,
            customData: JSON.stringify(data)
        },
        (resp, status) => {
            if(resp.includes('ERROR')) {
                console.log('Custom Data Error...');
                console.log(resp);
                $("#save-message").text("Error: Data Not Saved").show();
                setTimeout(() => {
                    $("#save-message").fadeOut("slow", "swing", () => { $("#save-message").text(""); });
                }, 3000);
            } else {
                console.log(resp);
                $("#save-message").text("Success: Data Saved").show();
                setTimeout(() => {
                    $("#save-message").fadeOut("slow", "swing", () => { $("#save-message").text(""); });
                }, 3000);
            }
        });
}