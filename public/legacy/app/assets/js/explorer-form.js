var data = {
  biological_concepts: [],
  entities_gene: [],
  entities_metabolites: [],
  entities_microbes: [],
  entities_mirna: [],
  patho_raw_hierarchy: [],
  phys_raw_hierarchy: []
};
var submissionType = 'single-single';
window.addEventListener("DOMContentLoaded", (event) => {
	document.getElementById('account-name').value = labname;
	// Laravel Version Link Correction
	const url = getPcmmEndpoint();
	const opts = {"body":"{\n\"jsonrpc\" : \"2.0\",\n\"method\" : \"GetAllIdeaNames\",\n\"id\" : \"55\",\n\"params\": null\n}","method":"POST","mode":"cors"};
	fetch(url,opts)
	.then(resp => resp.json())
	.then(json => json.result)
	.then((array) => {
	console.log(array)
	array.forEach(item => {
			if(item[0] === 'gene') {
				data.entities_gene = item[1].slice();
			} else if (item[0] === 'metabolite') {
				data.entities_metabolites = item[1].slice();
			} else if (item[0] === 'mirna') {
				data.entities_mirna = item[1].slice();
			} else if (item[0] === 'microbe') {
				data.entities_microbes = item[1].slice();
			} else if (item[0] === 'concept') {
				data.biological_concepts = item[1].slice();
			} else if (item[0] === 'physiology') {
				data.phys_raw_hierarchy = item[1].slice();
			} else if (item[0] === 'pathophysiology') {
				data.patho_raw_hierarchy = item[1].slice();
			}
		});
		return document.getElementsByClassName('explorer-input');
	})
	.then( collection => {
		for(let item of collection) {
			item.addEventListener('focus', clearUnfocusedInputs);
			autocomplete(item, data[item.id]);
		}
		return document.getElementsByClassName('form-radio-input');
	})
	.then( radios => {
		const addToTermBtn = document.getElementById('multi-add-to-term-btn');
		const addTermBtn = document.getElementById('multi-add-term-btn');
		for(let item of radios) {
			item.addEventListener('change', (event) => {
				document.getElementById('submission-build-list').innerHTML = '';
				const bulkGeneInput = document.getElementById('bioexpl-bulk-genes');
				const collection = document.getElementsByClassName('explorer-input');
				for(let item of collection) {
					autocomplete(item, data[item.id]);
				}
				if(item.checked) {
					submissionType = item.value;
					console.log(submissionType);
				}
				if(submissionType.includes('multi')) {
					console.log('adding class');
					document.getElementById('submission-build-col').classList.remove('d-none');
				} else {
					console.log('removing class');
					document.getElementById('submission-build-col').classList.add('d-none');
				}
				if(submissionType === 'single-single') {
					addToTermBtn.classList.add('d-none');
					addTermBtn.classList.add('d-none');
					bulkGeneInput.value = '';
					bulkGeneInput.disabled = true;
				} else if(submissionType === 'multi-single') {
					addToTermBtn.classList.add('d-none');
					addTermBtn.classList.remove('d-none');
					bulkGeneInput.value = '';
					bulkGeneInput.disabled = false;
				} else if(submissionType === 'single-multi') {
					addToTermBtn.classList.remove('d-none');
					addTermBtn.classList.add('d-none');
					bulkGeneInput.value = '';
					bulkGeneInput.disabled = false;
				} else if(submissionType === 'multi-multi') {
					addToTermBtn.classList.remove('d-none');
					addTermBtn.classList.remove('d-none');
					bulkGeneInput.value = '';
					bulkGeneInput.disabled = false;
				}
			})
		}
		addToTermBtn.addEventListener('click', (event) => {
			const collection = document.getElementsByClassName('explorer-input');
			const bulkGenes = getBulkGenes();
			if (bulkGenes !== null) {
				console.log(bulkGenes);
				bulkGenes.forEach((gene) => {
					addTermToSubmission(gene, 'entities_gene');
				});
				document.getElementById('bioexpl-bulk-genes').value = '';
				return;
			}
			for(let input of collection) {
				if(input.value !== '' && !input.classList.contains('invalid') && data[input.name].includes(input.value)) {
					termEntered = input.value;
					termCategory = input.name;
					addTermToSubmission(input.value,input.name);
					input.value = '';
				}
			}
		});
		addTermBtn.addEventListener('click', (event) => {
			const collection = document.getElementsByClassName('explorer-input');
			const bulkGenes = getBulkGenes();
			if (bulkGenes !== null) {
				bulkGenes.forEach((gene) => {
					addTermToSubmission(gene, 'entities_gene');
				});
				document.getElementById('bioexpl-bulk-genes').value = '';
				return;
			}
			for(let input of collection) {
		 		if(input.value !== '' && !input.classList.contains('invalid') && data[input.name].includes(input.value)) {
					termEntered = input.value;
					termCategory = input.name;
					addTermToSubmission(input.value,input.name);
					input.value = '';
				}
			}
		});
	})
	.catch( error => {
		console.log(error);
	});
	document.forms['explorer-form'].addEventListener('submit', (event) => {
	event.preventDefault();
	if(submissionType === 'single-single') {
		let inputs = document.getElementsByClassName('explorer-input');
		// console.log(inputs)
		let propertyToSearch = '';
			valueFound = false;
			for( let i=0; i<inputs.length; i++ ) {
			// console.log(`I= ${i}`)
				if( inputs[i].value.length > 0 && !inputs[i].classList.contains('invalid')) {
				console.log('found the correct input!')
						valueFound = true;
							propertyToSearch = inputs[i].name;
						if( data[propertyToSearch].includes( inputs[i].value ) && checkBoxValidity() && document.getElementById('explorer-name').value.trim() !== '' && !duplicateNameCheck(document.getElementById('explorer-name').value.trim())) {
							console.log('Success Validation')
							gatherExplorerFormData();
							clearAllInputs();
							// document.getElementById('alert-msg').innerHTML = 'The Bio-Explorer project has been submitted.' +
							// '  The project Should be ready for viewing within the next few minutes via the left side screen menu, Bio-Explorer > Completed Projects';
							// $('#alert').dialog('open');
							document.getElementById('explorer-name').value = '';
							break;
						} else {
					console.log('Odd...cannot find the selected word in the array')
							event.preventDefault();
							console.log('Failed Validation:2');
							break;
						}
				} else {
				console.log('this is not the input you are looking for...')
						console.log('Failed Validation:1');
				}
			}
			if( valueFound === false ) {
					if(document.getElementById('bioexpl-variance').value.trim().length && checkBoxValidity() && document.getElementById('explorer-name').value.trim() !== '' && !duplicateNameCheck(document.getElementById('explorer-name').value.trim())) {
						console.log('Variance Only Submission!!!');
						gatherExplorerFormData();
						clearAllInputs();
						// document.getElementById('alert-msg').innerHTML = 'The Bio-Explorer project has been submitted.' +
						// '  The project Should be ready for viewing within the next few minutes via the left side screen menu, Bio-Explorer > Completed Projects';
						// $('#alert').dialog('open');
						document.getElementById('explorer-name').value = '';
					}
			}
		} else if(submissionType === 'single-multi' || submissionType === 'multi-single') {
		console.log('building multi or custom query...');
		if(document.getElementById('submission-build-list').children.length > 0 && checkBoxValidity() && document.getElementById('explorer-name').value.trim() !== '' && !duplicateNameCheck(document.getElementById('explorer-name').value.trim())) {
			console.log('Success Validation');
					gatherExplorerFormData();
					clearAllInputs();
					document.getElementById('radio-single-single').click();
			document.getElementById('submission-build-list').innerHTML = '';
					document.getElementById('alert-msg').innerHTML = 'The Bio-Explorer project has been submitted.' +
					'  The project Should be ready for viewing within the next few minutes via the left side screen menu, Bio-Explorer > Completed Projects';
					$('#alert').dialog('open');
					document.getElementById('explorer-name').value = '';
		}
		} else {
		console.log('ERROR: submissionType not detected or unrecognized....');
		console.log('Well this is awkward...\nI dont know what to do....');
	}
	});
    $( function() {
	    $( "#bio-delete-confirm" ).dialog({
	      autoOpen: false,
	      resizable: false,
	      height: "auto",
	      width: 600,
	      modal: true,
	      buttons: {
	        "Delete Project": function() {
	            if (activeExplorerProject != '') {
	                let data = {'function':'deleteBioProject','parameters': {
							'AcctId': id,
							'Account':labname,
							'Project':activeExplorerProject,
							'ProjectId': activeExplorerProjectId
						}};
	                data = JSON.stringify(data);
	                httpRequest(data);
	            }
	          $( this ).dialog( "close" );
	        },
	        Cancel: function() {
	          $( this ).dialog( "close" );
	        }
	      }
	    });
	});
    $( function() {
	    $( "#bioexplorer-response" ).dialog({
	      autoOpen: false,
	      resizable: false,
	      height: "auto",
	      width: 600,
	      modal: true,
	      buttons: {
	      	Ok: function() {
	      		updateLists();
	      		$(this).dialog("close");
	      		document.getElementById('be-response-message').innerHTML = '';
	      	}
	      }
	    });
	});
	document.getElementById('include-all-terms').addEventListener('change', (event) => {
		Array.from(document.getElementsByClassName('explorer-checkbox')).forEach((checkBox) => {
			checkBox.checked = event.target.checked;
		});
	});
	document.getElementById('delete-bio-project-btn').addEventListener('click', (event) => {
		$('#bio-delete-confirm').dialog('open');
	});
	Array.from(document.getElementsByClassName('explorer-checkbox')).forEach((checkBox) => {
		checkBox.addEventListener('change', (event) => {
			if(!checkBox.checked && document.getElementById('include-all-terms').checked) {
				document.getElementById('include-all-terms').checked = false;
			} else if(checkBox.checked && !document.getElementById('include-all-terms').checked) {
				let amountChecked = 0;
				const allCheckBoxes = Array.from(document.getElementsByClassName('explorer-checkbox'));
				const allLength = allCheckBoxes.length;
				allCheckBoxes.forEach((item) => {
					if(item.checked) {
						amountChecked++;
					}
				});
				if(amountChecked === allLength) {
					document.getElementById('include-all-terms').checked = true;
				}
			}
		});
	});
	let singleGeneInput = document.getElementById('entities_gene');
	let bulkGeneInput = document.getElementById('bioexpl-bulk-genes');
	singleGeneInput.addEventListener('input', (event) => {
		console.log('singular gene changed');
		bulkGeneInput.value = '';
	});
	bulkGeneInput.addEventListener('input', (event) => {
		console.log('bulk gene changed');
		singleGeneInput.value = '';
	});
});

function checkBoxValidity() {
	let returnBoolean = false;
	Array.from(document.getElementsByClassName('explorer-checkbox')).forEach((checkBox) => {
		if(checkBox.checked) {
			returnBoolean = true;
		}
	});
	if(!returnBoolean) {
		document.getElementById('checkbox-validation-message').classList.remove('d-none');
	    return false;
	} else {
		document.getElementById('checkbox-validation-message').classList.add('d-none');
		return true;
	}
}

function gatherExplorerFormData() {
	let variance = getVariance();
	let bulkGenes = getBulkGenes();
	if(submissionType === 'single-single') {
		const elementIdArray = ['biological_concepts','patho_raw_hierarchy','phys_raw_hierarchy','entities_gene','entities_metabolites','entities_microbes','entities_mirna'];
		let inputValues = [];
		for(let i=0; i<elementIdArray.length;i++) {
			if(document.getElementById(elementIdArray[i]).value.trim() !== '') {
				inputValues.push(document.getElementById(elementIdArray[i]).value.trim());
				break;
			}
		}
		if(variance !== null) {
			variance.forEach((a) => {
				inputValues.push(a);
			});
		}
		const data = {
			"account-name": labname,
			"explorer-name": cleanString(document.getElementById('explorer-name').value.replace(/ /g, "_").replace(/&/g, "").replace(/;/g, "")).replace(/[^a-z0-9_-]/gi,''),
			queryArray: inputValues,
			includedTerms: returnIncludedTerms(),
			methodCall: 'LaunchBioExplorerRun'
		}
		// console.log(data)
		submitExplorerForm('explorer=' + JSON.stringify(data));
	} else if(submissionType === 'single-multi') {
		const data = {
			"account-name": labname,
			"explorer-name": cleanString(document.getElementById('explorer-name').value.replace(/ /g, "_").replace(/&/g, "").replace(/;/g, "")).replace(/[^a-z0-9_-]/gi,''),
			queryArray: returnSubmissionQuery(),
			includedTerms: returnIncludedTerms(),
			methodCall: 'LaunchMultiWordBioExplorerRun'
		}
			if(variance !== null) {
				variance.forEach((a) => {
					data.queryArray.push(a);
				});
			}
		// console.log(data)
		submitExplorerForm('explorer=' + JSON.stringify(data));
	} else if(submissionType === 'multi-single') {
		const data = {
			"account-name": labname,
			"explorer-name": cleanString(document.getElementById('explorer-name').value.replace(/ /g, "_").replace(/&/g, "").replace(/;/g, "")).replace(/[^a-z0-9_-]/gi,''),
			queryArray: returnSubmissionQuery(),
			includedTerms: returnIncludedTerms(),
			methodCall: 'LaunchBioExplorerRun'
		}
			if(variance !== null) {
				variance.forEach((a) => {
					data.queryArray.push(a);
				});
			}
		// console.log(data)
		submitExplorerForm('explorer=' + JSON.stringify(data));
	} else {
		console.log('ERROR: Cannot detect submissionType...');
	}
}

function getVariance() {
	let v = document.getElementById('bioexpl-variance').value.split("\n").map(a => a.trim());
	if(v.length > 0 && v[0] !== '') {
		return v;
	} else {
		return null;
	}
}

function getBulkGenes() {
	let g = document.getElementById('bioexpl-bulk-genes').value.split("\n").map(a => a.trim());
	if(g.length > 0 && g[0] !== '') {
		return g;
	} else {
		return null;
	}
}

function returnSubmissionQuery() {
	return Array.from(document.getElementById('submission-build-list').children).map(child => child.firstElementChild.innerText);
}

function returnIncludedTerms() {
	const inclusionString = [];
	Array.from(document.getElementsByClassName('explorer-checkbox')).forEach((checkBox) => {
    	if(checkBox.checked) {
    		inclusionString.push(checkBox.value);
    	}
    });
    return inclusionString.join();
}

function submitExplorerForm(string) {
	let url = "../assets/php/explorer-submit.php";
	let xhr = new XMLHttpRequest();
	xhr.open("POST", url, true);
	xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
	xhr.onreadystatechange = function() {
		if (xhr.readyState == 4 && xhr.status == 200) {
			console.log(xhr.responseText);
			if(xhr.responseText.includes('Status')) {
				let jsonResp = JSON.parse(xhr.responseText);
				console.log(jsonResp);
				let status = jsonResp.Status;
				document.getElementById('be-response-message').innerHTML = `<p>Status: ${status}</p>`;
			} else {
				document.getElementById('be-response-message').innerHTML = `<p>Project Submitted Successfully.</p>`;
			}
			if($("#alert").dialog("isOpen")) {
				document.getElementById('alert-ok-btn').click();
			}
			$("#bioexplorer-response").dialog("open");
			// if(xhr.responseText === 'Explorer Not Set!') {
			// 	document.getElementById('be-response-message').innerHTML = `<p>The BioExplorer Project that has been submitted failed due to the
			// 		server not receiving the proper data. The suggested solution is to log out of the system and log back in to try again.  If you
			// 		receive the same error again, please notify a CompBio team member so they may look further into the issue.</p>`;
			// if($("#alert").dialog("isOpen")) {
			// 	document.getElementById('alert-ok-btn').click();
			// }
			// $("#bioexplorer-response").dialog("open");
			// } else if(xhr.responseText.includes('Success')) {
			// 	let jsonResp = JSON.parse(xhr.responseText);
			// 	document.getElementById('be-response-message').innerHTML = `<p>BioExplorer Submission: ${jsonResp.Project}</p><p>The submission has completed successfully and
			// 		the project is now available in the Completed Projects list</p>`;
			// if($("#alert").dialog("isOpen")) {
			// 	document.getElementById('alert-ok-btn').click();
			// }
			// 	$("#bioexplorer-response").dialog("open");
			// } else {
			// 	try {
			// 		let jsonResp = JSON.parse(xhr.responseText);
			// 		if(jsonResp.Output.includes('Failure: Not enough ideas generated from term.') === true) {
			// 			document.getElementById('be-response-message').innerHTML = `<p>BioExplorer Submission: ${jsonResp.Project}</p><p>The submission has completed processing successfully with NULL results.</p>
			// 			<p>What does this mean?</p><p>The engine correctly completed processing without error, but there were no publications that matched this particular term.
			// 				For reference purposes, a project has been created and added to the "Completed Projects" list.  This project may be deleted at your discretion.</p>`;
			// 		if($("#alert").dialog("isOpen")) {
			// 			document.getElementById('alert-ok-btn').click();
			// 		}
			// 		$("#bioexplorer-response").dialog("open");
			// 		} else {
			// 			document.getElementById('be-response-message').innerHTML = `<p>BioExplorer Submission: ${jsonResp.Project}</p><p>This submission processing has ended in failure.</p>
			// 			<p>Error Output:<br>${jsonResp.Output.result}`;
			// 		if($("#alert").dialog("isOpen")) {
			// 			document.getElementById('alert-ok-btn').click();
			// 		}
			// 			$("#bioexplorer-response").dialog("open");
			// 		}
			// 	}
			// 	catch(error) {
			// 		console.log(error);
			// 		document.getElementById('be-response-message').innerHTML = `<p>The BioExplorer Submission has terminated processing and ended in error.</p>
			// 		<p>Error Output:<br>${xhr.responseText}`;
			// 	if($("#alert").dialog("isOpen")) {
			// 		document.getElementById('alert-ok-btn').click();
			// 	}
			// 		$("#bioexplorer-response").dialog("open");
			// 	}
			// }
		}
	}
	xhr.send(string);
}

function clearUnfocusedInputs(event) {
	const inputs = [...document.getElementsByClassName('explorer-input'),...document.getElementsByClassName('aers-input')];
	const currentInput = event.target;
	for(let input of inputs ) {
		if(input != currentInput) {
			input.value = '';
			input.classList.remove('invalid');
			input.classList.remove('invalid-input');
		}
	}
}

function clearAllInputs() {
	const inputs = document.getElementsByClassName('explorer-input');
	for(let input of inputs ) {
		input.value = '';
		input.classList.remove('invalid');
	}
	const checkBoxes = document.getElementsByClassName('form-check-input');
	for(let input of checkBoxes) {
	input.checked = false;
	}
	document.getElementById('bioexpl-variance').value = '';
}

function autocomplete(inp, arr) {
	var currentFocus;
	inp.addEventListener("input", function(e) {
		arr = (submissionType === 'single-multi') ? arr.filter((term) => !term.includes(' ')) : arr;
		var a,
			b,
			i,
			val = this.value;
		closeAllLists();
		if (!val) {
			return false;
		}
		currentFocus = -1;
		a = document.createElement("DIV");
		a.setAttribute("id", this.id + "-autocomplete-list");
        a.setAttribute("class", "autocomplete-items");
		this.parentNode.appendChild(a);
		const expressionNoSpace = new RegExp(`^${val}`, "ig");
        const expressionWithSpace = new RegExp(` ${val}`, "ig");
        const expressionWithHyphen = new RegExp(`\-${val}`, "ig");
		for (i = 0; i < arr.length; i++) {
			if (
				expressionNoSpace.test(arr[i]) ||
                expressionWithSpace.test(arr[i]) ||
                expressionWithHyphen.test(arr[i])
			) {
				b = document.createElement("DIV");
				let tempString = arr[i].replace(
					expressionNoSpace,
					`<strong>${val}</strong>`,
				);
				tempString = tempString.replace(
					expressionWithSpace,
					` <strong>${val}</strong>`,
                );
                b.innerHTML = tempString.replace(
                    expressionWithHyphen,
                    `-<strong>${val}</strong>`,
                );
				b.innerHTML += `<input type="hidden" value="${arr[i]}">`;
				b.addEventListener("click", function(e) {
					inp.value = this.getElementsByTagName(
						"input",
					)[0].value;
					closeAllLists();
				});
				a.appendChild(b);
			}
        }
        if (a && a.childElementCount < 1) {
            inp.classList.add('invalid');
        }
	});
	inp.addEventListener("keydown", function(e) {
        this.classList.remove('invalid');
        var x = document.getElementById(this.id + "-autocomplete-list");
        if (x) x = x.getElementsByTagName("div");
		if (e.keyCode == 40) {
			currentFocus++;
			addActive(x);
		} else if (e.keyCode == 38) {
			currentFocus--;
			addActive(x);
		} else if (e.keyCode == 13) {
			e.preventDefault();
			if (currentFocus > -1) {
				if (x) x[currentFocus].click();
			}
		}
	});
	function addActive(x) {
		if (!x) return false;
		removeActive(x);
		if (currentFocus >= x.length) currentFocus = 0;
		if (currentFocus < 0) currentFocus = x.length - 1;
		x[currentFocus].classList.add("autocomplete-active");
	}
	function removeActive(x) {
		for (var i = 0; i < x.length; i++) {
			x[i].classList.remove("autocomplete-active");
		}
	}
	function closeAllLists(elmnt) {
		var x = document.getElementsByClassName("autocomplete-items");
		for (var i = 0; i < x.length; i++) {
			if (elmnt != x[i] && elmnt != inp) {
				x[i].parentNode.removeChild(x[i]);
			}
		}
	}
	document.addEventListener("click", function(e) {
		closeAllLists(e.target);
	});
}

function selectExplorerProject(project,id) {
	target = event.target;
	for(let i=0; i<liExplorerArray.length; i++) {
		liExplorerArray[i].classList.remove('active-project');
	}
	target.classList.add('active-project');
	activeExplorerProject = project;
	activeExplorerProjectId = id;
	clearBioProjectInfo();
	getBioProjectInfo();
}

function showHideBioProjectInfo() {
	if(activeExplorerProject !== '') {
		document.getElementById('bio-project-info').removeAttribute('hidden');
	} else {
		document.getElementById('bio-project-info').setAttribute('hidden', '');
	}
}

function clearBioProjectInfo() {
	const bioTableCells = Array.from(document.getElementsByClassName('bio-info-cell'));
	bioTableCells.forEach((cell) => {
		cell.innerText = '';
	});
}

function getBioProjectInfo() {
	let data = {'function':'returnBioProjectInfo','parameters': {
		'AcctId': id,
		'Account':labname,
		'Project':activeExplorerProject,
		'ProjectId': activeExplorerProjectId
	}};
    data = JSON.stringify(data);
    httpRequest(data);
}

function populateBioInfoTable(info) {
	document.getElementById('bio-name').innerText = activeExplorerProject;
	document.getElementById('bio-sub-date').innerText = (info.created_at === null) ? 'No Data...' : info.created_at;
	document.getElementById('bio-val').innerText = (info.value === null) ? 'No Data...' : info.value.toString();
	document.getElementById('bio-class').innerText = (info.field === null) ? 'No Data...' : info.field;
	document.getElementById('bio-terms').innerText = (info.terms === null) ? 'No Data...' : info.terms.toString();
	showHideBioProjectInfo();
}

function returnTableValueSubmitted(object) {
	if(object.hasOwnProperty('Value_Submitted') && object.Value_Submitted !== null) {
		return (Array.isArray(object.Value_Submitted)) ? object.Value_Submitted.join() : object.Value_Submitted.replace(/^'|'$/g, '').replace(/\'\\'\'/g, "'");
	} else {
		return 'No Data...';
	}

	(info.hasOwnProperty('Value_Submitted') && info.Value_Submitted !== null) ? info.Value_Submitted.replace(/^'|'$/g, '').replace(/\'\\'\'/g, "'") : 'No Data...';
}

function addStringToTerm(term,category) {
	console.log('adding string')
	const activeItemArray = Array.from(document.getElementsByClassName('submission-list-item')).filter(item => item.classList.contains('submission-list-item-active'));
	if(activeItemArray.length > 0) {
		const activeItem = activeItemArray[0];
		const string = '"' + activeItem.children[0].innerText.split('"')[1] + ' ' + term + '"';
		activeItem.children[0].innerText = string;
		return true;
	} else {
		return false;
	}
}

function addTermToSubmission(term,category) {
	console.log('adding term')
	const submissionListItem = document.createElement('LI');
	submissionListItem.setAttribute('data-category', category);
	submissionListItem.classList.add('submission-list-item');
	const termWrap = document.createElement('SPAN');
	termWrap.classList.add('term-wrap');
	termWrap.innerText = `${term}`;
	const removeTermBtn = document.createElement('SPAN');
	removeTermBtn.classList.add('remove-term-btn');
	removeTermBtn.innerText = 'X';
	attachRemoveTermEvent(removeTermBtn);
	const submissionList = document.getElementById('submission-build-list');
	submissionListItem.appendChild(termWrap);
	submissionListItem.appendChild(removeTermBtn);
	submissionList.appendChild(submissionListItem);
}

function attachSelectEventToListItem(element) {
	element.addEventListener('click', (event) => {
		Array.from(document.getElementsByClassName('submission-list-item')).forEach((item) => {
			if(item === element) {
				element.classList.toggle('submission-list-item-active');
			} else {
				item.classList.remove('submission-list-item-active');
			}
		});
	});
}

function attachRemoveTermEvent(element) {
	element.addEventListener('click', (event) => {
		const listItemElement = element.parentElement;
		const listElement = listItemElement.parentElement;
		listElement.removeChild(listItemElement);
	});
}
let sharedBeActive = false;
let sharedBeProjectsBtn = document.getElementById('be-shared-projects');
sharedBeProjectsBtn.addEventListener('click', function () {
	if (sharedBeActive === false) {
		sharedBeActive = true;
		updateLists();
	} else {
		sharedBeActive = false;
		updateLists();
	}
	sharedBeProjectsBtn.classList.toggle('active');
});
/* Toggle CbProject Shared Status */
document.getElementById('toggle-bio-share-btn').addEventListener('click', function () {
	const projectName = activeExplorerProject;
	const projectId = activeExplorerProjectId;
	if (projectName !== '' && projectName !== null && projectId !== '' && projectId !== null) {
		const url = `${AppWebroot}beprojects/${projectId}/toggle-shared`;
		fetch(url, {
			method: 'POST',
			headers: {
				'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
			}
		})
			.then(response => response.json())
			.then(data => {
				console.log(data);
				if (data.success) {
					console.log(`Shared status toggled to: ${data.shared}`);
					updateLists();
				} else {
					console.log('Error toggling shared status');
				}
			})
			.catch(error => {
				console.error('Error:', error);
				console.log('An error occurred while toggling shared status.');
			});
	} else {
		console.error('Project name is empty or null.');
	}
});