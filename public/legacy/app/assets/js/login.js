"use strict";
var creds = {};
window.addEventListener( 'DOMContentLoaded', ( event ) => {
    if( window.location.href === 'https://gtac-compbio-demo.wustl.edu/' || window.location.href === 'http://gtac-compbio-demo.wustl.edu/' 
        || window.location.href === 'https://gtac-compbio-ex.wustl.edu/' || window.location.href === 'http://gtac-compbio-ex.wustl.edu/' ) {
        let el = document.getElementById('request-account');
        el.parentElement.removeChild( el );
        el = document.getElementById( 'span-divider' );
        el.parentElement.removeChild( el );
    }
} );
document.getElementById( 'form-submit' ).addEventListener( 'click', function() {
    creds.Laboratory = document.getElementById( 'name' ).value.trim();
    creds.Password = document.getElementById( 'password' ).value.trim();
    creds.webroot = Webroot;
    if ( creds.Laboratory != 'undefined' ) {
        submitData( prepareData( creds ) );
    }
} );
document.getElementById( 'form-wrap' ).addEventListener( 'keyup', function( event ) {
    event.preventDefault();
    if ( event.keyCode === 13 ) {
        document.getElementById( 'form-submit' ).click();
    }
} );

function prepareData( obj ) {
    let dataPost = 'AcctCreds=' + encodeURIComponent( JSON.stringify( obj ) );
    return dataPost;
}

function submitData( str ) {
    log( 'submitting data' );
    log( str );
    let url = window.location.href + 'app/assets/php/login.php';
    let xhr = new XMLHttpRequest();
    xhr.open( "POST", url, true );
    xhr.setRequestHeader( "Content-type", "application/x-www-form-urlencoded" );
    xhr.onreadystatechange = function() {
        if ( xhr.readyState == 4 && xhr.status == 200 ) {
            if ( xhr.responseText == false ) {
                document.getElementById( 'alert-message' ).innerHTML = 'Account name or password is incorrect!';
                document.getElementById( 'alert-message' ).classList.remove( 'alert-hidden' );
                delete creds.Laboratory;
                delete creds.Password;
                log( xhr.responseText );
                log( 'Response: false' );
            } else if ( xhr.responseText == true ) {
                window.sessionStorage.setItem( 'Laboratory', creds.Laboratory );
                document.getElementById( 'alert-message' ).innerHTML = '';
                if ( window.sessionStorage.getItem( 'Laboratory' ) == 'undefined' ) {
                    document.getElementById( 'alert-message' ).innerHTML = 'Ooops Something Went Wrong...<br>Retry Entering Credentials and Logging In Again<br>undefined account error';
                    document.getElementById( 'alert-message' ).classList.remove( 'alert-hidden' );
                } else {
                    window.location = ( Webroot + 'app/admin/' );
                }
            } else {
                document.getElementById( 'alert-message' ).innerHTML = 'Ooops Something Went Wrong...<br>Retry Entering Credentials and Logging In Again';
                document.getElementById( 'alert-message' ).classList.remove( 'alert-hidden' );
                log( xhr.responseText );
            }
        }
    };
    xhr.send( str );
}