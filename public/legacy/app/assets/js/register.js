"use strict";
var formInputArray = [].slice.call(document.getElementsByClassName('form-input'));
$(function() {
    $('#dialog-confirm').dialog({
        autoOpen: false,
        modal: true,
        show: {
            effect: 'scale'
        },
        hide: {
            effect: 'scale'
        },
        buttons: {
            OK: function() {
                $(this).dialog('close');
                window.location.replace(Webroot);
            }
        }
    });
});
document.getElementById('new-account-submit').addEventListener('click', function() {
    log('building form info object');
    getFormData();
});

document.getElementById('new-account-form').addEventListener('keyup', function(event) {
    event.preventDefault();
    if (event.keyCode === 13) {
        document.getElementById('new-account-submit').click();
    }
});

function getFormData() {
    let valid = true;
    let formData = {};
    for(let i=0;i<formInputArray.length-1;i++) {
        if(formInputArray[i].value.trim()!='') {
            formInputArray[i].classList.remove('invalid-input');
            if(i==2) {
                if(emailValidation(formInputArray[i].value.trim())==false) {
                    valid = false;
                    formInputArray[i].classList.add('invalid-input');                    
                }
            }
        } else {
            valid = false;
            formInputArray[i].classList.add('invalid-input');
        }
    }
    if(valid) {
        formData.Name = formInputArray[0].value.trim();
        formData.Organization = formInputArray[1].value.trim();
        formData.Email = formInputArray[2].value.trim();
        formData.Notes = formInputArray[3].value.trim();

        httpRequest(JSON.stringify(formData));
    }
}

function emailValidation(text) {
    var mailformat = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    if (text.match(mailformat)) {
        return true;
    } else {
        log('You have entered an invalid email address!');
        return false;
    }
}

function httpRequest(data) {
    $('#dialog-confirm').dialog('open');
    let header = 'Registration=';
    let request = header + data;
    let url = 'app/assets/php/account_request.php';
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            if(response=='request submitted') {
                $('#dialog-confirm').dialog('open');
            } else {
                log(response);
            }
        }
    }
    xhr.send(request);
}

