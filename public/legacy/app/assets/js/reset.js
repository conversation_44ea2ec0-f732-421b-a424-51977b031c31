"use strict";
var formInputArray = [].slice.call(document.getElementsByClassName('form-input'));
$(function() {
    $('#dialog-confirm').dialog({
        autoOpen: false,
        modal: true,
        show: {
            effect: 'scale'
        },
        hide: {
            effect: 'scale'
        },
        buttons: {
            OK: function() {
                $(this).dialog('close');
                window.location.replace(Webroot);
            }
        }
    });
    $('#dialog-alert-error').dialog({
        autoOpen: false,
        modal: true,
        show: {
            effect: 'scale'
        },
        hide: {
            effect: 'scale'
        },
        buttons: {
            OK: function() {
                $(this).dialog('close');
            }
        }
    });
});

document.getElementById('new-account-submit').addEventListener('click', function() {
    log('building form info object');
    let formInput = [formInputArray[0].value.trim(), formInputArray[1].value.trim()];
    getFormData(formInput);
});

document.getElementById('new-info-wrap').addEventListener('keyup', function(event) {
    event.preventDefault();
    if (event.keyCode === 13) {
        document.getElementById('new-account-submit').click();
    }
});

function getFormData(input) {
    if(emailValidation(input[1]) === true) {
        let resetData = {Account: input[0], Email: input[1]};
        log(resetData);
        httpRequest(JSON.stringify(resetData));
        log('Information --> request sent');
    }
}

function emailValidation(text) {
    var mailformat = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    if (text.match(mailformat)) {
        return true;
    } else {
        log('You have entered an invalid email address!');
        return false;
    }
}
const FalseFoundMessage = "The account name/email pair was not found.  Check if you have entered the correct information and try again.<br>If you continue to receive this message contact a team member for help.";
const SystemErrorMessage = "Something has gone wrong...<br>Try refreshing the page before resubmitting again.  If you continue to receive this message contact a team member for help.";
function httpRequest(data) {
    // $('#dialog-confirm').dialog('open');
    let header = 'Reset=';
    let request = header + data;
    let url = Webroot + 'app/assets/php/reset.php';
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            if(response=='request submitted') {
                $('#dialog-confirm').dialog('open');
            } else if(response === 'Matched Account false!') {
                console.log(response);
                document.getElementById('alert-error-message').innerHTML = FalseFoundMessage;
                $('#dialog-alert-error').dialog('open');
            } else if(response === 'No POST variable found!') {
                console.log(response);
                document.getElementById('alert-error-message').innerHTML = SystemErrorMessage;
                $('#dialog-alert-error').dialog('open');
            } else {
                console.log(response);
            }
        }
    }
    xhr.send(request);
}

