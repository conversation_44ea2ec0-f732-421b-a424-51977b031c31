"use strict";
var formInputArray = [].slice.call(document.getElementsByClassName('form-input'));
$(function() {
    $('#dialog-confirm').dialog({
        autoOpen: false,
        modal: true,
        show: {
            effect: 'scale'
        },
        hide: {
            effect: 'scale'
        },
        buttons: {
            OK: function() {
                $(this).dialog('close');
                window.location.replace(Webroot);
            }
        }
    });
});
document.getElementById('new-account-submit').addEventListener('click', function() {
    log('building form info object');
    let passwords = [formInputArray[0].value.trim(), formInputArray[1].value.trim()];
    getFormData(passwords);
});
document.getElementById('new-info-wrap').addEventListener('keyup', function(event) {
    log('keypress detected');
    if(document.activeElement != document.getElementById('new-account-submit')){
        event.preventDefault();
        if (event.keyCode === 13) {
            log('keycode = 13');
            document.getElementById('new-account-submit').click();
        }
    }
});

function getFormData(input) {
    if(input[0] == input[1]) {
        let resetData = {Password: input[0]};
        httpRequest(JSON.stringify(resetData));
        log('Information --> request sent');
    } else {
        document.getElementById('alert-message').style.visibility = 'visible';
    }
}

function httpRequest(data) {
    $('#dialog-confirm').dialog('open');
    let header = 'PasswordChangeSubmition=';
    let request = header + data;
    let url = Webroot + 'app/assets/php/reset_submit.php';
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            if(response=='SUCCESS!') {
                $('#dialog-confirm').dialog('open');
            } else {
                log(response);
            }
        }
    }
    xhr.send(request);
}

