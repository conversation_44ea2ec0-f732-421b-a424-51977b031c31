'use strict';
let projectRequest = sessionStorage.getItem('projectId').split(',');
projectRequest[0] = projectRequest[0].trim();
projectRequest[1] = projectRequest[1].trim();
let labname = projectRequest[0];
let expname = projectRequest[1];
projectRequest = JSON.stringify(projectRequest);
window.addEventListener('load', function() {
    let header = 'SetStage=';
    let request = header + projectRequest;
    let url = '../assets/php/staging.php';
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            log(response);
            if(response=='SessionSet') {
            	var coordinateScript = document.createElement('script');
				    coordinateScript.type = 'text/javascript';
				    coordinateScript.id = 'dataSrc';
				    coordinateScript.src = Webroot + 'data/' + labname.toUpperCase() + '/' + expname + '/' + expname + '_Theme_Coordinates.js';
				    document.body.appendChild(coordinateScript);
				var geneScript = document.createElement('script');
				    geneScript.type = 'text/javascript';
				    geneScript.src = Webroot + 'data/' + labname.toUpperCase() + '/' + expname + '/' + expname + '_Genes.js';
				    document.body.appendChild(coordinateScript);
				var geneScript = document.createElement('script');
				    geneScript.type = 'text/javascript';
				    geneScript.src = Webroot + 'app/assets/js/main.js';
				    document.body.appendChild(coordinateScript);  
				var statScript = document.createElement('script');
					geneScript.type = 'text/javascript';	
					geneScript.innerHTML = 'javascript: (function() {' +
        									'var script = document.createElement("script");' +
        									'script.onload = function() {' +
			            					'var stats = new Stats();' +
			            					'document.body.appendChild(stats.dom).setAttribute("id", "stats");' +
			            					'requestAnimationFrame(function loop() {' +
			                				'stats.update();' +
			                				'requestAnimationFrame(loop)' +
			            					'});' +
			        						'};' +
			        						'script.src = "https://rawgit.com/mrdoob/stats.js/master/build/stats.min.js";' +
			        						'document.head.appendChild(script);' +
			    							'})()';
			    removeLoading();
            } else {
                log('ERROR!');
            }
        }
    }
    xhr.send(request);
});
  
  function removeLoading() {
    var loading = document.getElementById('loadingScreen');
    var loadingImage = document.getElementById('loadingImage');
    loading.removeChild(loadingImage);
    document.body.removeChild(loading);
    log('creating help icons');
    let helpIconArray = document.getElementsByClassName('title');
    let doubleLabelIndex = 0;
    let labelText = '';
    for(let i=0;i<helpIconArray.length;i++) {
        log(helpIconArray[i].innerText);
        labelText = helpIconArray[i].innerText;
        labelText = labelText.replace(/ /g, '-');
        if(labelText == 'Filters') {
            doubleLabelIndex++;
            labelText = labelText + doubleLabelIndex.toString();
        }
        helpIconArray[i].innerHTML += '<i class="fa fa-question-circle fa-pull-right" aria-hidden="true" id="route-' + labelText + '"></i>';
    }
    helpIconArray = document.getElementsByClassName('fa fa-question-circle fa-pull-right');
    for(let i=0;i<helpIconArray.length;i++) {
        helpIconArray[i].addEventListener('click', function(event) {
            event.stopPropagation()
            helpRouting(event.target.id);
        });
    }          
    document.getElementById('help-tab').style.right = '11.6em';
  }