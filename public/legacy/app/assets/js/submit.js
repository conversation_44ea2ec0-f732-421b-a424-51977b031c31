"use strict";
var projectInfo = {};
var text;
var input;
var file;
var synonymValidation = 'warn';

document.getElementById('form-submit').addEventListener('click', function() {
    log('building form info object');
    formValidation();
});
document.getElementById('form-submit').addEventListener('keyup', function(event) {
    log('keypress detected');
    event.preventDefault();
    if (event.keyCode === 13) {
        log('keycode = 13');
        document.getElementById('form-submit').click();
    }
});
document.getElementById('info-wrap').addEventListener('keyup', function(event) {
    log('keypress detected');
    event.preventDefault();
    if (event.keyCode === 13) {
        log('keycode = 13');
        log(event.target.id)
        if(event.target.id!='copypaste' && event.target.id!='exp-list') {
            document.getElementById('form-submit').click();
        } else {
            log('nope');
        }
    }
});
// document.getElementById('uploadfiles').addEventListener('change', function(event) {
//     console.log('%cupload change event fired','color:yellow');
//     // openFile(event);
// });
document.getElementById('copypaste').addEventListener('input', function(event) {
    console.log('%ccopypaste input event fired','color:yellow');
    let text = event.target.value;
    cpInsert(text);
});
// function openFile(event) {
//     log('file uploading');
//     input = event.target;
//     if( input.files.length < 2 ) {
//         file = input.files[0];
//         if (file) {
//             if (file.name.substr(file.name.length - 3, file.name.length - 1).toLowerCase() == 'csv' ||
//                 file.name.substr(file.name.length - 3, file.name.length - 1).toLowerCase() == 'txt') {
//                 let reader = new FileReader();
//                 reader.onloadend = function() {
//                     console.log('reader onloadend event fired');
//                     text = reader.result;
//                     cpInsert(text);
//                 };
//                 reader.readAsText(file);
//             } else {
//                 log('file extension error --> file extension != csv or txt');
//                 setTimeout(function() {
//                     document.getElementById('filetype-error').innerHTML = '';
//                     document.getElementById('uploadfiles').value = '';

//                 }, 2000);
//                 document.getElementById('filetype-error').innerHTML = 'Uplaod .txt or .csv files only...';
//             }
//             log('sending to insert')
//             log('finished insertion')
//         }
//     } else {
//         console.log( input.files );
//         let inputFiles = Array.from( input.files );
//         document.getElementById('copypaste').value = '';
//         inputFiles.forEach((f) => {
//             let reader = new FileReader();
//             reader.onloadend = function() {
//                 text = reader.result;
//                 console.log('%cDEBUG!!!!','color:orange');
//                 // console.log(text);
//                 let tmpFileName = removeExtension(f.name);
//                 cpBatchInsert(text,tmpFileName);
//                 // console.log( text );
//             };
//             reader.readAsText(f);
//         });
//     }
// };

function removeExtension(filename) {
  return filename.substring(0, filename.lastIndexOf('.')) || filename;
}

function cpBatchInsert(cpText,fileName = '') {
    if(Array.isArray(cpText)) {
        cpText = cpText.toString();
    }
    let tempCheck = cpText;
    log('Batch Insert fired');
    tempCheck = tempCheck.toString();
    if(tempCheck.includes('\n')) {
        log('tempCheck contains linux newline characters');
    }
    if(tempCheck.includes('\r')){
        log('line breaks detected\nremoving R-line breaks');
        tempCheck = tempCheck.replace(/\r/g,'\n');
    }
    if(tempCheck.includes('\r\n')){
        log('line breaks detected\nremoving RN-line breaks');
        tempCheck = tempCheck.replace(/\r\n/g,'\n');
    }
    if(tempCheck.includes(' ')) {
        log('single spaces detected');
        tempCheck = tempCheck.replace(/ /g, '');
        log('spaces replaced with commas');
    }
    if(tempCheck.includes("\n\n")){
        log('double line breaks detected\nremoving double N-line breaks');
        tempCheck = tempCheck.replace(/\n\n/g,'\n');
    }
    if( Array.isArray( projectInfo.Entity_List )) {
        document.getElementById('copypaste').value += '\n+++' + fileName + '\n' + tempCheck;
        console.log(projectInfo.Entity_List);
        projectInfo.Entity_List.push('+++' + fileName);
        projectInfo.Entity_List = projectInfo.Entity_List.concat(tempCheck.split('\n'));
        console.log(projectInfo.Entity_List);
    } else {
        document.getElementById('copypaste').value = '+++' + fileName + '\n' + tempCheck;
        tempCheck = '+++' + fileName + '\n' + tempCheck;
        projectInfo.Entity_List = tempCheck.split('\n');
    }
    console.log('batch insert finallizing');
    console.log(projectInfo.Entity_List);
}

function submitProjectInfo(infoObj) {
    log('submitting project information');
    log(projectInfo);
    let url = "../assets/php/send.php";
    // let url = "../assets/php/testsend.php";
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            log(xhr.responseText);
        }
    };
    xhr.send(infoObj);
}

function submitCompBioProject(data) {
    log('submitting project information');
    log(projectInfo);
    // let url = "../assets/php/compbio_submit.php";
    let url = `${Webroot}legacy/app/assets/php/compbio_submit.php`;
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            log(xhr.responseText);
            updateLists();
        }
    };
    xhr.send(data);
}

// New version
// async function submitCompBioProject(data) {
//     log('Submitting project information');

//     // Set the URL for the request
//     let url = `${Webroot}legacy/app/assets/php/compbio_submit.php`;

//     try {
//         // Send the request asynchronously
//         let response = await fetch(url, {
//             method: "POST",
//             headers: {
//                 "Content-Type": "application/x-www-form-urlencoded"
//             },
//             body: data
//         });

//         // Process response once available
//         if (!response.ok) throw new Error(`HTTP error! Status: ${response.status}`);

//         let result = await response.text();
//         log(result);
//         updateLists();

//     } catch (error) {
//         console.error("Error submitting project:", error);
//     }
// }


/* Tagged for removal in future versions */
/* Switched to rpc call instead of calling run_py.php */
function runPyScript(data) {
    log('submitting project information');
    log(projectInfo);
    let url = "../assets/php/run_py.php";
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            log(xhr.responseText);
            updateLists();
        }
    };
    xhr.send(data);
}

function gatherInfo(name, contact, nConcepts) {
    console.log('gathering info')
    projectInfo.Laboratory_Name = labname;
    projectInfo.Id = id;
    projectInfo.Project_Name = name.replace(/ /g, '_').replace(/\./g, '_').replace(/[^a-z0-9_-]/gi,'');
    projectInfo.Project_Name = cleanString(projectInfo.Project_Name);
    projectInfo.Contact_Email = contact;
    if(nConcepts!='') {
        nConcepts = Number(nConcepts);
    }
    if(nConcepts<1) {
        nConcepts = 250;
    }
    if (projectInfo.Entity_List.length > 0 && projectInfo.Entity_List != '') {
        synonymValidation = true;
        document.getElementById('copypaste').classList.remove('invalid-input');
        cpGetExpression();
        if(synonymValidation === true) {
            console.log(projectInfo) 
            if( projectInfo.Entity_List.find(a => a.includes('+++')) === undefined ) {
                console.log('Regular Submission');
                projectInfo.Num_Entities = projectInfo.Entity_List.length;
                projectInfo.Entity_List = projectInfo.Entity_List.join('\n');
                log(projectInfo);
                log('Submitting Test!');
                submitProjectInfo('NewProject=' + JSON.stringify(projectInfo));
                submitCompBioProject(`CompBioProject=${JSON.stringify(projectInfo)}`);
                document.getElementById('info-wrap').blur();
                document.getElementById('form-submit').blur();
                $('#dialog-message').dialog('open');
                let inputs = document.getElementById('info-wrap').querySelectorAll('input');
                inputs[0].value = '';
                // inputs[1].value = '';
                document.getElementById('concept').value = 500;
                document.getElementById('copypaste').value = '';
                document.getElementById('exp-list').value = '';
                // document.getElementById('uploadfiles').value = '';
                document.getElementById('copypaste').classList.remove('invalid-input')
                projectInfo = {};
                updateLists();
            } else {
                console.log('Batch Submission!');
                let batchFileNames = projectInfo.Entity_List.filter( e => e.includes('+++'));
                let fNamesLength = batchFileNames.length;
                let batchNameIndices = filterIndices( projectInfo.Entity_List, e => e.includes('+++'));
                for( let i = 0; i < fNamesLength; i++ ) {
                    let copiedProjectInfo = JSON.parse(JSON.stringify( projectInfo ));
                    copiedProjectInfo.Project_Name = projectInfo.Project_Name + '_' + batchFileNames[i].replace('+++','');
                    if(( i + 1 ) == fNamesLength ) {
                        copiedProjectInfo.Entity_List = projectInfo.Entity_List.slice( batchNameIndices[i] + 1 );
                    } else {
                        copiedProjectInfo.Entity_List = projectInfo.Entity_List.slice( batchNameIndices[i] + 1, batchNameIndices[i + 1] );
                    }
                    copiedProjectInfo.Num_Entities = copiedProjectInfo.Entity_List.length;
                    copiedProjectInfo.Entity_List = copiedProjectInfo.Entity_List.join('\n');
                    log('Submitting Single of Batch');
                    console.log( copiedProjectInfo );
                    submitProjectInfo('NewProject=' + JSON.stringify(copiedProjectInfo));
                    submitCompBioProject(`CompBioProject=${JSON.stringify(copiedProjectInfo)}`);
                }
                document.getElementById('info-wrap').blur();
                document.getElementById('form-submit').blur();
                $('#dialog-message').dialog('open');
                let inputs = document.getElementById('info-wrap').querySelectorAll('input');
                inputs[0].value = '';
                inputs[1].value = '';
                document.getElementById('concept').value = 500;
                document.getElementById('copypaste').value = '';
                document.getElementById('exp-list').value = '';
                // document.getElementById('uploadfiles').value = '';
                document.getElementById('copypaste').classList.remove('invalid-input')
                projectInfo = {};
                updateLists();
            }
        } else if(synonymValidation == 'warn') {
            console.log('SV Warn')
            document.getElementById('info-wrap').blur();
            document.getElementById('form-submit').blur();
            $( "#dialog-syn-alert" ).dialog('open');
        } else {
            document.getElementById('copypaste').classList.add('invalid-input');
            document.getElementById('alert-msg').innerHTML = 'We did not recognize any of the entities within your input list.' +
                'Read through our notes above or check within our "help" section for the input list guidelines.  If you feel that you have ' +
                'reached this prompt due to a "CompBio" error or malfunction, please report the issue to one of our team members via email or ' +
                'the feedback button in the lower right side of the screen.<br>Thanks,<br>CompBio Team';
            document.getElementById('info-wrap').blur();
            document.getElementById('form-submit').blur();
            $('#alert').dialog('open');
        }
    } else {
        document.getElementById('copypaste').classList.add('invalid-input');
    }
}
const filterIndices = (a, pred) => a.reduce((acc, e, i) => {
  pred(e, i, a) && acc.push(i);
  return acc;
}, []);
function formValidation() {
    log('validating form values');
    let valid = true;
    let projectName = document.getElementById('pname').value.trim();
    let email = document.getElementById('contact').value.trim();
    let numConcept = document.getElementById('concept').value.trim();
    // Remove Context
    // let context = document.getElementById('context').value.trim();
    if (projectName == '' || duplicateNameCheck(projectName)) {
        document.getElementById('pname').classList.add('invalid-input');
        valid = false;
        console.log('Duplicate or Missing Project Name')
    } else {
        document.getElementById('pname').classList.remove('invalid-input');
    }
    if (email == '' || emailValidation(email) == false) {
        document.getElementById('contact').classList.add('invalid-input');
        valid = false;
        console.log('Invalid or Missing Email')
    } else {
        document.getElementById('contact').classList.remove('invalid-input');
    }
    if (valid) {
        console.log('Form Valid')
        gatherInfo(projectName, email, numConcept);
    }
}

function duplicateNameCheck(name) {
    return Array.from(
        document.getElementsByClassName('cb-project-names')
    )
    .concat(
        Array.from(
            document.getElementsByClassName('explorer-list-item')
        )
    )
    .map(
        element => element.innerText
    )
    .includes(name);
}

function cpInsert(cpText) {
    if(Array.isArray(cpText)) {
        cpText = cpText.toString();
    }
    let tempCheck = cpText;
    log('cpInsert fired');
    tempCheck = tempCheck.toString();
    if(tempCheck.includes('\n')) {
        log('tempCheck contains linux newline characters');
    }
    if(tempCheck.includes('\r')){
        log('line breaks detected\nremoving R-line breaks');
        tempCheck = tempCheck.replace(/\r/g,'\n');
    }
    if(tempCheck.includes('\r\n')){
        log('line breaks detected\nremoving RN-line breaks');
        tempCheck = tempCheck.replace(/\r\n/g,'\n');
    }
    if(tempCheck.includes(' ')) {
        log('single spaces detected');
        tempCheck = tempCheck.replace(/ /g, '');
        log('spaces replaced with commas');
    }
    if(tempCheck.includes("\n\n")){
        log('double line breaks detected\nremoving double N-line breaks');
        tempCheck = tempCheck.replace(/\n\n/g,'\n');
    }
    if(tempCheck.includes('\t')) {
        seperateEntsExps( tempCheck );
    } else {
        document.getElementById('copypaste').value = tempCheck;
        projectInfo.Entity_List = tempCheck.split('\n');
    }
    // if(projectInfo.Entity_List.length>0) {
    //     checkSynonyms(projectInfo.Entity_List.slice());
    // }
}

function seperateEntsExps( string ) {
    let allArray = string.split('\n');
    let entArray = [], expArray = [];
    allArray.forEach(( line ) => {
        if( line.includes('\t') ) {
            let lineArray = line.split('\t');
            entArray.push(lineArray[0].trim());
            expArray.push(lineArray[1].trim());
        }
    });
    document.getElementById('copypaste').value = entArray.join('\n');
    projectInfo.Entity_List = entArray;
    document.getElementById('exp-list').value = expArray.join('\n');
}

function cpGetExpression() {
    let list = document.getElementById('exp-list').value.trim();
    if(list!='') {
        log('cpInsert fired');
        if(typeof(list)!='string') {
            list = list.toString();
        }
        if(list.includes('\n')) {
            log('exp list contains linux newline characters');
        }
        if(list.includes('\r')){
            log('line breaks detected\nremoving R-line breaks');
            list = list.replace(/\r/g,'\n');
        }
        if(list.includes('\r\n')){
            log('line breaks detected\nremoving RN-line breaks');
            list = list.replace(/\r\n/g,'\n');
        }
        if(list.includes(' ')) {
            log('single spaces detected');
            list = list.replace(/ /g, '');
            log('spaces replaced with commas');
        }
        if(list.includes('\n\n')){
            log('double line breaks detected\nremoving double N-line breaks');
            list = list.replace(/\n\n/g,'\n');
        }
        document.getElementById('exp-list').value = list;
        projectInfo.Expression_List = list;
    }
}

async function checkSynonyms(list) {
    const debug = {};
    debug.list = [...list];
    console.log('Checking Synonyms....');
    console.log({'list':list});
    try {
        // const urls = await buildQuerys(list);
        // const promises = urls.map(url => fetch(url).then(r => r.json()));
        // const data = await Promise.all( promises ).then(r => [].concat.apply([], r));
        // const matchrate = await countValid(data);
        // debug.matchrate = matchrate;
        // debug.urls = [...urls];
        // debug.data = [...data];
        console.log(debug);
        debugLogging(JSON.stringify(debug));
        // if(matchrate < 1) {
        //     synonymValidation = false;
        // } else if(matchrate > 0 && matchrate < 50) {
        //     synonymValidation = 'warn';
        // } else {
        //     synonymValidation = true;
        // }
        synonymValidation = true;
    } catch ( error ) {
        console.log( error );
        debug.error = error;
        debugLogging(JSON.stringify(debug));
    }
}

function buildQuerys(strArray) {
    return new Promise( resolve => {
        const apiUrl = `${Webroot}cgi-bin/GetArticle?synonyms=`;
        const requests = [];
        while ( strArray.length > 0 ) {
            let links = `${apiUrl}${encodeURIComponent(strArray.shift().toLowerCase())}`;
            while ( links.length < 3600 && strArray.length > 0 ) {
                links += `+${encodeURIComponent(strArray.shift().toLowerCase())}`;
            }
            requests.push( links );
        }
        resolve( requests );
    } );
}

function countValid(array) {
    return new Promise( resolve => {
        let hits = 0;
        array.forEach(item => {
            if(item.ApprovedName !== '') {
                hits++;
            }
        });
        resolve(hits * 100 / array.length);
    });
}

function debugLogging(data) {
    console.log('debug sending');
    let url = "../assets/php/debug.php";
    let xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            console.log(xhr.responseText);
        } else {
            console.log('debug fail');
        }
    };
    xhr.send("debug=" + data);
}