"use strict";
var contactData, allSelections, list, sudoSelectedAcct = '', sudoSelectedExp = '';
tabArray.forEach(function(element) {
    element.addEventListener('click', function() {
        updateAccountLists();
    });
});
document.getElementById('edit-acctname-adminbtn').addEventListener('click', function() {
    if (sudoSelectedAcct != '') {
        let el = document.getElementById('sudo-tools');
        el.innerHTML = 'New Account Name<br><input type="text" id="name-edit"><br>';
        let title = $("#sudo-tools").dialog("option", "title");
        $("#sudo-tools").dialog("option", "title", "Account Edit");
       var height = $( ".selector" ).dialog( "option", "height" );
        $( "#sudo-tools" ).dialog( "option", "height", "auto" );
        var width = $( ".selector" ).dialog( "option", "width" );
        $( "#sudo-tools" ).dialog( "option", "width", 400 );
        $("#sudo-tools").dialog("option", "buttons",
            [{
                text: "Change Account Name",
                click: function() {
                    let newName = document.getElementById('name-edit').value.trim();
                    if(newName!=''){
                        newName = newName.replace(/ /g, '_');
                        let data = { 'function': 'sudoEditAcctName', 'parameters': { 'Account': sudoSelectedAcct, 'New': newName } };
                        data = JSON.stringify(data);
                        // log(data);
                        httpRequest(data);
                        $(this).dialog("close");
                    }
                }
            }]
        );
        $("#sudo-tools").dialog("open");
    }
});
document.getElementById('edit-acctemail-adminbtn').addEventListener('click', function() {
    if (sudoSelectedAcct != '') {
        let el = document.getElementById('sudo-tools');
        el.innerHTML = 'New Email<br><input type="text" id="email-edit"><br>';
        let title = $("#sudo-tools").dialog("option", "title");
        $("#sudo-tools").dialog("option", "title", "Email Change");
        var height = $( ".selector" ).dialog( "option", "height" );
        $( "#sudo-tools" ).dialog( "option", "height", "auto" );
        var width = $( ".selector" ).dialog( "option", "width" );
        $( "#sudo-tools" ).dialog( "option", "width", 400 );
        let buttons = $("#sudo-tools").dialog("option", "buttons");
        $("#sudo-tools").dialog("option", "buttons",
            [{
                text: "Change Email",
                click: function() {
                    let newEmail = document.getElementById('email-edit').value.trim();
                    if(newEmail!='' && emailValidation(newEmail)){
                        let data = {
                            'function': 'emailChange',
                            'parameters': {
                                'Account': sudoSelectedAcct,
                                'PrevEmail': document.querySelector("#sudo-table-info > table > tbody > tr:nth-child(3) > td").innerText,
                                'Email': newEmail
                            }
                        };
                        data = JSON.stringify(data);
                        // log(data);
                        httpRequest(data);
                        $(this).dialog("close");
                        sudoAccountSelect(sudoSelectedAcct);
                    }
                }
            }]
        );
        $("#sudo-tools").dialog("open");
    }
});
document.getElementById('edit-acctpass-adminbtn').addEventListener('click', function() {
    if (sudoSelectedAcct != '') {
        let el = document.getElementById('sudo-tools');
        el.innerHTML = 'New Password<br><input type="text" id="pass-edit"><br>';
        let title = $("#sudo-tools").dialog("option", "title");
        $("#sudo-tools").dialog("option", "title", "Password Change");
        var height = $( ".selector" ).dialog( "option", "height" );
        $( "#sudo-tools" ).dialog( "option", "height", "auto" );
        var width = $( ".selector" ).dialog( "option", "width" );
        $( "#sudo-tools" ).dialog( "option", "width", 400 );
        let buttons = $("#sudo-tools").dialog("option", "buttons");
        $("#sudo-tools").dialog("option", "buttons",
            [{
                text: "Change Password",
                click: function() {
                    let newPass = document.getElementById('pass-edit').value.trim();
                    if(newPass!=''){
                        let data = { 'function': 'sudoAcctPassChange', 'parameters': { 'Account': sudoSelectedAcct, 'Password': newPass } };
                        data = JSON.stringify(data);
                        // log(data);
                        httpRequest(data);
                        $(this).dialog("close");
                        sudoAccountSelect(sudoSelectedAcct);
                    }
                }
            }]
        );
        $("#sudo-tools").dialog("open");
    }
});
// document.getElementById('delete-project-adminbtn').addEventListener('click', function() {

// });
// document.getElementById('delete-acct-adminbtn').addEventListener('click', function() {

// });
document.getElementById('cp-project-adminbtn').addEventListener('click', function() {
    if(sudoSelectedAcct!='' && sudoSelectedExp!='') {
        let accountLists = [].slice.call(document.getElementsByClassName('account-list'));
        let accountListsItems = [].slice.call(accountLists[0].children);
        let acctOptionList = [];
        accountListsItems.forEach(function(item) {
            if(item.innerText!=sudoSelectedAcct) {
                acctOptionList.push('<option value="'+ item.innerText +'">'+ item.innerText + '</option>');
            }
        });
        document.getElementById('project-copy-from').innerHTML = '<h3>From:</h3>Account: ' + sudoSelectedAcct + '<br>Project: ' + sudoSelectedExp;
        document.getElementById('project-copy-to').innerHTML = '<h3>To:</h3>Account: <select id="cp-target-account">' + acctOptionList.join() + '</select>';
        $("#sudo-secondary-copy").dialog("open");
    }
});
document.getElementById('visualize-project-adminbtn').addEventListener('click', function() {
    if (sudoSelectedExp != '') {
        // newProjectWindow = window.open();
        // newProjectWindow.opener.focus();
        let data = {'function':'setSessionExp', 'parameters':{'Account':sudoSelectedAcct, 'Experiment':sudoSelectedExp}};
        data = JSON.stringify(data);
        // log(data);
        httpRequest(data);
    }
});
document.getElementById('display-input-adminbtn').addEventListener('click', function() {
    if (sudoSelectedExp != '') {
        let data = {'function':'returnInputList', 'parameters':{'Account':sudoSelectedAcct, 'Experiment':sudoSelectedExp}};
        data = JSON.stringify(data);
        // log(data);
        httpRequest(data);
    }
});
document.getElementById('edit-announcements-adminbtn').addEventListener('click', function() {
    log('edit-annoncments-adminbtn clicked');
    let el = document.getElementById('sudo-tools');
    el.innerHTML = 'Announcements:<br><textarea id="anon-edit">' +
            document.getElementById('announcements').innerText +
        '</textarea>';
    let title = $("#sudo-tools").dialog("option", "title");
    $("#sudo-tools").dialog("option", "title", "Editing...");
    var height = $( ".selector" ).dialog( "option", "height" );
    $( "#sudo-tools" ).dialog( "option", "height", 500 );
    var width = $( ".selector" ).dialog( "option", "width" );
    $( "#sudo-tools" ).dialog( "option", "width", 600 );
    let buttons = $("#sudo-tools").dialog("option", "buttons");
    $("#sudo-tools").dialog("option", "buttons",
        [{
            text: "Update",
            click: function() {
                let update = document.getElementById('anon-edit').value;
                let data = { 'function': 'sudoEditAnnouncements', 'parameters': { 'UpdatedInfo': update, 'Account': labname } };
                data = JSON.stringify(data);
                // log(data);
                httpRequest(data);
                $(this).dialog("close");
            }
        }]
    );
    $("#sudo-tools").dialog("open");
});
document.getElementById('req-autofill').addEventListener('click', function() {
    let node = document.getElementsByClassName('requests-action-container');
    if (node[0].style.visibility == 'visible') {
        let form = [].slice.call(document.getElementById('new-form-wrap').querySelectorAll('input'));
        form[0].value = returnParsedName(document.getElementById('pi').innerHTML.trim());
        form[1].value = document.getElementById('req-email').innerHTML.trim();
        autoPassWord(form[1].value);
    }
});
document.getElementById('delete-req').addEventListener('click', function() {
    let node = document.getElementsByClassName('requests-action-container');
    if (node[0].style.visibility == 'visible') {
        $("#request-delete-confirm").dialog('open');
    }
});
window.addEventListener('load', function() {
    let hideArr = [].slice.call(document.getElementsByClassName('sudo-hidden'));
    // log(hideArr);
    hideArr.forEach(function(el) {
        // log(el);
        el.style.visibility = 'hidden';
    });
    updateAccountLists();
    updateRequestsList();
    updateRequestItemsArray();
});

document.getElementById('new-email').addEventListener('keyup', function(event) {
    // log(event.target.value);
    autoPassWord(event.target.value);
});
document.getElementById('new-account-submit').addEventListener('click', function() {
    log('building form info object');
    let form = [].slice.call(document.getElementById('new-form-wrap').querySelectorAll('input'));
    createAccount(form);
});
document.getElementById('new-account-submit').addEventListener('keyup', function(event) {
    event.preventDefault();
    if (event.keyCode === 13) {
        log('keycode = 13');
        document.getElementById('new-account-submit').click();
    }
});
document.getElementById('new-form-wrap').addEventListener('keyup', function(event) {
    event.preventDefault();
    if (event.keyCode === 13) {
        log('keycode = 13');
        document.getElementById('new-account-submit').click();
    }
});

function returnParsedName(piLabName) {
    if(piLabName.indexOf(',') > 0) {
        let tmp = piLabName.split(',');
        let lastName = cleanString(tmp[0].trim().replace(/ /g, '_'));
        let firstName = cleanString(tmp[1].trim().replace(/ /g, '_'));
        let finalLabName = `${firstName.charAt(0)}${lastName}_LAB`.toUpperCase();
        return finalLabName;
    } else if(piLabName.indexOf(',') < 0 && piLabName.indexOf(' ') > 0) {
        let tmp = piLabName.split(' ');
        let firstName = cleanString(tmp[0].trim().replace(/ /g, '_'));
        let lastName = cleanString(tmp[1].trim().replace(/ /g, '_'));
        let finalLabName = `${firstName.charAt(0)}${lastName}_LAB`.toUpperCase();
        return finalLabName;
    } else {
        let singleName = cleanString(piLabName.trim().replace(/ /g, '_'));
        let finalLabName = `${singleName}_LAB`.toUpperCase();
        return finalLabName;
    }
}
function createAccount(form) {
    let mailformat = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
    if (form[1].value.trim().match(mailformat)) {
        let validForm = true;
        for (let i = 0; i < 4; i++) {
            form[i].classList.remove('invalid-input');
            form[i].value = form[i].value.trim();
            if (form[i].value == '') {
                form[i].classList.add('invalid-input');
                validForm = false;
            }
        }
        if (validForm) {
            if (form[2].value!='') {
                form[0].value = form[0].value.replace(/ /g, '_');
                let data = { 'function': 'createAccount', 'parameters': { 'AccountName': form[0].value.trim(), 'Email': form[1].value.trim(), 'Password': form[2].value.trim() } };
                if(form[3].checked) {
                    log('sudo priv');
                    data.parameters.Sudo = true;
                } else {
                    log('reg user');
                }
                // log(data)
                data = JSON.stringify(data);
                // log(data);
                httpRequest(data);
            } else {
                form[2].classList.add('invalid-input');
            }
        }
    } else {
        form[1].classList.add('invalid-input');
    }
}

function updateAccountLists() {
    let data = { 'function': 'accountLists', 'parameters': { 'Accounts': [] } };
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}

function updateRequestsList() {
    let data = { 'function': 'requestsList', 'parameters': { 'RequestElements': [], 'RequestData': [] } };
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}

function selectRequest(element) {
    let index = 0;
    list = document.querySelectorAll('.request-item');
    [].forEach.call(list, function(item) {
        item.classList.remove('active-project');
    });
    if (element.target.children.length > 0) {
        index = element.target.getAttribute('data-index');
    } else {
        index = element.target.parentElement.getAttribute('data-index')
    }
    list[index].classList.add('active-project');
    getRequestInfo(index);
}

function getRequestInfo(index) {
    let nodes = [].slice.call(document.getElementsByClassName('requests-action-container'));
    nodes.forEach(function(element) {
    	element.style.visibility = 'visible';
    });
    // node[0].style.visibility = 'visible';
    document.getElementById('pi').innerHTML = requestObjs[index]['Name'];
    document.getElementById('req-date').innerHTML = requestObjs[index]['Request_Date'];
    document.getElementById('org').innerHTML = requestObjs[index]['Organization'];
    document.getElementById('req-email').innerHTML = requestObjs[index]['Email'];
    document.getElementById('req-notes').innerHTML = requestObjs[index]['Notes'];
}

function updateRequestItemsArray() {
    let requestItemsArray = [].slice.call(document.querySelectorAll('li.request-item'));
    for (let i = 0; i < requestItemsArray.length; i++) {
        requestItemsArray[i].addEventListener('click', function(element) {
            selectRequest(element);
        });
    }
}

function sudoAccountSelect(id,name) {
    sudoSelectedAcct = name;
    sudoSelectedExp = '';
    // log(id);
    let data = { 'function': 'sudoAccountLists', 'parameters': { 'Id': id,'Account': name, 'Info': [], 'Projects': []}};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}

function sudoSelectExperiment(target) {
    // log(target.innerText);
    // log(acct.id);
    // console.log(acct);
    if( sudoSelectedAcct !== '') {
        sudoSelectedExp = target.innerText;
        let parentArr = [].slice.call(target.parentElement.children);
        parentArr.forEach(function(el) {
            el.classList.remove('active-project');
        });
        target.classList.add('active-project');
        let experiment = target.innerText;
        let expId = target.id;
        let data = { 'function': 'sudoExpInfo', 'parameters': { 'Account': sudoSelectedAcct, 'Experiment': experiment, 'ExpId': expId } };
        data = JSON.stringify(data);
        // log(data);
        httpRequest(data);
    } else {
        console.log('ERROR: Account Selection Failure!');
    }
}

function autoPassWord(text) {
    let d = new Date();
    let year = d.getFullYear();
    let month = (d.getMonth()+1>9)?(d.getMonth()+1).toString():'0'+(d.getMonth()+1).toString();
    let day = (d.getDate()>9)?d.getDate().toString():'0'+(d.getDate()).toString();
    let date = [year,month,day].join('-');
    let pass = date;
    if(text.search('@')>-1) {
        pass += text.substr(0, text.indexOf('@'))
    } else {
        pass += text;
    }
    document.getElementById('new-password').value = pass;
}

function sudoGetContactInformation() {
    let data = { 'function': 'returnContactInformation', 'parameters': { 'notification_information': [] }};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}
function emailSelectionDeselect() {
    let allSelections = Array.from($('.custom_selection_item'));
    allSelections.forEach((input) => {
        input.checked = false;
    });
    $('#email_sendto_selected').val('');
}
window.addEventListener( 'load', function() {
    sudoGetContactInformation();
    $('#include_replyto').val( currentAccountEmail );
    $('#notification_group_select').change(function(e) {
        let adminSelections,adminAndContributorSelections,requestorAndContributorSelections;
        let emailList = [];
        switch(this.value) {
            case "1":
                emailSelectionDeselect();
                allSelections = Array.from($('.selection_requestor_group'));
                allSelections.forEach((input) => {
                    input.checked = true;
                    emailList.push(input.value);
                });
                emailList = [...new Set(emailList.join('; ').split(';').map(email => email.trim()))];
                $('#email_sendto_selected').val(emailList.join('; '));
                break;
            case "2":
                emailSelectionDeselect();
                adminSelections = Array.from($('.selection_admin_group'));
                adminSelections.forEach((input) => {
                    input.checked = true;
                    emailList.push(input.value)
                });
                emailList = [...new Set(emailList.join('; ').split(';').map(email => email.trim()))];
                $('#email_sendto_selected').val(emailList.join('; '));
                break;
            case "3":
                emailSelectionDeselect();
                adminAndContributorSelections = Array.from($('.selection_admin_group'))
                    .concat(Array.from($('.selection_labadmins_group')));
                adminAndContributorSelections.forEach((input) => {
                    input.checked = true;
                    emailList.push(input.value);
                });
                emailList = [...new Set(emailList.join('; ').split(';').map(email => email.trim()))];
                $('#email_sendto_selected').val(emailList.join('; '));
                break;
            // case "4":
            //     allSelections = Array.from($('.custom_selection_item'));
            //     allSelections.forEach((input) => {
            //         input.checked = false;
            //     });
            //     requestorAndContributorSelections = Array.from($('.selection_contributor_group'))
            //     .concat(Array.from($('.selection_requestor_group')));
            //     requestorAndContributorSelections.forEach((input) => {
            //         input.checked = true;
            //         emailList.push(input.value);
            //     });
            //     $('#email_sendto_selected').val(emailList.join('; '));
            //     break;
            case "5":
                emailSelectionDeselect();
                break;
            default:
                console.log('Doing nothing...')
                break;
        }
    });
    document.getElementById('email_notification_tool_form').addEventListener('submit', function( e ) {
        event.preventDefault();
        submitEmailNotification();
    });
});

function submitEmailNotification() {
    let emailNotificationData = {
        replyTo: (  $('#include_replyto').is( ':checked' )) ? $('#include_replyto').val() : 0,
        selectedEmails: $('#email_sendto_selected').val(),
        manualEmails: $('#email_sendto_manual').val(),
        emailSubject: $('#email_subject').val(),
        emailBody: $('#email_body').val()
    }
    // console.log( emailNotificationData );
    let data = { 'function': 'sendEmailToolNotification', 'parameters': { 'notification_data': emailNotificationData }};
    data = JSON.stringify(data);
    // log(data);
    httpRequest(data);
}

function populateEmailSelect() {
    let lists = {
        sudos: [
            '<li class="list-group-item font-weight-bold">Sudo Admins <i class="fa fa-solid fa-angle-right"></i></li>'
        ],
        admins: [
            '<li class="list-group-item font-weight-bold">Lab Pi\'s and Lab Admins <i class="fa fa-solid fa-angle-right"></i></li>'
        ],
        labs: [
            '<li class="list-group-item font-weight-bold">Lab Users <i class="fa fa-solid fa-angle-right"></i></li>'
        ],
        all: [
            '<li class="list-group-item font-weight-bold">All Users <i class="fa fa-solid fa-angle-right"></i></li>'
        ]
    }
    // Sudo Admin
    // <li class="list-group-item"><input class="custom_selection_item selection_admin_group" type="checkbox" value="{{ $contact->email }}" /> {{ $contact->first_name }} {{ $contact->last_name }}</li>
    // Lab Admin
    // <li class="list-group-item"><input class="custom_selection_item selection_labadmins_group" type="checkbox" value="{{ $contact->email }}" /> {{ $contact->first_name }} {{ $contact->last_name }}</li>
    // Account
    // <li class="list-group-item"><input class="custom_selection_item selection_contributor_group" type="checkbox" value="{{ $contact->email }}" /> {{ $contact->first_name }} {{ $contact->last_name }}</li>
    // Notification Emails
    // <li class="list-group-item"><input class="custom_selection_item selection_requestor_group" type="checkbox" value="{{ $contact->email }}" /> {{ $contact->first_name }} {{ $contact->last_name }}</li>
    // contactData.account_contacts.forEach(( account ) => {
    //     if( account.sudo === true ) {
    //         lists.sudos.push( `<li class="list-group-item"><input class="custom_selection_item selection_admin_group" type="checkbox" value="${account.email}" /> ${account.name}</li>` );
    //     } else {
    //         lists.all.push( `<li class="list-group-item"><input class="custom_selection_item selection_contributor_group" type="checkbox" value="${account.email}" /> ${account.name}</li>` );
    //     }
    // });
    // contactData.notification_contacts.forEach(( email ) => {
    //     lists.all.push( `<li class="list-group-item"><input class="custom_selection_item selection_requestor_group" type="checkbox" value="${email}" /> ${email}</li>` );
    // });
    contactData.sudo_users.forEach(( user ) => {
        lists.sudos.push( `<li class="list-group-item"><input class="custom_selection_item selection_admin_group" type="checkbox" value="${user.email}" /> ${user.name}</li>` );
    });
    for (const lab in contactData.lab_admins) {
        let labEmails = contactData.lab_admins[lab].map(user => user.email);
        lists.admins.push(`<li class="list-group-item"><input class="custom_selection_item selection_labadmins_group" type="checkbox" value="${labEmails.join('; ')}" /> ${lab}</li>`);
    }
    for(const lab in contactData.lab_users) {
        let labEmails = contactData.lab_users[lab].map(user => user.email);
        lists.labs.push(`<li class="list-group-item"><input class="custom_selection_item selection_contributor_group" type="checkbox" value="${labEmails.join('; ')}" /> ${lab}</li>` );
    }
    contactData.all_users.forEach(( user ) => {
        lists.all.push( `<li class="list-group-item"><input class="custom_selection_item selection_requestor_group" type="checkbox" value="${user.email}" /> ${user.name}</li>` );
    });
    // contactData.lab_users.forEach(( lab ) => {
    //     lab.forEach(( user ) => {
    //         lists.labs.push( `<li class="list-group-item"><input class="custom_selection_item selection_contributor_group" type="checkbox" value="${user.email}" /> ${user.name}</li>` );
    //     });
    // });
    let mergedLists = [ ...lists.sudos, ...lists.admins, ...lists.labs, ...lists.all ].join('');
    let emailSelect = document.getElementById( 'custom_email_select');
    emailSelect.innerHTML = mergedLists;
    console.log( 'Selection Lists Populated' );
    console.log('Setting height...');
    $('#tab-10 > section').css('height', $('#tab-10 > section').height());
    console.log('height set!');
    $('.custom_selection_item').click(function(e) {
        console.log( 'selection clicked' );
        $('#notification_group_select').val('');
        let emailList = [];
        allSelections = Array.from($('.custom_selection_item'));
        allSelections.forEach((input) => {
            if(input.checked) {
                emailList.push(input.value);
            }
        });
        $('#email_sendto_selected').val(emailList.join('; '));
    });
}
