console.log('ThemelinkFileLoading');
let selectedThemelink = '';
window.addEventListener("DOMContentLoaded", (event) => {
    $(function() {
        $("#dialog-themelink").dialog({
            autoOpen: false,
            modal: true,
            width: 600,
            show: {
                effect: "scale"
            },
            hide: {
                effect: "scale"
            },
            buttons: {
                Ok: function() {
                    $(this).dialog('close');
                }
            },
            close: function() {
                document.getElementById('themelink-message').innerHTML = '';
            }
        });
    });
   //  const showCompAssertBtn = document.getElementById('show-assertions-btn');
   //  const showAssertLibsBtn = document.getElementById('show-assertlib-btn');
   //  const compAssertList = document.getElementById('completed-assert-generations');
   //  const assertLibsList = document.getElementById('assertion-eligible-libs');
   console.log('themelink JS loading');
    const visThemelinkBtn = document.getElementById('vis-themelink-btn');
    const themelinkFormDisplayBtn = document.getElementById('open-themelinkform-btn');
    const downLoadAssertFilesBtn = document.getElementById('dl-themelinkfiles-btn');
    //  const delAssertBtn = document.getElementById('delete-assert-btn');
    updateCompletedThemelink();
    visThemelinkBtn.addEventListener('click',function() {
      console.log('Visualize ThemeLink Comparison Btn Clicked');
        let url = `${Webroot}legacy/data/${labname.toUpperCase()}/account_info/themelinks/${selectedThemelink}/comparisons.pdf`;
      window.open(url, "_blank");
    });
    themelinkFormDisplayBtn.addEventListener('click',function() {
      console.log('Themelink Form Btn Clicked');
      showThemelinkForm();
    });
    downLoadAssertFilesBtn.addEventListener('click',function() {
      console.log('Themelink Download Btn Clicked');
      let url = `${Webroot}data/${labname.toUpperCase()}/account_info/themelinks/${selectedThemelink}/comparisons.pdf`;
      window.open(url);
    });
    document.getElementById('themelink-generation-form').addEventListener('submit', (event) => {
        event.preventDefault();
        verifyThemelinkSubmissionForm();
    });
});
async function verifyThemelinkSubmissionForm() {
    const themelinkData = {
        name: cleanString(document.getElementById('tl-generation-name').value.trim().replace(/ /g, "_").replace(/&/g, "").replace(/;/g, "")).replace(/[^a-z0-9_-]/gi,''),
        base: document.getElementById('tl-base-project').value,
        secondary: document.getElementById('tl-secondary-project').value,
        account: labname,
        AcctId: id
    };
    const duplicateNameResponse = await themelinkDuplicateNameCheck(themelinkData.AcctId, themelinkData.name);

    if (duplicateNameResponse.error) {
        console.error("Error checking duplicate name:", duplicateNameResponse.error);
        return;
    }

    if (duplicateNameResponse.exists) {
        document.getElementById('tl-generation-name').classList.add('is-invalid');
        return;
    } else {
        document.getElementById('tl-generation-name').classList.remove('is-invalid');
    }

    submitNewThemelink(themelinkData);
}
function submitNewThemelink(input) {
   //  let data = { "function": "createNewThemelink", "parameters": input };
    submitRequest(JSON.stringify(input));
    console.log(data);
    showThemelinkForm();
    document.getElementById('themelink-message').innerHTML = `<div class="text-center my-5"><div class="spinner-border text-light" style="width: 3rem; height: 3rem;" role="status"><span class="sr-only">Loading...</span></div><div><strong>Processing...</strong></div></div>`;
    $('#dialog-themelink').dialog('open');
}
function themelinkDuplicateNameCheck(userId, themelinkName) {
    const url = '../assets/php/themelinkDuplicateNameCheck.php';

    return fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ AcctId: userId, Name: themelinkName })
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error("Fetch error:", error);
            return { error: error.message }; // Return an error object instead of a raw error
        });
}
function submitRequest(data) {
    let header = 'createNewThemelink=';
    let request = header + data;
    let url = `${Webroot}legacy/app/assets/php/themelink_submission.php`;
    let xhr = new XMLHttpRequest();
    // xhr.timeout = 4000;
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    // xhr.ontimeout = function() {
    //     log('request-error -> ' + xhr.statusText);
    // }
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            console.log(response);
            document.getElementById('themelink-message').innerHTML = `<div class="text-center my-5"><div><strong>Themelink Comparison Complete.</strong></div></div>`;
            updateCompletedThemelink();
            // if(response != true) {
            //    console.log('ERROR: Themelink Warning/Error Thrown!')
            //    //  httpResponse(response);
            // } else {
            //     console.log('Themelink Comparison complete!');
            // }
        }
    }
    xhr.send(request);
}
function insertThemelinkList(themelinkArray) {
   console.log('inserting');
   const array = Object.values(themelinkArray);
   const themelinkList = document.getElementById('themelink-list');
   if( Array.isArray(array) && array.length > 0 ) {
      themelinkList.innerHTML = '';
      array.forEach((item) => {
         const li = document.createElement('LI');
         li.classList.add('themelink-list-item');
         li.classList.add('list-item');
         li.innerText = item.trim();
         themelinkList.appendChild(li);
         li.addEventListener('click', themelinkPointSelect);
      });
   } else {
      themelinkList.innerHTML = '<div style="margin: 5px">No Items To Display...</div>';
   }
}
function themelinkPointSelect(event) {
    clearActiveThemelinkListItem();
    selectedThemelink = event.target.innerText;
    hideThemelinkForm();
    event.target.classList.add('active-themelink');
    showThemelinkInfoTable();
}
function showThemelinkInfoTable() {
   console.log('Nothing Yet')
    const url = `${Webroot}legacy/data/${labname.toUpperCase()}/account_info/themelinks/${selectedThemelink}/info.json`;
    const opts = {"headers":{"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9","accept-language":"en-US,en;q=0.9","cache-control":"no-cache","pragma":"no-cache","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"none","sec-fetch-user":"?1","upgrade-insecure-requests":"1"},"referrerPolicy":"no-referrer-when-downgrade","method":"GET","mode":"cors","credentials":"include"};
    fetch(url, opts)
    .then((resp) => {
        if(!resp.ok) {
            throw new Error('Error: response ok failure');
        } else {
            return resp.json();
        }
    })
    .then((json) => {
        console.log(json);
        document.getElementById('tl-name').innerText = (typeof json.themelink_name === "undefined") ? 'No Data...' : json.themelink_name;
        document.getElementById('tl-creationdate').innerText = (typeof json.submission_timestamp === "undefined") ? 'No Data...' : json.submission_timestamp;
        document.getElementById('tl-basename').innerText = (typeof json.base_name === "undefined") ? 'No Data...' : json.base_name;
        document.getElementById('tl-secondaryname').innerText = (typeof json.secondary_name === "undefined") ? 'No Data...' : json.secondary_name;
      //   document.getElementById('themelink-info').classList.remove('d-none');
         $('#themelink-info').show();
    })
    .catch((error) => {
        console.log(error);
        document.getElementById('tl-name').innerText = 'No Data...';
        document.getElementById('tl-creationdate').innerText = 'No Data...';
        document.getElementById('tl-basename').innerText = 'No Data...';
        document.getElementById('tl-secondaryname').innerText = 'No Data...';
      //   document.getElementById('themelink-info').classList.remove('d-none');
       $('#themelink-info').show();
    });
}
function hideThemelinkInfoTable() {
    $('#themelink-info').hide();
    Array.from(document.getElementsByClassName('themelinktinfo-tablecell')).forEach(cell => cell.innerText = '');
}
function clearActiveThemelinkListItem() {
    Array.from(document.getElementsByClassName('themelink-list-item')).forEach(item => item.classList.remove('active-themelink'));
}
function hideThemelinkForm() {
    clearAssertionFormInvalidInputs();
    document.getElementById('tl-generation-name').value = '';
    Array.from(document.getElementsByClassName('tl-select')).forEach((select) => {
        select.innerHTML = '';
    });
    $("#create-themelink-form-section").hide();
    Array.from(document.getElementsByClassName('tl-btns')).forEach(btn => btn.removeAttribute('disabled'));
}
function showThemelinkForm() {
    hideThemelinkInfoTable();
    selectedThemelink = '';
    clearActiveThemelinkListItem();
    clearThemelinkFormInvalidInputs();
    document.getElementById('tl-generation-name').value = '';
    Array.from(document.getElementsByClassName('tl-select')).forEach((select) => {
        select.innerHTML = '';
    });
    addThemelinkSelectOptions();
    $("#create-themelink-form-section").show();
    Array.from(document.getElementsByClassName('tl-btns')).forEach(btn => btn.setAttribute('disabled', null));
}
function clearThemelinkFormInvalidInputs() {
    Array.from(document.getElementsByClassName('tl-form-input')).forEach(input => input.classList.remove('invalid-input'));
}
async function addThemelinkSelectOptions() {
    const projects = await getThemelinkEligibleProjects();
    console.log(projects);
    if(Array.isArray(projects)) {
        Array.from(document.getElementsByClassName('tl-select')).forEach((select) => {
            projects.forEach((project) => {
                const option = document.createElement('OPTION');
                option.value = project;
                option.innerText = project;
                select.appendChild(option);
            });
        });
    } else {
        console.log({'Error': 'Failure returning Themelink eligible projects array', 'ReturnValue': projects});
    }
}
function getThemelinkEligibleProjects() {
    const url = `${Webroot}legacy/app/assets/php/returnThemelinkEligible.php`;
    return fetch(url)
    .then(resp => resp.json())
    .catch(error => error);
}
async function updateCompletedThemelink() {
   const completedThemelinks = await getCompletedThemelinks();
    if(typeof completedThemelinks === 'object') {
      insertThemelinkList( completedThemelinks );
    } else {
        console.log({'Error': 'Failure returning Themelink eligible projects array', 'ReturnValue': completedThemelinks});
    }
}
function getCompletedThemelinks() {
   const url = `${Webroot}legacy/app/assets/php/returnThemelinkGenerations.php`;
   return fetch(url)
    .then(resp => resp.json())
    .catch(error => error);
}