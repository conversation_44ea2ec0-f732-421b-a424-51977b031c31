<?php 

echo 'start<br><br>';

include('../../../config/config.php');

$acctsJson = file_get_contents('../../../acct/accts.php');

$acctsJson = trim($acctsJson, "<?php '");

$acctsJson = json_decode($acctsJson, true);

$acctsPath = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data']);
$accountDirs = array_values(array_diff(scandir($acctsPath),['.','..']));

for($i=0;$i<count($accountDirs);$i++) {

	$infoDir = join(DIRECTORY_SEPARATOR, [$acctsPath, $accountDirs[$i], 'account_info']);

	if(!file_exists($infoDir)) {

		mkdir($infoDir, 0777);

	}

	$infoFile = join(DIRECTORY_SEPARATOR, [$infoDir, 'info.json']);
	
	if(!file_exists($infoFile)) {

		$infoWrap = [];
		$infoObj = (Object)[];

		for($z=0;$z<count($acctsJson);$z++) {

			if(strtoupper($acctsJson[$z]['Laboratory']) == strtoupper($accountDirs[$i])) {

				$infoObj->Laboratory = $acctsJson[$z]['Laboratory'];
				$infoObj->Email = $acctsJson[$z]['Email'];

				if(array_key_exists('Creation_Date', $acctsJson[$z])) {

					$infoObj->Creation_Date = $acctsJson[$z]['Creation_Date'];

				} else {
					
					$filetime =  filemtime($acctsPath.DIRECTORY_SEPARATOR.$accountDirs[$i]);
					$infoObj->Creation_Date = date("Y-m-d h:i:s",$filetime);
				
				}

				array_push($infoWrap, $infoObj);
				file_put_contents($infoFile, json_encode($infoWrap, JSON_PRETTY_PRINT));
				chmod($infoFile, 0777);				
				echo $acctsJson[$z]['Laboratory'].'<br>';

				break;

			}

		}

	}

}

?>