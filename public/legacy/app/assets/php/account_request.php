<?php
	include('../../../config/config.php');
	include('ipa.php');
	$ip = getRealIpAddr();
	$host = $_SERVER['SERVER_NAME'];
	date_default_timezone_set('America/Chicago');
	if(isset($_POST['Registration'])) {
		$log = array(date("Y-m-d h:i:s",time()));
		$data = json_decode($_POST['Registration'], true);
		$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'requests.json';
		$requestlist = json_decode(file_get_contents($path), true);
		$data['Request_Date'] = date("Y-m-d h:i:s",time());
		array_push($requestlist, $data);
		$file = fopen($path, 'w');
		flock($file, LOCK_EX);
		fwrite($file, json_encode($requestlist, JSON_PRETTY_PRINT));
		flock($file, LOCK_UN);
		fclose($file);


		$subject = 'Account Request: '.$data['Name'].' from '.$data['Organization'].' - '.$host;
		$to = '<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>';

		$message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

	<head>
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>Account Request</title>
	    <!--[if gte mso 9]><xml>
		      <o:OfficeDocumentSettings>
			    <o:AllowPNG/>
			    <o:PixelsPerInch>96</o:PixelsPerInch>
			  </o:OfficeDocumentSettings>
			</xml><![endif]-->
	    <style>
	    #title-wrap {
	        margin: auto 0;
	        font-size: 1.2rem;
	        text-align: center;
	        font-variant: small-caps;
	        font-weight: bold
	    }

	    table {
	        margin: auto;
	        border-collapse: collapse;
	        width: 100%
	    }

	    td,
	    th {
	        border: 1px solid #ddd;
	        text-align: left;
	        padding: 8px
	    }

	    #list {
	        text-align: center
	    }
	    </style>
	</head>

	<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
	    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
	    <div id="title-wrap"><h1 class="title">CompBio Account Request</h1>
	    </div>
	    <table width="100%" border="0" cellpadding="0" cellspacing="0" style="min-width: 100%;" role="presentation">
	        <tr>
	            <th>PI(Lab) Name: </th>
	            <td>' . $data['Name'] . '</td>
	        </tr>
	        <tr>
	            <th>Organization: </th>
	            <td>' . $data['Organization'] . '</td>
	        </tr>
	        <tr>
	            <th>Email: </th>
	            <td>' . $data['Email'] . '</td>
	        </tr>
	    </table>
	    <h3>Notes: (Optional) </h3>
	    <table width="100%" border="0" cellpadding="0" cellspacing="0" style="min-width: 100%;" role="presentation">
	        <tr>
	            <td id="list">' . $data['Notes'] . '</td>
	        </tr>
	    </table>
	</body>
	</html>';


		$headers = "MIME-Version: 1.0" . "\r\n";
		$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
		$headers .= 'From: <<EMAIL>>' . "\r\n";
		mail($to,$subject,$message,$headers);

		$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.'requests_log.txt';
		$entry = 'Name-> '.$data['Name'].', Org-> '.$data['Organization'].', Email-> '.$data['Email'];
		array_push($log, $entry);
		$file = fopen($path, 'a');
		flock($file, LOCK_EX);
		fwrite($file, implode(',', $log).PHP_EOL);
		flock($file, LOCK_UN);
		fclose($file);

		echo 'request submitted';
	}

	
