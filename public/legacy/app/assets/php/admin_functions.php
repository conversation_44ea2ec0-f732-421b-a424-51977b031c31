<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');
$defaultAssertionVersion = '1.0.1';
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON>vel and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
// use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\AssertionLibrary;
use App\Models\Folder;
use App\Models\Filter;
use Carbon\Carbon;
if(isset($_SESSION['LoggedIn']) && $_SESSION['LoggedIn'] === true) {
	$user_id = $_SESSION['Id'];
	$user = User::find($user_id);
}
if(isset($_SESSION['LoggedIn']))
{
	if(isset($_POST['PostRequest']))
	{
		$data = json_decode($_POST['PostRequest'], true);
		$function = $data['function'];
		if($function == 'reauthSession'){
			reauthSession($data['parameters']);
		} else if($function == 'logout')
		{
			$function();
		} else if($function == 'checkSessionStatus')
		{
				$prompt = $function();
				$data = array('switch_cmd' => 'SessionStatus', 'Prompt' => $prompt);
				$response = json_encode($data);
				print_r($response);
		} else
		{
			if($function == 'reauthSuccess')
			{
				if(isset($_SESSION['last_function']))
				{
					$function = $_SESSION['last_function'];
					unset($_SESSION['last_function']);
				}
				if(isset($_SESSION['last_parameters']))
				{
					$data['parameters'] = $_SESSION['last_parameters'];
					unset($_SESSION['last_parameters']);
				}
			}
			if($function != 'reauthSuccess')
			{
				$prompt = checkSessionStatus();
				if($prompt)
				{
					$_SESSION['last_function'] = $function;
					if(isset($data['parameters']))
					{
						$_SESSION['last_parameters'] = $data['parameters'];
					}
					$data = array('switch_cmd' => 'SessionStatus', 'Prompt' => $prompt);
					$response = json_encode($data);
					print_r($response);
				} else
				{
					$_SESSION['last_action'] = time();
					if(isset($data['parameters']))
					{
						$params = $data['parameters'];
						$function($params);
					} else
					{
						if (is_string($function))
						{
							$function();
						} else
						{
							file_put_contents(ABS_ROOT.'/acct/log/php_error.txt', serialize($data), FILE_APPEND);
						}
					}
				}
			} else
			{
				$data = array('switch_cmd' => 'DeadResponse', 'ReturnFrom' => 'reauthSuccess');
				$response = json_encode($data);
				print_r($response);
			}
		}
	}
} else 
{
	$data = array('switch_cmd' => 'ReturnRedirectLogin');
	$response = json_encode($data);
	print_r($response);
}


function updateLists($params)
{
/*
1. project name
2. creation date
3. size # of entities
4. saved profile data
<li class="row list-item project-list-item" onclick="selectExperiment('2020_07_22_BAL_ID_delta_naive_delta_UP')">
	<div class="col-6 cb-project-names">2020_07_22_BAL_ID_delta_naive_delta_UP</div>
	<div class="col-3 cb-project-timestamps">2022-09-18 07:29:14</div>
	<div class="col cb-project-num-ents">543</div>
	<div class="col cb-project-profile-data"><span class="pull-right dot"></span></div>
</li>
*/
    $debug = null;
    $id = $_SESSION['Id'];
    $user = User::find($id);
    $folders = $user->folders;
    if($params['Shared'] == true)
    {
        $cbProjects = $user->lab->sharedCbProjects->filter(function($project){
            return $project->folder_id == null;
        });
    } else
    {
        $cbProjects = $user->cbProjects()->where('folder_id', null)->get();
    }
    // $debug = null;
    // $debug = $cbProjects;

	$labname = (is_null($params['Account'])) ? $_SESSION['Laboratory'] : $params['Account'];

    $outerList = '';
    foreach($folders as $folder)
    {
        if($params['Shared'] === true)
        {
            $projects = $folder->projects()->where('shared', 1)->get();
        }
        else
        {
            $projects = $folder->projects;
        }
        if($params['Shared'] === true && $projects->count() > 0 || $params['Shared'] === false)
        {
            $outerList .= '<li class="list-item folder-list-btn" data-toggle="collapse" data-target="#folder-' . $folder->id . '" role="button" aria-expanded="false" aria-controls="folder-' . $folder->id . '" onclick="selectFolder(' . $folder->id . ')"><i id="folder-icon-' . $folder->id . '" class="fa fa-folder-o folder-icon" aria-hidden="true"></i>' . $folder->name . '</li>';
            $innerList = '<ul id="folder-' . $folder->id . '" class="collapse folder-list">';
            foreach($projects as $folderProject)
            {
                $innerList .=   '<li class="row list-item project-list-item folder-list-item" onclick="selectExperiment(\'' . $folderProject->name . '\',\'' . $folderProject->id . '\',\'' . $folder->name . '\',\'' . $folder->id . '\')">';
                $innerList .=   '	<div class="col-4 cb-project-names">' . $folderProject->name . '</div>';
                $innerList .=   '	<div class="col-3 cb-project-timestamps text-center">' . $folderProject->created_at .'</div>';
                $innerList .=	'	<div class="col cb-project-num-ents text-right">' . $folderProject->entity_count  .'</div>';
                if($folderProject->profiles()->exists())
                {
                    $innerList .=   '	<div class="col cb-project-profile-data nested-folder-item"><span class="pull-right dot"></span></div>';
                } else
                {
                    $innerList .=   '	<div class="col cb-project-profile-data nested-folder-item"><span class="pull-right dot dot-black"></span></div>';
                }
                // Checking for new shared property for sharing across labs
                if ($folderProject->shared === true) {
                    $innerList .=   '	<div class="col cb-project-shared"><span class="pull-right dot"></span></div>';
                } else {
                    $innerList .=   '	<div class="col cb-project-shared"><span class="pull-right dot dot-black"></span></div>';
                }
            }
            $innerList .= '</ul>';
            $outerList .= $innerList;
        }
    }
    array_push($params['folders'], $outerList);

    foreach ($cbProjects as $project)
    {
        $fileString =   '<li class="row list-item project-list-item standard-project-item';
        if($project->user_id != $id)
        {
            $fileString .= ' foreign-shared';
        }
        $fileString .=   '" onclick="selectExperiment(\'' . $project->name  . '\',\'' . $project->id . '\')">';
        $fileString .=   '	<div class="col-5 cb-project-names">' . $project->name . '</div>';
        $fileString .=   '	<div class="col-3 cb-project-timestamps">' . $project->created_at . '</div>';
        $fileString .=	 '	<div class="col cb-project-num-ents text-center">' . $project->entity_count . '</div>';
		  $dir = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}/{$project->name}/UserData/userProfileData.txt";
		//   $debug = $dir;
      //   if($project->profiles()->exists())
		if(file_exists($dir))
        {
            $fileString .=   '	<div class="col cb-project-profile-data"><span class="pull-right dot"></span></div>';
        } else
        {
            $fileString .=   '	<div class="col cb-project-profile-data"><span class="pull-right dot dot-black"></span></div>';
        }
        // Checking for new shared property for sharing across labs
        if ($project->shared === true) {
            $fileString .=   '	<div class="col cb-project-shared"><span class="pull-right dot"></span></div>';
        } else {
            $fileString .=   '	<div class="col cb-project-shared"><span class="pull-right dot dot-black"></span></div>';
        }

        $fileString .=   '</li>';
        $file = $fileString;
        array_push($params['complete'], $file);
    }

	$filters = $user->filters;
	if ($filters->count() > 0)
	{
		foreach ($filters as $filter)
		{
			$filter = "<li class='list-item filter-list-item' onclick='selectFilter(event.target)' data-id='".$filter->id."'>" . $filter->name . "</li>";
			array_push($params['filters'], $filter);
		}
	}
	if ($params['SharedBe'] === true)
	{
		$files = $user->lab->sharedBeProjects;
	} else
	{
		$files = $user->beProjects;
	}
	if ($files->count() > 0)
	{
		foreach ($files as $file)
		{
			$fileString =  '<li class="list-item explorer-list-item';
			if($file->user_id != $id)
			{
				$fileString .= ' foreign-shared';
			}
			$fileString .= '" onclick="selectExplorerProject(\'' . $file->name . '\',\'' . $file->id . '\')">' . $file->name;
			// if ($file->profiles()->exists())
				$dir = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}/{$file->name}/UserData/userProfileData.txt";
				if(file_exists($dir))
            {
                $fileString .= '<span class="dot"></span>';
            } else {
                $fileString .= '<span class="dot dot-black"></span>';
            }
				if ($file->shared === true) {
					$fileString .= '<span class="dot pull-right"></span>';
				} else {
					$fileString .= '<span class="dot dot-black pull-right"></span>';
				}
				$fileString .= '</li>';
			array_push($params['explorer'], $fileString);
		}
	}
	// if (isset($projectInfo) && array_key_exists('aers_projects', $projectInfo))
	// {
	// 	$files = (is_null($projectInfo['aers_projects'])) ? [] : $projectInfo['aers_projects'];
	// 	natcasesort($files);
	// 	foreach ($files as $file)
	// 	{
	// 		$hasData = false;
	// 		$profileDataPath = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', strtoupper($labname), $file, 'UserData', 'userProfileData.txt']);
	// 		if (file_exists($profileDataPath))
	// 		{
	// 			$profileData = json_decode(file_get_contents($profileDataPath), true);
	// 			if (count($profileData) > 0)
	// 			{
	// 				$hasData = true;
	// 			}
	// 		}
	// 		if ($hasData)
	// 		{
	// 			$file =  '<li class="list-item aers-list-item" onclick="selectAersProject(\'' . $file . '\')">' . $file . '<span class="pull-right dot"></span></li>';
	// 		} else {
	// 			$file =  '<li class="list-item aers-list-item" onclick="selectAersProject(\'' . $file . '\')">' . $file . '</li>';
	// 		}
	// 		array_push($params['aers'], $file);
	// 	}
	// }
	/*
	 * Check for currently running batch jobs
	 */
	$current_batch_jobs = runningBatchJobs($labname);
	if(is_array($current_batch_jobs))
	{
		$batch_files = array_shift($current_batch_jobs);
		$data = array('switch_cmd' => 'UpdateLists', 'Completed' => $params['complete'], 'Failed' => $params['failed'], 'Folders' => $params['folders'], 'Filters' => $params['filters'], 'explorer_projects' => $params['explorer'], 'aers_projects' => $params['aers'], 'Labname' => $labname, 'BatchFiles' => $batch_files, 'BatchJobs' => $current_batch_jobs);
		$response = json_encode($data);
		print_r($response);
		exit();
	}
	$data = array('switch_cmd' => 'UpdateLists', 'Completed' => $params['complete'], 'Failed' => $params['failed'], 'Folders'=>$params['folders'], 'Filters' => $params['filters'], 'explorer_projects' => $params['explorer'], 'aers_projects' => $params['aers'], 'Labname' => $labname, 'Debug' => $debug);
	$response = json_encode($data);
	print_r($response);
}
function runningBatchJobs($account)
{
	return false;
	$upcase_account = strtoupper($account);
	$const = ABS_ROOT;
	if(!file_exists("{$const}/data/{$upcase_account}/processing/batch"))
	{
		mkdir("{$const}/data/{$upcase_account}/processing/batch",0777);
		// chmod("{$const}/data/{$upcase_account}/processing/batch",0777);
	}
	chdir("{$const}/data/{$upcase_account}/processing/batch");
	$json_files = glob('*.json');
	if(count($json_files) > 0)
	{
		$batch_jobs = [$json_files];
		foreach($json_files as $file)
		{
			array_push($batch_jobs,json_decode(file_get_contents($file)));
		}
		return $batch_jobs;
	} else
	{
		return false;
	}
}

function cancelBatchProcess($params)
{
	$account = strtoupper($params['Account']);
	$filename = $params['Filename'];
	$const = ABS_ROOT;
	$path = "{$const}/data/{$account}/processing/batch/{$filename}";
	if(file_exists($path))
	{
		unlink($path);
		$message = 'File found and removed successfully';
	} else
	{
		$message = 'File NOT found and process cancelled successfully';
	}
	$data = array('switch_cmd' => 'BatchCanceled','Message'=> $message);
	$response = json_encode($data);
	print_r($response);
}

function createNewFilter($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$account = strtoupper($params['Account']);
	$filterName = $params['FilterName'];
	$baseProject = $params['Base'];
	$project2 = $params['Project2'];

	// $dirArray = array(ABS_ROOT,'data',strtoupper($account), 'account_info', 'filters');
	// $dir = join(DIRECTORY_SEPARATOR, $dirArray);
	$acctPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$account}";
	$dir = "/{$acctPath}/account_info/filters";

	if(!file_exists($dir))
	{
		mkdir($dir, 0777);
	}

	$baseJsPath = "/{$acctPath}/{$baseProject}/{$baseProject}_Theme_Coordinates.js";

	if(file_exists($baseJsPath))
	{
		$project2JsPath = "/{$acctPath}/{$project2}/{$project2}_Theme_Coordinates.js";
		if(file_exists($project2JsPath))
		{
			$baseJs = file_get_contents($baseJsPath);
			$baseJs = rtrim(rtrim(rtrim(rtrim(trim($baseJs, 'var data = ')) , '];')) , ',').']';
			$baseJs = json_decode($baseJs, true);

			$project2Js = file_get_contents($project2JsPath);
			$project2Js = rtrim(rtrim(rtrim(rtrim(trim($project2Js, 'var data = ')) , '];')) , ',').']';
			$project2Js = json_decode($project2Js, true);

			$baseConcepts = [];
			$project2Concepts = [];

			foreach($baseJs as $baseIndex)
			{
				$tempConcepts = [];
				$tempConcepts = array_keys($baseIndex['Concepts']);
				$baseConcepts = array_merge($baseConcepts, $tempConcepts);
			}

			foreach($project2Js as $project2Index)
			{
				$tempConcepts = [];
				$tempConcepts = array_keys($project2Index['Concepts']);
				$project2Concepts = array_merge($project2Concepts, $tempConcepts);
			}

			$baseConcepts = array_map('strtolower', $baseConcepts);
			$project2Concepts = array_map('strtolower', $project2Concepts);

			if($baseConcepts != null && $project2Concepts != null)
			{
				$filterList = array_intersect($baseConcepts, $project2Concepts);
				$filterList = array_values(array_unique($filterList));

				if(count($filterList) > 0)
				{
					if(!file_exists("{$dir}/{$filterName}"))
					{
						mkdir("{$dir}/{$filterName}", 0777);

					}

					$filterFile = json_encode($filterList);
					file_put_contents("{$dir}/{$filterName}/filter.json", $filterFile);

					$creationDate = date("Y-m-d h:i:s",time());
					$listLength = count($filterList);

					$filterInfo = [
						'FilterName' => $filterName,
						'CreationDate' => $creationDate,
						'BaseProject'=> $baseProject,
						'Project2' => $project2,
						'ListLength' => $listLength
					];

					$filterInfoFile = json_encode($filterInfo);

					file_put_contents("{$dir}/{$filterName}/filterInfo.json", $filterInfoFile);

					Filter::create([
						'user_id' => $user->id,
						'name' => $filterName,
						'base' => $baseProject,
						'secondary' => $project2,
						'list' => $filterList,
					]);

					$data = array('switch_cmd' => 'FilterCreated');
				   $response = json_encode($data);
				   print_r($response);

				}
			}
		}
	}
}

function createFilterProject($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$lab = $user->lab;
	$account = $lab->name;
	$filterName = $params['FilterName'];
	$baseProject = $params['Base'];
	$baseProjectId = $params['BaseId'];
	$project2 = $params['Project2'];
	$project2Id = $params['Project2Id'];
	$labPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}";
	$projectPath = "{$labPath}/{$filterName}";
	$status = '';
	if(!file_exists($projectPath))
	{
		$baseInputPath = "${labPath}/{$baseProject}/Input/input.txt";
		if(file_exists($baseInputPath))
		{
			$project2InputPath = "{$labPath}/{$project2}/Input/input.txt";
			if(file_exists($project2InputPath))
			{
				$baseInput = trim(file_get_contents($baseInputPath));
				$baseInput = explode("\n", $baseInput);
				$baseInput = array_map('strtolower', $baseInput);

				$project2Input = trim(file_get_contents($project2InputPath));
				$project2Input = explode("\n", $project2Input);
				$project2Input = array_map('strtolower', $project2Input);

				if($baseInput != null && $project2Input != null)
				{
					$filterList = array_intersect($baseInput, $project2Input);
					$filterList = array_values(array_unique($filterList));

					if(count($filterList) > 1)
					{
						$status = 'Success';

					} else
					{
						$status = 'OverlapQuantity';
					}
				} else
				{
					$status = 'NullInputList';
					$filterList = [];
				}
			} else
			{
				$status = 'Project2InputList';
				$filterList = [];
			}
		} else
		{
			$status = 'BaseInputList';
			$filterList = [];
		}
	} else
	{
		$status = 'DuplicateProjectName';
		$filterList = [];
	}

	$data = array('switch_cmd' => 'FilterProjectReturn', 'status' => $status, 'list' => $filterList, 'project' => $filterName);
	$response = json_encode($data);
	print_r($response);
}

function getFilterInfo($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$filterName = $params['FilterName'];
	$filter = $user->filters->where('name', $filterName)->first();
	if($filter)
	{
		$data = array(
			'switch_cmd' => 'ReturnFilterInfo', 
			'FilterName' => $filter->name, 
			'CreationDate' => $filter->created_at, 
			'Base' => $filter->base, 
			'Project2' => $filter->secondary, 
			'ListLength' => count($filter->list)
		);		
	} else
	{
		$data = array('switch_cmd' => 'ReturnFilterInfo', 'FilterName' => 'NoFilterFound', 'CreationDate' => 'NoFilterFound', 'Base' => 'NoFilterFound', 'Project2' => 'NoFilterFound', 'ListLength' => 'NoFilterFound');
	}
	$response = json_encode($data);
	print_r($response);
}

function getFilterList($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$filterName = $params['FilterName'];
	$filter = $user->filters->where('name', $filterName)->first();
	if($filter) {
		$filterList = $filter->list;
		$filterStr = implode("\n", $filterList);
	} else {
		$filterStr = 'NoList';
	}
	$data = array('switch_cmd' => 'InputList', 'List' => $filterStr );
   $response = json_encode($data);
   print_r($response);
}

function deleteFilter($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$account = strtoupper($params['Account']);
	$filterName = $params['FilterName'];
	$acctPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$account}";
	$dir = "/{$acctPath}/account_info/filters/{$filterName}";
	$filter = Filter::where('user_id', $user->id)->where('name', $filterName)->first();
	if(!is_null($filter)) {
		$filter->delete();
	}
	if(file_exists($dir))
	{
		recursiveDelete($dir);
	}
	$data = array('switch_cmd' => 'FilterCreated');
    $response = json_encode($data);
    print_r($response);
}

function recursiveDelete($dir)
	{
    if(is_file($dir))
	{
        return unlink($dir);
    } elseif (is_dir($dir))
	{
        $scan = glob(rtrim($dir,'/').'/*');
        foreach($scan as $index=>$path)
		{
            recursiveDelete($path);
        }
        return @rmdir($dir);
    }
}

function setSessionExp($params)
{
	if(!isset($_SESSION)){
		session_start();
	}
	$expname = $params['Experiment'];
	$labname = $params['Account'];
	$projectId = $params['ExpId'];
	$project = CbProject::find($projectId) ?? BeProject::find($projectId);
	$id = $params['Id'] ?? $_SESSION['Id'];
	$user = User::find($id);
	$account = strtoupper($labname);
	$key = uniqid();
	$_SESSION['ProjectKeys'][$key] = ['Account'=>strtoupper($labname),'Project'=>$expname, 'ProjectId'=>$projectId];
	$_SESSION['ExpName'] = $expname;
	$acctPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}";
	$path = "{$acctPath}/{$expname}/Input";
	/* Check if bio-explorer project */
	$bioExplorerProject = (isset($params['BioExp']) && $params['BioExp'] === true) ? true : false;
	/* Check if aers project */
	$aersProject = false;
	if($bioExplorerProject === false && !isset($params['BioExp'])) {
		if(file_exists($path.DIRECTORY_SEPARATOR.'info.json'))
		{
			$projectInfo = json_decode(file_get_contents($path.DIRECTORY_SEPARATOR.'info.json'));
			if(isset($projectInfo->Bio_Explorer) && $projectInfo->Bio_Explorer)
			{
				$bioExplorerProject = true;
			} elseif(isset($projectInfo->AERS_Project) && $projectInfo->AERS_Project)
			{
				$aersProject = true;
			}
		}
	}
	if($bioExplorerProject === false) {
		$project = CbProject::find($projectId);
		if(!is_null($project)) {
			$directory = $project->directory;
			$_SESSION['ProjectKeys'][$key]['Directory'] = $directory;
			$entitylist = $project->list;
			$expressionlist = $project->expression;
			if(is_null($expressionlist)) {
				unset($expressionlist);
			} else {
				$expressionlist = implode("\n",$expressionlist);
			}
		}
		if(is_null($entitylist)) {
			$dir = "{$path}/input.txt";
			if(file_exists("{$directory}/Input/input.txt")) {
				$entitylist = file_get_contents("{$directory}/Input/input.txt");
			} elseif(file_exists($dir)) {
				$entitylist = file_get_contents($dir);
			}
		} else {
			$entitylist = implode("\n",$entitylist);
		}
		if(!isset($expressionlist)) {
			$dir = "{$path}/expression.txt";
			if(file_exists("{$directory}/Input/expression.txt")) {
				$entitylist = file_get_contents("{$directory}/Input/expression.txt");
			} elseif(file_exists($dir))
			{
				$expressionlist = file_get_contents($dir);
			} 
		}
	} else {
		$project = BeProject::find($projectId);
		$_SESSION['ProjectKeys'][$key]['Directory'] = $project->directory;
	}
	if(isset($entitylist) && isset($expressionlist)){
		$data = array('switch_cmd' => 'SetExp', 'URL' => CBV_WEBROOT."/?$key", 'Key' => $key, 'ProjectAcct' => $labname, 'Project' => $expname, 'Entity_List' => $entitylist, 'Expression_List' => $expressionlist ?? null, "BioExplorer" => $bioExplorerProject, 'AERS' => $aersProject, 'Id' => $id, 'ProjectId' => $projectId);
	} elseif(isset($entitylist)) {
		$data = array('switch_cmd' => 'SetExp', 'URL' => CBV_WEBROOT."/?$key", 'Key' => $key, 'ProjectAcct' => $labname, 'Project' => $expname, 'Entity_List' => $entitylist, "BioExplorer" => $bioExplorerProject, 'AERS' => $aersProject, 'Id' => $id, 'ProjectId' => $projectId);
	} else {
		$data = array('switch_cmd' => 'SetExp', 'URL' => CBV_WEBROOT."/?$key", 'Key' => $key, 'ProjectAcct' => $labname, 'Project' => $expname, "BioExplorer" => $bioExplorerProject, 'AERS' => $aersProject, 'Id' => $id, 'ProjectId' => $projectId);
	}
	if(file_exists($project->directory)) {
		$aar = generateAutoAnnotationFile($labname, $expname, $project->directory);
	} else {
		$aar = generateAutoAnnotationFile($labname, $expname);
	}
	$data['AAR'] = $aar;
	$response = json_encode($data);
	print_r($response);
}
function generateAutoAnnotationFile($labname,$expname,$path = null)
{
	if(!$path)
	{
		$lab = Lab::where('name', $labname)->first();
		if($lab) {
			$path = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$lab->directory}/{$expname}";
		} else {
			$path = $path = config('app.custom_env.ACCOUNT_STORAGE_ROOT') . "/" . strtoupper($labname) . "/" . $expname;
		}
	}
	if (file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json')
		&& '' != file_get_contents($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json'))
	{
		return true;
	} else if (file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json')
		&& '' == file_get_contents($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json')
		|| !file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json')
		&& file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.txt'))
	{
		if (file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json'))
		{
			unlink($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json');
		}
		$data_path = $path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.txt';
		$parsed = processFullAnnotationFile($data_path);
		$json = json_encode($parsed, JSON_INVALID_UTF8_SUBSTITUTE);
		$json_path = str_replace('_Auto_Annotation.txt', '_Auto_Annotation.json', $data_path);
		file_put_contents($json_path, $json);
		return true;
	} else if(!file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.json')
		&& !file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.txt'))
	{
		if(file_exists($path . DIRECTORY_SEPARATOR . $expname . '_cd_theme_matrix.txt')
			&& file_exists($path . DIRECTORY_SEPARATOR . $expname . '_Theme_Coordinates.js'))
		{
			if(file_exists('/usr/local/bin/plabels')
				&& file_exists('/var/lib/compbio/Auto_Annotation_Labels.txt')
				&& file_exists('/var/lib/compbio/PubMed_Word_Frequency.txt')
				&& file_exists('/var/lib/compbio/MasterGeneSynonymInput.txt'))
			{
				$cmd = '/usr/local/bin/plabels  /var/lib/compbio/Auto_Annotation_Labels.txt /var/lib/compbio/PubMed_Word_Frequency.txt /var/lib/compbio/MasterGeneSynonymInput.txt ';
				$cmd .= $path . DIRECTORY_SEPARATOR . $expname . '_cd_theme_matrix.txt ';
				$cmd .= $path . DIRECTORY_SEPARATOR . $expname . '_Theme_Coordinates.js ';
				$cmd .= $path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.txt';
				list($r_cmd, $o, $r) = executeAutoAnnotationCmd($cmd);
				$data_path = $path . DIRECTORY_SEPARATOR . $expname . '_Auto_Annotation.txt';
				if(file_exists($data_path))
				{
					$parsed = processFullAnnotationFile($data_path);
					$json = json_encode($parsed, JSON_INVALID_UTF8_SUBSTITUTE);
					$json_path = str_replace('_Auto_Annotation.txt', '_Auto_Annotation.json', $data_path);
					file_put_contents($json_path, $json);
					return true;
				}
			} else
			{
				return 'False 3';
			}
		} else
		{
			return 'False 2';
		}
	} else
	{
		return 'False 1';
	}
}
function executeAutoAnnotationCmd($command)
{
	exec($command, $output, $return);
	return [$command, $output, $return];
}
function processFullAnnotationFile($file_path)
{
	$file = file_get_contents($file_path);
	$final = array();
	$exp = "/Theme [0-9]+/";
	$annotations = preg_split($exp, $file);
	$header = array_shift($annotations);
	$tmpHeader = (object)[];
	$tmpHeader->name = 'header';
	$tmpHeader->annotations = $header;
	$index = 1;
	foreach ($annotations as $annotation) {
		$tmp = (object)[];
		$tmp->name = 'Theme ' . $index;
		$annotation = trim($annotation);
		$annotation = explode("\n", $annotation);
		foreach ($annotation as $key => $value) {
			$annotation[$key] = trim($value);
		}
		$tmp->annotations = array_filter($annotation);
		$index++;
		array_push($final, $tmp);
	}
	array_unshift($final,$tmpHeader);
	return $final;
}
function returnTrashDirPath($path) {
    $parts = explode('/', rtrim($path, '/')); // Split path into parts
    if (count($parts) < 2) {
        return false; // Ensure there's enough depth in the path
    }

    $lastDir = array_pop($parts); // Get the last directory
    $parts[] = 'trash'; // Add 'trash' before the last directory
    $parts[] = $lastDir; // Append the last directory back

    return implode('/', $parts); // Rebuild the path
}

function deleteProject($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$labname = $params['Account'];
	$expname = $params['Experiment'];
	$expId = $params['ExpId'];
	$status = 'failed';
	$msg = '';
	$user_id = $_SESSION['Id'];
	
	if(!empty($expId) && !empty($user_id)) {
		if($user->id === $user_id) {
			$project = CbProject::find($expId);
			if(!is_null($project)) {
				if(!file_exists($project->directory) && !empty($project->name)) {
					$dir = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}/{$project->name}";
				} else {
					$dir = $project->directory;
				}
				if(!empty($dir) && file_exists($dir)) {
					$trashDir = returnTrashDirPath($dir);
					if(!empty($trashDir)) {
						if(file_exists($trashDir))
						{
							$incrementdir = $trashDir;
							$i=1;
							while(file_exists($incrementdir))
							{
								$numstr = "$i";
								$incrementdir = $trashDir."($numstr)";
								$i++;
							}
							$trashDir = $incrementdir;
						}
						$rn = rename($dir,$trashDir);
						$ar = $project->delete();
						if($rn === true && $ar > 0) {
							$status = 'success';
						} else {
							if($rn === false) {
								$msg = 'Filesystem rename failed.';
							}
							if($ar <= 0) {
								$msg .= 'DB delete returned a 0 or less affected records.';
							}
						}
						
					} else {
						$msg = 'DirPath to TrashPath conversion failed.  Contact admin or support team.';
					}
				} else {
					$msg = 'Missing project directory OR project cannot be found in filesystem at location stored in db.';
				}
			} else {
				$msg = 'Project not found in database.';
			}
		} else {
			$msg = 'Current User does not own the selected Project.';
		}
	} else {
		$msg = 'ExpId NOT received.';
	}

	$data = array('switch_cmd' => 'Delete', 'Experiment' => $expname, 'Status' => $status, 'Msg' => $msg);
	$response = json_encode($data);
	print_r($response);
}

function deleteBioProject($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$user_id = $_SESSION['Id'];
	$expname = $params['Project'];
	$expId = $params['ProjectId'];
	$status = 'failed';
	$msg = '';
	if(!empty($expId) && !empty($user_id)) {
		if($user->id === $user_id) {
			$project = BeProject::find($expId);
			if(!is_null($project)) {
				if(!file_exists($project->directory) && !empty($project->name)) {
					$dir = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}/{$project->name}";
				} else {
					$dir = $project->directory;
				}
				if(!empty($dir) && file_exists($dir)) {
					$trashDir = returnTrashDirPath($dir);
					if(!empty($trashDir)) {
						if(file_exists($trashDir))
						{
							$incrementdir = $trashDir;
							$i=1;
							while(file_exists($incrementdir))
							{
								$numstr = "$i";
								$incrementdir = $trashDir."($numstr)";
								$i++;
							}
							$trashDir = $incrementdir;
						}
						$rn = rename($dir,$trashDir);
						$ar = $project->delete();
						if($rn === true && $ar > 0) {
							$status = 'success';
						} else {
							if($rn === false) {
								$msg = 'Filesystem rename failed.';
							}
							if($ar <= 0) {
								$msg .= 'DB delete returned a 0 or less affected records.';
							}
						}
					} else {
						$msg = 'DirPath to TrashPath conversion failed.  Contact admin or support team.';
					}
				} else {
					$msg = 'Missing project directory OR project cannot be found in filesystem at location stored in db.';
				}
			} else {
				$msg = 'Project not found in database.';
			}
		} else {
			$msg = 'Current User does not own the selected Project.';
		}
	} else {
		$msg = 'ExpId NOT received.';
	}
	
	$data = array('switch_cmd' => 'Delete', 'Experiment' => $expname, 'Status' => $status, 'Msg' => $msg);
	$response = json_encode($data);
	print_r($response);
}

function deleteBioProjectOld($params)
{
	if(isset($params['Account']) && isset($params['Project']))
	{
		$account = strtoupper($params['Account']);
		$project = $params['Project'];
		$src = join(DIRECTORY_SEPARATOR, [ABS_ROOT,'data',$account,$project]);
		if(file_exists($src))
		{
			$dest = join(DIRECTORY_SEPARATOR, [ABS_ROOT,'data',$account,'trash',$project]);
			$result = rename($src,$dest);
			if($result)
			{
				$path = join(DIRECTORY_SEPARATOR , [ABS_ROOT,'data',$account,'account_info','projects.json']);
				if(file_exists($path))
				{
					$json = json_decode(file_get_contents($path));
					array_push($json->deleted, $project);
					$pos = array_search($project, $json->explorer_projects);
					unset($json->explorer_projects[$pos]);
					$json->explorer_projects = array_values($json->explorer_projects);
					file_put_contents($path, json_encode($json, JSON_PRETTY_PRINT), LOCK_EX);
					$data = array('switch_cmd' => 'Delete', 'Experiment' => $project);
					$response = json_encode($data);
					print_r($response);
				} else
				{
					echo 'PATH does not exist';
				}
			} else
			{
				echo 'RESULT is false';
			}
		} else
		{
			echo 'SRC does not exists';
		}
	}
}


function deleteAersProject($params)
{
	if(isset($params['Account']) && isset($params['Project']))
	{
		$account = strtoupper($params['Account']);
		$project = $params['Project'];
		$src = join(DIRECTORY_SEPARATOR, [ABS_ROOT,'data',$account,$project]);
		if(file_exists($src))
		{
			$dest = join(DIRECTORY_SEPARATOR, [ABS_ROOT,'data',$account,'trash',$project]);
			$result = rename($src,$dest);
			if($result)
			{
				$path = join(DIRECTORY_SEPARATOR , [ABS_ROOT,'data',$account,'account_info','projects.json']);
				if(file_exists($path))
				{
					$json = json_decode(file_get_contents($path));
					array_push($json->deleted, $project);
					$pos = array_search($project, $json->aers_projects);
					unset($json->aers_projects[$pos]);
					$json->aers_projects = array_values($json->aers_projects);
					file_put_contents($path, json_encode($json, JSON_PRETTY_PRINT), LOCK_EX);
					$data = array('switch_cmd' => 'Delete', 'Experiment' => $project);
					$response = json_encode($data);
					print_r($response);
				} else
				{
					echo 'PATH does not exist';
				}
			} else
			{
				echo 'RESULT is false';
			}
		} else
		{
			echo 'SRC does not exists';
		}
	}
}


function returnExpData($params)
{
	// Dead Function and NOT being used anymore!!
	$labname = $params['Account'];
	$expname = $params['Experiment'];
	$filearray = array(ABS_ROOT, 'data', strtoupper($labname), urlencode($expname), 'Input', 'info.json');
	$filelink = join(DIRECTORY_SEPARATOR, $filearray);
	if(file_exists($filelink))
	{
		$info = json_decode(file_get_contents($filelink));
	} else
	{
		$info = 'No Data';
	}
	$data = array('switch_cmd' => 'ExpInfo', 'Information' => $info);
	$response = json_encode($data);
	print_r($response);
}

function returnBioProjectInfo($params)
{
	$projectId = $params['ProjectId'];
	$project = BeProject::find($projectId);
	if(!empty($project)) {
		$info = $project;
	} else {
		$info = 'No Data!';
	}
	$data = array('switch_cmd' => 'BioProjectInfo', 'Data' => $info);
	$response = json_encode($data);
	print_r($response);
}

function returnAersProjectInfo($params)
{
	$account = strtoupper($params['Account']);
	$project = $params['Project'];
	$path = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', $account, $project, 'Input', 'info.json']);
	if(file_exists($path))
	{
		$info = json_decode(file_get_contents($path));
	} else
	{
		$info = 'No Data';
	}
	$data = array('switch_cmd' => 'AersProjectInfo', 'Data' => $info);
	$response = json_encode($data);
	print_r($response);
}

function returnInputList($params)
{
	$expname = $params['Experiment'];
	$expId = $params['ExpId'];
	$project = CbProject::find($expId);
	$list = $project->list;
	$list = implode("\n", $list);
	// Check for expression data
	$expression = $project->expression;
	if(filled($expression))
	{
		$expression = implode("\n", $expression);
	} 
	if(isset($params['Reanalyze']))
	{
		$data = array('switch_cmd' => 'ReanalyzeInputList', 'List' => $list, 'Experiment' => $expname, 'Project_Details' => $params['Project_Details'], 'ExpressionData' => $expression);
	} else
	{
		$data = array('switch_cmd' => 'InputList', 'List' => $list );
	}
	$response = json_encode($data);
	print_r($response);
}

function logout()
{
	session_unset();
	session_destroy();
	$data = array('switch_cmd' => 'LoggedOut');
	$response = json_encode($data);
	print_r($response);
}

function passwordChange($params)
{
	$complete = false;
	$account = $params['Account'];
	$password = password_hash($params['Password'], PASSWORD_DEFAULT);
	$filearray = array(ABS_ROOT, 'acct','accts.php');
	$filelink = join(DIRECTORY_SEPARATOR, $filearray);
	if(file_exists($filelink))
	{
		$fileContents = file_get_contents($filelink);
		if($fileContents != null)
		{
			$fileContents = trim($fileContents, "<?php '");
			$accounts = json_decode($fileContents, true);
		}
	}
	if(isset($accounts))
	{
		for($i=0;$i<count($accounts);$i++)
		{
			if(strtolower($accounts[$i]['Laboratory']) == strtolower($account))
			{
				$accounts[$i]['Password'] = $password;
			    $file = fopen($filelink, 'w');
			    flock($file, LOCK_EX);
				$accountsWrite = json_encode($accounts, JSON_PRETTY_PRINT);
				fwrite($file, "<?php '".$accountsWrite);
				flock($file, LOCK_UN);
				fclose($file);
				$complete = true;
			}
		}
	}
	if($complete)
	{
		$data = array('switch_cmd' => 'PasswordChanged');
		$response = json_encode($data);
		print_r($response);
	} else
	{
		echo 'ERROR!';
	}
}

function emailChange($params)
{
	// $account = $params['Account'];
	$email = $params['Email'];
    $prevEmail = $params['PrevEmail'];
    if(Auth::user()->email === $prevEmail || Auth::user()->sudo)
    {
        $user = User::findWhere('email',$prevEmail);
        if($user !== null)
        {
        $user->email = $email;
        $user->save();
        $data = array('switch_cmd' => 'EmailChanged', 'Email' => $email);
        $response = json_encode($data);
        print_r($response);
    } else
    {
        echo 'ERROR!';
    }
}
	// $filearray = array(ABS_ROOT, 'acct','accts.php');
	// $filelink = join(DIRECTORY_SEPARATOR, $filearray);
	// if(file_exists($filelink))
	// {
	// 	$fileContents = file_get_contents($filelink);
	// 	if($fileContents != null)
	// 	{
	// 		$fileContents = trim($fileContents, "<?php '");
	// 		$accounts = json_decode($fileContents, true);
	// 	}
	// }
	// for($i=0;$i<count($accounts);$i++)
	// {
	// 	if(strtolower($accounts[$i]['Laboratory']) == strtolower($account))
	// 	{
	// 		$accounts[$i]['Email'] = $email;
	// 	    $file = fopen($filelink, 'w');
	// 	    flock($file, LOCK_EX);
	// 		$accountsWrite = json_encode($accounts, JSON_PRETTY_PRINT);
	// 		fwrite($file, "<?php '".$accountsWrite);
	// 		flock($file, LOCK_UN);
	// 		fclose($file);
	// 		$complete = true;
	// 	}
	// }

	// if($complete)
	// {
	// 	$complete = false;
	// 	$dirArray = array(ABS_ROOT, 'data', strtoupper($account), 'account_info', 'info.json');
	// 	$path = join(DIRECTORY_SEPARATOR, $dirArray);
	// 	$accountinfo = json_decode(file_get_contents($path), true);
	// 	$accountinfo[0]['Email'] = $email;
	// 	$file = fopen($path, 'w');
	// 	flock($file, LOCK_EX);
	// 	fwrite($file, json_encode($accountinfo, JSON_PRETTY_PRINT));
	// 	flock($file, LOCK_UN);
	// 	fclose($file);
	// 	$complete = true;
	// 	if($complete)
	// 	{
	// 		$data = array('switch_cmd' => 'EmailChanged', 'Email' => $email);
	// 		$response = json_encode($data);
	// 		print_r($response);
	// 	} else
	// 	{
	// 		echo 'ERROR!';
	// 	}
	// } else
	// {
	// 	echo 'ERROR!';
	// }
}
function sudoAcctPassChange($params)
{
	$complete = false;
	$account = $params['Account'];
	$password = password_hash($params['Password'], PASSWORD_DEFAULT);
	$filearray = array(ABS_ROOT, 'acct', 'accts.php');
	$filelink = join(DIRECTORY_SEPARATOR, $filearray);
	if (file_exists($filelink)) {
		$fileContents = file_get_contents($filelink);
		if ($fileContents != null) {
			$fileContents = trim($fileContents, "<?php '");
			$accounts = json_decode($fileContents, true);
		}
	}
	for ($i = 0; $i < count($accounts); $i++) {
		if (strtolower($accounts[$i]['Laboratory']) == strtolower($account)) {
			$accounts[$i]['Password'] = $password;
			$file = fopen($filelink, 'w');
			flock($file, LOCK_EX);
			$accountsWrite = json_encode($accounts, JSON_PRETTY_PRINT);
			fwrite($file, "<?php '" . $accountsWrite);
			flock($file, LOCK_UN);
			fclose($file);
			$complete = true;
		}
	}
	if ($complete) {
		$data = array('switch_cmd' => 'PasswordChanged');
		$response = json_encode($data);
		print_r($response);
	} else {
		echo 'ERROR!';
	}
}
function returnAnnouncements()
{
	$pathArray = array(ABS_ROOT, 'acct', 'announcements.json');
	$path = join(DIRECTORY_SEPARATOR, $pathArray);
	if(file_exists($path))
	{
		$fileContents = json_decode(file_get_contents($path), true);
		$info = $fileContents['Announcements'];
	} else
	{
		$info = 'No Announcements Yet...';
	}

	$data = array('switch_cmd' => 'AnonInfo', 'Announcements' => $info);
	$response = json_encode($data);
	print_r($response);
}

/*Sudo*/
function sudoCpProject($params)
{
	$pathArray = array(ABS_ROOT, 'data', strtoupper($params['SrcAccount']), $params['SrcProject']);
	$src = join(DIRECTORY_SEPARATOR, $pathArray);
	$pathArray = array(ABS_ROOT, 'data', strtoupper($params['DestAccount']), $params['SrcProject']);
	$dest = join(DIRECTORY_SEPARATOR, $pathArray);

	recurse_copy($src, $dest);

	$explorerProject = false;
	$infoJsonPath = $dest.DIRECTORY_SEPARATOR.'Input'.DIRECTORY_SEPARATOR.'info.json';
	$infoJson = json_decode(file_get_contents($infoJsonPath));

	if(isset($infoJson->Bio_Explorer) && $infoJson->Bio_Explorer)
	{
		$explorerProject = true;
	}

	$pathArray = array(ABS_ROOT, 'data', strtoupper($params['DestAccount']), 'account_info', 'projects.json');
	$dest = join(DIRECTORY_SEPARATOR, $pathArray);

	if(file_exists($dest))
	{
		$json = json_decode(file_get_contents($dest));
		if($explorerProject)
		{
			if(is_null($json->explorer_projects))
			{
				$json->explorer_projects = [];
				array_push($json->explorer_projects, $params['SrcProject']);
			} else
			{
				array_push($json->explorer_projects, $params['SrcProject']);
			}
		} else
		{
			array_push($json->projects, $params['SrcProject']);
		}
		file_put_contents($dest, json_encode($json, JSON_PRETTY_PRINT), LOCK_EX);
	}

	$data = array('switch_cmd' => 'CopyProject', 'WriteResp' => 'Copy Completed');
	$response = json_encode($data);
	print_r($response);
}

function sudoEditAnnouncements($params)
{
	$log = array(date("Y-m-d h:i:s",time()));
	array_push($log, ' --> UpdatedBy--> '. $params['Account']);
	$dirArray = array(ABS_ROOT, 'acct', 'announcements.json');
	$path = join(DIRECTORY_SEPARATOR, $dirArray);
	if(file_exists($path))
	{
		$fileContents = json_decode(file_get_contents($path), true);
		$fileContents['Announcements'] = $params['UpdatedInfo'];
		array_push($fileContents['EditLog'], join($log));
	} else
	{
		$fileContents = array('Announcements' => $params['UpdatedInfo'], 'EditLog' => array(join($log)));
	}
	$file = fopen($path, 'w');
	flock($file, LOCK_EX);
	$returnval = fwrite($file, json_encode($fileContents, JSON_PRETTY_PRINT));
	flock($file, LOCK_UN);
	fclose($file);

	$data = array('switch_cmd' => 'AnonUpdate', 'WriteResp' => $returnval);
	$response = json_encode($data);
	print_r($response);
}

function sudoReturnInputList($params)
{
	$account = $params['Account'];
	$expname = $params['Experiment'];
	$linkarray = array(ABS_ROOT,'data',strtoupper($account), urlencode($expname), 'Input','input.txt');
	$listlink = join(DIRECTORY_SEPARATOR, $linkarray);
	if(file_exists($listlink))
	{
		$list = file_get_contents($listlink);
	} else
	{
		$list = 'No Data...';
	}
	$data = array('switch_cmd' => 'InputList', 'List' => $list );
	$response = json_encode($data);
	print_r($response);
}

function sudoEditAcctName($params)
{
	$complete = false;
	$account = $params['Account'];
	$newname = $params['New'];
	$filearray = array(ABS_ROOT, 'acct','accts.php');
	$filelink = join(DIRECTORY_SEPARATOR, $filearray);
	if(file_exists($filelink))
	{
		$fileContents = file_get_contents($filelink);
		if($fileContents != null)
		{
			$fileContents = trim($fileContents, "<?php '");
			$accounts = json_decode($fileContents, true);
		}
	}
	for($i=0;$i<count($accounts);$i++)
	{
		if(strtolower($accounts[$i]['Laboratory']) == strtolower($account))
		{
			$accounts[$i]['Laboratory'] = $newname;
		    $file = fopen($filelink, 'w');
		    flock($file, LOCK_EX);
			$accountsWrite = json_encode($accounts, JSON_PRETTY_PRINT);
			fwrite($file, "<?php '".$accountsWrite);
			flock($file, LOCK_UN);
			fclose($file);
			$complete = true;
		}
	}
	if($complete)
	{
		$complete = false;
		$dirArray = array(ABS_ROOT, 'data', strtoupper($account), 'account_info', 'info.json');
		$path = join(DIRECTORY_SEPARATOR, $dirArray);
		$accountinfo = json_decode(file_get_contents($path), true);
		$accountinfo[0]['Laboratory'] = $newname;
		$file = fopen($path, 'w');
		flock($file, LOCK_EX);
		fwrite($file, json_encode($accountinfo, JSON_PRETTY_PRINT));
		flock($file, LOCK_UN);
		fclose($file);
		$complete = true;
		if($complete)
		{
			$complete = false;
			$dirArray = array(ABS_ROOT, 'data', strtoupper($account));
			$olddir = join(DIRECTORY_SEPARATOR, $dirArray);
			$dirArray = array(ABS_ROOT, 'data', strtoupper($newname));
			$newdir = join(DIRECTORY_SEPARATOR, $dirArray);
			rename($olddir, $newdir);
			$complete = true;
			if($complete)
			{
				$data = array('switch_cmd' => 'EditAccount', 'NewName' => $newname);
				$response = json_encode($data);
				print_r($response);
			} else
			{
				echo 'ERROR!';
			}
		} else
		{
			echo 'ERROR!';
		}
	} else
	{
		echo 'ERROR!';
	}
}

function sudoExpInfo($params)
{
	$labname = $params['Account'];
	$exp = $params['Experiment'];
    $expId = $params['ExpId'];
    $info = CbProject::find($expId)->toArray();
    $info['Name'] = $info['name'];
    $info['created_at'] = Carbon::parse($info['created_at'])->toDateTimeString();
    $data = array('switch_cmd' => 'SudoExpInfo', 'Information' => $info);
	$response = json_encode($data);
	print_r($response);
}

function sudoAccountLists($params)
{
	$labname = $params['Account'];
    $files = CbProject::where('user_id',$params['Id'])->get();
    foreach ($files as $file)
	{
        $file =  "<li class='list-item' onclick='sudoSelectExperiment(event.target)' id='{$file->id}'>{$file->name}</li>";
        array_push($params['Projects'], $file);
    }
    $user = User::find($params['Id']);
    $userLab = $user->lab->name;
    $info = $user->toArray();
    $info['created_at'] = Carbon::parse($info['created_at'])->toDateTimeString();
    $info['LabName'] = $userLab;
    $params['Info'] = $info;
    $data = array('switch_cmd' => 'SudoProjects', 'Info' => $params['Info'], 'ProjectsList' => $params['Projects']);
    $response = json_encode($data);
    print_r($response);
}


function accountLists($params)
{
	// session_start();
    if(User::find($_SESSION['Id'])->sudo)
    {
        $users = User::where('id', '!=', $_SESSION['Id'])->get();
        foreach($users as $user)
        {
            $user = "<li class='list-item' id='".$user->id."'>" . $user->email . "</li>";
            array_push($params['Accounts'], $user);
        }
        $data = array('switch_cmd' => 'AccountsList', 'List' => $params['Accounts']);
        $response = json_encode($data);
        print_r($response);
    } else
    {
        echo 'ERROR!';
    }
}

function deleteRequestItem($params)
{
	$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'requests.json';
	$requests = json_decode(file_get_contents($path), true);
	for($i=0;$i<count($requests);$i++)
	{
		if($requests[$i]['Name']==$params['RequestName'] && $requests[$i]['Request_Date']==$params['RequestDate'] &&
			$requests[$i]['Organization']==$params['RequestOrg'] && $requests[$i]['Email']==$params['RequestEmail'] )
		{
			unset($requests[$i]);
			$requests = array_values($requests);
			$file = fopen($path, 'w');
			flock($file, LOCK_EX);
			fwrite($file, json_encode($requests, JSON_PRETTY_PRINT));
			flock($file, LOCK_UN);
			fclose($file);

			$log = array(date("Y-m-d h:i:s",time()));
			$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.'requests_log.txt';
			$entry = 'Name-> '.$params['RequestName'].', Org-> '.$params['RequestOrg'].', Email-> '.$params['RequestEmail'];
			array_push($log, $entry);
			$file = fopen($path, 'a');
			flock($file, LOCK_EX);
			fwrite($file, implode(',', $log).PHP_EOL);
			flock($file, LOCK_UN);
			fclose($file);

		    $data = array('switch_cmd' => 'RequestDeleted', 'RequestEntry' => $params);
		    $response = json_encode($data);
		    print_r($response);
		    break;
		}
	}
}

function requestsList($params)
{
	$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'requests.json';
	// $requests = json_decode(file_get_contents($path), true);
	$requests = [];
	$index = 0;
	foreach ($requests as $request)
	{
		array_push($params['RequestElements'], "<li class='list-item request-item' data-index='".$index."'>
			<span class='request-name'>".$request['Name']."</span>
			<span class='request-org'>".$request['Organization']."</span>
			<span class='request-date'>".$request['Request_Date']."</span>
			</li>");
		array_push($params['RequestData'], $request);
		$index++;
	}
    $data = array('switch_cmd' => 'RequestsList', 'List' => $params['RequestElements'], 'RequestObjs' => $params['RequestData']);
    $response = json_encode($data);
    print_r($response);
}

function createAccount($params)
{
	$log = array(date("Y-m-d h:i:s",time()));
	$acctsfile = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'accts.php';
	if(file_exists($acctsfile))
	{
		$fileContents = file_get_contents($acctsfile);
		if($fileContents != null)
		{
			$fileContents = trim($fileContents, "<?php '");
			$accounts = json_decode($fileContents, true);
		}
	}
	$match = false;
	if(!isset($accounts))
	{
		$accounts = array();
	} else
	{
		for($i=0;$i<count($accounts);$i++)
		{
			if(strtolower($accounts[$i]['Laboratory'])==strtolower($params['AccountName']))
			{
				$match = true;
				array_push($log, 'Duplicate Account');
			}
		}
	}
	if(!($match)){
	    $id = $accounts[count($accounts)-1]['Id'] + 1;
	    $newacct = array('Id'=>"$id", 'Creation_Date'=>date("Y-m-d h:i:s",time()), 'Laboratory'=>$params['AccountName'], 'Email'=>$params['Email'], 'Password'=>password_hash($params['Password'], PASSWORD_DEFAULT));
	    if(isset($params['Sudo']))
		{
	    	$newacct['Sudo'] = true;
	    	array_push($log, '*Sudo*');
 	    }
 	    $acctinfo = array(array('Creation_Date' => $newacct['Creation_Date'], 'Laboratory' => $newacct['Laboratory'], 'Email' => $newacct['Email']));
 	    array_push($accounts, $newacct);
 	    $file = fopen($acctsfile, 'w');
 	    flock($file, LOCK_EX);
 	    fwrite($file, "<?php '".json_encode($accounts, JSON_PRETTY_PRINT));
 	    flock($file, LOCK_UN);
 	    fclose($file);
    	$newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']);
    	mkdir($newdir, 0777);
    	$newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'failed';
    	mkdir($newdir, 0777);
    	$newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'processing';
    	mkdir($newdir, 0777);
    	$newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'trash';
    	mkdir($newdir, 0777);
    	$newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']).DIRECTORY_SEPARATOR.'account_info';
    	mkdir($newdir, 0777);
    	$path = $newdir.DIRECTORY_SEPARATOR.'info.json';
    	$file = fopen($path, 'w');
    	flock($file, LOCK_EX);
    	fwrite($file, json_encode($acctinfo, JSON_PRETTY_PRINT));
    	flock($file, LOCK_UN);
    	fclose($file);
 	    array_push($log, 'AccountCreated-> '.$params['AccountName']);
 	    $path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.'log.txt';
 	    $file = fopen($path, 'a');
 	    flock($file, LOCK_EX);
 	    fwrite($file, implode(',', $log).PHP_EOL);
 	    flock($file, LOCK_UN);
 	    fclose($file);
    	$newdir = ABS_ROOT.DIRECTORY_SEPARATOR.'data'.DIRECTORY_SEPARATOR.strtoupper($newacct['Laboratory']);
    	include('email.php');
    	emailNewAccount($params['AccountName'], $params['Email'], $params['Password']);
		emailNewAccountAdminNotification($params['AccountName'], $params['Email']);

		$email_record_status = addNewEmailRecord( $params['Email'] );

 		if(file_exists($newdir))
		{
	 	    $data = array('switch_cmd' => 'AccountCreated', 'NewAccount' => $newacct['Laboratory'], 'Email' => $newacct['Email']);
	    	$response = json_encode($data);
	    	print_r($response);
 	    }
	}
}

function addNewEmailRecord( $email )
{
	$notification_contacts_path = (file_exists('/var/www/html/acct/notification_contacts.json')) ? '/var/www/html/acct/notification_contacts.json' : '../../../acct/notification_contacts.json';
	$account_contacts_path = (file_exists('/var/www/html/acct/account_contacts.json')) ? '/var/www/html/acct/account_contacts.json' : '../../../acct/account_contacts.json';

	$notification_contacts = (file_exists($notification_contacts_path)) ? json_decode(file_get_contents($notification_contacts_path),true) : [];
	$account_contacts = (file_exists($account_contacts_path)) ? json_decode(file_get_contents($account_contacts_path),true) : [];

	if(in_array($email, $notification_contacts))
	{
		$index = array_search( $email,$notification_contacts );
		unset( $notification_contacts[ $index ]);
		file_put_contents($notification_contacts_path, json_encode(array_values($notification_contacts)));
	}

	if (!in_array( $email, $account_contacts ))
	{
		array_push($account_contacts, $email);
		file_put_contents($account_contacts_path, json_encode(array_values($account_contacts)));
		return 'added to account contacts';
	} else {
		return 'contact already exists';
	}
}

function recurse_copy($src,$dst)
{
    $dir = opendir($src);
    @mkdir($dst);
    while(false !== ( $file = readdir($dir)) )
	{
        if (( $file != '.' ) && ( $file != '..' ))
		{
            if ( is_dir($src . '/' . $file) )
			{
                recurse_copy($src . '/' . $file,$dst . '/' . $file);
            } else
            {
                copy($src . '/' . $file,$dst . '/' . $file);
            }
        }
    }
    closedir($dir);
}

function moveProjectToFolder($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$account = $params['Account'];
   $folderId = $params['FolderId'];
	$projects = $params['Projects'];
	if($folderId === 'root') {
		$folderId = null;
	}

	// Check if folder exists when folderId is not null
	if($folderId !== null && !Folder::find($folderId)) {
		$data = array('switch_cmd' => 'MoveToFolder', 'Status' => 'failed', 'Message' => 'Folder not found');
		$response = json_encode($data);
		print_r($response);
		exit();
	}

	foreach($projects as $projectId) {
		$project = CbProject::find($projectId);
		$project->folder_id = $folderId;
		$project->save();
	}

	$data = array('switch_cmd' => 'MoveToFolder');
	$response = json_encode($data);
	print_r($response);
}

function createProjectFolder($params)
{
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$account = $params['Account'];
	$projectsToMove = $params['NewFolderData']['addProjects'];
	$folderName = $params['NewFolderData']['folderName'];

	$status = 'success';
	if(property_exists($user->folders, $folderName))
	{
		$status = 'Folder already exists';
	} else
	{
		$folder = Folder::create([
			'name' => $folderName,
			'user_id' => $user->id,
		]);
		foreach($projectsToMove as $project)
		{
			$project = CbProject::find($project);
			$project->folder_id = $folder->id;
			$project->save();
		}
	}

	$data = array('switch_cmd' => 'NewProjectFolder', 'Status' => $status);
	$response = json_encode($data);
	print_r($response);
}

function reauthSession($params)
{
	$match = false;
	$fileContents = file_get_contents('../../../acct/accts.php');
	$fileContents = trim($fileContents, "<?php '");
	$accounts = json_decode($fileContents, true);
	for($i=0;$i<count($accounts);$i++)
	{
		if($accounts[$i]['Laboratory']==$params['Laboratory']
			&& password_verify($params['Password'], $accounts[$i]['Password']))
		{
			$match = true;
			$id = $accounts[$i]['Id'];
			$email = $accounts[$i]['Email'];
			if(isset($accounts[$i]['Sudo']))
			{
				$sudo = true;
			}
		}
	}
	if($match == true)
	{
		$_SESSION['last_action'] = time();
		if(!isset($_SESSION['LoggedIn']))
		{
			$_SESSION['LoggedIn'] = true;
			$_SESSION['Id'] = $id;
			$_SESSION['Laboratory'] = $params['Laboratory'];
			$_SESSION['Email'] = $email;
			if(isset($sudo))
			{
				$_SESSION['Sudo'] = $sudo;
			}
			if(isset($params['CBV']))
			{
				$key = $params['ProjectKey'];
				$_SESSION['ProjectKeys'][$key] = ['Account'=>strtoupper($params['ProjectAcct']),'Project'=>$params['Project']];
				$_SESSION['ExpName'] = $params['Project'];
			}
		}
	}

	$data = array('switch_cmd' => 'Reauth', 'Status' => $match);
	$response = json_encode($data);
	print_r($response);
}

function checkSessionStatus()
{
	// $isAuthorized = Auth::check();
	// if ($isAuthorized) {
	// 	return false;
	// } else {
	// 	return true;
	// }
	return false;
}

function hypothesisToAssertionNameChange($params)
{
	$oldPath = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($params['Account']), 'account_info', 'hypothesis']);
	$path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($params['Account']), 'account_info', 'assertion']);
	if(file_exists($oldPath))
	{
		rename($oldPath, $path);
	}
}

function returnAssertionGenerations($params)
{
	// session_start();
    $id = $params['AcctId'] ?? $_SESSION['Id'];
    $user = User::find($id);

    $assertionArray = [];
    $assertWithLibs = [];
    $assertion2Array = $user->assertions()->pluck('name')->toArray();

    $data = array('switch_cmd' => 'ReturnAssertion', 'Assertion' => $assertionArray, 'AssertWithLibs' => $assertWithLibs, 'Assertion2' => $assertion2Array);
    $response = json_encode($data);
    print_r($response);

	// if(isset($params['Account']))
	// {
	// 	hypothesisToAssertionNameChange($params);
	// 	$assertWithLibs = returnAssertionsWithLibs($params['Account']);
	// 	$path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($params['Account']), 'account_info', 'assertion']);
	// 	$assertionArray = [];
	// 	if(file_exists($path))
	// 	{
	// 		$assertionArray = array_diff(scandir($path), ['.','..']);
	// 	} else
	// 	{
	// 		mkdir($path);
	// 		chmod($path, 0777);
	// 	}

	// 	$assertion2Array = returnAssertion2Generations($params['Account']);
	// 	$data = array('switch_cmd' => 'ReturnAssertion', 'Assertion' => $assertionArray, 'AssertWithLibs' => $assertWithLibs, 'Assertion2' => $assertion2Array);
    // 	$response = json_encode($data);
    // 	print_r($response);
	// }
}

function returnAssertionsWithLibs($acct)
{
	$assertions = (array)[];
	$path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($acct), 'account_info', 'assertion_w_libs']);
	if(file_exists($path))
	{
		$assertions = array_diff(scandir($path), ['.','..']);
	} else
	{
		mkdir($path);
		chmod($path, 0777);
	}
	return $assertions;
}

function returnAssertion2Generations($acct)
{
	$assertions = (array)[];
	$path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($acct), 'account_info', 'assertion2']);
	if(file_exists($path))
	{
		$assertions = array_diff(scandir($path), ['.','..']);
	} else
	{
		mkdir($path);
		chmod($path, 0777);
	}
	return $assertions;
}

function createNewAssertion($params)
{
	$ip = getRealIpAddr();
	$dataPath = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($params['Account'])]);
	if(checkAssertionProjectDependencyFiles($dataPath, $params['base'], $params['secondary']))
	{
		$assertInfo = (object)[];
		$assertInfo->account = $params['Account'];
		$assertInfo->name = $params['name'];
		$assertInfo->creation_date = date("Y-m-d h:i:s",time());
		$assertInfo->base = $params['base'];
		$assertInfo->isBaseLib = $params['isBaseLib'];
		$assertInfo->secondary = $params['secondary'];
		$assertInfo->isSecondaryLib = $params['isSecondaryLib'];
		$assertInfo->isLibAssertion = ($params['isBaseLib'] === true || $params['isSecondaryLib'] === true) ? true : false;
		$assertInfo->isLibToLibAssertion = ($params['isBaseLib'] === true && $params['isSecondaryLib'] === true) ? true : false;
		$assertInfo->engine = $GLOBALS['defaultAssertionVersion'];

		$path = join(DIRECTORY_SEPARATOR,[$dataPath, 'account_info', 'assertion', $assertInfo->name]);
		mkdir($path);
		chmod($path, 0777);

		file_put_contents($path.DIRECTORY_SEPARATOR.'info.json', json_encode($assertInfo, JSON_PRETTY_PRINT));

		if($assertInfo->isLibToLibAssertion === true)
		{
			sendToMultiLibAssertionLoop($assertInfo, $dataPath, $path);
			exit;
		} else if($assertInfo->isLibAssertion === true)
		{
			sendToAssertionLoop($assertInfo, $dataPath, $path);
			exit;
		} else
		{
			$arg1 = "{$dataPath}/{$assertInfo->base}/{$assertInfo->base}";
			$arg2 = "{$dataPath}/{$assertInfo->secondary}/{$assertInfo->secondary}";
			$arg3 = "{$path}/{$assertInfo->name}";
			list ($cmd, $o, $r) = initiateAssertionCommand($arg1,$arg2,$arg3);
			$execOuput = implode(",", $o);
			date_default_timezone_set('America/Chicago');
			$log = join( ' -> ', array(date("Y-m-d h:i:s",time()), $ip, "Account: {$assertInfo->account}", "Name: {$assertInfo->name}", "Base: {$assertInfo->base}", "Secondary: {$assertInfo->secondary}", "Version: {$assertInfo->engine}", "Cmd: {$cmd}", "Output: {$execOuput}", "Return: {$r}")).PHP_EOL;
			file_put_contents('../../../acct/log/assertion.log', $log, FILE_APPEND | LOCK_EX);
			$data = array('switch_cmd' => 'ReturnAssertionSubmission', 'parameters' => $params, 'assertInfo' => $assertInfo, 'ReturnStatus' => $r);
			if($r !== 0)
			{
				$data['err'] = end($o);
				$path = join(DIRECTORY_SEPARATOR,[$dataPath, 'account_info', 'assertion', $assertInfo->name]);
				recursiveDelete($path);
			}
			$response = json_encode($data);
			print_r($response);
		}
	} else
	{
		$params['dataPath'] = $dataPath;
		$data = array('switch_cmd' => 'AssertionRequirementsFailure', 'parameters' => $params);
		$response = json_encode($data);
		print_r($response);
	}
}

function sendToMultiLibAssertionLoop($data, $acctPath, $assertionPath)
{
	$assertWithLibs = $acctPath.DIRECTORY_SEPARATOR.'account_info'.DIRECTORY_SEPARATOR.'assertion_w_libs';
	if(!file_exists($assertWithLibs))
	{
		mkdir($assertWithLibs);
		chmod($assertWithLibs, 0777);
	}
	$newAssertionPath = str_replace(DIRECTORY_SEPARATOR.'assertion'.DIRECTORY_SEPARATOR, DIRECTORY_SEPARATOR.'assertion_w_libs'.DIRECTORY_SEPARATOR, $assertionPath);
	rename($assertionPath, $newAssertionPath);
	$baseInfoPath = join(DIRECTORY_SEPARATOR, [$acctPath,'account_info','assertion_libs',$data->base,'lib_info.json']);
	$secondInfoPath = join(DIRECTORY_SEPARATOR, [$acctPath,'account_info','assertion_libs',$data->secondary,'lib_info.json']);
	if(file_exists($baseInfoPath) && file_exists($secondInfoPath))
	{
		$libInfo1 = json_decode(file_get_contents($baseInfoPath));
		$libInfo2 = json_decode(file_get_contents($secondInfoPath));
		$data->LibInfo = $libInfo1;
		$data->LibInfo2 = $libInfo2;
		$projects1 = $libInfo1->Projects;
		$projects2 = array_values(array_diff($libInfo2->Projects,$projects1));
		$data->RunResults = (array)[];
		$ip = getRealIpAddr();
		$logProjects1 = '['.join(',', $projects1).']';
		$logProjects2 = '['.join(',', $projects2).']';
		date_default_timezone_set('America/Chicago');
		$log = array(date("Y-m-d h:i:s",time()), $ip, "Account: {$data->account}", "Name: {$data->name}", "BaseLibrary: {$data->base}", "SecondLibrary: {$data->secondary}", "BaseProjects: {$logProjects1}", "SecondProjects: {$logProjects2}", "Version: {$data->engine}");
		$errCount = 0;
		$index = 0;
		foreach($projects1 as $project1)
		{
			foreach($projects2 as $project2)
			{
				$tmpName = "{$project1}.Vs.{$project2}";
				$tmpRunPath = "{$newAssertionPath}/{$tmpName}";
				$runLog = (object)[];
				$runLog->Name = $tmpName;
				$runLog->Project1 = $project1;
				$runLog->Project2 = $project2;
				mkdir($tmpRunPath);
				chmod($tmpRunPath, 0777);
				$arg1 = "{$acctPath}/{$project1}/{$project1}";
				$arg2 = "{$acctPath}/{$project2}/{$project2}";
				$arg3 = "{$tmpRunPath}/{$tmpName}";
				list ($cmd, $o, $r) = initiateAssertionCommand($arg1,$arg2,$arg3);
				$runLog->Cmd = $cmd;
				$runLog->Output = $o;
				$runLog->Return = $r;
				if($r !== 0)
				{
					$runLog->similarity = 'n/a';
					$errCount++;
					$runLog->err = end($o);
					recursiveDelete($tmpRunPath);
				} else
				{
					$runLog->similarity = json_decode(file_get_contents("{$arg3}.json"))->similarity;
				}
				array_push($data->RunResults, (array)$runLog);
				$index++;
			}

			array_push($log, 'RunResults: '.recursive_implode($data->RunResults,' : ',true));
			$log = join(' -> ', $log).PHP_EOL;
			file_put_contents('../../../acct/log/assertion_w_libs.log', $log, FILE_APPEND | LOCK_EX);
			file_put_contents($newAssertionPath.DIRECTORY_SEPARATOR.'info.json', json_encode($data), LOCK_EX);
			$respData = array('switch_cmd' => 'ReturnAssertionSubmission', 'assertInfo' => $data);
			if($errCount >= $index)
			{
				$r = 1;
				$respData['err'] = recursive_implode($data->RunResults,' -> ',true);
				recursiveDelete($newAssertionPath);
			}
			$respData['ReturnStatus'] = $r;
			$response = json_encode($respData);
			print_r($response);
			exit;
		}
		exit;
	} else
	{
		recursiveDelete($newAssertionPath);
		echo 'ERROR! One Or Both Of The Library Info File(s) Not Found';
		exit;
	}
}

function sendToAssertionLoop($data, $acctPath, $assertionPath)
{
	$assertWithLibs = $acctPath.DIRECTORY_SEPARATOR.'account_info'.DIRECTORY_SEPARATOR.'assertion_w_libs';
	if(!file_exists($assertWithLibs))
	{
		mkdir($assertWithLibs);
		chmod($assertWithLibs, 0777);
	}
	$newAssertionPath = str_replace(DIRECTORY_SEPARATOR.'assertion'.DIRECTORY_SEPARATOR, DIRECTORY_SEPARATOR.'assertion_w_libs'.DIRECTORY_SEPARATOR, $assertionPath);
	rename($assertionPath, $newAssertionPath);
	if($data->isBaseLib === true)
	{
		$singleProject = $data->secondary;
		$libProject = $data->base;
	} else if($data->isSecondaryLib === true)
	{
		$singleProject = $data->base;
		$libProject = $data->secondary;
	}
	$projectInfoFile = join(DIRECTORY_SEPARATOR, [$acctPath,'account_info','assertion_libs',$libProject,'lib_info.json']);
	if(file_exists($projectInfoFile))
	{
		$data->SingleProject = $singleProject;
		$libInfo = json_decode(file_get_contents($projectInfoFile));
		$data->LibInfo = $libInfo;
		$data->RunResults = (array)[];
		$ip = getRealIpAddr();
		$projects = array_values(array_diff($libInfo->Projects,[$singleProject]));
		$logProjects = '['.join(',', $projects).']';
		date_default_timezone_set('America/Chicago');
		$log = array(date("Y-m-d h:i:s",time()), $ip, "Account: {$data->account}", "Name: {$data->name}", "SingleProject: {$singleProject}", "Library: {$libProject}", "LibraryProjects: {$logProjects}", "Version: {$data->engine}");
		$errCount = 0;
		$index = 0;
		foreach($projects as $project)
		{
			$tmpName = "{$singleProject}.Vs.{$project}";
			$tmpRunPath = "{$newAssertionPath}/{$tmpName}";
			$runLog = (object)[];
			$runLog->Name = $tmpName;
			$runLog->SingleProject = $singleProject;
			$runLog->LibProject = $project;
			mkdir($tmpRunPath);
			chmod($tmpRunPath, 0777);
			$arg1 = "{$acctPath}/{$singleProject}/{$singleProject}";
			$arg2 = "{$acctPath}/{$project}/{$project}";
			$arg3 = "{$tmpRunPath}/{$tmpName}";
			list ($cmd, $o, $r) = initiateAssertionCommand($arg1,$arg2,$arg3);
			$runLog->Cmd = $cmd;
			$runLog->Output = $o;
			$runLog->Return = $r;
			if($r !== 0)
			{
				$runLog->similarity = 'n/a';
				$errCount++;
				$runLog->err = end($o);
				recursiveDelete($tmpRunPath);
			} else
			{
				$runLog->similarity = json_decode(file_get_contents("{$arg3}.json"))->similarity;
			}
			array_push($data->RunResults, (array)$runLog);
			$index++;
		}

		array_push($log, 'RunResults: '.recursive_implode($data->RunResults,' : ',true));
		$log = join(' -> ', $log).PHP_EOL;
		file_put_contents('../../../acct/log/assertion_w_libs.log', $log, FILE_APPEND | LOCK_EX);
		file_put_contents($newAssertionPath.DIRECTORY_SEPARATOR.'info.json', json_encode($data), LOCK_EX);
		$respData = array('switch_cmd' => 'ReturnAssertionSubmission', 'assertInfo' => $data);
		if($errCount >= $index)
		{
			$r = 1;
			$respData['err'] = recursive_implode($data->RunResults,' -> ',true);
			recursiveDelete($newAssertionPath);
		}
		$respData['ReturnStatus'] = $r;
		$response = json_encode($respData);
		print_r($response);
		exit;
	} else
	{
		recursiveDelete($assertionPath);
		echo 'ERROR! Library File Not Found';
		exit;
	}
	exit;
}

function initiateAssertionCommand($path1, $path2, $outputPath, $version = NULL)
{
	$engine = ($version === NULL) ? $GLOBALS['defaultAssertionVersion'] : $version;
	$argument1 = "--cdm_fp_0={$path1}_cd_theme_matrix.txt";
	$argument2 = "--cdm_fp_1={$path2}_cd_theme_matrix.txt";
	$argument3 = "--pdf_fp={$outputPath}.pdf";
	// $argument4 = "--csv_fp={$outputPath}.csv";
	$argument4 = "--json_fp={$outputPath}.json";

	// $command = "/huge1/nyee/subgraph_identification/{$engine}/subgraph_identification.py {$argument1} {$argument2} {$argument3} {$argument4} {$argument5} 2>&1";
	$command = "/huge1/nyee/subgraph_identification/{$engine}/subgraph_identification.py {$argument1} {$argument2} {$argument3} {$argument4} 2>&1";
	exec($command, $output, $return);
	return [$command, $output, $return];
}

function checkAssertionProjectDependencyFiles($path, $project1, $project2, $isBaseLib = false, $isSecondaryLib = false)
{
	if( $isBaseLib === true && $isSecondaryLib === true )
	{
		return true;
	}
	if($isBaseLib === false) {
		$project1 = $project1 = BeProject::find($project1) ?? CbProject::find($project1);
		$project1 = $project1->name;
	}
	if($isSecondaryLib === false) {
		$project2 = $project2 = BeProject::find($project2) ?? CbProject::find($project2);
		$project2 = $project2->name;
	}
	if(
		file_exists("{$path}/{$project1}/{$project1}_cd_theme_matrix.txt") || 
		$isBaseLib === true && 
		file_exists("{$path}/{$project2}/{$project2}_cd_theme_matrix.txt") || 
		$isSecondaryLib === true
		)
	{
		return true;
	} else
	{
		return false;
	}
}


function deleteAssertionSubmission($params)
{
	$ip = getRealIpAddr();
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$dataPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}";
	$assert2DirPath = "{$dataPath}/account_info/assertion2";
	if(isset($params['assert2']))
	{
		$assertName = $params['assertName'];
		$path = "{$assert2DirPath}/{$assertName}";
		$assrtId = $params['AsrtId'];
		$assrt = Assertion::find($assrtId);
		$assrt->delete();
	} else if(isset($params['assertLib']))
	{
		$assertName = $params['assertName'];
		$path = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', strtoupper($params['account']), 'account_info', 'assertion_libs', $assertName]);
	} else if(isset($params['assertName']))
	{
		$assertName = $params['assertName'];
		$path = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', strtoupper($params['account']), 'account_info', 'assertion', $assertName]);
	} else
	{
		$assertName = $params['batch'];
		$path = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', strtoupper($params['account']), 'account_info', 'assertion_w_libs', $assertName]);
	}
	$output = recursiveDelete($path);
	if($output)
	{
		$o = 'success';
	} else
	{
		$o = 'failed';
	}
	date_default_timezone_set('America/Chicago');
	$log = join( ' -> ', array(date("Y-m-d h:i:s",time()), $ip, "Account: {$params['account']}", "Name: {$assertName}", "Cmd: !!Delete!!", "Output: {$o}")).PHP_EOL;
	file_put_contents('../../../acct/log/assertion.log', $log, FILE_APPEND | LOCK_EX);

	$deleteAssertionLog = (object)[];
	$deleteAssertionLog->log = $log;

	$data = array('switch_cmd' => 'AssertionDeleted', 'parameters' => $params, 'deleteAssertionLog' => $deleteAssertionLog);
	$response = json_encode($data);
	print_r($response);
}
function deleteAssertionLibrary($params)
{
	$ip = getRealIpAddr();
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$assertionLib = AssertionLibrary::find($params['assertLibId']);
	$deleted = $assertionLib->delete();
	if($deleted) {
		$msg = 'Library Deleted';
	} else {
		$msg = 'Library Deletion Failed';
	}
	$data = array('switch_cmd' => 'AssertionDeleted', 'parameters' => $params, 'deleteAssertionLog' => $msg);
	$response = json_encode($data);
	print_r($response);
}

function createNewAssertionLib($params)
{
	if(isset($params['Account']) && isset($params['library']) && isset($params['projects']) && isset($_SESSION['Id']))
	{
		// $path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($params['Account']), 'account_info', 'assertion_libs']);
		// if(!file_exists($path))
		// {
		// 	mkdir($path);
		// 	chmod($path, 0777);
		// }
		// $libName = $params['library'];
		// $libProjects = $params['projects'];
		// $info = (object)[];
		// $info->Name = $libName;
		// $info->CreationDate = date("Y-m-d h:i:s",time());
		// $info->Total_Projects = count($libProjects);
		// $info->Projects = $libProjects;
		// mkdir($path.DIRECTORY_SEPARATOR.$libName);
		// chmod($path.DIRECTORY_SEPARATOR.$libName, 0777);
		// $completed = file_put_contents($path.DIRECTORY_SEPARATOR.$libName.DIRECTORY_SEPARATOR.'lib_info.json', json_encode($info));
		// if($completed !== false)
		// {
		// 	returnAssertionLibs($params);
		// } else
		// {
		// 	echo 'Library Creation Failure';
		// }
		$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
		$assertionLib = $user->assertionLibraries()->where('name', $params['library'])->first();
		if($user && !$assertionLib) {
			$assertionLib = AssertionLibrary::create([
				'user_id' => $params['AcctId'] ?? $_SESSION['Id'],
				'name' => $params['library'],
				'projects' => $params['projects'],
				'project_ids' => $params['projectIds']
			]);
		}
		returnAssertionLibs($params);
	}
}

function returnAssertionLibs($params)
{
	if(isset($params['Account']) && isset($_SESSION['Id']))
	{
		// $path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'data', strtoupper($params['Account']), 'account_info', 'assertion_libs']);
		// $libs = [];
		// if(file_exists($path))
		// {
		// 	$libs = array_diff(scandir($path), ['.','..']);
		// }
		// else
		// {
		// 	mkdir($path);
		// 	chmod($path, 0777);
		// }
		$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
		$libs = $user->assertionLibraries;
		$libsData = (array)[];
		foreach($libs as $lib)
		{
			$data = (object)[];
			$data->id = $lib->id;
			$data->libName = $lib->name;
			$data->projects = $lib->projects;
			$data->project_ids = $lib->project_ids;
			array_push($libsData, $data);
		}
		$data = array('switch_cmd' => 'ReturnAssertionLibs', 'AssertionLibs' => $libsData);
    	$response = json_encode($data);
    	print_r($response);
	}
}

function returnDefaultEngine()
{
	$data = array('switch_cmd' => 'ConsoleLog' , 'output' => $GLOBALS['defaultAssertionVersion']);
	$response = json_encode($data);
	print_r($response);
}

/**
 * Recursively implodes an array with optional key inclusion
 *
 * Example of $include_keys output: key, value, key, value, key, value
 *
 * @access  public
 * @param   array   $array         multi-dimensional array to recursively implode
 * @param   string  $glue          value that glues elements together
 * @param   bool    $include_keys  include keys before their values
 * @param   bool    $trim_all      trim ALL whitespace from string
 * @return  string  imploded array
 */
function recursive_implode(array $array, $glue = ',', $include_keys = false, $trim_all = true)
{
	$glued_string = '';

	// Recursively iterates array and adds key/value to glued string
	array_walk_recursive($array, function($value, $key) use ($glue, $include_keys, &$glued_string)
	{
		$include_keys and $glued_string .= $key.$glue;
		$glued_string .= $value.$glue;
	});

	// Removes last $glue from string
	strlen($glue) > 0 and $glued_string = substr($glued_string, 0, -strlen($glue));

	// Trim ALL whitespace
	$trim_all and $glued_string = preg_replace("/(\s)/ixsm", '', $glued_string);

	return (string) $glued_string;
}

function createNewAssertion2($params)
{
	$ip = getRealIpAddr();
	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$dataPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}";
	$assert2DirPath = "{$dataPath}/account_info/assertion2";
	if($user && file_exists($dataPath))
	{
		if(!file_exists($assert2DirPath))
		{
			mkdir($assert2DirPath,0777);
		}
		if(checkAssertionProjectDependencyFiles($dataPath, $params['base'], $params['secondary'], $params['isBaseLib'], $params['isSecondaryLib']))
		{
			if(!$params['isBaseLib']) {
				$project1 = BeProject::find($params['base']) ?? CbProject::find($params['base']);
			}
			if(!$params['isSecondaryLib']) {
				$project2 = BeProject::find($params['secondary']) ?? CbProject::find($params['secondary']);
			}
			$assertInfo = (object)[];
			$assertInfo->account = $params['Account'];
			$assertInfo->name = $params['name'];
			$assertInfo->creation_date = date("Y-m-d h:i:s",time());
			$assertInfo->base = $project1->name ?? $params['base'];
			$assertInfo->isBaseLib = $params['isBaseLib'];
			$assertInfo->secondary = $project2->name ?? $params['secondary'];
			$assertInfo->isSecondaryLib = $params['isSecondaryLib'];
			$assertInfo->isLibAssertion = ($params['isBaseLib'] === true || $params['isSecondaryLib'] === true) ? true : false;
			$assertInfo->isLibToLibAssertion = ($params['isBaseLib'] === true && $params['isSecondaryLib'] === true) ? true : false;
			$assertInfo->useSignificant = $params['useSignificant'];

			// $assertInfo->engine = '2.4.3';
			$assertInfo->engine = (file_exists(config('app.custom_env.ASSERTION_ENGINE_ROOT').'2.4.3_MP')) ? '2.4.3_MP' : '2.4.3';

			$path = "{$assert2DirPath}/{$assertInfo->name}";
			mkdir($path,0777);

			if($assertInfo->isBaseLib === true)
			{
				$lib1 = AssertionLibrary::find($assertInfo->base);
				$assertInfo->base = $lib1->name;
				$arg1 = getAssertionLibArgument($dataPath,$lib1,'rows',$assertInfo->useSignificant);
			} else
			{
				if($assertInfo->useSignificant)
				{
					$arg1 = "--in_fp_rows {$dataPath}/{$assertInfo->base}/{$assertInfo->base}_cd_significant_theme_matrix.txt";
				} else
				{
					$arg1 = "--in_fp_rows {$dataPath}/{$assertInfo->base}/{$assertInfo->base}_cd_theme_matrix.txt";
				}
			}
			if($assertInfo->isSecondaryLib === true)
			{
				$lib2 = AssertionLibrary::find($assertInfo->secondary);
				$assertInfo->secondary = $lib2->name;
				$arg2 = getAssertionLibArgument($dataPath,$lib2,'cols',$assertInfo->useSignificant);
			} else
			{
				if ($assertInfo->useSignificant)
				{
					$arg2 = "--in_fp_cols {$dataPath}/{$assertInfo->secondary}/{$assertInfo->secondary}_cd_significant_theme_matrix.txt";
				} else
				{
					$arg2 = "--in_fp_cols {$dataPath}/{$assertInfo->secondary}/{$assertInfo->secondary}_cd_theme_matrix.txt";
				}
			}

			file_put_contents("{$path}/info.json", json_encode($assertInfo, JSON_PRETTY_PRINT));

			$arg3 = "--out_dir {$path}";
			$arg4 = '--json_fn=out.json';
			$arg5 = ( $assertInfo->useSignificant ) ? "--file_suffix \"_cd_significant_theme_matrix.txt\"" : "--file_suffix \"_cd_theme_matrix.txt\"";
			$command = config('app.custom_env.ASSERTION_ENGINE_ROOT')."/{$assertInfo->engine}/assertion_engine.py --random_gene_dir=".config('app.custom_env.RANDOM_RUN_ROOT')." {$arg1} {$arg2} {$arg3} {$arg4} {$arg5} --n_random_genes ".config('app.custom_env.N_RANDOM_GENES');
			$user_id = $_SESSION['Id'];
			$email = $_SESSION['Email'];
			$tmpWorkerData = (object)[];
			$tmpWorkerData->assertInfo = $assertInfo;
			$tmpWorkerData->path = $path;
			$tmpWorkerData->command = $command;
			$tmpWorkerData->user_id = $user_id;
			$tmpWorkerData->ip = $ip;
			$tmpWorkerData->email = $email;
			$tempFile = tempnam(sys_get_temp_dir(), 'batch_');
			file_put_contents($tempFile, json_encode($tmpWorkerData));
			exec("nohup php assertion_submit_worker.php $tempFile > /dev/null 2>&1 &");
			
			$data = array('switch_cmd' => 'ReturnAssertionSubmission', 'parameters' => $params, 'assertInfo' => $assertInfo, 'ReturnStatus' => 'Processing');
			$response = json_encode($data);
			print_r($response);
		} else
		{
			$params['dataPath'] = $dataPath;
			$data = array('switch_cmd' => 'AssertionRequirementsFailure', 'parameters' => $params);
			$response = json_encode($data);
			print_r($response);
		}
	} else
	{
		$params['dataPath'] = $dataPath;
		$data = array('switch_cmd' => 'AssertionRequirementsFailure', 'parameters' => $params);
		$response = json_encode($data);
		print_r($response);
	}
}

function getAssertionLibArgument($acctPath,$lib,$direction,$useSignificant)
{
	$string = "--in_fp_{$direction}";
	$projects = $lib->projects;
	foreach($projects as $project)
	{
		if( $useSignificant )
		{
			$string .= " {$acctPath}/{$project}/{$project}_cd_significant_theme_matrix.txt";
		} else
		{
			$string .= " {$acctPath}/{$project}/{$project}_cd_theme_matrix.txt";
		}
	}
	return $string;
}

// Check Matrix Files for Significant Themes Option
function returnMatrixFileCheck($params)
{
	$id = $params['AcctId' ?? $_SESSION['Id']];
	$user = User::find($id);
	$account = $params['Account'];
	$target1 = $params['Target1'];
	$isLibrary1 = $params['IsLibrary1'];
	$accountPath =  config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$user->lab->directory}";
	if( $isLibrary1 )
	{
		$library1 = AssertionLibrary::find($target1);
		if( $library1 )
		{
			$projectIds = $library1->project_ids;
			foreach( $projectIds as $projectId )
			{
				$project = BeProject::find($projectId) ?? CbProject::find($projectId);
				if($project)
				{
					$targetPathMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_theme_matrix.txt";
					$targetPathSignificantMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_significant_theme_matrix.txt";
					if ( file_exists( $targetPathMatrix ) && file_exists( $targetPathSignificantMatrix ))
					{
						$params['Result'] = true;
					} else
					{
						$params['Result'] = false;
						break;
					}
				} else
				{
					$params['Result'] = false;
					break;
				}
			}
		} else
		{
			$params['Result'] = false;
		}
	} else
	{
		$project = BeProject::find($target1) ?? CbProject::find($target1);
		if($project) {
			$targetPathMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_theme_matrix.txt";
			$targetPathSignificantMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_significant_theme_matrix.txt";
			if(file_exists( $targetPathMatrix ) && file_exists( $targetPathSignificantMatrix ))
			{
				$params['Result'] = true;
			} else
			{
				$params['Result'] = false;
			}
		} else {
			$params['Result'] = false;
		}
	}
	if($params['Result'])
	{
		$target2 = $params['Target2'];
		$isLibrary2 = $params['IsLibrary2'];
		if( $isLibrary2 )
		{
			$library2 = AssertionLibrary::find($target2);
			if( $library2 )
			{
				$projectIds = $library2->project_ids;
				foreach( $projectIds as $projectId )
				{
					$project = BeProject::find($projectId) ?? CbProject::find($projectId);
					if($project)
					{
						$targetPathMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_theme_matrix.txt";
						$targetPathSignificantMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_significant_theme_matrix.txt";
						if ( file_exists( $targetPathMatrix ) && file_exists( $targetPathSignificantMatrix ))
						{
							$params['Result'] = true;
						} else
						{
							$params['Result'] = false;
							break;
						}
					} else
					{
						$params['Result'] = false;
						break;
					}
				}
			} else
			{
				$params['Result'] = false;
			}
		} else
		{
			$project = BeProject::find($target2) ?? CbProject::find($target2);
			if($project) {
				$targetPathMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_theme_matrix.txt";
				$targetPathSignificantMatrix = "{$accountPath}/{$project->name}/{$project->name}_cd_significant_theme_matrix.txt";
				if(file_exists( $targetPathMatrix ) && file_exists( $targetPathSignificantMatrix ))
				{
					$params['Result'] = true;
				} else
				{
					$params['Result'] = false;
				}
			} else {
				$params['Result'] = false;
			}
		}
	}
	$data = array('switch_cmd' => 'AssertionMatrixFileCheck', 'parameters' => $params);
	$response = json_encode($data);
	print_r($response);
}

function returnContactInformation()
{
	// $account_contacts = [];
	// // $fileContents = file_get_contents('../../../acct/accts.php');
	// $fileContents = null;
	// if ($fileContents != null)
	// {
	// 	$fileContents = trim($fileContents, "<?php '");
	// 	$accountsJSON = json_decode($fileContents, true);
	// 	$account_contacts = array_map( function( $account )
	// 	{
	// 		if( array_key_exists( 'Sudo',$account ) && $account['Sudo'] === TRUE )
	// 		{
	// 			return array(
	// 				'name' => $account['Laboratory'],
	// 				'email' => $account['Email'],
	// 				'admin' => 1
	// 			);
	// 		} else {
	// 			return array(
	// 				'name' => $account['Laboratory'],
	// 				'email' => $account['Email'],
	// 				'admin' => 0
	// 			);
	// 		}
	// 	}, $accountsJSON);
	// }
	// $notification_contacts = returnNotificationContacts();
	// $notification_information = array(
	// 	'account_contacts' => $account_contacts,
	// 	'notification_contacts' => array_values( $notification_contacts )
	// );

	$sudo_users = User::where('sudo',true)
		->where('active',true)
		->get();
	$labs = Lab::all();
	$lab_users = [];
	$lab_admins = [];
	foreach( $labs as $lab )
	{
		$lab_users[$lab->name] = User::where('lab_id',$lab->id)
			->where('active',true)
			->get();

		$lab_admins[$lab->name] = User::where('lab_id',$lab->id)
			->where('active',true)
			->where('lab_admin',true)
			->get();
	}
	$all_users = User::where('active',true)
		->get();

	$notification_information = array(
		'all_users' => $all_users,
		'lab_users' => $lab_users,
		'lab_admins' => $lab_admins,
		'sudo_users' => $sudo_users
	);
	// $acct_users = User::where('sudo',false)
	// 	->where('active',true)
	// 	->get();
	


	$data = array('switch_cmd' => 'ReturnContactData', 'contactData' => $notification_information);
	$response = json_encode($data);
	print_r($response);
}

function returnNotificationContacts()
{
	$notification_contacts_path = (file_exists('/var/www/html/acct/notification_contacts.json')) ? '/var/www/html/acct/notification_contacts.json' : '../../../acct/notification_contacts.json';
	$notification_contacts = (file_exists($notification_contacts_path)) ? json_decode( file_get_contents( $notification_contacts_path ),true) : [];
	return $notification_contacts;
}

function sendEmailToolNotification( $params )
{
	$emails = array_unique( array_merge( explode(';', $params['notification_data']['selectedEmails'] ), explode(';', $params['notification_data']['manualEmails'] )));
	$subject = htmlspecialchars( $params['notification_data']['emailSubject'] );
	$message = nl2br( htmlspecialchars( $params['notification_data']['emailBody'] ));
	$reply_to = $params['notification_data']['replyTo'];
	foreach( $emails as $email )
	{
		$email = trim( $email );
		if( !empty( $email ))
		{
			submitEmailToolNotification( $email, $subject, $message, $reply_to );
		}
	}
	$data = array('switch_cmd' => 'ReturnEmailToolResp', 'emailToolResp' => 'Emails Sent');
	$response = json_encode($data);
	print_r($response);
}

function submitEmailToolNotification( $email,$subject,$message,$reply_to )
{
	$host = (isset($_SERVER['SERVER_NAME'])) ? $_SERVER['SERVER_NAME'] : $_SERVER['HTTP_HOST'];
	$to = $email;
	$subject = $subject;
	$body = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CompBio Project Completion</title>
    <!--[if gte mso 9]><xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml><![endif]-->
    <style>
    #title-wrap {
        margin: auto 0;
        font-size: 1.2rem;
        text-align: center;
        font-variant: small-caps;
        font-weight: bold
    }
	 #info {
		padding: 5px;
	 }
    </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
    <div id="title-wrap">
        <h2 class="title">'. $subject .'</h2>
		  <small>From Server: '. $host .'</small>
    </div>
    <hr>
    <div id="info-wrap">
        <div id="info">
	 		'. $message .'
        </div>
    </div>
</body>

</html>';

	$headers = "MIME-Version: 1.0" . "\r\n";
	$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
	$headers .= 'From: <<EMAIL>>' . "\r\n";
	if( isset( $reply_to ) && $reply_to !== 0)
	{
		$headers .= 'Reply-To: '. $reply_to. "\r\n";
	}
	mail($to, $subject, $body, $headers);
}
