<?php
    ini_set('memory_limit', '-1');
    set_time_limit(0);
    include('ipa.php');
    include('../../../config/config.php');
    if(isset($_POST['aers']))
    {
        $data = json_decode($_POST['aers'], true);
        $labname = $data['account-name'];
        $proName = $data['aers-submit-name'];
        $aersValue = $data['queryArray'];
        $includedTerms = $data['includedTerms'];
        $methodCall = "LaunchAERSBioExplorerRun";
        $numConcepts = 0;
        $email = '<EMAIL>';
        $formData = (object) [
            'lab' => $labname,
            'project' => $proName,
            'value' => implode(",",$aersValue),
            'numConcepts' => 0,
            'email' => $email,
            'method' => $methodCall,
            'terms' => $includedTerms
        ];
        aersSubmit( $formData );
    } else
    {
        exit('AERS Not Set!');
    }

    function aersSubmit( $data )
    {
        $ip = getRealIpAddr();
        $server = "http://sandbox7.wucon.wustl.edu";
        $labUp = strtoupper($data->lab);
        $processingDir = join(DIRECTORY_SEPARATOR, [ABS_ROOT,'data',$labUp,'processing',$data->project]);
        $extDir = "/var/www/html/aers/{$data->project}/{$data->project}";
        mkdir($processingDir);
        chmod($processingDir, 0777);
        $command = '{"jsonrpc":"2.0","method":"'.$data->method.'","id":"101","params":[["'.$data->value.'"],"'.$data->terms.'","'.$extDir.'"]}';
        $dirStatus = file_get_contents("{$server}/aers/aers_controller.php?dir_name={$data->project}");
        $o = submitAERSCommand($command);
        $log = (object)[];
        $log->ip = $ip;
        $log->command = $command;
        $log->output = $o;
        date_default_timezone_set('America/Chicago');
        $logEntry = date("Y-m-d h:i:s",time()).':'.preg_replace("/\r|\n|  /","",json_encode($log)).PHP_EOL;
        $logPath = join(DIRECTORY_SEPARATOR,[ABS_ROOT,'acct','log','aers_submit.log']);
        file_put_contents($logPath, $logEntry, FILE_APPEND);
        $info = [
            'Creation_Date' => date("Y-m-d h:i:s",time()),
            'AERS_Project' => true,
            'Method' => $data->method,
            'Value_Submitted' => $data->value,
            'Terms' => $data->terms
        ];
        if(strpos($o, 'Success') !== false)
        {
            $info['NullResult'] = false;
            $userDataPath = join(DIRECTORY_SEPARATOR,[$processingDir,'UserData']);
            mkdir($userDataPath);
            chmod($userDataPath, 0777);
            $wgetCommand = 'wget -r -nH -np --cut-dirs=2 -R "index.html*" http://sandbox7.wucon.wustl.edu/aers/'.$data->project.'/ -P '.$processingDir.DIRECTORY_SEPARATOR;
            exec($wgetCommand,$exOut,$exVar);
            $jsonPath = join(DIRECTORY_SEPARATOR,[ABS_ROOT,'data',$labUp,'account_info','projects.json']);
            if(file_exists($jsonPath))
            {
                $jsonObj = json_decode(file_get_contents($jsonPath));
            } else
            {
                $jsonObj = '{"folders": {},"projects": [], "explorer_projects": [], "aers_projects": [], "failed": [],"deleted": []}';
                $jsonObj = json_decode($jsonObj);
            }
            if(!property_exists($jsonObj, 'aers_projects'))
            {
                $jsonObj->aers_projects = [];
            }
            array_push($jsonObj->aers_projects, $data->project);
            file_put_contents($jsonPath, json_encode($jsonObj, JSON_PRETTY_PRINT), LOCK_EX);
        } else
        {
            $info['NullResult'] = true;
        }
        $inputDir = join(DIRECTORY_SEPARATOR,[$processingDir,'Input']);
        mkdir($inputDir);
        chmod($inputDir, 0777);
        $jsonInfo = json_encode($info, JSON_PRETTY_PRINT);
        $infoFilePath = join(DIRECTORY_SEPARATOR,[$inputDir,'info.json']);
        file_put_contents($infoFilePath, $jsonInfo);
        $finalProjectPath = ($info['NullResult'] === false) ? join(DIRECTORY_SEPARATOR,[ABS_ROOT,'data',$labUp,$data->project]) : join(DIRECTORY_SEPARATOR,[ABS_ROOT,'data',$labUp,'failed',$data->project]);
        if(file_exists($finalProjectPath))
        {
            rmrf($finalProjectPath);
        }
        rename($processingDir, $finalProjectPath);
        $remStatus = file_get_contents("{$server}/aers/aers_controller.php?rem_name={$data->project}");
        $data = array('switch_cmd' => 'ReturnAers', 'Project' => $data->project, 'Output' => json_decode($o), 'RemoveStatus' => $remStatus);
        $response = json_encode($data);
        print_r($response);
        exit;
    }

    function submitAERSCommand($cmd)
    {
        $server = "http://sandbox7.wucon.wustl.edu";
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "{$server}:8001/pcmmrpc");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $cmd);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');

        $headers = array();
        $headers[] = 'Connection: keep-alive';
        $headers[] = 'Content-Type: text/plain;charset=UTF-8';
        $headers[] = 'Accept: */*';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);


        $result = curl_exec($ch);
        if (curl_errno($ch))
        {
            return 'Error:' . curl_error($ch);
        }
        curl_close($ch);

        return $result;
    }

    /**
     * Remove the directory and its content (all files and subdirectories).
     * @param string $dir the directory name
     */
    function rmrf($dir)
    {
        foreach (glob($dir) as $file)
        {
            if (is_dir($file))
            {
                rmrf("$file/*");
                rmdir($file);
            } else
            {
                unlink($file);
            }
        }
    }
?>

