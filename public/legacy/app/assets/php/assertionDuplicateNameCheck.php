<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // <PERSON>trap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Lab;
use App\Models\Assertion;
use App\Models\AssertionLibrary;

header('Content-Type: application/json'); // Set response header

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(400); // Bad request
    echo json_encode(["error" => "Invalid request method."]);
    exit;
}

// Decode JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['AcctId']) || !isset($input['Name'])) {
    http_response_code(422); // Unprocessable Entity
    echo json_encode(["error" => "Missing required parameters."]);
    exit;
}

$user = User::find($input['AcctId']);

if (!$user) {
    http_response_code(404); // Not Found
    echo json_encode(["error" => "User not found."]);
    exit;
}

$labDir = config('app.custom_env.ACCOUNT_STORAGE_ROOT') . "/{$user->lab->directory}";
$assertionDir = "{$labDir}/account_info/assertion2";

if (file_exists("$assertionDir/{$input['Name']}")) {
    echo json_encode(["exists" => true]);
} else {
    echo json_encode(["exists" => false]);
}