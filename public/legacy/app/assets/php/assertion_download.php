<?php
session_start();
if(isset($_SESSION['Laboratory']) && isset($_GET['account']) && isset($_SESSION['LoggedIn']) && isset($_SESSION['Id']))
{
	$account = strtoupper($_GET['account']);
	if(isset($_GET['assertion']))
	{
		$assertion = $_GET['assertion'];
		$path = "../../../data/{$account}/account_info/assertion/{$assertion}";
	} else
	{
		$assertion = $_GET['batch'];
		$path = "../../../data/{$account}/account_info/assertion_w_libs/{$assertion}";
	}
	if(file_exists($path))
	{
		$rootPath = realpath($path);
		$zip = new ZipArchive();
		$zip->open("../../../dl/assertion/{$assertion}.zip", ZipArchive::CREATE | ZipArchive::OVERWRITE);
		$files = new RecursiveIteratorIterator(
		    new RecursiveDirectoryIterator($rootPath),
		    RecursiveIteratorIterator::LEAVES_ONLY
		);
		foreach ($files as $name => $file)
		{
		    if (!$file->isDir())
		    {
		        $filePath = $file->getRealPath();
		        $relativePath = substr($filePath, strlen($rootPath) + 1);
		        $zip->addFile($filePath, $relativePath);
		    }
		}
		$zip->close();
		$zipPath = realpath("../../../dl/assertion/{$assertion}.zip");
		if(file_exists($zipPath))
		{
			header('Content-Description: File Transfer');
			header("Content-Type: application/zip");
			header('Content-Disposition: attachment; filename="'.basename($zipPath).'"');
			header('Expires: 0');
			header('Cache-Control: must-revalidate');
			header('Pragma: public');
			header('Content-Length: ' . filesize($zipPath));
			ob_clean();
			readfile($zipPath);
		    unlink($zipPath);
		    exit;
		}
	} else
	{
		print_r(json_encode('["!!!!Error!!!! Path not found!"]'));
	}
}

?>