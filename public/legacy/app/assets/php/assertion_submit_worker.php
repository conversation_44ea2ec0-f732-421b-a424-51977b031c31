<?php
require('../../../config/config.php');
include('ipa.php');
ini_set('memory_limit', '-1');
set_time_limit(0);
date_default_timezone_set('America/Chicago');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$_SERVER['REMOTE_ADDR'] = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
if ($argc > 1) {
    $filename = $argv[1];
    if (file_exists($filename)) {
        $projectData = json_decode(file_get_contents($filename));
        unlink($filename);
        assertionSubmit($projectData);
    } else {
      echo 'Missing TEMP file!!!';
    }
} else {
   echo 'Missing argc variables!!!';
}
function assertionSubmit($workerData)
{
   	$assertInfo = $workerData->assertInfo;
		$path = $workerData->path;
      $command = $workerData->command;
      $user_id = $workerData->user_id;
		$email = $workerData->email;
      $ip = $workerData->ip;
		$log = (object)[];
		$log->ip = $ip;
		$log->info = $assertInfo;
		$log->cmd1 = $command;
		exec($command,$output,$return);
		$log->cmd1Output = $output;
		$log->cmd1Return = $return;
		$command = config('app.custom_env.ASSERTION_ENGINE_ROOT')."{$assertInfo->engine}/index.py --root={$path} --json_fn=out.json";
		$log->cmd2 = $command;
		exec($command,$output,$return);
		$log->cmd2Output = $output;
		$log->cmd2Return = $return;
		$log = date("Y-m-d h:i:s",time()).':'.json_encode($log).PHP_EOL;
		file_put_contents( config('app.custom_env.ABS_ROOT').'/acct/log/assertion.log', $log, FILE_APPEND);

		if($return !== 0)
		{
			$data['err'] = $return;
			recursiveDelete($path);
			sendFailureNotification($email, $assertInfo->name);
		} else
		{
			$assertion = Assertion::create([
					'user_id' => $user_id,
					'name' => $assertInfo->name,
					'base' => $assertInfo->base,
					'is_base_lib' => $assertInfo->isBaseLib,
					'secondary' => $assertInfo->secondary,
					'is_secondary_lib' => $assertInfo->isSecondaryLib,
					'is_lib_assertion' => $assertInfo->isLibAssertion,
					'is_lib_to_lib_assertion' => $assertInfo->isLibToLibAssertion,
					'use_significant' => $assertInfo->useSignificant,
					'engine' => $assertInfo->engine
			]);
			$info = json_decode(file_get_contents("{$path}/info.json"));
			$info->id = $assertion->id;
			$info->user_id = $user_id;
			file_put_contents("{$path}/info.json", json_encode($info, JSON_PRETTY_PRINT));
			sendSuccessNotification($email, $assertInfo->name);
		}
}
function recursiveDelete($dir)
{
    if(is_file($dir))
	{
        return unlink($dir);
    } elseif (is_dir($dir))
	{
        $scan = glob(rtrim($dir,'/').'/*');
        foreach($scan as $index=>$path)
		{
            recursiveDelete($path);
        }
        return @rmdir($dir);
    }
}
function sendSuccessNotification($email, $project)
{
   $app_name = config('app.name');
   $subject = "Assertion Completion: $project - $app_name";
   $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Assertion Completion</title>
<!--[if gte mso 9]><xml>
      <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
   </xml><![endif]-->
<style>
#title-wrap {
   margin: auto 0;
   font-size: 1.2rem;
   text-align: center;
   font-variant: small-caps;
   font-weight: bold
}
</style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
<div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
<div id="title-wrap">
   <h2 class="title">Assertion Notification</h2>
</div>
<hr>
<div id="info-wrap">
   <div id="info">
      <p><span style="font-weight: bold;">Assertion Name: </span>'. $project .'</p>
      <p>The submitted Assertion Generation has completed successfully and is now accessible from within the Account Administration page.</p>
      <p>Thanks,</p>
      <p>GTAC and CompBio Team</p>
   </div>
</div>
</body>

</html>';

   $headers = "MIME-Version: 1.0" . "\r\n";
   $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
   $headers .= 'From: <<EMAIL>>' . "\r\n";
   mail($email,$subject,$message,$headers);

}

function sendFailureNotification($email, $project, $err = null)
{
   $app_name = config('app.name');
   $subject = "Assertion Generation Failure: $project - $app_name";
   $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
   <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Assertion Generation Failure</title>
   <!--[if gte mso 9]><xml>
         <o:OfficeDocumentSettings>
         <o:AllowPNG/>
         <o:PixelsPerInch>96</o:PixelsPerInch>
         </o:OfficeDocumentSettings>
      </xml><![endif]-->
   <style>
   #title-wrap {
      margin: auto 0;
      font-size: 1.2rem;
      text-align: center;
      font-variant: small-caps;
      font-weight: bold
   }
   </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
   <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
   <div id="title-wrap"><h2 class="title">Assertion Notification</h2>
   </div>
   <hr>
   <div id="info-wrap">
      <div id="info">
         <p><span style="font-weight: bold;">Assertion Name: </span>'. $project .'</p>
         <p>The submitted Assertion Generation has completed unsuccessfully.';
         if($err !== null)
         {
            $message .= 'With message: <br><br>'.$err.'<br>';
         }
         $message .= '</p>
			<p>Try adjusting the input parameters and resubmitting.</p>
         <p>If you feel this failure was due to a system error, please try to resubmit the project or contact a member of our team and we will investigate further and/or help get your project processed.</p>
         <p>Thanks,</p>
         <p>GTAC and CompBio Team</p>
      </div>
   </div>
</body>

</html>';
   $headers = "MIME-Version: 1.0" . "\r\n";
   $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
   $headers .= 'From: <<EMAIL>>' . "\r\n";
   mail($email,$subject,$message,$headers);
   $email = '<EMAIL>';
   mail($email,$subject,$message,$headers);
}