<?php
include('../../../config/config.php');

$data = json_decode($_POST['PostRequest'],true);

if (isset($data['function']) && isset($data['parameters'])) {
   $function = $data['function'] ?? null;
   $params = $data['parameters'] ?? null;
   $json = $function($params);
   print_r($json);
} else {
   die('Error!');
}
// Full Annotation
function getFullAnnotation($params)
{
   /* 
	Cmd Notes:
		Executable for auto annotation:
		/usr/local/bin/plabels

		Basic calling syntax:
		plabels  <Annotation Label File>  < Word Frequency File>  <Gene Synonym File>  <Project CD Matrix>  <Project Theme Coordinate File>  <Output File>

		Example usage:
		/usr/local/bin/plabels  /var/lib/compbio/Auto_Annotation_Labels.txt /var/lib/compbio/PubMed_Word_Frequency.txt /var/lib/compbio/MasterGeneSynonymInput.txt  
		<ProjectPath>/<Project Name>_cd_theme_matrix.txt  <ProjectPath>/<ProjectName>_Theme_Coordinates.js  <ProjectPath>/<ProjectName>_Auto_Annotation.txt	
	*/

   $account = $params['Account'];
   $project = $params['Project'];

   $dirArray = array(ABS_ROOT, 'data', strtoupper($account), urlencode($project));
   $base_dir = join(DIRECTORY_SEPARATOR, $dirArray);
   $exe_output = (object)[];
   $cmd = '';
   if (file_exists($base_dir . DIRECTORY_SEPARATOR . $project . '_Auto_Annotation.txt')) {
      $resp = 'file does exists';
   } else {
      $resp = 'file does NOT exists';
      $cmd = '/usr/local/bin/plabels  /var/lib/compbio/Auto_Annotation_Labels.txt /var/lib/compbio/PubMed_Word_Frequency.txt /var/lib/compbio/MasterGeneSynonymInput.txt ';
      $cmd .= $base_dir . '/' . $project . '_cd_theme_matrix.txt ';
      $cmd .= $base_dir . '/' . $project . '_Theme_Coordinates.js ';
      $cmd .= $base_dir . '/' . $project . '_Auto_Annotation.txt';

      list($r_cmd, $o, $r) = executeAutoAnnotationCmd($cmd);
      $exe_output->cmd = $r_cmd;
      $exe_output->output = $o;
      $exe_output->return = $r;
   }

   $data_path = $base_dir . DIRECTORY_SEPARATOR . $project . '_Auto_Annotation.txt';
   $resp_array = processFullAnnotationFile($data_path);
   $header = $resp_array['header'];
   $parsed = $resp_array['final'];

   $data = array('switch_cmd' => 'FullAnnotateResp', 'Params' => $params, 'Resp' => $resp, 'Cmd' => $cmd, 'Exe_Output' => $exe_output, 'Header' => $header, 'Parsed_Annotes' => $parsed);
   // $response = json_encode($data, JSON_INVALID_UTF8_SUBSTITUTE);
   $response = json_encode($data, JSON_INVALID_UTF8_IGNORE);
   return $response;
}

function processFullAnnotationFile($file_path)
{
   $file = file_get_contents($file_path);
   $final = array();
   $file = str_replace('&', 'and', $file);
   $exp = "/Theme [0-9]+/";
   $annotations = preg_split($exp, $file);
   $header = array_shift($annotations);
   if(!empty($header)) 
   {
      $header_array = explode("\n", $header);
      array_shift($header_array);
      foreach( $header_array as $key => $value )
      {
         $header_array[$key] = trim($value);
      }
      $header = $header_array;
   }
   $index = 1;
   foreach ($annotations as $annotation) {
      $tmp = (object)[];
      $tmp->name = 'Theme ' . $index;
      $annotation = trim($annotation);
      $annotation = explode("\n", $annotation);
      foreach ($annotation as $key => $value) {
         $annotation[$key] = trim($value);
      }
      $tmp->annotations = array_filter($annotation);
      $index++;
      array_push($final, $tmp);
   }
   $resp = array( 'header' => $header, 'final' => $final);
   return $resp;
}

function executeAutoAnnotationCmd($command)
{
   exec($command, $output, $return);
   return [$command, $output, $return];
}