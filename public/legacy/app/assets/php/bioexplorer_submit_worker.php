<?php
require('../../../config/config.php');
include('ipa.php');
ini_set('memory_limit', '-1');
set_time_limit(0);
date_default_timezone_set('America/Chicago');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$_SERVER['REMOTE_ADDR'] = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
if ($argc > 1) {
    $filename = $argv[1];
    if (file_exists($filename)) {
        $projectData = json_decode(file_get_contents($filename));
        unlink($filename);
        bioExplorerSubmit($projectData);
    } else {
      echo 'Missing TEMP file!!!';
    }
} else {
   echo 'Missing argc variables!!!';
}
function bioExplorerSubmit( $workerData )
{
   $data = $workerData->formData;
   $user_id = $workerData->user_id;
   $email = $workerData->email;
   $ip = $workerData->ip;
   /**
   * $argValue = escapeshellarg($data->value);
   * $terms = escapeshellarg($data->terms);
   * $command = "/usr/local/bin/run_compbio.py -z \"{$argValue}\" -y \"{$terms}\" --gene-file /var/www/html/temp/gene_file.txt --context-file /var/www/html/temp/context.txt --num-concepts {$data->numConcepts} --experiment-name {$data->project} --lab-name {$data->lab} -m {$data->email}";
   */
   $labUp = strtoupper($data->lab);
   $labPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$labUp}";
   $processingDir = "{$labPath}/processing/{$data->project}";
   mkdir($processingDir, 0777);
   $command = '{"jsonrpc": "2.0","method": "'.$data->method.'","id": "225","params": [["'.implode('","', $data->value).'"],"'.$data->terms.'","'.$processingDir.'/'.$data->project.'"]}';
   $o = submitCommand($command);
   $log = date("Y-m-d h:i:s",time());
   $logEntry = preg_replace("/\r|\n|  /","","{$log} -> {$ip} -> Output(all) : {$o} -> Command : {$command}").PHP_EOL;
   $logPath = config('app.custom_env.ABS_ROOT').'/acct/log/explorer_submit.log';
   file_put_contents( $logPath, $logEntry, FILE_APPEND);

   if (strpos($o, 'Success') !== false && strpos($o, 'Failure') == false)
   {
      rename("{$labPath}/processing/{$data->project}", "{$labPath}/{$data->project}");
      mkdir("{$labPath}/{$data->project}/Input", 0777);
      $info = [
         'Creation_Date' => $log,
         'Bio_Explorer' => true,
         'Method' => $data->method,
         'Value_Submitted' => $data->value,
         'Terms' => $data->terms
      ];
      if(strpos($o, 'Failure: Not enough ideas generated from term.') !== false)
      {
         $info['NullResult'] = true;
      } else
      {
         $info['NullResult'] = false;
         mkdir("{$labPath}/{$data->project}/UserData", 0777);
      }
      $id = $user_id;
      $beProject = [
         'user_id' => $id,
         'name' => $data->project,
         'status' => 'complete',
         'field' => null,
         'value' => (gettype($data->value) == 'string') ? (array)str_replace("'", "", $data->value) : $data->value,
         'method' => $data->method,
         'terms' => (isset($data->terms)) ? explode(",", $data->terms) : null,
         'directory' => "{$labPath}/{$data->project}"
      ];
      BeProject::create($beProject);
      
      $jsonInfo = json_encode($info, JSON_PRETTY_PRINT);
      file_put_contents("{$labPath}/{$data->project}/Input/info.json", $jsonInfo);
      sendSuccessNotification($email, $data->project);
      $data = array('switch_cmd' => 'ReturnBioExplorer', 'Project' => $data->project, 'Output' => json_decode($o));
      $response = json_encode($data);
      print_r($response);
   } else
   {
      if(file_exists("{$labPath}/failed/{$data->project}"))
      {
         $i = 1;
         while (file_exists("{$labPath}/{$data->project}{$i}") || $i > 1000) {
            $i++;
         }
         rename("{$labPath}/processing/{$data->project}", "{$labPath}/failed/{$data->project}{$i}");
      } else {
         rename("{$labPath}/processing/{$data->project}", "{$labPath}/failed/{$data->project}");
      }
      if(strpos($o, 'Failure') !== false)
      {
         $json_obj = json_decode($o);
         if (json_last_error() === JSON_ERROR_NONE && isset($json_obj->result)) {
            $errmsg = $json_obj->result;
            sendFailureNotification($email, $data->project, $errmsg);
         } else {
            sendFailureNotification($email, $data->project);
         }
      } else {
         sendFailureNotification($email, $data->project);
      }
      $data = array('switch_cmd' => 'ReturnBioExplorer', 'Project' => $data->project, 'Output' => $o);
      $response = json_encode($data);
      print_r($response);
   }
}

function submitCommand($cmd)
{
   $ch = curl_init();
   curl_setopt($ch, CURLOPT_URL, 'localhost:8001/pcmmrpc');
   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
   curl_setopt($ch, CURLOPT_POST, 1);
   curl_setopt($ch, CURLOPT_POSTFIELDS, $cmd);
   curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');
   $headers = array();
   $headers[] = 'Connection: keep-alive';
   $headers[] = 'Content-Type: text/plain;charset=UTF-8';
   $headers[] = 'Accept: */*';
   curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
   $result = curl_exec($ch);
   if (curl_errno($ch))
   {
      return 'Error:' . curl_error($ch);
   }
   curl_close($ch);
   return $result;
}

/**
 * Remove the directory and its content (all files and subdirectories).
 * @param string $dir the directory name
 */
function rmrf($dir)
{
   foreach (glob($dir) as $file)
   {
      if (is_dir($file))
      {
         rmrf("$file/*");
         rmdir($file);
      } else
      {
         unlink($file);
      }
   }
}

function sendSuccessNotification($email, $project)
{
   $app_name = config('app.name');
   $subject = "BioExplorer Project Completion: $project - $app_name";
   $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>BioExplorer Project Completion</title>
<!--[if gte mso 9]><xml>
      <o:OfficeDocumentSettings>
      <o:AllowPNG/>
      <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
   </xml><![endif]-->
<style>
#title-wrap {
   margin: auto 0;
   font-size: 1.2rem;
   text-align: center;
   font-variant: small-caps;
   font-weight: bold
}
</style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
<div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
<div id="title-wrap">
   <h2 class="title">BioExplorer Project Notification</h2>
</div>
<hr>
<div id="info-wrap">
   <div id="info">
      <p><span style="font-weight: bold;">Project Name: </span>'. $project .'</p>
      <p>The project submitted to BioExplorer has completed successfully and is now accessible from within the Account Administration page.</p>
      <p>Thanks,</p>
      <p>GTAC and CompBio Team</p>
   </div>
</div>
</body>

</html>';

   $headers = "MIME-Version: 1.0" . "\r\n";
   $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
   $headers .= 'From: <<EMAIL>>' . "\r\n";
   mail($email,$subject,$message,$headers);

}

function sendFailureNotification($email, $project, $err = null)
{
   $app_name = config('app.name');
   $subject = "BioExplorer Project Failure: $project - $app_name";
   $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
   <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>BioExplorer Project Submission Failure</title>
   <!--[if gte mso 9]><xml>
         <o:OfficeDocumentSettings>
         <o:AllowPNG/>
         <o:PixelsPerInch>96</o:PixelsPerInch>
         </o:OfficeDocumentSettings>
      </xml><![endif]-->
   <style>
   #title-wrap {
      margin: auto 0;
      font-size: 1.2rem;
      text-align: center;
      font-variant: small-caps;
      font-weight: bold
   }
   </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
   <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
   <div id="title-wrap"><h2 class="title">BioExplorer Project Notification</h2>
   </div>
   <hr>
   <div id="info-wrap">
      <div id="info">
         <p><span style="font-weight: bold;">Project Name: </span>'. $project .'</p>
         <p>The project submitted to BioExplorer has completed unsuccessfully.';
         if($err !== null)
         {
            $message .= 'With message: <br><br>'.$err.'<br>';
         }
         $message .= '</p>
         <p>If you feel this failure was due to a system error, please try to resubmit the project or contact a member of our team and we will investigate further and/or help get your project processed.</p>
         <p>Thanks,</p>
         <p>GTAC and CompBio Team</p>
      </div>
   </div>
</body>

</html>';
   $headers = "MIME-Version: 1.0" . "\r\n";
   $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
   $headers .= 'From: <<EMAIL>>' . "\r\n";
   mail($email,$subject,$message,$headers);
   $email = '<EMAIL>';
   mail($email,$subject,$message,$headers);
}