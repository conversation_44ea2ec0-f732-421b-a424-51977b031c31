<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
// include('compbio_submit.php');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
$id = $_SESSION['Id'];
$ip = getRealIpAddr();
if (isset($_POST['cb_batch_data'])) {
   $batchProject = json_decode($_POST['cb_batch_data']);
   $gen_info = array_shift($batchProject);
   if (!empty($gen_info->account) && !empty($gen_info->contactEmail)) {
      $upAcct = strtoupper($gen_info->account);
      $data_dir_path = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$upAcct}/processing/";
      $batch_status_dir = $data_dir_path.'batch';
      if(!file_exists($batch_status_dir))
      {
         mkdir($batch_status_dir,0777);
      }
      $batch_job_names = array_column($batchProject,'name');
      $current_index = 0;
      array_unshift($batch_job_names, $current_index);
      $batch_id = uniqid();
      // $output = '';
      file_put_contents("{$batch_status_dir}/{$batch_id}.json",json_encode($batch_job_names));
      foreach ($batchProject as $singleProject) 
      {
         if(file_exists("{$batch_status_dir}/{$batch_id}.json"))
         {
            $status_array = json_decode(file_get_contents("{$batch_status_dir}/{$batch_id}.json"));
            $current_index++;
            $status_array[0] = $current_index;
            file_put_contents("{$batch_status_dir}/{$batch_id}.json", json_encode($status_array));
            $singleProject->account = $gen_info->account;
            $singleProject->email = $gen_info->contactEmail;
            $singleProject->user_id = $id;
            $singleProject->ip = $ip;
            // $projectData = escapeshellarg(json_encode($singleProject));
            $tempFile = tempnam(sys_get_temp_dir(), 'batch_');
            file_put_contents($tempFile, json_encode($singleProject));
            exec("nohup php submit_batch_project_worker.php $tempFile > /dev/null 2>&1 &");
            // exec("php submit_batch_project_worker.php $projectData > /dev/null 2>&1 &");
         // $projectData = escapeshellarg(json_encode($singleProject));
         // exec("php submit_batch_project_worker.php $projectData > /dev/null 2>&1 &");
         } else {
            echo 'Batch Status File NOT Found...';
            break;
         }
      }
      if (file_exists("{$batch_status_dir}/{$batch_id}.json"))
      {
         echo 'Cleanup Batch Status File...';
         unlink("{$batch_status_dir}/{$batch_id}.json");
      }
      exit('Loop Complete and Exiting Script...');
   } else {
      echo 'Error: Account or Email Data NOT Received!';
      exit('Exiting Script...');
   }
} else {
   echo 'Error: Post Data NOT Received!';
   exit('Exiting Script...');
}

?>