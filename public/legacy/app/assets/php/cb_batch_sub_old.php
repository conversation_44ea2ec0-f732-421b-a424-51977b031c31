<?php
include('compbio_submit.php');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON>vel and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
// $kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
// $request = Illuminate\Http\Request::capture();
// $response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;

if (isset($_POST['cb_batch_data'])) {
   $batchProject = json_decode($_POST['cb_batch_data']);
   $gen_info = array_shift($batchProject);
   if (!empty($gen_info->account) && !empty($gen_info->contactEmail)) {
      $id = $_SESSION['Id'];
      $upAcct = strtoupper($gen_info->account);
      $data_dir_path = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$upAcct}/processing/";
      $batch_status_dir = $data_dir_path.'batch';
      if(!file_exists($batch_status_dir))
      {
         mkdir($batch_status_dir,0777);
      }
      $batch_job_names = array_column($batchProject,'name');
      $current_index = 0;
      array_unshift($batch_job_names, $current_index);
      $batch_id = uniqid();
      $output = '';
      file_put_contents("{$batch_status_dir}/{$batch_id}.json",json_encode($batch_job_names));
      foreach ($batchProject as $singleProject) 
      {
         if(file_exists("{$batch_status_dir}/{$batch_id}.json"))
         {
            $status_array = json_decode(file_get_contents("{$batch_status_dir}/{$batch_id}.json"));
            $current_index++;
            $status_array[0] = $current_index;
            file_put_contents("{$batch_status_dir}/{$batch_id}.json", json_encode($status_array));
            $singleProject->account = $gen_info->account;
            $singleProject->email = $gen_info->contactEmail;
            submitSingleBatchProject($singleProject);
            // $projectData = escapeshellarg(json_encode($singleProject));
            // exec("php submit_batch_project_worker.php $projectData > /dev/null 2>&1 &");
         } else {
            break;
         }
      }
      print_r($output);
      if (file_exists("{$batch_status_dir}/{$batch_id}.json"))
      {
         unlink("{$batch_status_dir}/{$batch_id}.json");
      }
      exit('Loop Complete and Exiting Script...');
   } else {
      echo 'Error: Account or Email Data NOT Received!';
      exit('Exiting Script...');
   }
} else {
   echo 'Error: Post Data NOT Received!';
   exit('Exiting Script...');
}

function submitSingleBatchProject($singleInfo)
{
      global $id;
      $ip = getRealIpAddr();
      date_default_timezone_set('America/Chicago');
      $timeStamp = date("Y-m-d h:i:s", time());
      $log = array($timeStamp, $ip);
      $info = (object)[];
      $list = implode(PHP_EOL, $singleInfo->ents);
      $acct = $singleInfo->account;
      $upAcct = strtoupper($acct);
      $email = $singleInfo->email;
      $projectName = $singleInfo->name;
      $tmpPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$upAcct}/processing/{$projectName}";
      mkdir($tmpPath, 0777);
      mkdir("{$tmpPath}/UserData", 0777);
      mkdir("{$tmpPath}/Input", 0777);
      $info->Creation_Date = $timeStamp;
      $info->Submitted_By = $email;
      $info->Num_Entities = count($singleInfo->ents);
      $info->Status = "processing";
      // Build CbProject Obj
      $cbProject = [
         "name" => $projectName,
         "user_id" => $id,
         "status" => 'complete',
         "entity_count" => $info->Num_Entities,
         "list" => explode("\n", trim($list))
      ];
      file_put_contents("{$tmpPath}/Input/info.json", json_encode($info, JSON_PRETTY_PRINT));
      if (count($singleInfo->expr) > 0) {
         $expList = implode(PHP_EOL, $singleInfo->expr);
         $cbProject['expression'] = explode("\n", trim($expList));
         file_put_contents("{$tmpPath}/Input/expression.txt", $expList);
      }
      file_put_contents("{$tmpPath}/Input/input.txt", $list);
      $cmdPath1 = $tmpPath . '/Input/input.txt';
      $cmdPath2 = $tmpPath . '/' . $projectName;
      $command = '{"jsonrpc": "2.0","method": "LaunchBKGRun","id": "225","params": ["' . $cmdPath1 . '","' . $cmdPath2 . '"]}';
      $response = submitCommand($command);
      array_push($log, "Account: {$acct}", "Project: {$projectName}", "Contact: {$email}", "Command: {$command}", "Response: {$response}");
      $log = preg_replace("/\r|\n|  /", "", implode(",", $log)) . PHP_EOL;
      $tmpLogPath = config('app.custom_env.ABS_ROOT').'/acct/log/batch_submission.log';
      file_put_contents($tmpLogPath, $log, FILE_APPEND);
      $finalPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$upAcct}";
      $projectFinalPath = "{$finalPath}/{$projectName}";
      if (strpos($response, 'Success') !== false) 
      {
         $cbProject['directory'] = $projectFinalPath;
         $info->Status = 'complete';
         file_put_contents("{$tmpPath}/Input/info.json", json_encode($info, JSON_PRETTY_PRINT));
         rename($tmpPath, "{$finalPath}/{$projectName}");
         CbProject::create($cbProject);
         $anote_params = array(
            'Account' => $acct,
            'Project' => $projectName
         );
         $anote_resp = getFullAnnotation($anote_params);
         sendSuccessNotification($email, $projectName);
      } else 
      {
         $info->Status = 'failed';
         file_put_contents("{$tmpPath}/Input/info.json", json_encode($info, JSON_PRETTY_PRINT));
         $failedPath = "{$finalPath}/failed/{$projectName}";
         $originalName = $projectName;
         $i = 1;
         while (file_exists($failedPath)) {
            $projectName = $originalName;
            $failedPath = "{$finalPath}/failed/{$projectName}{$i}";
            $i++;
         }
         rename($tmpPath, "{$finalPath}/failed/{$projectName}");
         sendFailureNotification($email, $projectName);
      }
      // $data = array('switch_cmd' => 'CompBioSubmissionResponse', 'Result' => $info->Status);
      // $response = json_encode($data);
      // return $response;

}
