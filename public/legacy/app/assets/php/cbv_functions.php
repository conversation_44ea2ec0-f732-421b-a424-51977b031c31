<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // <PERSON>trap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\AssertionLibrary;
use App\Models\Folder;
use App\Models\Filter;
use Carbon\Carbon;
if(isset($_SESSION['LoggedIn']))
{
	if(isset($_POST['PostRequest'])) {
		$data = json_decode($_POST['PostRequest'], true);
		if(isset($data['function'] )) {
			$function = $data['function'];
			if($function == 'reauthSession'){
				reauthSession($data['parameters']);
			} else if($function == 'logout') {
				$function();
			} else if($function == 'checkSessionStatus') {
					$prompt = $function();
					$data = array('switch_cmd' => 'SessionStatus', 'Prompt' => $prompt);
					$response = json_encode($data);
					print_r($response);
			} else {
				if($function == 'reauthSuccess') {
					if(isset($_SESSION['last_function'])) {
						$function = $_SESSION['last_function'];
						unset($_SESSION['last_function']);
					}
					if(isset($_SESSION['last_parameters'])) {
						$data['parameters'] = $_SESSION['last_parameters'];
						unset($_SESSION['last_parameters']);
					}
				}
				if($function != 'reauthSuccess') {
					$prompt = checkSessionStatus();
					if($prompt) {
						$_SESSION['last_function'] = $function;
						if(isset($data['parameters'])) {
							$_SESSION['last_parameters'] = $data['parameters'];
						}
						$data = array('switch_cmd' => 'SessionStatus', 'Prompt' => $prompt);
						$response = json_encode($data);
						print_r($response);
					} else {
						$_SESSION['last_action'] = time();
						if(isset($data['parameters'])) {
							$params = $data['parameters'];
							if($data['function'] === 'abstractQueryPHP') {
								require_once(ABS_ROOT.'/app/visualizer/abstract/php/abstract.php');
								$function($params);
							} else {
								$function($params);
							}
						} else {
							if(is_string($function)) {
								$function();
							} else {
								file_put_contents(ABS_ROOT.'/acct/log/php_error.txt', serialize($data), FILE_APPEND);
							}
						}
					}
				} else {
					$data = array('switch_cmd' => 'DeadResponse', 'ReturnFrom' => 'reauthSuccess');
					$response = json_encode($data);
					print_r($response);
				}
			}
		} else {
			// $props = array_keys($_POST);
			// $response = json_encode($_POST['PostRequest']);
			// print_r($response);
			echo 'Function not set!';
		}
	}
}
//process.php
function processPHP($params) {
	$acctId = $params['AcctId'];
	$account = $params['Account'];
	$projectId = $params['ProjectId'];
	$projectName = $params['Project'];
	$profile = $params['Profile'];
	$isBioExplorer = $params['BioExplorerType'];
	$project = ($isBioExplorer) ? BeProject::find($projectId) : CbProject::find($projectId);
	$dir = "{$project->directory}/UserData/userProfileData.txt";
	$clientData = json_decode($params['ProfileData'], true);
	$cmd = 'FileWritten';
	$safeguard = false;

	if(file_exists($dir)) {
		$serverData = json_decode(file_get_contents($dir), true);
	} else {
		$serverData = array();
		$userDataDirectory = "{$project->directory}/UserData";
		if(!file_exists($userDataDirectory))
		{
			mkdir($userDataDirectory);
		}
	}
	if(isset($params['ProfileDelete'])) {
		if(array_key_exists($profile, $serverData)) {
			unset($serverData[$profile]);
			$safeguard = true;
		}
		$cmd = 'ProfileDeleted';
	} else {
		$serverData[$profile] = $clientData[$profile];
		$safeguard = true;
	}
	if(count($serverData) < 1) {
		$serverToClientData = 'Empty';
		unlink($dir);
	} else {
		$serverToClientData = json_encode($serverData);
	}

	$data = array('switch_cmd' => $cmd, 'ProfileData' => $serverToClientData);

	if($safeguard === true && json_last_error() === 0 && count($serverData) > 0) {
		file_put_contents($dir, json_encode($serverData));
	}

    $response = json_encode($data);
    print_r($response);
}
//End

//return.php
function returnPHP($params) {
	$acctId = $params['AcctId'];
	$projectId = $params['ProjectId'];
   $labname = strtoupper($params['Account']);
   $expname = $params['Project'];
	$isBioExplorer = $params['BioExplorerType'];
	$project = ($isBioExplorer) ? BeProject::find($projectId) : CbProject::find($projectId);	
	$dir = "{$project->directory}/UserData/userProfileData.txt";
	if(file_exists($dir)) {
		$str = file_get_contents($dir);
    	$data = array('switch_cmd' => 'SavedProfiles',  'ProfileData' => $str);
	}else {
    	$data = array('switch_cmd' => 'NoFile');
	}
    $response = json_encode($data);
    print_r($response);
}
//End

//export.php
function exportPHP($params) {
	$labname = strtoupper($params['Account']);
	$expname = $params['Project'];
	$acctId = $params['AcctId'];
	$projectId = $params['ProjectId'];
	$isBioExplorer = $params['BioExplorerType'];
	$project = ($isBioExplorer) ? BeProject::find($projectId) : CbProject::find($projectId);	
	$dir = "{$project->directory}/";
	$jsonDecoded = ( is_array( $params['Data'] )) ? $params['Data'] : json_decode( $params['Data'],true );
	$filename = array_shift($jsonDecoded);
	if(file_exists($dir)) {
		$filelink = "{$dir}{$filename}.csv";
		$shortLink = str_replace(config('app.custom_env.ACCOUNT_STORAGE_ROOT'),'',$dir);
		$downloadlink = "legacy/data/{$shortLink}/{$filename}.csv";
	}
	for($i=0;$i<count($jsonDecoded);$i++) {
		for($z=0;$z<count($jsonDecoded[$i]['Genes']);$z++) {
			array_push($jsonDecoded[$i], $jsonDecoded[$i]['Genes'][$z]);
		}
		unset($jsonDecoded[$i]['Genes']);
		for($z=0;$z<count($jsonDecoded[$i]['Concepts']);$z++) {
			array_push($jsonDecoded[$i], $jsonDecoded[$i]['Concepts'][$z]);
		}
		unset($jsonDecoded[$i]['Concepts']);
	}
	$jsonDecoded = transposeData($jsonDecoded);
	for($i=0;$i<count($jsonDecoded)-7;$i++) {
		for($z=0;$z<count($jsonDecoded[$i]);$z++) {
			if(!(array_key_exists($z, $jsonDecoded[$i]))) {
				$jsonDecoded[$i][$z] = "*";
			}
		}
		ksort($jsonDecoded[$i]);
	}
	$fp = fopen($filelink, 'w');
	flock($fp, LOCK_EX);

	foreach($jsonDecoded as $row){
	    fputcsv($fp, $row);
	}
	flock($fp, LOCK_UN);
	fclose($fp);
	header("Content-type: text/csv");
  	header("Content-disposition: attachment; filename = $filename.csv");
    $data = array('switch_cmd' => 'ExportLink',  'Link' => $downloadlink);
    $response = json_encode($data);
    print_r($response);
}

function transposeData($data) {
  $retData = array();
    foreach ($data as $row => $columns) {
      foreach ($columns as $row2 => $column2) {
          $retData[$row2][$row] = $column2;
      }
    }
  return $retData;
}
//End

//removecsv.php
function removecsvPHP($params) {
   //  $jsonString = $params['FilePath'];
   //  $jsonString = ABS_ROOT.DIRECTORY_SEPARATOR.str_replace('/', DIRECTORY_SEPARATOR, $jsonString);
		// $shortLink = str_replace(config('app.custom_env.ACCOUNT_STORAGE_ROOT'),'',$dir);
		$jsonString = str_replace('legacy/data', config('app.custom_env.ACCOUNT_STORAGE_ROOT'),$params['FilePath']);
		// $downloadlink = "legacy/data/{$shortLink}/{$filename}.csv";
    if(file_exists($jsonString)) {
    	$unlinkresponse = unlink($jsonString);
    	if($unlinkresponse) {
    		$str = "FileRemoved";
    	} else {
    		$str = "error!";
    	}
    } else {
    	$str = "Cannot find specified file--> {$jsonString}";
    }
	$data = array('switch_cmd' => 'RemoveExport',  'Response' => $str);
    $response = json_encode($data);
    print_r($response);
}
//End

function getFilterList($params) {

	$user = User::find($params['AcctId'] ?? $_SESSION['Id']);
	$filterName = $params['FilterName'];
	$filter = $user->filters->where('name', $filterName)->first();
	if($filter) {
		$filterList = $filter->list;
	} else {
		$filterList = 'NoList';
	}
	$data = array('switch_cmd' => 'ReturnFilterList',  'List' => $filterList);
   $response = json_encode($data);
   print_r($response);
}

function logout() {
	session_unset();
	session_destroy();
	$data = array('switch_cmd' => 'LoggedOut');
	$response = json_encode($data);
	print_r($response);
}


function reauthSession($params) {
	$match = false;
	$fileContents = file_get_contents('../../../acct/accts.php');
	$fileContents = trim($fileContents, "<?php '");
	$accounts = json_decode($fileContents, true);
	for($i=0;$i<count($accounts);$i++) {
		if($accounts[$i]['Laboratory']==$params['Laboratory']
			&& password_verify($params['Password'], $accounts[$i]['Password'])) {
			$match = true;
			$id = $accounts[$i]['Id'];
			$email = $accounts[$i]['Email'];
			if(isset($accounts[$i]['Sudo'])) {
				$sudo = true;
			}
		}
	}
	if($match == true) {
		$_SESSION['last_action'] = time();
		if(!isset($_SESSION['LoggedIn']) || !$_SESSION['LoggedIn'] || $_SESSION['LoggedIn'] == 0) {
			$_SESSION['LoggedIn'] = true;
			$_SESSION['Id'] = $id;
			$_SESSION['Laboratory'] = $params['Laboratory'];
			$_SESSION['Email'] = $email;
			if(isset($sudo)) {
				$_SESSION['Sudo'] = $sudo;
			}
			if(isset($params['CBV'])) {
				$key = $params['ProjectKey'];
				$_SESSION['ProjectKeys'][$key] = ['Account'=>strtoupper($params['ProjectAcct']),'Project'=>$params['Project']];
				$_SESSION['ExpName'] = $params['Project'];
			}
		}
	}

	$data = array('switch_cmd' => 'Reauth', 'Status' => $match);
	$response = json_encode($data);
	print_r($response);
}
function checkSessionStatus() {
	$expireAfter = 60;
	$prompt = false;
	if(isset($_SESSION['last_action'])){
		$secondsInactive = time() - $_SESSION['last_action'];
		$expireAfterSeconds = $expireAfter * 60;
		// $expireAfterSeconds = $expireAfter;
	    if($secondsInactive >= $expireAfterSeconds){
	    	$prompt = true;
    	}
	} else {
		$prompt = true;
	}
	return $prompt;
}

/* Return Input List */
function returnInputList($params)
{
	$expname = $params['Experiment'];
	$expId = $params['ExpId'];
	$project = CbProject::find($expId);
	$list = $project->list;
	$list = implode("\n", $list);
	// Check for expression data
	$expression = $project->expression;
	if(filled($expression))
	{
		$expression = implode("\n", $expression);
	}
	if( isset($params['EntMapFlag']) && $params['EntMapFlag'] === true )
	{
		$data = array(
			'switch_cmd' => 'EntMapListReturn',
			'List' => $list,
			'ExpressionList' => $expression
		);
	} else {
		$data = array( 
			'switch_cmd' => 'InputList', 
			'List' => $list, 
			'ExpressionList' => $expression 
		);
	}
	$response = json_encode($data);
	print_r($response);
}
