<?php

if(isset($_GET['cmd']) && $_GET['cmd'] === 'copy' && isset($_GET['auth']) && $_GET['auth'] === 'cmarcum')
{
	displayMessage('ATTENTION: You may want to copy the page output text for your records....');
	$cleanDir = '../../../temp/clean';
	if(file_exists($cleanDir))
	{
		recursiveDelete($cleanDir);
	}
	mkdir($cleanDir);
	$accts = array_values(array_diff(scandir('../../../data'), ['.gitkeep','.','..']));
	displayMessage('Accounts Detected:');
	displayMessage($accts);
	foreach($accts as $acct)
	{
		displayMessage("Account: {$acct}");
		$trashDir = "../../../data/{$acct}/trash";
		$trash = array_values(array_diff(scandir($trashDir),['.','..']));
		if(!empty($trash))
		{
			displayMessage('Trash Projects:');
			displayMessage($trash);
			
			$archiveDir = "{$cleanDir}/trash/{$acct}";
			mkdir($archiveDir, 0755, true);
			foreach($trash as $project)
			{
				$inputFile = "{$trashDir}/{$project}/Input/input.txt";
				if(file_exists($inputFile))
				{
					copy($inputFile, "{$archiveDir}/{$project}.txt");
				}
			}
		} else 
		{
			displayMessage('trash empty');
		} 
		$failedDir = "../../../data/{$acct}/failed";
		$failed = array_values(array_diff(scandir($failedDir), ['.','..']));
		if(!empty($failed))
		{
			displayMessage('Failed Projects:');
			displayMessage($failed);

			$archiveDir = "{$cleanDir}/failed/{$acct}";
			mkdir($archiveDir, 0755, true);
			foreach($failed as $project)
			{
				$inputFile = "{$failedDir}/$project/Input/input.txt";
				if(file_exists($inputFile))
				{
					copy($inputFile, "{$archiveDir}/{$project}.txt");
				}
			}
		} else
		{
			displayMessage('failed empty');
		}
	}
	displayMessage('ATTENTION: You may want to copy and save the page output text for your records....');
} elseif(isset($_GET['cmd']) && $_GET['cmd'] === 'remove' && isset($_GET['auth']) && $_GET['auth'] === 'cmarcum')
{
	displayMessage('ATTENTION: You may want to copy and save the page output text for your records....');
	$tempDir = '../../../temp/clean';
	if(file_exists($tempDir))
	{
		recursiveDelete($tempDir);
		displayMessage('Deleting temp/clean');
	}
	$accts = array_values(array_diff(scandir('../../../data'), ['.gitkeep','.','..']));
	displayMessage('Accounts Detected:');
	displayMessage($accts);
	foreach($accts as $acct)
	{
		displayMessage("Account: {$acct}");
		$trashDir = "../../../data/{$acct}/trash";
		if(file_exists($trashDir))
		{
			displayMessage('Removing trash...');
			recursiveDelete($trashDir);
			mkdir($trashDir, 0777);
			displayMessage('New empty trash directory created');
		} else
		{
			displayMessage('no trash directory found...');
		}
		$failedDir = "../../../data/{$acct}/failed";
		if(file_exists($failedDir))
		{
			displayMessage('Removing failures...');
			recursiveDelete($failedDir);
			mkdir($failedDir, 0777);
			displayMessage('New empty failed directory created');
		} else
		{
			displayMessage('no failed directory found...');
		}
	}
	displayMessage('ATTENTION: You may want to copy and save the page output text for your records....');
} else
{
	echo 'Are you supposed to be here?';
}
function displayMessage($message)
{
	if(is_string($message))
	{
		echo "<hr>{$message}";
	} else if(is_array($message))
	{
		echo '<hr><pre>';
		print_r($message);
		echo '</pre>';
	}
}
function recursiveDelete($dir) {
    if(is_file($dir)) {
        return unlink($dir);
    } elseif (is_dir($dir)) {
        $scan = glob(rtrim($dir,'/').'/*');
        foreach($scan as $index=>$path) {
            recursiveDelete($path);
        }
        return @rmdir($dir);
    }	
}
?>