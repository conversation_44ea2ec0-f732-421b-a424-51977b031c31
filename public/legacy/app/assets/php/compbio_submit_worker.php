<?php
require('../../../config/config.php');
include('ipa.php');
ini_set('memory_limit', '-1');
set_time_limit(0);
date_default_timezone_set('America/Chicago');

// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // <PERSON>trap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$_SERVER['REMOTE_ADDR'] = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
if ($argc > 1) {
    $filename = $argv[1];
    if (file_exists($filename)) {
        $projectData = json_decode(file_get_contents($filename));
        unlink($filename);
        processCbProject($projectData);
    } else {
      echo 'Missing TEMP file!!!';
    }
} else {
   echo 'Missing argc variables!!!';
}

function processCbProject($bData)
{
   $timeStamp = date("Y-m-d h:i:s",time());
   $info = (object)[];
   $data = (array)$bData->data;
   $log = (array)$bData->log;
   $id = $bData->user_id;
   $list = $data['Entity_List'];
   $acct = $data['Laboratory_Name'];
   $upAcct = strtoupper($acct);
   $email = $data['Contact_Email'];
   $projectName = $data['Project_Name'];
   $tmpPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$upAcct}/processing/{$projectName}";
   mkdir($tmpPath, 0777);
   mkdir("{$tmpPath}/UserData", 0777);
   mkdir("{$tmpPath}/Input", 0777);
      
   if(isset($data['Creation_Date']))
   {
      $info->Creation_Date = $data['Creation_Date'];
      $info->Reanalyze_Date = $timeStamp;
   } else
   {
      $info->Creation_Date = $timeStamp;
   }
   $info->Submitted_By = $email;
   /*Probably can remove but will leave in for now*/
   $info->Num_Entities = $data['Num_Entities'];
   $info->Num_Concepts = "500";
   /*End*/
   $info->Status = "processing";
      
      $cbProject = [
            "name" => $projectName,
            "user_id" => $id,
            "status" => 'complete',
            "entity_count" => $info->Num_Entities,
            "concept_count" => $info->Num_Concepts,
            "list" => explode("\n", trim($list))
      ];
   file_put_contents("{$tmpPath}/Input/info.json", json_encode($info, JSON_PRETTY_PRINT));
/* Fix/Patch Expression List not being created */
   if (isset($data['Expression_List'])) 
   {
      $expList = $data['Expression_List'];
      $cbProject['expression'] = explode("\n", trim($expList));
      file_put_contents("{$tmpPath}/Input/expression.txt", $expList);
   }

   file_put_contents("{$tmpPath}/Input/input.txt", $list);
   $cmdPath1 = $tmpPath.'/Input/input.txt';
   $cmdPath2 = $tmpPath.'/'.$projectName;
   $command = '{"jsonrpc": "2.0","method": "LaunchBKGRun","id": "225","params": ["'.$cmdPath1.'","'.$cmdPath2.'"]}';
   $response = submitCommand($command);
   array_push($log, "Account: {$acct}", "Project: {$projectName}", "Contact: {$email}", "Command: {$command}", "Response: {$response}");
   $log = preg_replace("/\r|\n|  /","", implode(",",$log)).PHP_EOL;
   $tmpLogPath = config('app.custom_env.ABS_ROOT').'/acct/log/cb_submission.log';
   file_put_contents($tmpLogPath, $log, FILE_APPEND);
   $finalPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$upAcct}";

   $projectFinalPath = "{$finalPath}/{$projectName}";

   if(strpos($response, 'Success') !== false)
   {
      $cbProject['directory'] = $projectFinalPath;
      $info->Status = 'complete';
      file_put_contents("{$tmpPath}/Input/info.json", json_encode($info, JSON_PRETTY_PRINT));
      rename($tmpPath, "{$finalPath}/{$projectName}");
      CbProject::create($cbProject);
      
      $anote_params = array(
         'Account' => $acct,
         'Project' => $projectName
      );
      $anote_resp = getFullAnnotation($anote_params);
      sendSuccessNotification($email, $projectName);
   } else
   {
      $info->Status = 'failed';
      file_put_contents("{$tmpPath}/Input/info.json", json_encode($info, JSON_PRETTY_PRINT));
      $failedPath = "{$finalPath}/failed/{$projectName}";
      $originalName = $projectName;
      $i = 1;
      while(file_exists($failedPath))
      {
         $projectName = $originalName;
         $failedPath = "{$finalPath}/failed/{$projectName}{$i}";
         $i++;
      }
      rename($tmpPath, "{$finalPath}/failed/{$projectName}");
      sendFailureNotification($email, $projectName);
   }
}
function submitCommand($cmd)
    {
		$ch = curl_init();
   	    curl_setopt($ch, CURLOPT_URL, 'localhost:8001/pcmmrpc');
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $cmd);
		curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');

		$headers = array();
		$headers[] = 'Connection: keep-alive';
		$headers[] = 'Content-Type: text/plain;charset=UTF-8';
		$headers[] = 'Accept: */*';
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);


		$result = curl_exec($ch);
		if (curl_errno($ch))
		{
			return 'Error:' . curl_error($ch);
		}
		curl_close($ch);

		return $result;
    }

    function sendSuccessNotification($email, $project)
    {
        $subject = "Project Completion: $project";
        $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CompBio Project Completion</title>
    <!--[if gte mso 9]><xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml><![endif]-->
    <style>
    #title-wrap {
        margin: auto 0;
        font-size: 1.2rem;
        text-align: center;
        font-variant: small-caps;
        font-weight: bold
    }
    </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
    <div id="title-wrap">
        <h2 class="title">CompBio Project Notification</h2>
    </div>
    <hr>
    <div id="info-wrap">
        <div id="info">
            <p><span style="font-weight: bold;">Project Name: </span>'. $project .'</p>
            <p>The project submitted to CompBio has completed successfully and is now accessible from within the Account Administration page. Please contact us or submit any problems with the project using the feedback tool within the application.</p>
            <p>Thanks,</p>
            <p>GTAC and CompBio Team</p>
        </div>
    </div>
</body>

</html>';

        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= 'From: <<EMAIL>>' . "\r\n";
        mail($email,$subject,$message,$headers);

    }

    function sendFailureNotification($email, $project)
    {
	    $subject = "Project Processing Failure: $project";
        $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

	<head>
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>CompBio Project Submission Failure</title>
	    <!--[if gte mso 9]><xml>
	          <o:OfficeDocumentSettings>
	            <o:AllowPNG/>
	            <o:PixelsPerInch>96</o:PixelsPerInch>
	          </o:OfficeDocumentSettings>
	        </xml><![endif]-->
	    <style>
	    #title-wrap {
	        margin: auto 0;
	        font-size: 1.2rem;
	        text-align: center;
	        font-variant: small-caps;
	        font-weight: bold
	    }
	    </style>
	</head>

	<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
	    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
	    <div id="title-wrap"><h2 class="title">CompBio Project Notification</h2>
	    </div>
	    <hr>
	    <div id="info-wrap">
	        <div id="info">
	            <p><span style="font-weight: bold;">Project Name: </span>'. $project .'</p>
	            <p>The project submitted to CompBio has completed unsuccessfully.  Most processing failures are due to errors in the input list.  Make sure to read the "Notices" on the submission tab in the account "Administration Panel" and confirm that your inputs are adhering to the guidelines.  For further assistance you may contact a member of our team and we will investigate further and/or help get your project processed.</p>
	            <p>Thanks,</p>
	            <p>GTAC and CompBio Team</p>
	</body>

	</html>';
	    $headers = "MIME-Version: 1.0" . "\r\n";
	    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
	    $headers .= 'From: <<EMAIL>>' . "\r\n";
	    mail($email,$subject,$message,$headers);
	    $email = '<EMAIL>';
	    mail($email,$subject,$message,$headers);
    }

/* Include and Prepare Auto-Annotations Processing In Project Processing Sequence */
	function getFullAnnotation($params)
	{
		/* 
		Cmd Notes:
			Executable for auto annotation:
			/usr/local/bin/plabels

			Basic calling syntax:
			plabels  <Annotation Label File>  < Word Frequency File>  <Gene Synonym File>  <Project CD Matrix>  <Project Theme Coordinate File>  <Output File>

			Example usage:
			/usr/local/bin/plabels  /var/lib/compbio/Auto_Annotation_Labels.txt /var/lib/compbio/PubMed_Word_Frequency.txt /var/lib/compbio/MasterGeneSynonymInput.txt  
			<ProjectPath>/<Project Name>_cd_theme_matrix.txt  <ProjectPath>/<ProjectName>_Theme_Coordinates.js  <ProjectPath>/<ProjectName>_Auto_Annotation.txt	
		*/

		$account = strtoupper($params['Account']);
		$project = $params['Project'];
		$base_dir = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$account}/{$project}";
		$exe_output = (object)[];
		$cmd = '';
		if (file_exists("{$base_dir}/{$project}_Auto_Annotation.txt")) {
			$resp = 'file does exists';
		} else {
			$resp = 'file does NOT exists';
			$cmd = '/usr/local/bin/plabels  /var/lib/compbio/Auto_Annotation_Labels.txt /var/lib/compbio/PubMed_Word_Frequency.txt /var/lib/compbio/MasterGeneSynonymInput.txt ';
			$cmd .= "{$base_dir}/{$project}_cd_theme_matrix.txt ";
			$cmd .= "{$base_dir}/{$project}_Theme_Coordinates.js ";
			$cmd .= "{$base_dir}/{$project}_Auto_Annotation.txt";

			list($r_cmd, $o, $r) = executeAutoAnnotationCmd($cmd);
			$exe_output->cmd = $r_cmd;
			$exe_output->output = $o;
			$exe_output->return = $r;
		}

		$data_path = "{$base_dir}/{$project}_Auto_Annotation.txt";
		$parsed = processFullAnnotationFile($data_path);
		$json = json_encode($parsed, JSON_INVALID_UTF8_SUBSTITUTE);
		$json_path = str_replace('_Auto_Annotation.txt', '_Auto_Annotation.json', $data_path);
		file_put_contents($json_path, $json);

		$data = array( 'switch_cmd' => 'FullAnnotateResp', 'Params' => $params, 'Resp' => $resp, 'Cmd' => $cmd, 'Exe_Output' => $exe_output );
		return $data;
	}

	function processFullAnnotationFile($file_path)
	{
		$file = str_replace('&','and',file_get_contents($file_path));
		$final = array();
		$exp = "/Theme [0-9]+/";
		$annotations = preg_split($exp, $file);
		$header = array_shift($annotations);
		$tmpHeader = (object)[];
		$tmpHeader->name = 'header';
		$tmpHeader->annotations = $header;
		$index = 1;
		foreach ($annotations as $annotation) {
			$tmp = (object)[];
			$tmp->name = 'Theme ' . $index;
			$annotation = trim($annotation);
			$annotation = explode("\n", $annotation);
			foreach ($annotation as $key => $value) {
				$annotation[$key] = trim($value);
			}
			$tmp->annotations = array_filter($annotation);
			$index++;
			array_push($final, $tmp);
		}
		array_unshift($final, $tmpHeader);
		return $final;
	}
	function executeAutoAnnotationCmd($command)
	{
		exec($command, $output, $return);
		return [$command, $output, $return];
	}