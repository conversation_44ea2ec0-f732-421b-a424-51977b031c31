<?php
	// session_start();
	// include('ipa.php');
	// $ip = getRealIpAddr();
	// $sessionData = $_SESSION;
	// $debug = $_POST['debug'];
	// date_default_timezone_set('America/Chicago');
	// $log = array(date("Y-m-d h:i:s",time()), $ip, $sessionData, $debug);
	// $logEntry = stripslashes(preg_replace("/\r|\n/","", json_encode($log))).PHP_EOL;
	// file_put_contents('/var/www/html/acct/log/debug.log', $logEntry, FILE_APPEND | LOCK_EX);
	// echo 'logged!';
	// exit;
	echo 'HEY THERE!';
?>