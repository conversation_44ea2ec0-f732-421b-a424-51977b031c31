<?php
include('../../../config/config.php');
$host = (isset($_SERVER['SERVER_NAME'])) ? $_SERVER['SERVER_NAME'] : $_SERVER['HTTP_HOST'];
    if (isset($_POST['Email'])) {
        $project = $_POST['Experiment'];
        $to = $_POST['Email'];
        $subject = "Project Completion: $project";
        $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CompBio Project Completion</title>
    <!--[if gte mso 9]><xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml><![endif]-->
    <style>
    #title-wrap {
        margin: auto 0;
        font-size: 1.2rem;
        text-align: center;
        font-variant: small-caps;
        font-weight: bold
    }
    </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
    <div id="title-wrap">
        <h2 class="title">CompBio Project Notification</h2>
    </div>
    <hr>
    <div id="info-wrap">
        <div id="info">
            <p><span style="font-weight: bold;">Project Name: </span>'. $project .'</p>
            <p>The project submitted to CompBio has completed successfully and is now accessible from within the Account Administration page. Please contact us or submit any problems with the project using the feedback tool within the application.</p>
            <p>Thanks,</p>
            <p>GTAC and CompBio Team</p>
        </div>
    </div>
</body>

</html>';

        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= 'From: <<EMAIL>>' . "\r\n";
        mail($to,$subject,$message,$headers);
    }

function emailNewAccount($acct, $email, $pass) {
    $host = (isset($_SERVER['SERVER_NAME'])) ? $_SERVER['SERVER_NAME'] : $_SERVER['HTTP_HOST'];
    $to = $email;
    $subject = "Compbio Account Created";
    $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CompBio Project Completion</title>
    <!--[if gte mso 9]><xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml><![endif]-->
    <style>
    #title-wrap {
        margin: auto 0;
        font-size: 1.2rem;
        text-align: center;
        font-variant: small-caps;
        font-weight: bold
    }
    </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
    <div id="title-wrap">
        <h2 class="title">CompBio Account Created</h2>
    </div>
    <hr>
    <div id="info-wrap">
        <div id="info">
            <p>
                Your request has been approved and a new Compbio account has been created. <strong><b>Please Note</b></strong> that you will need to be on the WUSTL network, either physically or by VPN, to access the tool.  It is highly recommended to change the account password after the first login via the "Account > Maintenance" tab.
            </p>

            <p>
                Your login credentials are as follows:
                <br><br>
                <strong><b>Account Name:</b></strong>          ' . $acct . '
                <br>
                <strong><b>Temporary Password:</b></strong>    ' . $pass . '
                <br><br>
            </p>
            <p>
                You can access your CompBio account here:     <a href="' . WEBROOT . '">' . WEBROOT . '</a>
                <br><br>
                Please feel free to contact <a href="mailto:<EMAIL>">Chad Storer</a> or <a href="mailto:<EMAIL>">Ruteja Barve</a> if you would like a brief intro and training for CompBio.
            </p>
        </div>
    </div>
</body>

</html>';

        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= 'From: <<EMAIL>>' . "\r\n";
        mail($to,$subject,$message,$headers);
        $subject .= " - $host";
        mail('<EMAIL>',$subject,$message,$headers);
}

function emailNewAccountAdminNotification($acct, $email)
{
    $host = (isset($_SERVER['SERVER_NAME'])) ? $_SERVER['SERVER_NAME'] : $_SERVER['HTTP_HOST'];
    $machine_name = strtoupper( explode( '.',$host )[0] );
    $to = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
    $subject = $machine_name." - CompBio New Account Created";
    $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CompBio Project Completion</title>
    <!--[if gte mso 9]><xml>
          <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml><![endif]-->
    <style>
    #title-wrap {
        margin: auto 0;
        font-size: 1.2rem;
        text-align: center;
        font-variant: small-caps;
        font-weight: bold
    }
    </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
    <div id="title-wrap">
        <h2 class="title">CompBio Account Created</h2>
    </div>
    <hr>
    <div id="info-wrap">
        <div id="info">
            <p>This is an auto-generated notification from '. $host .' </p>
            <p><span style="font-weight: bold;">Account Name: </span>' . $acct . '</p>
            <p><span style="font-weight: bold;">Registered Email: </span>' . $email . '</p>
            <p>-CompBio Admin Notification</p>
        </div>
    </div>
</body>

</html>';

    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= 'From: <<EMAIL>>' . "\r\n";
    mail($to, $subject, $message, $headers);
    $subject .= " - $host";
    mail('<EMAIL>', $subject, $message, $headers);
}


?>

