<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
ini_set('memory_limit', '-1');
set_time_limit(0);
date_default_timezone_set('America/Chicago');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
$user_id = $_SESSION['Id'];
$ip = getRealIpAddr();

if(isset($_POST['explorer']))
    {
        $data = json_decode($_POST['explorer'], true);
        $labname = $data['account-name'];
        $proName = $data['explorer-name'];
        $explorerValue = $data['queryArray'];
        $includedTerms = $data['includedTerms'];
        $methodCall = $data['methodCall'];
        $numConcepts = 0;
        $email = $_SESSION['Email'];
        $formData = (object) [
            'lab' => $labname,
            'project' => $proName,
            'value' => $explorerValue,
            'numConcepts' => 0,
            'email' => $email,
            'method' => $methodCall,
            'terms' => $includedTerms
        ];
        $tmpWorkerData = (object)[];
        $tmpWorkerData->formData = $formData;
        $tmpWorkerData->user_id = $user_id;
        $tmpWorkerData->ip = $ip;
        $tmpWorkerData->email = $email;
        $tempFile = tempnam(sys_get_temp_dir(), 'batch_');
        file_put_contents($tempFile, json_encode($tmpWorkerData));
        exec("nohup php bioexplorer_submit_worker.php $tempFile > /dev/null 2>&1 &");
        // $data = array('switch_cmd' => 'ReturnBioExplorer', 'Project' => $data->project, 'Output' => json_decode($o));
        $data = array('switch_cmd' => 'ReturnBioExplorer', 'Status' => 'Submitted Successfully...');
        $response = json_encode($data);
        print_r($response);
        // bioExplorerSubmit( $formData );
    } else
    {
        exit('Explorer Not Set!');
    }

    function bioExplorerSubmit( $data )
    {

        // $argValue = escapeshellarg($data->value);
        // $terms = escapeshellarg($data->terms);
        // $command = "/usr/local/bin/run_compbio.py -z \"{$argValue}\" -y \"{$terms}\" --gene-file /var/www/html/temp/gene_file.txt --context-file /var/www/html/temp/context.txt --num-concepts {$data->numConcepts} --experiment-name {$data->project} --lab-name {$data->lab} -m {$data->email}";

        $labUp = strtoupper($data->lab);
        $labPath = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$labUp}";
        $processingDir = "{$labPath}/processing/{$data->project}";

        mkdir($processingDir);     

        $command = '{"jsonrpc": "2.0","method": "'.$data->method.'","id": "225","params": [["'.implode('","', $data->value).'"],"'.$data->terms.'","'.$processingDir.'/'.$data->project.'"]}';

        // exec($command, $o, $v);
        $o = submitCommand($command);

        date_default_timezone_set('America/Chicago');
        $log = date("Y-m-d h:i:s",time());
        // $output = json_encode($o);

        $logEntry = preg_replace("/\r|\n|  /","","{$log} -> {$ip} -> Output(all) : {$o} -> Command : {$command}").PHP_EOL;
        $logPath = config('app.custom_env.ABS_ROOT').'/acct/log/explorer_submit.log';
        file_put_contents( $logPath, $logEntry, FILE_APPEND);

        if (strpos($o, 'Success') !== false && strpos($o, 'Failure') == false)
        {
            if(file_exists("{$labPath}/{$data->project}"))
            {
                rmrf("{$labPath}/{$data->project}");
            }
            rename("{$labPath}/processing/{$data->project}", "{$labPath}/{$data->project}");
            mkdir("{$labPath}/{$data->project}/Input");
            $info = [
                'Creation_Date' => $log,
                'Bio_Explorer' => true,
                'Method' => $data->method,
                'Value_Submitted' => $data->value,
                'Terms' => $data->terms
            ];
            if(strpos($o, 'Failure: Not enough ideas generated from term.') !== false)
            {
                $info['NullResult'] = true;
            } else
            {
                $info['NullResult'] = false;
                mkdir("{$labPath}/{$data->project}/UserData");
            }
            $id = $_SESSION['Id'];
            $beProject = [
                'user_id' => $id,
                'name' => $data->project,
                'status' => 'complete',
                'field' => null,
                'value' => (gettype($data->value) == 'string') ? (array)str_replace("'", "", $data->value) : $data->value,
                'method' => $data->method,
                'terms' => (isset($data->terms)) ? explode(",", $data->terms) : null,
                'directory' => "{$labPath}/{$data->project}/"
            ];
            BeProject::create($beProject);
            
            $jsonInfo = json_encode($info, JSON_PRETTY_PRINT);
            file_put_contents("{$labPath}/{$data->project}/Input/info.json", $jsonInfo);

            $data = array('switch_cmd' => 'ReturnBioExplorer', 'Project' => $data->project, 'Output' => json_decode($o));
            $response = json_encode($data);
            print_r($response);
        } else
        {
            rename("{$labPath}/processing/{$data->project}", "{$labPath}/failed/{$data->project}");
            $data = array('switch_cmd' => 'ReturnBioExplorer', 'Project' => $data->project, 'Output' => $o);
            $response = json_encode($data);
            print_r($response);
        }
    }

    function submitCommand($cmd)
    {
        $ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, 'localhost:8001/pcmmrpc');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $cmd);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');

        $headers = array();
        $headers[] = 'Connection: keep-alive';
        $headers[] = 'Content-Type: text/plain;charset=UTF-8';
        $headers[] = 'Accept: */*';
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $result = curl_exec($ch);
        if (curl_errno($ch))
        {
            return 'Error:' . curl_error($ch);
        }
        curl_close($ch);

        return $result;
    }

    /**
     * Remove the directory and its content (all files and subdirectories).
     * @param string $dir the directory name
     */
    function rmrf($dir)
    {
        foreach (glob($dir) as $file)
        {
            if (is_dir($file))
            {
                rmrf("$file/*");
                rmdir($file);
            } else
            {
                unlink($file);
            }
        }
    }
?>

