<?php

function getFileContentsAsArray($filename) {
    $path = '../explorer/'.$filename.'.txt';
    $contents = file_get_contents($path);
    $array = explode("\n", $contents);
    return $array;
}
function returnMergedAndFilteredArray($array1, $array2) {
    $mergedArray = array_merge($array1, $array2);
    $filteredArray = array_unique($mergedArray);
    return $filteredArray;
}

$files = [
    'biological_concepts',
    'entities_gene',
    'entities_metabolites',
    'entities_microbes',
    'entities_mirna',
    'patho_raw_hierarchy',
    'phys_raw_hierarchy'
];
$data = [];
foreach ($files as $file)
{
    if($file === 'biological_concepts')
    {
        array_push($data, ['concept', getFileContentsAsArray($file)]);
    }
    else if($file === 'entities_gene')
    {
        array_push($data, ['gene', getFileContentsAsArray($file)]);
    }
    else if($file === 'entities_metabolites')
    {
        array_push($data, ['metabolite', getFileContentsAsArray($file)]);
    }
    else if($file === 'entities_microbes')
    {
        array_push($data, ['microbe', getFileContentsAsArray($file)]);
    }
    else if($file === 'entities_mirna')
    {
        array_push($data, ['mirna', getFileContentsAsArray($file)]);
    }
    else if($file === 'patho_raw_hierarchy')
    {
        array_push($data, ['pathophysiology', getFileContentsAsArray($file)]);
    }
    else if($file === 'phys_raw_hierarchy')
    {
        array_push($data, ['physiology', getFileContentsAsArray($file)]);
    }
}
$obj = (object)[];
$obj->result = $data;
$response = json_encode($obj);
echo $response;

?>