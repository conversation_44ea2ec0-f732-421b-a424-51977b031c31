<?php 
	if(isset($_POST['FailedProject'])) {
		$data = json_decode($_POST['FailedProject'], true);
		$list = $data['Entity_List'];
		$proName = $data['Project_Name'];
		$labname = $data['Laboratory_Name'];
		$uplabname = strtoupper($labname);
		$faildir = "../../../data/{$uplabname}/{$proName}";
		$newdir = "../../../data/{$uplabname}/failed/{$proName}";
		if(file_exists($newdir)) {
			$i=0;
			do {
				$i++;
				$numstr = "$i";
				$newdir = "../../../data/{$uplabname}/failed/{$proName}".$numstr;
			} while(file_exists($newdir));
		}

		mkdir($newdir, 0777, true);

		$tempfile = fopen("$newdir/input.txt", 'w');
		fwrite($tempfile, $list);
		fclose($tempfile);

		$jsonwrite = json_encode($data, JSON_PRETTY_PRINT);

		$tempfile = fopen("$newdir/postdata.json", 'w');
		fwrite($tempfile, $jsonwrite);
		fclose($tempfile);

		if(file_exists($faildir)) {
			deleteAll($faildir);
		}

	}

	function deleteAll($directory) {
		//Production		
		// $directory = escapeshellarg($directory);
		// exec("rm -rf $directory");
		//Development WAMP
		$directory = escapeshellarg($directory);
		exec("rmdir /s /q $directory");
	}