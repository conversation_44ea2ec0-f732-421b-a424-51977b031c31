<?php 
	if(isset($_POST['CreateInfo'])) {
                $patharray = array(ABS_ROOT, 'data');
		$datapath = join(DIRECTORY_SEPARATOR, $patharray);
                $fileslab = array_values(array_diff(scandir($datapath), ['.','..', 'Lab']));
                for($i=0;$i<count($fileslab);$i++) {
                	$filesexp = array_values(array_diff(scandir("{$datapath}/{$fileslab[$i]}"), ['.','..','failed', 'processing','trash','account_info']));
                	for($z=0;$z<count($filesexp);$z++) {
                		$targetdir = "{$datapath}/{$fileslab[$i]}/{$filesexp[$z]}";
                		$targetfile = "{$datapath}/{$fileslab[$i]}/{$filesexp[$z]}/Input/info.json";
                		if(!file_exists($targetfile)) {
                			$date = date("Y-m-d h:i:s", filectime($targetdir));
                			$listdir = "{$targetdir}/Input/input.txt";
                			if(file_exists($listdir)) {
                				$listfile = file_get_contents($listdir);
                				$listArray = explode("\n", $listfile);
                				$listcount = count($listArray);
                				$listcountstr = "$listcount";
                			} else {
                				$listcount = 'No Data...';
                			}
                                        $projectinfo = array('Creation_Date' => "$date", 'Submitted_By' => 'No Data...', 'Num_Entities' => $listcountstr, 'Num_Concepts' => 'No Data...', 'Status' => 'complete');
                                        //Remove Context
                			// $projectinfo = array('Creation_Date' => "$date", 'Submitted_By' => 'No Data...', 'Num_Entities' => $listcountstr, 'Context' => 'No Data...', 'Num_Context' => 'No Data...', 'Num_Concepts' => 'No Data...', 'Status' => 'complete');
                			if(!file_exists($targetdir.'/Input')) {
                				mkdir("$targetdir/Input", 0777);
                			}
                			$filewrite = fopen($targetfile, 'w');
                                        flock($filewrite, LOCK_EX);
                			fwrite($filewrite, json_encode($projectinfo, JSON_PRETTY_PRINT));
                                        flock($filewrite, LOCK_UN);
                			fclose($filewrite);
                			echo 'loop finished';
                		}
                	}
                }
	}