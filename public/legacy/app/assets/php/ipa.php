<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
	function getRealIpAddr(){
		if ( !empty($_SERVER['HTTP_CLIENT_IP']) ) {
			// Check IP from internet.
			$ip = $_SERVER['HTTP_CLIENT_IP'];
		} elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']) ) {
			// Check IP is passed from proxy.
			$ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
		} elseif (!empty($_SERVER['REMOTE_ADDR'])) {
			// Get IP address from remote address.
			$ip = $_SERVER['REMOTE_ADDR'];
		} elseif(!empty($_SESSION['ip'])) {
			$ip = $_SESSION['ip'];
		} else {
			return null;
		}
		return $ip;
	}
?>