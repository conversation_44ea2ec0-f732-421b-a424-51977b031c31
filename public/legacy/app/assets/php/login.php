<?php
	session_start();
	include('ipa.php');
	$ip = getRealIpAddr();
	date_default_timezone_set('America/Chicago');
	$log = array(date("Y-m-d h:i:s",time()), $ip);

	if(isset($_POST['AcctCreds'])) {
		$str = urldecode($_POST['AcctCreds']);
		$creds = json_decode($str, true);
		array_push($log, $creds['Laboratory']);
		if(file_exists('../../../acct/accts.php')) {
			$fileContents = file_get_contents('../../../acct/accts.php');
			if($fileContents != null) {
				$fileContents = trim($fileContents, "<?php '");
				$accounts = json_decode($fileContents, true);
			}
		} else {
			echo 'doesnt exist';
		}
		$match = false;
		if(isset($accounts)) {
			for($i=0;$i<count($accounts);$i++) {
				if($accounts[$i]['Laboratory']==$creds['Laboratory']
					&& password_verify($creds['Password'], $accounts[$i]['Password'])) {
					$match = true;
					$email = $accounts[$i]['Email'];
					$id = $accounts[$i]['Id'];
					if(isset($accounts[$i]['Sudo'])) {
						$sudo = true;
					}
				}
			}
		}
		if($match == true) {
			$_SESSION['last_action'] = time();
			$_SESSION['Laboratory'] = $creds['Laboratory'];
			$_SESSION['Email'] = $email;
			$_SESSION['Id'] = $id;
			$_SESSION['LoggedIn'] = true;
			$_SESSION['ip'] = $ip;
			if(isset($sudo)) {
				$_SESSION['Sudo'] = $sudo;
			}
			array_push($log, 'Success');
 			echo true;
		} else {
			array_push($log, '!!Fail!!');
			echo false;
		}
	} else {
		echo 'AcctCreds Error';
		array_push($log, '!!POST_ERROR!!');
	}

	$file = fopen('../../../acct/log/access_log.txt', 'a');
	flock($file, LOCK_EX);
	fwrite($file, implode('->', $log).PHP_EOL);
	flock($file, LOCK_UN);
	fclose($file);

	exit;
?>