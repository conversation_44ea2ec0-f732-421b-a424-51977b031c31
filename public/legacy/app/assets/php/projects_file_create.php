<?php 

include('../../../config/config.php');

$accountDirs = array_values(array_diff(scandir(join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data'])),['.','..']));

for($i=0;$i<count($accountDirs);$i++) {
	// if(!file_exists(join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', $accountDirs[$i], 'account_info','projects.json']))) {
		
		$folders = (Object) array();
		
		$failedDir = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', $accountDirs[$i], 'failed']);

		if(!file_exists($failedDir)) {

			mkdir($failedDir, 0777);
		
		}

		$failed = array_values(array_diff(scandir($failedDir), ['.','..']));
		
		$trashDir = join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', $accountDirs[$i], 'trash']);

		if(!file_exists($trashDir)) {

			mkdir($trashDir, 0777);
		
		}

		$deleted = array_values(array_diff(scandir($trashDir), ['.','..']));

		$projects = array_values(array_diff(scandir(join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', $accountDirs[$i]])), ['.','..','failed','trash','processing','account_info']));

		$jsonObj = (Object) array('folders'=>$folders,'projects'=>$projects,'failed'=>$failed,'deleted'=>$deleted);

		file_put_contents(join(DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', $accountDirs[$i], 'account_info', 'projects.json']), json_encode($jsonObj, JSON_PRETTY_PRINT));

		echo 'Finished-> '.$accountDirs[$i].'<br><br>';
		// echo '<pre>';
		// var_dump(json_encode($jsonObj, JSON_PRETTY_PRINT));			
		// echo '</pre>';
	// }
}

?>