<?php
include '../../../config/config.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
	if( isset( $_POST['tables'] )) {
		$account = strtoupper($_POST['account']);
		$project = $_POST['project'];
		$path = ABS_ROOT."/data/{$account}/{$project}";
		$filename = urldecode($_POST['filename']);
		$tables = json_decode($_POST['tables'], true);
		$str = '"Entity ID","Entity Score","Expression Value","Theme Name (Score)"'."\n";
		$tCount = count($tables);
		$tIndex = 0;
		foreach($tables as $table) {
			for($i=0;$i<count($table['entities']);$i++) {
				$expVal = $table['expressions'][$i] ?? '';
				$str .= "\"{$table['entities'][$i]}\",\"{$table['scores'][$i]}\",\"{$expVal}\",\"{$table['name']} ({$table['score']})\"\n";
			}
			$tIndex++;
			if($tIndex !== $tCount) {
				$str .= "\"\"\n";
			}
		}
		file_put_contents("{$path}/{$filename}.csv", $str);
		header("Content-type: text/csv");
  		header("Content-disposition: attachment; filename = $filename.csv");
  		$downloadlink = WEBROOT."legacy/data/{$account}/{$project}/{$filename}.csv";
  		echo $downloadlink;
	} else {
		echo 'No table data';
	}
} else if ($_SERVER['REQUEST_METHOD'] === 'GET') {
	$q = $_GET['q'];
	$account = strtoupper(urldecode($_GET['a']));
	$project = urldecode($_GET['p']);
	$link = $_GET['l'];
	$filename = basename($link);
	$file = join( DIRECTORY_SEPARATOR, [ABS_ROOT, 'data', $account, $project, $filename] );
	if($q === 'delete') {
		if(file_exists($file)) {
			unlink($file);
			echo 'File Deleted';
		} else {
			echo 'file does not exist';
		}
	}
} else {
	echo 'Whaaaat?';
}