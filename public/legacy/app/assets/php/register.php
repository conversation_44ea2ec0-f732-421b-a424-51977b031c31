<?php 
	include('ipa.php');
	$ip = getRealIpAddr();
	date_default_timezone_set('America/Chicago');
	$log = array(date("Y-m-d h:i:s",time()), $ip);

	if(file_exists('../../../acct/accts.php')) {
		$fileContents = file_get_contents('../../../acct/accts.php');
		if($fileContents != null) {
			$fileContents = trim($fileContents, "<?php '");
			$accounts = json_decode($fileContents, true);
		}
	}
	if(isset($_POST['NewAccount'])) {
	    $data = urldecode($_POST['NewAccount']);
	    // array_push($log, $data);
	    $newAcct = json_decode($data, true);
		$match = false;
	    if(isset($accounts)) {
			for($i=0;$i<count($accounts);$i++) {
				if(strtolower($accounts[$i]['Laboratory']) == strtolower($newAcct['Laboratory'])) {
					$match = true;
					array_push($log, 'DupName');
				} else if($accounts[$i]['Email'] == $newAcct['Email']) {
					array_push($log, 'DupEmail');
				}
			}
	    } else {
	    	$accounts = array();
	    	$log2 = '!!!Accounts Empty!!!';
	    }
	    
	    if($match == false) {
	    	$id = $accounts[count($accounts)-1]['Id'] + 1;
	      	$newAcct = array("Id" => "$id") + $newAcct;
	        $file = fopen('../../../acct/accts.php', 'w');
	        $pass = $newAcct['Password'];	
	        $newAcct['Password'] = password_hash($pass, PASSWORD_DEFAULT);
			array_push($accounts, $newAcct);
			$accountsWrite = json_encode($accounts, JSON_PRETTY_PRINT);
			fwrite($file, "<?php '".$accountsWrite);
			fclose($file);
	    	array_push($log, 'Write Success!'); 
	    	$file = fopen('../../../acct/log/log.txt', 'a');
	    	if(isset($log2)) {
	    		fwrite($file, $log2.PHP_EOL);
	    	}
	    	fwrite($file, implode(',', $log).PHP_EOL);
	    	fclose($file);
	    	$newdir = strtoupper($newAcct['Laboratory']);
	    	mkdir("../../../data/$newdir", 0777);
	    	echo true;
		} else {
			if($match==true) {
				echo "duplicate";
			}
	    	array_push($log, 'Write Failed!'); 
	    	$file = fopen('../../../acct/log/log.txt', 'a');	
	    	fwrite($file, implode(',', $log).PHP_EOL);
	    	fclose($file);	    			
		}    	
	
	} else {
		echo 'error -> no POST data received';
	}
?>
