<?php
include('../../../config/config.php');
include('ipa.php');
$ip = getRealIpAddr();
date_default_timezone_set('America/Chicago');
if(isset($_POST['Reset'])) {
		$log = array(date("Y-m-d h:i:s",time()), $ip);
		$data = json_decode($_POST['Reset'], true);
		$resetaccount = $data['Account'];
		$email = $data['Email'];

		if(file_exists('../../../acct/accts.php')) {
			$fileContents = file_get_contents('../../../acct/accts.php');
			if($fileContents != null) {
				$fileContents = trim($fileContents, "<?php '");
				$accounts = json_decode($fileContents, true);
			}
		}
		$matchedAccount = false;
		for($i=0;$i<count($accounts);$i++) {
			if($accounts[$i]['Laboratory'] == $resetaccount && $accounts[$i]['Email'] == $email) {
				$matchedAccount = true;
			}
		}
		if($matchedAccount === true) {
			$key = uniqid(true);
			$hashedKey = password_hash($key, PASSWORD_DEFAULT);
			$clientIP = $ip;

			$resetData = array('Request_Date' => date("Y-m-d h:i:s",time()), 'Account' => $resetaccount, 'Email' => $email, 'Key' => $key, 'Requesting_IP' => $clientIP);

			$resetlist = [];

			$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'reset.json';
			if(file_exists($path)) {
				$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'reset.json';
				$resetlist = json_decode(file_get_contents($path), true);
			}
			$prevRequest = false;
			for($i=0;$i<count($resetlist);$i++) {
				if($resetlist[$i]['Account'] == $resetData['Account']) {
					$resetlist[$i] = $resetData;
					$prevRequest = true;
				}
			}
			if($prevRequest == false) {
				array_push($resetlist, $resetData);
			}

			$file = fopen($path, 'w');
			flock($file, LOCK_EX);
			fwrite($file, json_encode($resetlist, JSON_PRETTY_PRINT));
			flock($file, LOCK_UN);
			fclose($file);

			$urlparams = array('Account'=>$resetaccount, 'Email'=>$email, 'Key'=>$hashedKey);
			$json = json_encode($urlparams);
			$base =  strtr(base64_encode($json), '+/=', '._-');
			$url = WEBROOT.'reset/?reset='.$base;

			$subject = 'CompBio Password Reset: '.$resetData['Account'];
			$to = $email;

			$message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
		<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

		<head>
		    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		    <meta name="viewport" content="width=device-width, initial-scale=1.0">
		    <title>CompBio Password Reset Request</title>
		    <!--[if gte mso 9]><xml>
			      <o:OfficeDocumentSettings>
				    <o:AllowPNG/>
				    <o:PixelsPerInch>96</o:PixelsPerInch>
				  </o:OfficeDocumentSettings>
				</xml><![endif]-->
		    <style>
		    #title-wrap {
		        margin: auto 0;
		        font-size: 1.2rem;
		        text-align: center;
		        font-variant: small-caps;
		        font-weight: bold
		    }

		    table {
		        margin: auto;
		        border-collapse: collapse;
		        width: 100%
		    }

		    td,
		    th {
		        border: 1px solid #ddd;
		        text-align: left;
		        padding: 8px
		    }

		    #list {
		        text-align: center
		    }
		    </style>
		</head>

		<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
		    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
		    <div id="title-wrap"><h1 class="title">Password Reset Request</h1>
		    </div>
		    <table width="100%" border="0" cellpadding="0" cellspacing="0" style="min-width: 100%;" role="presentation">
		        <tr>
		            <th>Account: </th>
		            <td>' . $resetData['Account'] . '</td>
		        </tr>
		        <tr>
		            <th>Email: </th>
		            <td>' . $resetData['Email'] . '</td>
		        </tr>
		    </table>
		    <h3>Link </h3>
		    <p>Click on the following link to reset your account password.</p>
		    <p><a href="'.$url.'">-RESET PASSWORD-</a></p>
		    <p>If you or your team did not initiate a password reset request please email one of the CompBio team members or Curtis Marcum at <a href="mailto:<EMAIL>"><EMAIL></a></p>
		    <p>Thanks,<br>-CompBio Team</p>
		</body>
		</html>';

			$headers = "MIME-Version: 1.0" . "\r\n";
			$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
			$headers .= 'From: <<EMAIL>>' . "\r\n";
			mail($to,$subject,$message,$headers);
			mail('<EMAIL>', $subject, $message, $headers);
			$clientIP = $ip;

			$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.'reset_log.txt';
			$entry = 'Account-> '.$resetData['Account'].', Email-> '.$resetData['Email'].', Requesting_IP-> '.$clientIP;
			array_push($log, $entry);
			$file = fopen($path, 'a');
			flock($file, LOCK_EX);
			fwrite($file, implode(',', $log).PHP_EOL);
			flock($file, LOCK_UN);
			fclose($file);
			// print_r(array($to, $subject, $message, $headers));
			echo 'request submitted';
		} else 
		{
			echo 'Matched Account false!';
		}
	} else 
	{
		echo 'No POST variable found!';
	}