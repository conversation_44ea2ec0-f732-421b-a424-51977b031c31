<?php
include('../../../config/config.php');
session_start();
date_default_timezone_set('America/Chicago');
include('ipa.php');
$ip = getRealIpAddr();
if(isset($_POST['PasswordChangeSubmition']) && isset($_SESSION['Reset'])) {
	$log = array(date("Y-m-d h:i:s",time()), $ip);
	$data = json_decode($_POST['PasswordChangeSubmition'], true);

	$password = $data['Password'];
	$email = $_SESSION['Reset']['Email'];
	$account = $_SESSION['Reset']['Account'];
	$key = $_SESSION['Reset']['Key'];

	$path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'acct', 'reset.json']);
	if(file_exists($path)) {
		$resetlist = json_decode(file_get_contents($path), true);
		$verified = false;
		for($i=0;$i<count($resetlist);$i++) {
			if($account == $resetlist[$i]['Account'] && $email == $resetlist[$i]['Email'] &&  $resetlist[$i]['Key'] == $key) {
				unset($resetlist[$i]);
				$resetlist = array_values($resetlist);
				file_put_contents($path, json_encode($resetlist, JSON_PRETTY_PRINT));
				$verified = true;
				break;
			}
        }

        $path = join(DIRECTORY_SEPARATOR,[ABS_ROOT, 'acct', 'accts.php']);
        if($verified === true && file_exists($path)) {
        	$fileContents = trim(file_get_contents($path), "<?php '");
        	if($fileContents != null) {
        		$accounts = json_decode($fileContents, true);
        		$matchedAccount = false;
        		for($i=0;$i<count($accounts);$i++) {
        			if($accounts[$i]['Laboratory'] == $account && $accounts[$i]['Email'] == $email) {
        				$accounts[$i]['Password'] = password_hash($password, PASSWORD_DEFAULT);
        				$matchedAccount = true;
        				break;

        			}
        		}

				$acctsFile = fopen($path, 'w');
				flock($acctsFile, LOCK_EX);
				fwrite($acctsFile, "<?php '".json_encode($accounts, JSON_PRETTY_PRINT));
        		flock($acctsFile, LOCK_UN);
        		fclose($acctsFile);
        		if($matchedAccount == true) {
					$subject = 'CompBio Password Changed';
					$to = $email;

					$message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

	<head>
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>CompBio</title>
	    <!--[if gte mso 9]><xml>
		      <o:OfficeDocumentSettings>
			    <o:AllowPNG/>
			    <o:PixelsPerInch>96</o:PixelsPerInch>
			  </o:OfficeDocumentSettings>
			</xml><![endif]-->
	    <style>
	    #title-wrap {
	        margin: auto 0;
	        font-size: 1.2rem;
	        text-align: center;
	        font-variant: small-caps;
	        font-weight: bold
	    }

	    table {
	        margin: auto;
	        border-collapse: collapse;
	        width: 100%
	    }

	    td,
	    th {
	        border: 1px solid #ddd;
	        text-align: left;
	        padding: 8px
	    }

	    #list {
	        text-align: center
	    }
	    </style>
	</head>

	<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
	    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
	    <div id="title-wrap"><h2 class="title">Updated Account Information</h2>
	    </div>
	    <table width="100%" border="0" cellpadding="0" cellspacing="0" style="min-width: 100%;" role="presentation">
	        <tr>
	            <th>Account: </th>
	            <td>' . $account . '</td>
	        </tr>
	        <tr>
	            <th>Email: </th>
	            <td>' . $email . '</td>
	        </tr>
	        <tr>
	            <th>Password: </th>
	            <td>' . $password . '</td>
	        </tr>
	    </table>
	    <p>If you or your team did not initiate this change, please email one of the CompBio team members or Curtis Marcum at <a href="mailto:<EMAIL>"><EMAIL></a></p>
	    <p>Thanks,<br>-CompBio Team</p>
	</body>
	</html>';

					$headers = "MIME-Version: 1.0" . "\r\n";
					$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
					$headers .= 'From: <<EMAIL>>' . "\r\n";
					mail($to,$subject,$message,$headers);
					mail('<EMAIL>', $subject, $message, $headers);

					$clientIP = $ip;

					$path = ABS_ROOT.DIRECTORY_SEPARATOR.'acct'.DIRECTORY_SEPARATOR.'log'.DIRECTORY_SEPARATOR.'password_change.txt';
					$entry = 'Account-> '.$account.', Email-> '.$email.', Requesting_IP-> '.$clientIP;
					array_push($log, $entry);
					$file = fopen($path, 'a');
					flock($file, LOCK_EX);
					fwrite($file, implode(',', $log).PHP_EOL);
					flock($file, LOCK_UN);
					fclose($file);
					echo 'SUCCESS!';
        		} else {
        			echo 'matchedAccount == false error';
        		}
        	} else {
        		flock($path, LOCK_UN);
        	}
        } else {
        	echo 'verified == false error or accts.php error';
        }
	} else {
		echo 'reset.json error';
	}
} else {
	echo 'Post error or session[reset] error';
}