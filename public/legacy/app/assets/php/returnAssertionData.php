<?php
session_start();
if(1>0){
	if(isset($_POST['account']) && isset($_POST['assertion']))
	{
		$account = strtoupper($_POST['account']);
		$assertion = $_POST['assertion'];
		if(isset($_POST['assertionBatchName']) && $_POST['assertionBatchName'] !== '')
		{
			$assertionBatchName = $_POST['assertionBatchName'];
			$path = "../../../data/{$account}/account_info/assertion_w_libs/{$assertionBatchName}/{$assertion}/{$assertion}.json";
		} else
		{
			$path = "../../../data/{$account}/account_info/assertion/{$assertion}/{$assertion}.json";
		}
		if(file_exists($path))
		{
			$final = json_decode(file_get_contents($path));
		} else
		{
			$path = "../../../data/{$account}/account_info/assertion/{$assertion}/{$assertion}.csv";
			if(file_exists($path))
			{
				$csv = trim(file_get_contents($path));
				$final = (object)[];
				$final->subgraphs = (array)[];
				$collector = (array)[];
				$lines = explode("\n", $csv);
				foreach($lines as $line)
				{
					$temp = (object)[];
					$temp->score = -1;
					$temp->nodes = explode("\t",$line);
					array_push($collector, $temp);
				}
				$final->subgraphs = array_values($collector);
			} else
			{
				print_r(json_encode('["!!!!Error!!!! No paths found!"]'));
				exit;
			}
		}
		if(isset($_POST['assertionBatchName']) && $_POST['assertionBatchName'] !== '')
		{
			$path = "../../../data/{$account}/account_info/assertion_w_libs/{$assertionBatchName}/{$assertion}/custom.json";
		} else
		{
			$path = "../../../data/{$account}/account_info/assertion/{$assertion}/custom.json";
		}
		if(file_exists($path))
		{
			$final->custom = json_decode(file_get_contents($path));
		}
		print_r(json_encode($final));
	} else
	{
		print_r(json_encode('["!!!!Error!!!!"]'));
	}
}
?>