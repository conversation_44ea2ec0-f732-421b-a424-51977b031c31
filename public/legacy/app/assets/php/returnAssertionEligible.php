<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');

// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
	if($_SESSION['LoggedIn'] && isset($_SESSION['Laboratory']) && $_SESSION['Laboratory'] !== '' && isset($_SESSION['Id']))
	{
		$eligible = (array)[];
		$account = strtoupper($_SESSION['Laboratory']);
		$user =  User::find($_SESSION['Id']);
		$lab = $user->lab;
		$projects = $user->cbProjects->merge($user->beProjects);
		$dirname = $lab->directory;
		$path = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$dirname}";
		foreach($projects as $project)
		{
			$matrixFile = "{$path}/{$project->name}/{$project->name}_cd_theme_matrix.txt";
			if(file_exists($matrixFile))
			{
				$ts = $project->created_at;
				array_push($eligible, $project->name.'_!!_'.$ts.'_!!_'.$project->id);
			}
		}
		if(isset($_GET['libForm']))
		{
			if($_GET['libForm'] === 'false')
			{
				$libs = $user->assertionLibraries->pluck('name');
				$eligible = array_values(array_merge($libs,$eligible));
			}
		}
		print_r(json_encode($eligible));
	}
	// New/Updated Version
	function getProjectDateTime($project_path)
	{
	    $timestamp = 0;

	    // Try getting creation date from info.json
	    $info_json = "{$project_path}/Input/info.json";
	    if (file_exists($info_json)) {
	        $info = json_decode(file_get_contents($info_json));
	        if (!empty($info->Creation_Date)) {
	            return $info->Creation_Date;
	        }
	    }

	    // Fallback: get the earliest timestamp among all files (approximate creation)
	    $earliest = PHP_INT_MAX;

	    $rii = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($project_path));

	    foreach ($rii as $file) {
	        if ($file->isFile()) {
	            $ctime = $file->getCTime(); // inode change time
	            $mtime = $file->getMTime(); // last modified
	            $time = min($ctime, $mtime); // use earliest reasonable time
	            if ($time < $earliest) {
	                $earliest = $time;
	            }
	        }
	    }

	    // Format as readable date
	    if ($earliest !== PHP_INT_MAX) {
	        return date("Y-m-d H:i:s", $earliest);
	    }

	    return null;
	}
?>