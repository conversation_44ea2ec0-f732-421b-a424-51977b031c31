<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');

// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
if($_SESSION['LoggedIn'] && isset($_SESSION['Laboratory']) && $_SESSION['Laboratory'] !== '' && isset($_SESSION['Id']))
{
	$account = strtoupper($_SESSION['Laboratory']);
	$user = User::find($_SESSION['Id']);
	// $path = "../../../data/{$account}/account_info/assertion_libs";
	// $libs = (array)[];
	// if(file_exists($path))
	// {
	// 	$libs = array_values(array_diff(scandir($path), ['.','..']));
	// }
	$libs = $user->assertionLibraries;
	print_r(json_encode($libs));
}