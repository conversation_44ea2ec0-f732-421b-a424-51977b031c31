<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');

// Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use App\Models\AssertionLibrary;
use Carbon\Carbon;

header('Content-Type: application/json'); // Set response header

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
	http_response_code(400); // Bad request
	echo json_encode(["error" => "Invalid request method."]);
	exit;
}

// Decode JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate input
if (!isset($input['LibId'])) {
	http_response_code(422); // Unprocessable Entity
	echo json_encode(["error" => "Missing required parameters."]);
	exit;
}

$library = AssertionLibrary::find($input['LibId']);

if (!$library) {
	http_response_code(404); // Not Found
	echo json_encode(["error" => "Library not found."]);
	exit;
}
$data = $library->toArray();
$data['created_at'] = $library->created_at->format('Y-m-d H:i:s');
echo json_encode($data);
exit;