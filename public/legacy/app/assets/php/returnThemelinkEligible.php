<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');

// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\CbProject;
use App\Models\CbProfile;
use App\Models\BeProject;
use App\Models\BeProfile;
use App\Models\Assertion;
use App\Models\Folder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
if($_SESSION['LoggedIn'] && isset($_SESSION['Laboratory']) && $_SESSION['Laboratory'] !== '' && isset($_SESSION['Id']))
{
   $eligible = (array)[];
   $account = strtoupper($_SESSION['Laboratory']);
   $user =  User::find($_SESSION['Id']);
   $lab = $user->lab;
   $projects = $user->cbProjects->merge($user->beProjects);
   $dirname = $lab->directory;
   $path = config('app.custom_env.ACCOUNT_STORAGE_ROOT')."/{$dirname}";
   foreach ($projects as $project) {
      $coordsFile = "{$path}/{$project->name}/{$project->name}_Theme_Coordinates.js";
      if (file_exists($coordsFile)) {
         array_push($eligible, $project->name);
      }
   }
   print_r(json_encode($eligible));
}
