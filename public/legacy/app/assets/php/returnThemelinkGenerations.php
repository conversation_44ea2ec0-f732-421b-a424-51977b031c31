<?php
if (session_status() !== PHP_SESSION_ACTIVE) {
	session_start();
}
require('../../../config/config.php');
include('ipa.php');
date_default_timezone_set('America/Chicago');
// // Register the Composer autoloader...
require APPABS_ROOT . '/vendor/autoload.php';
// // Bootstrap <PERSON> and handle the request...
$app = require_once APPABS_ROOT . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$request = Illuminate\Http\Request::capture();
$response = $kernel->handle($request);

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Lab;
use App\Models\Themelink;

if ($_SESSION['LoggedIn'] && isset($_SESSION['Laboratory']) && $_SESSION['Laboratory'] !== '') {
   $id = $_SESSION['Id'];
   $user = User::find($id);
   $themelink_array = $user->themelinks()->pluck('name')->toArray();
   // $account = strtoupper($_SESSION['Laboratory']);
   // $path = '../../../' . join(DIRECTORY_SEPARATOR, ['data', $account, 'account_info', 'themelinks']);
   // $themelink_array = (array)[];
   // if (file_exists($path)) {
   //    $themelink_array = array_diff(scandir($path), ['.', '..']);
   // } else {
   //    mkdir($path,0777);
   // }
   $response = json_encode($themelink_array);
   print_r($response);
}
