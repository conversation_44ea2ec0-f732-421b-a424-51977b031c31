<?php 
	include('ipa.php');
	$ip = getRealIpAddr();
	date_default_timezone_set('America/Chicago');
	$log = array(date("Y-m-d h:i:s",time()), $ip);
	if (isset($_POST['NewProject'])) {
		$data = json_decode($_POST['NewProject'], true);

		$tempStr = $data['Entity_List'];

		$tempFile = fopen('../../../temp/gene_file.txt', 'w') or die('Unable to open file!!');
		flock($tempFile, LOCK_EX);
		fwrite($tempFile, $tempStr);
		flock($tempFile, LOCK_UN);
		fclose($tempFile);
		// Remove Context
		// $numContext = $data['Num_Context'];
		// $contextString = $data['Context'];
		// $context = str_replace(',', "\n", $contextString);

		// $tempFile = fopen('../../../temp/context.txt', 'w') or die('Unable to open file!!');
		// flock($tempFile, LOCK_EX);
		// fwrite($tempFile, $context);
		// flock($tempFile, LOCK_UN);
		// fclose($tempFile);

		$numConcepts = $data['Num_Concepts'];

		$proName = $data['Project_Name'];
		$labname = $data['Laboratory_Name'];
		array_push($log, "Acct-->$labname", "Project-->$proName");
		$uplabname = strtoupper($labname);
		$newdir = "../../../data/{$uplabname}/processing/{$proName}/UserData";
		mkdir($newdir, 0777, true);
		
		$tempStr = $data['Entity_List'];		
		$newdir2 = "../../../data/{$uplabname}/processing/{$proName}/Input";
		mkdir($newdir2,0777);
		$inputFile = fopen($newdir2.'/input.txt', 'w') or die('Unable to open file!!');
		flock($inputFile, LOCK_EX);
		fwrite($inputFile, $tempStr);
		flock($inputFile, LOCK_UN);
		fclose($inputFile);

		if(isset($data['Expression_List'])) {
			$inputFile = fopen($newdir2.'/expression.txt', 'w') or die('Unable to open file!!');
			flock($inputFile, LOCK_EX);
			fwrite($inputFile, $data['Expression_List']);
			flock($inputFile, LOCK_UN);
			fclose($inputFile);
		}

		$email = $data['Contact_Email'];
		array_push($log, "Contact_Email-->$email");

		$date = date("Y-m-d h:i:s",time());
		$projectinfo = array('Creation_Date' => "$date", 'Submitted_By' => "$email", 'Num_Entities' => $data['Num_Entities'], 'Num_Concepts' => "$numConcepts", 'Status' => 'processing');
		// Remove Context
		// $projectinfo = array('Creation_Date' => "$date", 'Submitted_By' => "$email", 'Num_Entities' => $data['Num_Entities'], 'Context' => "$context", 'Num_Context' => "$numContext", 'Num_Concepts' => "$numConcepts", 'Status' => 'processing');
		if(isset($data['Creation_Date'])){
			$projectinfo['Creation_Date'] = $data['Creation_Date'];
			$projectinfo['Reanalyze_Date'] = $date;
		}
		$infofile = fopen($newdir2.'/info.json', 'w') or die('Unable to open file!!');
		flock($infofile, LOCK_EX);
		fwrite($infofile, json_encode($projectinfo, JSON_PRETTY_PRINT));
		flock($infofile, LOCK_UN);
		fclose($infofile);

		$command = "/usr/local/bin/run_compbio.py --gene-file /var/www/html/temp/gene_file.txt --context-file /var/www/html/temp/context.txt --num-concepts $numConcepts --experiment-name $proName --lab-name $labname -m $email";

		exec($command, $o, $v);
		array_push($log, "Output--> $v");
		$file = fopen('../../../acct/log/submit_log.txt', 'a');
		flock($file, LOCK_EX);
		fwrite($file, implode(', ', $log).PHP_EOL);
		flock($file, LOCK_UN);
		fclose($file);
		if($v==0) {
			$projectinfo['Status'] = 'complete';
			$infofile = fopen($newdir2.'/info.json', 'w') or die('Unable to open file!!');
			flock($infofile, LOCK_EX);
			fwrite($infofile, json_encode($projectinfo, JSON_PRETTY_PRINT));
			flock($infofile, LOCK_UN);
			fclose($infofile);			
			$currentdir = "../../../data/{$uplabname}/processing/{$proName}";
			$finaldir = "../../../data/{$uplabname}/{$proName}";
			rename($currentdir, $finaldir);

			//Add to projects JSON file
			$projectsJson = "../../../data/{$uplabname}/account_info/projects.json";

			if(file_exists($projectsJson)) {

				$projectsJsonObj = json_decode(file_get_contents($projectsJson));

			} else {

				$projectsJsonObj = '{"folders": {},"projects": [], "explorer_projects": [], "failed": [],"deleted": []}';
				$projectsJsonObj = json_decode($projectsJsonObj);

			}


			array_push($projectsJsonObj->projects, $proName);

			file_put_contents($projectsJson, json_encode($projectsJsonObj, JSON_PRETTY_PRINT), LOCK_EX);

			
		} else {

			$projectinfo['Status'] = 'failed';
			$infofile = fopen($newdir2.'/info.json', 'w') or die('Unable to open file!!');
			flock($infofile, LOCK_EX);
			fwrite($infofile, json_encode($projectinfo, JSON_PRETTY_PRINT));
			flock($infofile, LOCK_UN);
			fclose($infofile);		

			$list = $data['Entity_List'];
			$faildir = "../../../data/{$uplabname}/processing/{$proName}";

			$newdir = "../../../data/{$uplabname}/failed/{$proName}";
			if(file_exists($newdir)) {
				$i=0;
				do {
					$i++;
					$numstr = "$i";
					$newdir = "../../../data/{$uplabname}/failed/{$proName}".$numstr;
				} while(file_exists($newdir));
			}
			$failbasedir = "../../../data/{$uplabname}/failed";
			if(!file_exists($failbasedir)) {
				mkdir($failbasedir, 0777);
			}
			rename($faildir, $newdir);

			//Add to projects JSON file
			$projectsJson = "../../../data/{$uplabname}/account_info/projects.json";

			if(file_exists($projectsJson)) {

				$projectsJsonObj = json_decode(file_get_contents($projectsJson));

			} else {

				$projectsJsonObj = '{"folders": {},"projects": [],"failed": [],"deleted": []}';
				$projectsJsonObj = json_decode($projectsJsonObj);

			}

			array_push($projectsJsonObj->failed, $proName);

			file_put_contents($projectsJson, json_encode($projectsJsonObj, JSON_PRETTY_PRINT), LOCK_EX);
			

			//Failed Notification
	        $to = $data['Contact_Email'];
	        $subject = "Project Processing Failure: $proName";
	        $message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
	<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

	<head>
	    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	    <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <title>CompBio Project Submission Failure</title>
	    <!--[if gte mso 9]><xml>
	          <o:OfficeDocumentSettings>
	            <o:AllowPNG/>
	            <o:PixelsPerInch>96</o:PixelsPerInch>
	          </o:OfficeDocumentSettings>
	        </xml><![endif]-->
	    <style>
	    #title-wrap {
	        margin: auto 0;
	        font-size: 1.2rem;
	        text-align: center;
	        font-variant: small-caps;
	        font-weight: bold
	    }
	    </style>
	</head>

	<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
	    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
	    <div id="title-wrap"><h2 class="title">CompBio Project Notification</h2>
	    </div>
	    <hr>
	    <div id="info-wrap">
	        <div id="info">
	            <p><span style="font-weight: bold;">Project Name: </span>'. $proName .'</p>
	            <p>The project submitted to CompBio has completed unsuccessfully.  Most processing failures are due to errors in the input list.  Make sure to read the "Notices" on the submission tab in the account "Administration Panel" and confirm that your inputs are adhering to the guidelines.  For further assistance you may contact a member of our team and we will investigate further and/or help get your project processed.</p>
	            <p>Thanks,</p>
	            <p>GTAC and CompBio Team</p>
	</body>

	</html>';        
	        $headers = "MIME-Version: 1.0" . "\r\n";
	        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
	        $headers .= 'From: <<EMAIL>>' . "\r\n";
	        mail($to,$subject,$message,$headers);
	        $to = '<EMAIL>';
	        mail($to,$subject,$message,$headers);        

        //Notification End	

		}	
	}
	// function deleteAll($directory) {
		//Production		
		// $directory = escapeshellarg($directory);
		// exec("rm -rf $directory");
		//Development WAMP
		// $directory = escapeshellarg($directory);
		// exec("rmdir /s /q $directory");
	// }

?>