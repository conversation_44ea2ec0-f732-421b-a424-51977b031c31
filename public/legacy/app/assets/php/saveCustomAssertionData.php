<?php

if(isset($_POST['account']) && isset($_POST['assertion']) && isset($_POST['customData']))
{
	$account = strtoupper($_POST['account']);
	$assertion = $_POST['assertion'];
	$customData = json_decode($_POST['customData']);
	if(isset($_POST['assertionBatchName']) && $_POST['assertionBatchName'] !== '')
	{
		$assertionBatchName = $_POST['assertionBatchName'];
		$path = "../../../data/{$account}/account_info/assertion_w_libs/{$assertionBatchName}/{$assertion}";
	} else
	{
		$path = "../../../data/{$account}/account_info/assertion/{$assertion}";
	}
	if(file_exists($path))
	{
		$path = "{$path}/custom.json";
		$status = file_put_contents($path, json_encode($customData, JSON_PRETTY_PRINT));
		if($status === false)
		{
			print_r('ERROR:3');
		} else
		{
			print_r('SUCCESS');
		}
	} else
	{
		print_r('ERROR:2');
	}
} else
{
	print_r('ERROR:1');
}

?>