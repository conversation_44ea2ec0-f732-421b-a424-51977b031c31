<?php
	if (isset($_POST['NewProject'])) {

	$data = json_decode($_POST['NewProject'], true);
	// $list = implode('\n', $data['Entity_List']);
	// $list = $data['Entity_List'];
	$to = "<EMAIL>";
	$subject = "CompBio Project Submission";

	$message = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Submission</title>
    <!--[if gte mso 9]><xml>
	      <o:OfficeDocumentSettings>
		    <o:AllowPNG/>
		    <o:PixelsPerInch>96</o:PixelsPerInch>
		  </o:OfficeDocumentSettings>
		</xml><![endif]-->
    <style>
    #title-wrap {
        margin: auto 0;
        text-align: center;
        font-variant: small-caps;
        font-weight: bold
    }

    table {
        margin: auto;
        border-collapse: collapse;
        width: 100%
    }

    td,
    th {
        border: 1px solid #ddd;
        border-color: rgba(221,221,221,0.5);
        text-align: left;
        padding: 1px;
    }

    #list {
        text-align: center
    }
    </style>
</head>

<body style="margin:0;padding:0;min-width:100%;background-color:#ffffff;">
    <div style="display:none;font-size:1px;color:#ffffff;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;"></div>
    <div id="title-wrap"><h2 class="title">CompBio Project Submission Form</h2>
    </div>
    <table width="100%" border="0" cellpadding="0" cellspacing="0" style="min-width: 100%;" role="presentation">
        <tr>
            <th>Lab:</th>
            <td>' . $data['Laboratory_Name'] . '</td>
        </tr>
        <tr>
            <th>Project Name:</th>
            <td>' . $data['Project_Name'] . '</td>
        </tr>
        <tr>
            <th>Email:</th>
            <td>' . $data['Contact_Email'] . '</td>
        </tr>
        <tr>
            <th>Num Entities:</th>
            <td>' . $data['Num_Entities'] . '</td>
        </tr>
    </table>
</body>

</html>';


	$headers = "MIME-Version: 1.0" . "\r\n";
	$headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
	$headers .= 'From: <<EMAIL>>' . "\r\n";
	mail($to,$subject,$message,$headers);
	echo 'email sent';
	}
?>

