                                <h3 class="display-4 page-title text-center d-none d-md-block mb-2">Assertion Generation</h3>
                                <h3 class="page-title text-center d-md-none mb-2">Assertion Generation</h3>
                                <section class="tab-content">
                                    <div class="row tab-content-row">
                                        <div id="completed-assert-generations" class="col-md-6">
                                            <h5 class="content-title col-12">Completed Assertion Generation(s):</h5>
                                            <div class="box-outline-wrap">
                                                <ul id="assert-list">
                                                        <i class="fa fa-spinner fa-pulse fa-5x fa-fw" style="position: absolute;top: 35%;left: 40%"></i>
                                                        <span class="sr-only">Loading...</span>
                                                </ul>
                                            </div>
                                        </div>
                                        <div id="assertion-eligible-libs" class="col-md-6 d-none">
                                            <h5 class="content-title col-12">Assertion Eligible Libraries:</h5>
                                            <div class="box-outline-wrap">
                                                <ul id="assert-lib-list">
                                                        <i class="fa fa-spinner fa-pulse fa-5x fa-fw" style="position: absolute;top: 35%;left: 40%"></i>
                                                        <span class="sr-only">Loading...</span>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="col-md-6 justify-content-start btn-panel-group">
                                            <div class="row">
                                               <div class="col-lg-6">
                                                    <button type="button" id="vis-assert-btn" class="btn btn-primary btn-block btn-standard assert-btns" disabled>Visualize Assertion</button>
                                                </div>
                                               <div class="col-lg-6">
                                                    <button type="button" id="show-assertions-btn" class="btn btn-primary btn-block btn-standard list-assert-btns" disabled>Show Completed Assertions</button>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-lg-6">
                                                     <button type="button" id="open-assert2form-btn" class="btn btn-primary btn-block btn-standard assert-btns">New Assertion 2.0</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="open-assertform-btn" class="btn btn-primary btn-block btn-standard assert-btns">New Assertion</button>
                                                </div>
                                            </div>
                                            <div class="row">
                                               <div class="col-lg-6">
                                                    <button type="button" id="dl-assertfiles-btn" class="btn btn-primary btn-block btn-standard assert-btns" disabled>Download Assertion</button>
                                                </div>
                                                <div class="col-lg-6">
                                                    <button type="button" id="delete-assert-btn" class="btn btn-primary btn-block btn-standard assert-btns" disabled>Delete Assertion</button>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                               <div class="col-lg-6">
                                                    <button type="button" id="new-assertlib-btn" class="btn btn-primary btn-block btn-standard lib-assert-btns" disabled>New Assertion Library</button>
                                                </div>
                                               <div class="col-lg-6">
                                                    <button type="button" id="show-assertlib-btn" class="btn btn-primary btn-block btn-standard list-assert-btns">Show Libraries</button>
                                                </div>
                                            </div>
                                            <div class="row">
                                               <div class="col-lg-6">
                                                    <button type="button" id="delete-assertlib-btn" class="btn btn-primary btn-block btn-standard lib-assert-btns" disabled>Delete Library</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row tab-content-row">
                                        <div id="assert-info" class="col-6 d-none">
                                            <div class="box-outline-wrap table-responsive" style="display: block;">
                                                <table class="table info-table">
                                                    <tr>
                                                        <th>Title:</th>
                                                        <td id="assert-name" class="assertinfo-tablecell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Creation Date:</th>
                                                        <td id="assert-creationdate" class="assertinfo-tablecell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Base Project:</th>
                                                        <td id="assert-basename" class="assertinfo-tablecell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Secondary Project:</th>
                                                        <td id="assert-secondaryname" class="assertinfo-tablecell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Engine Version:</th>
                                                        <td id="assert-engversion" class="assertinfo-tablecell"></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                        <div id="assert-lib-info" class="col-6 d-none">
                                            <div class="box-outline-wrap table-responsive" style="display: block;">
                                                <table class="table info-table">
                                                    <tr>
                                                        <th>Library Name:</th>
                                                        <td id="assert-lib-name" class="assertinfo-lib-tablecell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Creation Date:</th>
                                                        <td id="assert-lib-creationdate" class="assertinfo-lib-tablecell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Assertion Count:</th>
                                                        <td id="assert-lib-count" class="assertinfo-lib-tablecell"></td>
                                                    </tr>
                                                    <tr>
                                                        <th>Library Included Assertions:</th>
                                                        <td id="assert-lib-included" class="assertinfo-lib-tablecell"></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                        <div id="create-assert-form" class="col-6 d-none">
                                            <h5 class="content-title col-12" id="assert-form-title">Generate New Assertion</h5>
                                            <hr class="section-seperate">
                                            <form id="assert-generation-form" class="input-form">
                                                <div class="form-group">
                                                    <label for="assert-generation-name">Assertion Name: </label>
                                                    <input type="text" class="form-control assert-form-input" id="assert-generation-name" required>
                                                </div>
                                                <div class="form-group text-center form-notice">
                                                    <p>*Please note that comparing two libraries is not yet supported*</p>
                                                </div>
                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="assert-base-project">Base Project/Library: </label>
                                                        <select id="assert-base-project" class="form-control assert-form-input assert-select" required>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                      <label for="assert-secondary-project">Secondary Project/Library: </label>
                                                      <select id="assert-secondary-project" class="form-control assert-form-input assert-select" required>
                                                      </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <button type="submit" id="init-create-assertion" class="form-control btn btn-primary btn-standard" style="margin-top:0">Generate Assertion</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        <div id="create-assert2-form" class="col-6 d-none">
                                            <h5 class="content-title col-12" id="assert-form-title">Generate New Assertion 2.0</h5>
                                            <hr class="section-seperate">
                                            <form id="assert2-generation-form" class="input-form">
                                                <div class="form-group">
                                                    <label for="assert2-generation-name">Assertion Name: </label>
                                                    <input type="text" class="form-control assert-form-input" id="assert2-generation-name" required>
                                                    <div class="invalid-feedback">
                                                        Lab assertion dupilicate name.
                                                    </div>
                                                </div>
                                                <div class="form-row">
                                                    <div class="form-group col-md-6">
                                                        <label for="assert2-base-project">Base Project/Library: </label>
                                                        <select id="assert2-base-project" class="form-control assert-form-input assert-select" required>
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                      <label for="assert2-secondary-project">Secondary Project/Library: </label>
                                                      <select id="assert2-secondary-project" class="form-control assert-form-input assert-select" required>
                                                      </select>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <button type="submit" id="init-create-assertion2" class="form-control btn btn-primary btn-standard" style="margin-top:0">Generate Assertion</button>
                                                    </div>
                                                    <div class="form-group col-md-6">
                                                        <div class="form-check-inline">
                                                            <label class="form-check-label" for="significant_themes">
                                                                <input type="checkbox" class="form-check-input" id="significant_themes">Use only concepts from siginificant themes
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                        <div id="create-assertlib-form" class="col d-none">
                                            <h5 class="content-title col-12" id="assert-form-title">Generate New Assertion Library</h5>
                                            <hr class="section-seperate">
                                            <form id="assertlib-generation-form" class="input-form">
                                                <div class="form-row">
                                                    <div class="col">
                                                        <div class="form-group mb-0">
                                                            <label for="assert-generationlib-name">Library Name: </label>
                                                            <input type="text" id="assert-generationlib-name" class="form-control" placeholder="Enter Name..." required>
                                                        </div>
                                                        <div class="form-group">
                                                            <span id="lib-count-warn" class="d-none">You must add two or more projects to create a library...</span>
                                                            <!-- <select id="lib-assertion-select" class="form-control" multiple="multiple">
                                                            </select> -->
                                                            <div class="form-row">
                                                                <div id="assert-projname-colhead" class="col">
                                                                    Name
                                                                </div>
                                                                <div id="assert-date-colhead" class="col">
                                                                    Date
                                                                </div>
                                                            </div>
                                                            <div class="table-responsive" id="lib-assertion-select-wrapper">
                                                                <table class="table table-light" id="lib-assertion-select">

                                                                </table>
                                                            </div>
                                                            <small class="form-text text-muted pl-3">
                                                                <ul>
                                                                    <li>Click to select projects followed by clicking "Add To Library" to move selection to "Library List"</li>
                                                                    <li>Double click to add directly to the List, Ctrl+Click to select multiple items, Shift+Click to select blocks of items.</li>
                                                                </ul>
                                                            </small>
                                                        </div>
                                                        <div class="row">
                                                            <div class="form-group col">
                                                                <button id="assert-tolib-btn" class="form-control btn btn-primary btn-standard">Add To Library</button>
                                                            </div>
                                                            <div class="form-group col">
                                                                <button id="init-lib-btn" class="form-control btn btn-primary btn-standard">Create Library</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="row">
                                                            <div class="form-group col">
                                                                <label for="lib-assertion-list">Assertion Library List: </label>
                                                            </div>
                                                            <div class="form-group col">
                                                                <button id="lib-assert-list-clear" class="form-control btn btn-primary btn-standard">Clear List</button>
                                                            </div>
                                                        </div>
                                                        <div class="form-group">
                                                            <div id="lib-list-parent">
                                                                <ol id="lib-assertion-list">
                                                                </ol>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div id="dialog-assertion" title="Assertion Generation" class="dialog-load-hidden" style="display:none;" >
                                        <p id="assertion-message"></p>
                                    </div>
                                </section>