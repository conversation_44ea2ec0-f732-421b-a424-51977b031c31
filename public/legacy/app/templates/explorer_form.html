<form autocomplete="off" name="explorer-form" id="explorer-form" method="post" action="../assets/php/explorer-submit.php" enctype="multipart/form-data">
    <input type="hidden" name="explorer">
    <div class="row d-flex justify-content-around">
        <div class="col-8">
            <div class="row">
                <div class="col">
                    <div class="row">
                        <fieldset class="col mb-3 border">
                            <legend class="w-auto">Project Name</legend>
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <input type="text" class="form-control" id="explorer-name" name="explorer-name" required/>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <div class="row">
                        <fieldset class="col mb-3 border">
                            <legend class="w-auto">Concepts</legend>
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <input type="text" class="form-control explorer-input" id="biological_concepts" name="biological_concepts" />
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <div class="row">
                        <fieldset class="col mb-3 border form-group">
                            <legend class="w-auto">Pathophysiology</legend>
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <input type="text" class="form-control explorer-input" id="patho_raw_hierarchy" name="patho_raw_hierarchy" />
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <div class="row">
                        <fieldset class="col mb-3 border mx-auto">
                            <legend class="w-auto">Physiology</legend>
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <input type="text" class="form-control explorer-input" id="phys_raw_hierarchy" name="phys_raw_hierarchy" />
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="col-4 d-none" id="submission-build-col">
                    <fieldset class="col mb-3 border mx-auto">
                        <legend class="w-auto">Submission</legend>
                        <div class="form-group">
                            <ol id="submission-build-list">

                            </ol>
                        </div>
                        <div class="form-group">
                            <div class="sub-build-btns">
                                <button type="button" id="multi-add-to-term-btn" class="btn btn-primary btn-standard btn-block d-none">Add To Term</button>
                                <button type="button" id="multi-add-term-btn" class="btn btn-primary btn-standard btn-block d-none">Add Term</button>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>
            <div class="row">
                <fieldset class="col-12 border mx-auto mb-3">
                    <legend class="w-auto">Entities</legend>
                    <div class="row">
                        <div class="col-12 col-md-6 mb-2">
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <label for="entities_gene">Gene</label>
                                    <input type="text" class="form-control explorer-input" id="entities_gene" name="entities_gene" />
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 mb-2">
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <label for="entities_metabolites">Metabolite</label>
                                    <input type="text" class="form-control explorer-input" id="entities_metabolites" name="entities_metabolites" />
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 mb-2">
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <label for="entities_microbes">Microbe</label>
                                    <input type="text" class="form-control explorer-input" id="entities_microbes" name="entities_microbes" />
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 mb-2">
                            <div class="form-group">
                                <div class="autocomplete w-100">
                                    <label for="entities_mirna">MicroRNA</label>
                                    <input type="text" class="form-control explorer-input" id="entities_mirna" name="entities_mirna" />
                                </div>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="row">
                <fieldset class="col border ml-0 mr-1 mb-3 mr-1">
                    <legend class="w-auto">Variant(s)</legend>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group w-100">
                                <textarea id="bioexpl-variance" name="bioexpl-variance" class="w-100" placeholder="Copy and Paste the Line Seperated Variant(s) List Here..." rows="7"></textarea>
                                <!-- <small id="variance-help" class="form-text text-muted">Some helpful text here.</small> -->
                            </div>
                        </div>
                    </div>
                </fieldset>
                <fieldset class="col border mr-0 ml-1 mb-3">
                    <legend class="w-auto">Bulk Genes</legend>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group w-100">
                                <textarea id="bioexpl-bulk-genes" name="bioexpl-bulk-genes" class="w-100" placeholder="Copy and Paste the Line Seperated Genes List Here..." rows="7" disabled></textarea>
                                <!-- <small id="bulk-genes-help" class="form-text text-muted">Some helpful text here.</small> -->
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
        <div class="col-3">
            <div class="row">
                <fieldset class="col mb-3 border form-group">
                    <legend class="w-auto">Query Type</legend>
                    <div class="form-check mt-2 mb-4">
                        <input class="form-radio-input" type="radio" name="sub-type" value="single-single" id="radio-single-single" checked>
                        <label class="form-check-label" for="radio-single-single">
                          Single Term Query
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-radio-input" type="radio" name="sub-type" value="single-multi" id="radio-single-multi">
                        <label class="form-check-label" for="radio-single-multi">
                         Create Multi-Word Term Query
                        </label>
                    </div>
                    <div class="form-check mb-4 mt-2">
                        <input class="form-radio-input" type="radio" name="sub-type" value="multi-single" id="radio-multi-single">
                        <label class="form-check-label" for="radio-multi-single">
                         Multi-Term Query
                        </label>
                    </div>
<!--                     <div class="form-check my-4">
                        <input class="form-radio-input" type="radio" name="sub-type" value="multi-multi" id="radio-multi-multi">
                        <label class="form-check-label" for="radio-multi-multi">
                          Multi-Term/Multi-Word
                        </label>
                    </div>   -->
                </fieldset>
            </div>
            <div class="row">
                <fieldset class="col mb-3 border form-group">
                    <legend class="w-auto">Included Terms</legend>
                    <div class="invalid-message d-none" id="checkbox-validation-message">
                        At least one term must be selected.
                    </div>
                    <div class="form-check mt-2 mb-4">
                        <input class="form-check-input" type="checkbox" name="include-all-terms" value="all" id="include-all-terms">
                        <label class="form-check-label" for="include-all-terms">
                          All Terms
                        </label>
                    </div>
                    <hr class="border-light">
                    <div class="form-check mb-4 mt-2">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="concept-check" value="concept" id="concept-check">
                        <label class="form-check-label" for="concept-check">
                          Concept
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="pathophysiology-check" value="pathophysiology" id="pathophysiology-check">
                        <label class="form-check-label" for="pathophysiology-check">
                          Pathophysiology
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="physiology-check" value="physiology" id="physiology-check">
                        <label class="form-check-label" for="physiology-check">
                          Physiology
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="gene-check" value="gene" id="gene-check">
                        <label class="form-check-label" for="gene-check">
                          Gene
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="metabolite-check" value="metabolite" id="metabolite-check">
                        <label class="form-check-label" for="metabolite-check">
                          Metabolite
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="microbe-check" value="microbe" id="microbe-check">
                        <label class="form-check-label" for="microbe-check">
                          Microbe
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="mirna-check" value="microrna" id="mirna-check">
                        <label class="form-check-label" for="mirna-check">
                          MicroRNA
                        </label>
                    </div>
                    <div class="form-check my-4">
                        <input class="form-check-input explorer-checkbox" type="checkbox" name="variant-check" value="variant" id="variant-check">
                        <label class="form-check-label" for="variant-check">
                          Variant
                        </label>
                    </div>
                </fieldset>
            </div>
            <div class="row">
                <div class="col-12">
                    <div class="form-group">
                        <input type="text" id="account-name" name="account-name" hidden/>
                        <button type="submit" class="btn btn-primary btn-standard btn-block" id="explorer-submit-btn">Submit</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<div id="bioexplorer-response" title="BioExplorer Submission Status" class="dialog-load-hidden" style="display:none;" >
    <p id="be-response-message"></p>
</div>