
    <!--Usersnap Widget-->
    <script type="text/javascript">
    (function() {
        var s = document.createElement("script");
        s.type = "text/javascript";
        s.async = true;
        s.src = '//api.usersnap.com/load/a18feae2-4712-4f5b-b4c3-8287a03c1546.js';
        var x = document.getElementsByTagName('script')[0];
        x.parentNode.insertBefore(s, x);
    })();
    </script>
    <script type="text/javascript">
        let postBody = {
            "jsonrpc":"2.0",
            "method":"GetPCMMVesrionInfo",
            "id":"1001",
            "params":""
        };
        let opts = {"body": JSON.stringify( postBody ),"method":"POST","mode":"cors"};
        let pcmmEndpoint = getPcmmEndpoint();
        fetch(pcmmEndpoint, opts)
        .then(resp => resp.json())
        .then((json) => { 
            console.log(json);
            let versionElement = document.getElementById('version-display');
            if(versionElement) {
                versionElement.innerHTML = '<span id="pcmm-version">PCMM: v' + json.result.PCMMVersion + '</span>&emsp;' +
                                            '<span id="pcmm-data-file">Data File: ' + json.result.PCMMDatafile.replace(/^.*[\\\/]/, '').split('.')[0] + '</span>';
            } else {
                console.log('!!!VersionElement NOT Found!!!');
            }
            /* Example Response
            {
                "jsonrpc": "2.0",
                "id": "1001",
                "result": {
                    "PCMMVersion":"2.6.0",
                    "PCMMDatafile":"/huge1/cmm_min_2023_06_06.bin",
                    "PCMMShaHash":"6994074933a99be75b02b32e18f691fdbdb48fb7"
                }
            }
            */
        });
    </script>
</body>

</html>