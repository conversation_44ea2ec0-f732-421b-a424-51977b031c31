<style type="text/css">
	#notes-tab,#ent-map-tab,#chat-tab,#help-tab {
	    /* font-family: Roboto, sans-serif !important; */
		 font-family: sans-serif;
	    font-size: 14px !important;
	    text-transform: uppercase;
	    bottom: 0;
	    position: fixed !important;
	    /* border-right: 1px solid #000000; */
	    border-radius: 5px 5px 0 0 !important;
	    padding: 0 17px !important;
	    height: 0;
	    line-height: 30px !important;
	    cursor: pointer !important;

	    width: auto !important;
	    margin: 0 !important;
	    color: #fff;
	    box-sizing: content-box;
	    z-index: 3147483637;
  		-webkit-transition: height 0.5s;
  		transition: height 0.5s;
		user-select:none;
 	}

	#ent-map-tab {
		right: 15em;
		background-color: #27215f !important;
	}
	#notes-tab {
		right: 7em;
		background-color: #27215f !important;
		text-shadow: none !important;
	}
	#chat-tab {
		right: 28.5em;
		background-color: #27215f !important;
	}
	#help-tab {
		right: 0em;
		background-color: #1b80f3 !important;
	}
	.notes-tab-inner {
	    position: relative;
	    bottom: -3px;
	}
	.help-tab-inner {
	    position: relative;
	    bottom: -3px;
	}
	div.vis-tabs {
		border: 1px #ffffff solid;
		border-bottom: none;
	}
	div.vis-tabs:hover {
		height: 35px !important;
		border-width: 1.5px;
	}
</style>

<!-- <div id="chat-tab" class="vis-tabs">
	<div class="notes-tab-inner">
		<i class="fa fa-comment" aria-hidden="true"></i> Chat
	</div>
</div> -->
<div id="chat-tab" class="vis-tabs">
	<div class="notes-tab-inner" style="text-transform: initial;">
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" width="20" height="20"
			style="vertical-align: middle; margin-right: 4px; margin-bottom: 8px" fill="currentColor" aria-hidden="true">
			<path
				d="M32 224H64V416H32A32 32 0 0 1 0 384V256A32 32 0 0 1 32 224zm512-48V448a64.1 64.1 0 0 1 -64 64H160a64.1 64.1 0 0 1 -64-64V176a80 80 0 0 1 80-80H288V32a32 32 0 0 1 64 0V96H464A80 80 0 0 1 544 176zM264 256a40 40 0 1 0 -40 40A40 40 0 0 0 264 256zm-8 128H192v32h64zm96 0H288v32h64zM456 256a40 40 0 1 0 -40 40A40 40 0 0 0 456 256zm-8 128H384v32h64zM640 256V384a32 32 0 0 1 -32 32H576V224h32A32 32 0 0 1 640 256z">
			</path>
		</svg> ChatGPT
	</div>
</div>
<div id="ent-map-tab" class="vis-tabs">
	<div class="notes-tab-inner">
		<i class="fa fa-percent" aria-hidden="true"></i> Entities Mapped
	</div>
</div>
<div id="notes-tab" class="vis-tabs">
	<div class="notes-tab-inner">
		<i class="fa fa-pencil-square-o" aria-hidden="true"></i> Notes
	</div>
</div>
<div id="help-tab" class="vis-tabs">
	<div class="help-tab-inner">
		<i class="fa fa-question-circle" aria-hidden="true"></i> Help
	</div>
</div>
<script>
	document.getElementById('chat-tab').addEventListener('click', function() {
		const chatBox = document.getElementById('chat-box');
		if (showChat) {
			chatBox.style.visibility = "hidden";
			showChat = false;
		} else {
			chatBox.style.visibility = "visible";
			showChat = true;
		}
	});
	document.getElementById('chat-container-min').addEventListener('click', function() {
		document.getElementById('chat-tab').click();
	});
	document.getElementById('notes-tab').addEventListener('click', function() {
		if($('#dialog-notes').dialog('isOpen')==true) {
			$('#dialog-notes').dialog('close');
		} else {
			$('#dialog-notes').dialog('open');
		}
	});
	document.getElementById('ent-map-tab').addEventListener('click', function() {
		prepareEntityCurve();
	});
	document.getElementById('help-tab').addEventListener('click', function() {
		window.open(Webroot+'legacy/help/CompBio_Manual.pdf');
	});	
</script>