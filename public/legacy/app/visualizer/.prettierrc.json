{"overrides": [{"files": "./epmdev/js/**/*.js", "parser": "typescript", "printWidth": 80, "tabWidth": 2, "semi": true, "bracketSpacing": true, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid", "useTabs": false}, {"files": "./js/mainCfg.js", "parser": "typescript", "printWidth": 80, "tabWidth": 2, "semi": true, "bracketSpacing": true, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid", "useTabs": false}, {"files": "./epmdev/scss/**/*.scss", "parser": "scss", "printWidth": 80, "tabWidth": 2, "semi": true, "bracketSpacing": true, "singleQuote": true, "trailingComma": "none", "arrowParens": "avoid", "useTabs": false}]}