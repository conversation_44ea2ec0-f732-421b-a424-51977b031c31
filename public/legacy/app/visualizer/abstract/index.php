<?php
    include('../../../config/config.php');
    session_start();
    if(isset($_SESSION['LoggedIn'])){
        include(TEMPLATES_ROOT.DIRECTORY_SEPARATOR.'cpv_header.php');
        echo '<title>GTAC-CompBio Theme Abstract Summaries-WUSTL</title>';
        if(file_exists('css/abstract.min.css')) {
            echo '<link rel="stylesheet" type="text/css" href="css/abstract.min.css?ver='.filemtime('css/abstract.min.css').'">';
        } else {
            echo '<link rel="stylesheet" type="text/css" href="css/abstract.css?ver='.filemtime('css/abstract.css').'">';
        }
    } else {
        session_destroy();
        header('Location: '.WEBROOT);exit;
    }
?>
<style>
  .pub-title {
    text-transform: capitalize;
  }
  .label {
    margin-bottom: 0;
  }
  h4.pub-id {
    text-transform: uppercase;
    font-family: sans-serif;
  }
  p.pub-abstract, p.pub-full-text {
    font-weight: normal;
    margin-top: 2px;
    /* text-transform: capitalize; */
  }
  span {
    font-weight: bold;
  }
  .full-text-label {
    margin: 2em 0;
  }
  .full-text {
    display: none;
  }
  div.btns > button {
    padding: 5px 10px;
  }
/* Loading Spinner */
.loader {
  margin-left: auto;
  margin-right: auto;
  margin-top: 25vh;
  border: 16px solid #f3f3f3;
  border-radius: 50%;
  border-top: 16px solid #3498db;
  width: 120px;
  height: 120px;
  -webkit-animation: spin 1.5s linear infinite; /* Safari */
  animation: spin 1.5s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.keys li {
    text-align: center;
    list-style: none;
    font-family: Raleway;
    font-weight: bold;
}
.keys ul {
    display: flex;
    flex-flow: row;
    justify-content: space-evenly;
}
.keys {
    border: solid 1px #000;
    border-radius: 5px;
    margin: 0 50px;
}
#version-display {
    display: none;
}
</style>
</head>
<!-- <body onload="getQuery()"> -->
<body>
    <div id="abstract-heading">
        <h1>Abstracts Relating To Your Selection</h1>
        <p>Click the "See Original Document" to see the source of the Abstract</p>
    </div>
    <div class="keys">
        <ul>
            <li id="be-label-swap">Selected Entity: <span style="color:#ff0000;">Red</span></li>
            <li>Selected Concept: <span style="color:#0000ff;">Blue</span></li>
            <li>Concepts in Theme: <span style="color:#cc00cc;">Purple</span></li>
            <li>Concepts in Project: <span style="color:#00aa00;">Green</span></li>
        </ul>
        <ul id="terms"></ul>        
    </div>
    <div id="result-count"></div>
    <div id="content"><div class="loader"></div></div>
    <?php 
        if(file_exists('js/abstract.min.js')) {
            echo '<script src="js/abstract.min.js?ver='.filemtime('js/abstract.min.js').'"></script>';
        } else {
            echo '<script src="js/abstract.js?ver='.filemtime('js/abstract.js').'"></script>';
        }
        echo file_get_contents(TEMPLATES_ROOT.DIRECTORY_SEPARATOR.'footer.html'); 
    ?>