'use strict';
// console.log( 'JS loaded' );
if (sessionStorage.hasOwnProperty('TabData') == false) {
    sessionStorage.setItem('TabData', window.opener.localStorage.getItem('abstractData'));
    window.opener.localStorage.removeItem('abstractData');
}
window.bioExplorerProjectType = (sessionStorage.getItem('BioExplorerProject') === "true") ? true : false;
if (bioExplorerProjectType === true) {
    document.getElementById('be-label-swap').innerHTML = document.getElementById('be-label-swap').innerHTML.replace('Entity', 'Term');
}
let abstractData = JSON.parse(sessionStorage.getItem('TabData'));
log(abstractData);
let key = abstractData.Key;
let a = abstractData.a;
let b = abstractData.b;
let queryCall;
if (abstractData.hasOwnProperty('c')) {
    let c = abstractData.c;
    let colorKeys = [].slice.call(document.getElementsByTagName('ul'))[0].children;
    colorKeys[0].style.display = 'none';
    colorKeys[1].innerHTML = 'Selected Concepts: <span style="color:#0000ff;">Blue</span>';
    queryCall = a + ',' + b + ',' + c;
    appendQueryTerms(null, `${a},${b}`)
} else {
    queryCall = a + ',' + b;
    appendQueryTerms(a, b);
}
let data3;
let outputHtml = document.getElementById('content');
let len;
let themeConcepts = abstractData.ThemeConcepts;
let allConcepts = [];
let isOrigVis = (abstractData.Version === true) ? true : false;
window.opener.data.forEach(function (item) {
    let itemArr = Object.keys(item.Concepts);
    allConcepts = allConcepts.concat(itemArr);
});
let terms = {
    'a': abstractData.a,
    'b': abstractData.b,
    'all': allConcepts
};
if (abstractData.hasOwnProperty('ThemeConcepts') == true) {
    terms.theme = abstractData.ThemeConcepts;
}

function appendQueryTerms(gene, concept) {
    if (gene === null) {
        const label = 'Concepts: ';
        let concepts = concept.split(',').map((item) => `<span style="color:#0000ff;">${(item.includes('(')) ? item.substring(3) : item}</span>`);
        const output = `<li>${label}${concepts.join()}</li>`;
        document.getElementById('terms').innerHTML = output;
    } else {
        const label = ['Entity: ', 'Concept: '];
        gene = (bioExplorerProjectType === true) ? `<li>Term: <span style="color:#ff0000;">${(gene.includes('(')) ? gene.substring(3) : gene}</span></li>` : `<li>Entity: <span style="color:#ff0000;">${(gene.includes('(')) ? gene.substring(3) : gene}</span></li>`;
        concept = `<li>Concept: <span style="color:#0000ff;">${(concept.includes('(')) ? concept.substring(3) : concept}</span></li>`;
        const output = `${gene}${concept}`;
        document.getElementById('terms').innerHTML = output;
    }
    // console.log('Query Terms Appended...');
}

function removeNestedSpans() {
    // console.log('Searching for nested spans')
    let allSpans = document.getElementById('content').getElementsByTagName('SPAN');
    for (let span of allSpans) {
        if (span.childElementCount > 0) {
            span.innerHTML = span.innerText;
            // console.log('removed nested span')
        }
    }
}

function getQuery() {
    let data = { 'function': 'abstractQueryPHP', 'parameters': { 'Key': key, 'Query': queryCall, 'Terms': terms } };
    // console.log( data );
    data = JSON.stringify(data);
    httpRequest(data);
}

/* 
 * !! Updated !! 
 * Make PCMM Direct Call
 * Eliminate abstractQueryPHP call
 * Eliminates need for <project_name>_Genes_HTML.json file
 */
function pcmmAbstractQuery() {
    if (abstractData.hasOwnProperty('AiSummary') && abstractData.AiSummary === true) {
        let data4 = {
            "UseChatGPT": "true",
            "KeyEntity": abstractData.a,
            "KeyConcept": abstractData.b,
            "ThemeConcepts": abstractData.ThemeConcepts,
            "ProjectConcepts": allConcepts,
            "PMIDList": []
        };
        let postBody = {
            "jsonrpc": "2.0",
            "method": "GetScoredArticleList",
            "id": "299",
            "params": data4
        }
        let opts = { "body": JSON.stringify(postBody), "method": "POST", "mode": "cors" };
        let pcmmEndpoint = getPcmmEndpoint();
        fetch(pcmmEndpoint, opts)
            .then(resp => resp.json())
            .then((json) => {
                console.log(json);
                if (json.hasOwnProperty('result') && json.result.length > 0) {
                    const el = document.createElement('DIV');
                    el.classList.add('abstract');
                    el.innerHTML = `<hr>
                            <h4 class="label" style="margin-bottom:5px">Chatgpt Summary of Top Abstracts:</h4>
                            <p class="pub-abstract" style="padding:5px 10px;">${json.result[0]}</p>`;
                    document.getElementById('content').innerHTML = '';
                    document.getElementById('content').appendChild(el);
                }
            })
            .then(() => {
                $("#abstract-heading").html('<h1>ChatGpt Generated Summary</h1>');
                $("body").append('<h4 style="padding:15px 25px;font-family:sans-serif;margin:25px;background-color:#eeecff;border:1px solid #373070;">Disclaimer for ChatGPT summaries: <span style="color:blue;">These summaries are generated by ChatGPT based on the information contained in the top abstracts identified by CompBio.  They are meant to provide a short, rapid assessment of key information associated with the selected entity and concept in the context of the given theme.  The number of abstracts can vary but typically falls into the range of the 8 to 10, if that many are available from the analysis.  Please note, that the query restricts ChatGPT to simple summarization of the provided text to reduce the probability of ChatGPT introducing false statements.  Within these constraints falsehoods have been rare, though not zero, so it is recommended to review the abstracts for confirmation if important information is being gleaned from the summary.</span></h4>');
            })
            .catch(error => console.log('Error:', error));
    } else {
        let data4 = {
            "UseChatGPT": "false",
            "KeyEntity": abstractData.a,
            "KeyConcept": abstractData.b,
            "ThemeConcepts": abstractData.ThemeConcepts,
            "ProjectConcepts": allConcepts,
            "PMIDList": []
        };
        let postBody = {
            "jsonrpc": "2.0",
            "method": "GetScoredArticleList",
            "id": "299",
            "params": data4
        }
        let opts = { "body": JSON.stringify(postBody), "method": "POST", "mode": "cors" };
        let pcmmEndpoint = getPcmmEndpoint();
        fetch(pcmmEndpoint, opts)
            .then(resp => resp.json())
            .then((json) => {
                console.log(json);
                if (json.hasOwnProperty('result') && json.result.length > 0) {
                    document.getElementById('content').innerHTML = '';
                    for (let publication of json.result) {
                        addToPageContent(publication);
                    }
                    if (Array.isArray(json.result)) {
                        appendResultsCount(json.result.length);
                    } else {
                        appendResultsCount();
                    }
                }
            })
            .catch(error => console.log('Error:', error));
    }
}

function httpRequest(data) {
    // console.log('cbv_function request made');
    let header = 'PostRequest=';
    let request = header + encodeURIComponent(data);
    let url = '../../assets/php/cbv_functions.php';
    let xhr = new XMLHttpRequest();
    xhr.open('POST', url, true);
    xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function () {
        if (xhr.readyState == 4 && xhr.status == 200) {
            console.log('success');
            data3 = xhr.responseText;
            if (data3 == 'Error') {
                console.log(data3);
            } else if (data3 == 'No Matches Found...') {
                noAbstractData(data3);
            } else if (abstractData.hasOwnProperty('AiSummary') && abstractData.AiSummary === true) {
                data3 = JSON.parse(data3);
                console.log(data3);
                let setCount = 0;
                let set = [];
                data3.Publications = Array.from(new Set(data3.Publications));
                // console.log(data3.Publications)
                let data4 = {
                    "UseChatGPT": "true",
                    "KeyEntity": abstractData.a,
                    "KeyConcept": abstractData.b,
                    "ThemeConcepts": abstractData.ThemeConcepts,
                    "ProjectConcepts": data3.AllConcepts,
                    "PMIDList": data3.Publications
                };
                // "method": "GetCordArticle",
                let postBody = {
                    "jsonrpc": "2.0",
                    "method": "GetScoredArticleList",
                    "id": "299",
                    "params": data4
                }
                let opts = { "body": JSON.stringify(postBody), "method": "POST", "mode": "cors" };
                let pcmmEndpoint = getPcmmEndpoint();
                fetch(pcmmEndpoint, opts)
                    .then(resp => resp.json())
                    .then((json) => {
                        console.log(json);
                        if (json.hasOwnProperty('result') && json.result.length > 0) {
                            const el = document.createElement('DIV');
                            el.classList.add('abstract');
                            el.innerHTML = `<hr>
                            <h4 class="label" style="margin-bottom:5px">Chatgpt Summary of Top Abstracts:</h4>
                            <p class="pub-abstract" style="padding:5px 10px;">${json.result[0]}</p>`;
                            document.getElementById('content').innerHTML = '';
                            document.getElementById('content').appendChild(el);
                        }
                    })
                    .then(() => {
                        $("#abstract-heading").html('<h1>ChatGpt Generated Summary</h1>');
                        $("body").append('<h4 style="padding:15px 25px;font-family:sans-serif;margin:25px;background-color:#eeecff;border:1px solid #373070;">Disclaimer for ChatGPT summaries: <span style="color:blue;">These summaries are generated by ChatGPT based on the information contained in the top abstracts identified by CompBio.  They are meant to provide a short, rapid assessment of key information associated with the selected entity and concept in the context of the given theme.  The number of abstracts can vary but typically falls into the range of the 8 to 10, if that many are available from the analysis.  Please note, that the query restricts ChatGPT to simple summarization of the provided text to reduce the probability of ChatGPT introducing false statements.  Within these constraints falsehoods have been rare, though not zero, so it is recommended to review the abstracts for confirmation if important information is being gleaned from the summary.</span></h4>');
                    })
                    .catch(error => console.log('Error:', error));
            } else {
                data3 = JSON.parse(data3);
                console.log(data3);
                let setCount = 0;
                let set = [];
                data3.Publications = Array.from(new Set(data3.Publications));
                // console.log(data3.Publications)
                let data4 = {
                    "UseChatGPT": "false",
                    "KeyEntity": abstractData.a,
                    "KeyConcept": abstractData.b,
                    "ThemeConcepts": abstractData.ThemeConcepts,
                    "ProjectConcepts": data3.AllConcepts,
                    "PMIDList": data3.Publications
                };
                // "method": "GetCordArticle",
                let postBody = {
                    "jsonrpc": "2.0",
                    "method": "GetScoredArticleList",
                    "id": "299",
                    "params": data4
                }
                let opts = { "body": JSON.stringify(postBody), "method": "POST", "mode": "cors" };
                let pcmmEndpoint = getPcmmEndpoint();
                fetch(pcmmEndpoint, opts)
                    .then(resp => resp.json())
                    .then((json) => {
                        console.log(json);
                        if (json.hasOwnProperty('result') && json.result.length > 0) {
                            // let count = 0;
                            document.getElementById('content').innerHTML = '';
                            for (let publication of json.result) {
                                // setTimeout(function() {
                                addToPageContent(publication);
                                // }, 0);
                                // count++;
                            }
                            appendResultsCount();
                        }
                    })
                    .catch(error => console.log('Error:', error));
            }
        }
    };
    xhr.send(request);
}
// function getChatGptSummaryProxy( obj ) {
//     $.ajax({
//         url : location.origin + '/pcmm_proxy_api/get_abstract_article.php',
//         type : 'POST',
//         data : JSON.stringify( array ),
//         success : function(response) {
//             response = JSON.parse(JSON.parse( response ));
//             console.log(response);
//             if(typeof response !== 'undefined' && response.result.length > 0) {
//                 let count = 0;
//                 for(let publication of response.result) {
//                     setTimeout(function() {
//                         addToPageContent(publication);
//                     }, 0);
//                     count++;
//                 }
//                 setTimeout(removeNestedSpans(), 3000);
//             } else {
//                 console.log('ERROR: pcmm query returned no results...')
//             }
//             appendResultsCount();
//         },
//         error: (error) => {
//             console.log(error)
//         }
//     });
// }
function getPublicationsProxy(array) {
    $.ajax({
        url: location.origin + '/pcmm_proxy_api/get_abstract_article.php',
        type: 'POST',
        data: JSON.stringify(array),
        success: function (response) {
            response = JSON.parse(JSON.parse(response));
            console.log(response);
            if (typeof response !== 'undefined' && response.result.length > 0) {
                let count = 0;
                for (let publication of response.result) {
                    setTimeout(function () {
                        addToPageContent(publication);
                    }, 0);
                    count++;
                }
                setTimeout(removeNestedSpans(), 3000);
            } else {
                console.log('ERROR: pcmm query returned no results...')
            }
            appendResultsCount();
        },
        error: (error) => {
            console.log(error)
        }
    });
}
function getPublications(array) {
    let pcmmEndpoint = getPcmmEndpoint();
    $.ajax({
        url: pcmmEndpoint,
        type: 'POST',
        data: JSON.stringify({
            "jsonrpc": "2.0",
            "method": "GetCordArticle",
            "id": "225",
            "params": array
        }),
        dataType: "json",
        success: function (response) {
            if (response.result.length > 0) {
                let count = 0;
                for (let publication of response.result) {
                    setTimeout(function () {
                        addToPageContent(publication);
                    }, 0);
                    count++;
                }
                setTimeout(removeNestedSpans(), 3000);
            } else {
                console.log('ERROR: pcmm query returned no results...')
            }
            appendResultsCount();
        },
        error: (error) => {
            console.log(error)
        }
    });
}

function appendResultsCount(count = document.getElementById('content').querySelectorAll('div.abstract').length) {
    document.getElementById('result-count').innerText = `${count} Abstracts Returned`;
}

function addToPageContent(article) {
    const el = document.createElement('DIV');
    el.classList.add('abstract');
    el.innerHTML = data3Prep(article);
    document.getElementById('content').appendChild(el);
    if (article.ID.includes('PMC')) {
        const fullText = el.getElementsByClassName('full-text')[0];
        const labels = el.getElementsByClassName('full-text-label');
        let [label1, label2, label3] = [...labels];
        label1.addEventListener('click', (event) => {
            $(label1).toggle();
            $(label2).toggle();
            $(label3).toggle();
            $(fullText).toggle("fast", "swing");
        });
        label2.addEventListener('click', (event) => {
            $(label3).toggle();
            $(label2).toggle();
            $(label1).toggle();
            $(fullText).toggle("fast", "swing");
        });
        label3.addEventListener('click', (event) => {
            $(label3).toggle();
            $(label2).toggle();
            $(label1).toggle();
            $(fullText).toggle("fast", "swing");
        });
    }
}

function data3Prep(item) {
    item.Title = (item.Title.trim().toLowerCase() === '') ? 'No Data...' : item.Title.trim();
    item.Abstract = (item.Abstract.trim().toLowerCase() === '') ? 'No Data...' : item.Abstract.trim();
    item.FullText = (item.FullText.trim().toLowerCase() === '') ? 'No Data...' : item.FullText.trim();
    let output = '';
    if (item.ID.includes('PMC')) {
        output += '<hr>';
        output += '<h2 class="pub-title">' + item.Title + '</h2>';
        output += '<h4 class="pub-id">' + item.ID + '</h4>';
        output += '<h4 class="label">Abstact:</h4>';
        output += '<p class="pub-abstract">' + item.Abstract + '</p>';
        output += '<h4 class="label origin-link-label">Document Link:</h4>';
        output += '<p class="origin-link">https://pmc.ncbi.nlm.nih.gov/articles/' + item.ID + '/</p>';
        output += '<div class="links"><a href="https://pmc.ncbi.nlm.nih.gov/articles/' + item.ID + '/" target="_blank">See Original Document</a></div>';
        output += '<div class="btns"><button class="label full-text-label">Show Full Text</button>';
        output += '<button class="label full-text-label" style="display:none">Hide Full Text</button></div>';
        output += '<div class="full-text"><h4 class="label">Full Text:</h4><p class="pub-full-text">' + item.FullText + '</p></div>';
        output += '<div class="btns"><button class="label full-text-label" style="display:none">Hide Full Text</button></div>';
    } else {
        output += '<hr>';
        output += '<h2 class="pub-title">' + item.Title + '</h2>';
        output += '<h4 class="pub-id">PMID: ' + item.ID + '</h4>';
        output += '<h4 class="label">Abstact:</h4>';
        output += '<p class="pub-abstract">' + item.Abstract + '</p>';
        output += '<h4 class="label origin-link-label">Document Link:</h4>';
        output += '<p class="origin-link">https://pubmed.ncbi.nlm.nih.gov/' + item.ID + '/</p>';
        output += '<div class="links"><a href="https://pubmed.ncbi.nlm.nih.gov/' + item.ID + '/" target="_blank">See Original Document</a></div>';
    }
    return output;
}

function noAbstractData(msg) {
    // console.log( msg );
    document.getElementById('content').innerHTML = '<h2>Sorry...no matches have been found.</h2>';
}

function buildDescriptionHTML({ geneId, conceptName, conceptId }) {
    let html = 'Abstracts Relating To ';
    if (geneId) {
        html += `Entity <span class="red">${geneId}</span> and Concept <span class="blue">${conceptName}</span>`;
    } else {
        html += `Concept <span class="blue">${conceptName}</span> and Concept <span class="blue">${conceptId}</span>`;
    }
    return html;
}

window.onload = () => {
    if (abstractData.hasOwnProperty('c')) {
        let c = abstractData.c;
        let colorKeys = [].slice.call(document.getElementsByTagName('ul'))[0].children;
        colorKeys[0].style.display = 'none';
        colorKeys[1].innerHTML = 'Selected Concepts: <span style="color:#0000ff;">Blue</span>';
        queryCall = a + ',' + b + ',' + c;
    } else {
        queryCall = a + ',' + b;
    }
    if (isOrigVis) {
        // getQuery();
        pcmmAbstractQuery();
    } else {
        const projectNameDiv = document.getElementById('project-name');
        const abstractDescription = document.getElementById('js-abstract-description');
        const projectDescription = window.opener.projectDescription;
        projectNameDiv.textContent = `Project: ${projectDescription.name}`;
        let qParams = utils.extractQueryParams(location.search);
        abstractDescription.innerHTML = buildDescriptionHTML(qParams);
        userData.init();
        if (!userData.isAuthTokenPresent) {
            router.navigate({
                state: router.STATES.LOGIN,
                changeLocation: true,
                replace: true
            });
            return;
        }
        // console.log( qParams );
        backendAPI.getProjectAbstracts(qParams.projectId, {
            geneId: qParams.geneId,
            conceptId: qParams.conceptId,
            concept: qParams.conceptName,
            termsAll: terms.all,
            termsTheme: terms.theme
        })
            .then(resp => {
                // console.log( resp );
                if (resp.Publications instanceof Array && resp.Publications.length === 0) {
                    alert('No Publications Found!');
                } else {
                    data3Prep(resp);
                }
            })
            .catch(err => {
                errorHandlers.handleAll(err);
            });
    }
};

function outputTestData() {
    const test = [
        {
            "ID": "CUID:5y1qgjr8",
            "Title": "will novel virus go pandemic or be contained",
            "Abstract": "the repatriation of 565 japanese citizens from wuhan china in late january offered scientists an unexpected opportunity to learn a bit more about the novel coronavirus 2019-ncov raging in that city to avoid domestic spread of the virus japanese officials screened every passenger for disease symptoms and tested them for the virus after they landed eight tested positive but four of those had no symptoms at all says epidemiologist hiroshi nishiura of hokkaido university sapporowhich is a bright red flag for epidemiologists who are trying to figure out what the fast-moving epidemic has in store for humanity if many infections go unnoticed as the japanese finding suggests that vastly complicates efforts to contain the outbreak two months after 2019-ncov emergedand with well over 20 000 cases and 427 deaths as science went to pressmathematical modelers have been racing to predict where the virus will move next how big a toll it might ultimately take and whether isolating patients and limiting travel will slow it but to make confident predictions they need to know much more about how easily the virus spreads how sick it makes people and whether infected people with no symptoms can still infect others some of that information is coming out of china but amid the all-out battle to control the virus and with diagnostic capabilities in short supply chinese researchers cannot answer all the questions countries with just a handful of cases such as japan can also reveal important data says preben aavitsland of the norwegian institute of public health its up to all countries now that receive cases to collect as much information as possible with the limited information so far scientists are sketching out possible paths that the virus might take weighing the likelihoods of each and trying to determine the fallout were at this stage where defined scenarios and the evidence for and against them are really important because it allows people to plan better says marc lipsitch an epidemiologist at the harvard t h chan school of public health these scenarios break into two broad categories the world gets the virus under controlor it doesnt the most optimistic scenario is one in which 2019-ncov remains mostly confined to china where 99 of the confirmed cases have occurred so far by 4 february two dozen other countries had together reported 195 cases there has obviously been a huge amount of spread within china but elsewhere theres no evidence of any kind of substantial human-to-human transmission says robin thompson a mathematical epidemiologist at the university of oxford the risk probably isnt as high as some models have been projecting if no other countries see sustained transmission and the quarantines and other measures taken in china start to reduce the number of infections there the risk of spread might gradually go down and the virus might eventually be quashed this happened with the severe acute respiratory syndrome sars outbreak in 2003 which ended after fewer than 9000 cases thats what the world health organization who which last week declared the outbreak a public health emergency of international concern hopes for this time in a press conference director-general tedros adhanom ghebreyesus called for a global version of the approach his team took in the current ebola outbreak fight the disease at the source and try to keep it from gaining a foothold elsewhere focus on the epicenter tedros said if you have several epicenters it is chaos epidemiologist marion koopmans of erasmus medical center says it may not be that hard to contain the virus in a new locale as long as the first cases are detected and isolated earlyprovided the virus is not highly transmissible we dont see it taking off in the 200 or so cases seeded outside of china koopmans says if that pattern holds there still is the possibility it will bend off she and others suspect the climate may help influenza typically only spreads during the winter months and hits northern and southern china at different times if that is true for 2019-ncov its spread might start to slow down in the northern hemisphere within a few months that is a big question mark were trying to assess at the moment says joseph wu a modeler at the university of hong kong but is containment realistic success will depend in part on whether infected people who dont have symptoms can spread the virus asymptomatic people are hard to find and isolate so if they can spread disease 2019-ncov will be very difficult to stop in china says alessandro vespignani a modeler of infectious diseases at northeastern university but if asymptomatic transmission is rare he says isolation and social distancing can have a big impact so far it has been difficult to get a handle on this question some data from china seem to support asymptomatic transmission but none are clear-cut a widely reported 30 january letter in the new england journal of medicine described the case of a chinese businesswoman who touched off a cluster of four cases in germany before she became sick herself but 4 days later it became clear the researchers had not contacted the woman who had flown back to china before the paper was published in a later phone interview she said she had experienced some symptoms while in germany in follow-up results announced in a 4 february press release the researchers noted that some patients they studied shed virus even though their symptoms were mild thats almost as bad as asymptomatic transmission says virologist christian drosten of the charit university hospital in berlin patients with mild symptoms are unlikely to seek medical care and may not even stay home giving the virus ample opportunities to spread far and wide based on what they have seen so far many researchers think its probably too late to contain the virus as the virus continues to spread in china the risk of exportation to other countries grows and sooner or later we will see it spread in another country aavitsland says so far there has been no sustained transmission outside of china but lipsitch expects that to change i would be really shocked if in 2 or 3 weeks there wasnt ongoing transmission with hundreds of cases in several countries on several continents if the virus does spread to all corners of the world in a pandemic several questions will loom large what percentage of the population will become infected and of those how many will get very sick or die more severe cases place heavier demands on health care systemshospitals in wuhan are already overwhelmedand result in greater fears and disruption of daily life a deadly pandemic might force the world to make stark choices about fair access to medicines or vaccines if they become available it might also lead to widespread restrictions on domestic travel akin to those already in force in china aavitsland says if on the other hand 2019-ncov resembles the common cold or a mild flu the spread of the virus would be less alarming existing travel bans likely would be lifted understanding the severity and case fatality rate is a challenge with any new pathogen when a new influenza strain emerged in 2009and went on to cause a pandemicmany worried it might turn out to be a nasty variety it took months to establish that the new virus killed only about one in 10 000 patients so far mortality among known 2019-ncov cases is about 2 and some reports say 20 of infected people suffer severe disease but these figures may overlook tens of thousands of people with mild diseasesay a sore throat or a low-grade feverwho never seek medical care and may not even know they were infected with 2019-ncov many may have no symptoms at all so what looks like a horrific disease may be the horrific tip of a very large iceberg lipsitch says the fact that four japanese evacuees were asymptomatic is a case in point studies in china have also reported some cases with few or no symptoms whats missing is a large study in china lipsitch says he suggests some fraction of the tests that are available in a place with many cases should be set aside for that purpose current recommendations in china call for testing people with clear symptoms only if indeed 2019-ncov becomes pandemic humanity may be stuck with it indefinitely after spreading far and wide the virus might become endemic in the human population just like four other coronaviruses that cause the common cold and occasionally cause fresh outbreaks how much death and disease it would cause is anyones guess the silver lining of the epidemic is that scientists have collected and shared information at record speed every day that goes by we know more and every day that goes by we can do better modeling vespignani says unfortunately this beast is moving very fast",
            "FullText": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
            "Summary": "",
            "URL": "https://doi.org/10.1126/science.367.6478.610"
        },
        {
            "ID": "23379566",
            "Title": "human gastrin-releasing peptide triggers growth of hepg2 cells through blocking endoplasmic reticulum stress-mediated apoptosis",
            "Abstract": "gastrin-releasing peptide grp is a kind of neural peptide that plays an important role in the growth of various human cancer cells however very little is known about the relationship between grp and apoptosis in human hepatocellular carcinoma cells this study investigated the influences of grp on apoptosis as well as the mechanism that triggers hepg2 growth the effects of grp on cell proliferation were examined by analysis of lactate dehydrogenase the grp caspase 12 and chop protein were detected in hepg2 and hl-7702 cells by western blot and endoplasmic reticulum er stress-related mrna transcription was detected by reverse transcription polymerase chain reaction to explore the specific pathway by which grp induces the cell growth we investigated the apoptosis-related pathway the expression of grp in hl-7702 cells inhibited tunicamycin triggered er stress-associated xbp1 atf4 and traf2 mrna transcription three main er stress-unfolded protein response pathway proteins including spliced xbp1 cleaved atf6 ire1-a perk and eif2-a were increased significantly furthermore the cleaved caspase 12 activation was blocked and chop expression was inhibited when grp was expressed either in hepg2 or hl-7702 cells in conclusion grp triggers the growth of hepg2 cells through blocking the er stress-mediated pathway",
            "FullText": "",
            "Summary": "",
            "URL": ""
        }
    ];
    if (document.getElementById('content').innerText === "Sorry...no matches have been found.") {
        document.getElementById('content').innerHTML = '';
    }
    test.forEach((item) => {
        addToPageContent(item);
    });
    document.getElementById('result-count').innerText = `${test.length} Abstracts Returned`;
}