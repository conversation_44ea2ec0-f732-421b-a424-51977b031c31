<?php
ini_set('memory_limit', '-1');
ini_set('max_execution_time', '-1');

function getSynonyms($array)
{
	$url = ( $_SERVER['SERVER_NAME'] === 'localhost' ) ? 'http://at-2.wucon.wustl.edu:8001/pcmmrpc' : 'localhost:8001/pcmmrpc';
	$curl = curl_init();
	curl_setopt_array( $curl, 
		array(
			CURLOPT_URL => $url,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_ENCODING => '',
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS => '{"jsonrpc":"2.0","method":"GetEntitySynonyms","id":"101","params":'.json_encode($array).'}',
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			),
		)
	);
	$response = curl_exec($curl);
	curl_close($curl);
	$result = json_decode($response)->result;
	$solution = $array;
	foreach($result as $item)
	{
		$solution = array_values(array_filter(array_unique(array_merge(
			$solution,
			[$item->ApprovedName],
			$item->Synonyms
		))));
	}
	return $solution;
}

function abstractQueryPHP($params) {
	if(isset($_SESSION['ProjectKeys'])) 
	{
		$projectkey = $params['Key'];
		$labname = strtoupper($_SESSION['ProjectKeys'][$projectkey]['Account']);
		$expname = $_SESSION['ProjectKeys'][$projectkey]['Project'];
	} else 
	{
		echo 'Error';
	}
	if(isset($params['Query'])) 
	{
		$abstrKeys = explode(',', $params['Query']);
		$json_str = file_get_contents(ABS_ROOT."/data/{$labname}/".urlencode($expname)."/".urlencode($expname)."_Genes_HTML.json");
		$json_data = json_decode($json_str, true);

		$err = json_last_error();
		if($err != 0) 
		{
			var_dump($err);
		} else 
		{
			$returnData = [];
			$arrIndex = 0;
			$resultIndexes = [];
			$arrLen = count($json_data);
			$matched = false;
			$filter = count($abstrKeys);
			if($filter<3)
			{
				$filter = "Gene_ID";
				for($i=0;$i<$arrLen;$i++) 
				{
					if($json_data[$i][$filter] == $abstrKeys[0] 
					&& $json_data[$i]["Concept"] == $abstrKeys[1]) 
					{
						array_push($resultIndexes, $i);
						$matched = true;
					}
				}
			} else if($filter==3) 
			{
				$filter = "Concept_ID";
				for($i=0;$i<$arrLen;$i++) 
				{
					if($json_data[$i][$filter] == $abstrKeys[0] 
					&& $json_data[$i]["Concept"] == $abstrKeys[1] 
					|| $json_data[$i][$filter] == $abstrKeys[1] 
					&& $json_data[$i]["Concept"] == $abstrKeys[0]) 
					{
						array_push($resultIndexes, $i);
						$matched = true;
					}
				}
			}
			if($matched==true)
			{
				$resultLen = count($resultIndexes);
				for($i=0;$i<$resultLen;$i++)
				{
					$pmid[$arrIndex] = array("PMID"=>$json_data[$resultIndexes[$i]]["PMID"]);
					$arrIndex++;
				}
				$infoArray = [];
				$iCount = count($pmid);
				for($i=0;$i<$iCount;$i++) 
				{
					array_push($infoArray, $pmid[$i]['PMID']);
				}
				$returnData['Publications'] = $infoArray;
				$returnData['GeneTerms'] = getSynonyms(array(str_ireplace(['(e)','(p)'], '', $params['Terms']['a'])));
				$returnData['ConceptTerms'] = getSynonyms(array(str_ireplace(['(e)','(p)'], '', $params['Terms']['b'])));
				$returnData['AllConcepts'] = getSynonyms(str_ireplace(['(e)', '(p)'], '', $params['Terms']['all']));
				if(isset($params['Terms']['theme'])) 
				{
					$returnData['ThemeTerms'] = getSynonyms(str_ireplace(['(e)', '(p)'], '', $params['Terms']['theme']));
				}
				print_r(json_encode($returnData));
			} else 
			{
				echo 'No Matches Found...';
			}
		}
	} else 
	{
		echo 'Error';
	}
}

/* Deprecated Cgi-Bin Api */
function multiSynonymsApiQuery($arr) {
	$request_scheme = (isset($_SERVER['REQUEST_SCHEME'])) ? $_SERVER['REQUEST_SCHEME'] . '://' : 'http://';
	$baseURL = !strpos(WEBROOT, 'localhost') ? WEBROOT : $request_scheme.'at-1.wucon.wustl.edu/';
	$urls = [];
	$finalarray = $arr;
	while(count($arr)>0) {

		$url = "{$baseURL}cgi-bin/GetArticle?synonyms=".urlencode(array_shift($arr));

		while(iconv_strlen($url)<100 && count($arr)>0) {

			$url = $url.'+'.urlencode(array_shift($arr));

		}

		array_push($urls, $url);
	}

	foreach($urls as $url) {
		$response = file_get_contents($url);
		$terms = json_decode($response, true);
		foreach($terms as $term){
			$finalarray = array_values(array_unique(array_merge($term['Synonyms'], $finalarray)));
		}
	}
	return $finalarray;
}
// Notes for pcmm api query
/*
	Query
	$curl = curl_init();
	curl_setopt_array($curl, array(
	CURLOPT_URL => 'http://at-1.wucon.wustl.edu:8001/pcmmrpc',
	CURLOPT_RETURNTRANSFER => true,
	CURLOPT_ENCODING => '',
	CURLOPT_MAXREDIRS => 10,
	CURLOPT_TIMEOUT => 0,
	CURLOPT_FOLLOWLOCATION => true,
	CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
	CURLOPT_CUSTOMREQUEST => 'POST',
	CURLOPT_POSTFIELDS =>'{"jsonrpc":"2.0","method":"GetEntitySynonyms","id":"101","params":["psa","pparg"]}',
	CURLOPT_HTTPHEADER => array(
		'Content-Type: application/json'
	),
	));
	$response = curl_exec($curl);
	curl_close($curl);
	Result
	{
		"jsonrpc": "2.0",
		"id": "101",
		"result": [
		{
			"Query" : "psa",
			"ApprovedName" : "klk3",
			"Synonyms" : [
				"psa",
				"gammaseminoprotein",
				"gamma-seminoprotein",
				"seminin",
				"kallikrein3",
				"kallikrein-3",
				"semenogelase"
			]
		},
		{
			"Query" : "pparg",
			"ApprovedName" : "pparg",
			"Synonyms" : [
				"pparg1",
				"pparg2",
				"nr1c3",
				"ppargamma",
				"ppar-gamma"
			]
		}
	]
	}
*/
//End
?>