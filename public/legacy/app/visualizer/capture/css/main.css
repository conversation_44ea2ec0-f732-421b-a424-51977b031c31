/*SVG Capture Stylesheet*/
body {
	margin: 0;
	padding: 0;
	overflow: hidden;
}
.draggable-element {
    user-select: none;
    cursor: move;
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;
/*    .thumbnails-list{
        cursor: pointer;
    }	*/
}
.draggable-element:active {
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
}
button#download {
	position: absolute;
	top: 25px;
	right: 25px;
}
div.text-input {
    top: 0;
    position: absolute;
    text-align: center;
    width: 100%;
    height: 100%;
    display: inline-flex;
}
input.text-input {
    margin: 5px;
}
button.text-input {
    margin: 5px;
    padding-left: 15px;
    padding-right: 15px;
}
div.text-input-wrap {
    margin: 15% auto auto auto;
    padding: 25px;
    color: #ffffff;
    background: rgba(0,0,0,0.75);
}
