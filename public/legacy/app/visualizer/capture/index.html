<!DOCTYPE html>
<html>
<head>
    <link rel="icon" href="../../../app/assets/images/sphere.ico" type="image/ico" sizes="16x16">
    <link rel="stylesheet" href="css/main.css">
    <title>Compbio Capture - Editing Tool</title>
    <style>
    #dpi{
        height: 1in;
        width: 1in;
        position: absolute;
        left: -100%;
        top: -100%;    
    }
    </style>
    <script src="../../../libs/jquery/jquery-3.4.1.min.js"></script>
</head>
<body>
    <button id="download">Download</button>
    <div id="content">
        <div id='dpi'></div>
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="svg">
            <style type="text/css">
            text.sphere-text {
                 --webkit-text-stroke: 1px #000000;
            }
            text.sphere-text>tspan {
                white-space: nowrap;
            }
            </style>
        </svg>
    </div>
    <!-- Dependencies -->
    <script src="../../../libs/snap_svg/snap.svg-min.js"></script>
    <!-- Functional -->
    <script src="js/main.js"></script>
    <script src="js/edit.js"></script>
    <!-- <script src="js/line.js"></script> -->
    <script>
        function getDPI(){
            return $('#dpi').height();
        }
        window.onload = () => {
            console.log(getDPI());
            console.log(screen.width + "," + screen.height);
        }
    </script>
</body>
</html>