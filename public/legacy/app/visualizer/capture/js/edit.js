"use strict";
function attachDblClickHandler() {
	return new Promise( ( resolve ) => {
		const labelElements = Array.from( document.getElementsByClassName('sphere-label') );
		labelElements.forEach( function( element ) {
			element.addEventListener( 'dblclick', function(e) {
				openTextEdit( element );
			});
		});
		resolve( true );
	});
}

function openTextEdit( element ) {
	const inputs = createTextInput();
	inputs[0].addEventListener( 'keyup', function(e) {
		if( e.keyCode === 13 ) {
			event.preventDefault();
			inputs[1].click();
		} else if( event.keyCode === 27 ) {
			event.preventDefault();
			inputs[2].click();
		}
	});
	inputs[1].addEventListener( 'click', function(e) {
		Snap.select(`#${CSS.escape(element.id)}`).attr({ text: inputs[0].value });
		removeTextInput();
	});
	inputs[2].addEventListener( 'click', function(e) {
		removeTextInput();
	});
}

function createTextInput() {
	const div1 = document.createElement('DIV');
	div1.classList.add('text-input');
	div1.id = 'text-input-parent';
	const div2 = document.createElement('DIV');
	div2.classList.add('text-input-wrap');
	const input = document.createElement('INPUT');
	input.type = 'text';
	input.id = 'new-text';
	input.classList.add('text-input');
	const button1 = document.createElement('BUTTON');
	button1.id = 'apply-btn';
	button1.innerText = 'Apply';
	button1.classList.add('text-input');
	const button2 = document.createElement('BUTTON');
	button2.id = 'cancel-btn';
	button2.innerText = 'Abort';
	button2.classList.add('text-input');	
	div2.appendChild(input);
	div2.appendChild(button1);
	div2.appendChild(button2);
	div1.appendChild(div2);
	document.body.appendChild(div1);
	input.focus();
	return [ input, button1, button2 ];
}

function removeTextInput() {
	const parent = document.getElementById('text-input-parent');
	while( parent.firstChild ) {
		parent.removeChild( parent.firstChild );
	}
	document.body.removeChild( parent );
}