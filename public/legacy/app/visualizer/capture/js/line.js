"use strict";
console.log('Line.JS Loading')
function loadLineTools( svg ) {
    return new Promise( ( resolve ) => {
    	console.log('LoadLineTools...loading')
        App.lineTools = {};
        App.lineTools.startPosition = null;
        App.lineTools.endPosition = null;
        App.lineTools.lineArray = [];
        App.lineTools.lineIndex = 0;
        App.lineTools.composing = false;
        svg.addEventListener( 'mousedown', ( event ) => {
            if ( App.lineTools.composing ) {
                return;
            } else if ( event.ctrlKey ) {
                const rect = svg.getBoundingClientRect();
                const x = ( event.clientX - rect.left ) + App.coordinateOffset.x;
                const y = ( event.clientY - rect.top ) + App.coordinateOffset.y;
                App.lineTools.composing = true;
                App.lineTools.startPosition = new Vector2D( x, y );
                constructLine( App.lineTools.startPosition, App.lineTools.startPosition, App.lineTools.lineIndex, false );
            }
        } );
        svg.addEventListener( 'mouseup', ( event ) => {
        	if( !App.lineTools.composing ) {
        		return;
        	} else {
        		const rect = svg.getBoundingClientRect();
                const x = ( event.clientX - rect.left ) + App.coordinateOffset.x;
                const y = ( event.clientY - rect.top ) + App.coordinateOffset.y;
        		App.lineTools.composing = false;
        		App.lineTools.endPosition = new Vector2D( x, y );
        		constructLine( App.lineTools.startPosition, App.lineTools.endPosition, App.lineTools.lineIndex, true );
        		App.lineTools.lineIndex++;
        	}
        } );
        svg.addEventListener( 'mousemove', ( event ) => {
        	if( !App.lineTools.composing ) {
        		return;
        	} else {
        		const rect = svg.getBoundingClientRect();
                const x = ( event.clientX - rect.left ) + App.coordinateOffset.x;
                const y = ( event.clientY - rect.top ) + App.coordinateOffset.y;
                App.lineTools.endPosition = new Vector2D( x, y );
                constructLine( App.lineTools.startPosition, App.lineTools.endPosition, App.lineTools.lineIndex, true );
        	}
        } );
        resolve( true );
    } );
}

function constructLine( start, end, index, drop ) {
    //Declare a new Line in SVG
    let line = document.createElementNS( 'http://www.w3.org/2000/svg', 'line' );
    line.setAttribute( 'stroke', 'black' );
    line.setAttribute( 'stroke-width', 2 );
    line.setAttribute( 'class', 'svg-line' );

    let id = `line${index}`;

    //If we are moving and existent line, set "line" to the current line, else, give to the new line the id attribute.
    if ( drop ) { line = document.querySelector( '#' + id ) } else { line.setAttribute( 'id', id ) };
    // If we are creating a new line, define its start position
    if ( !drop ) line.setAttribute( 'x1', start.x );
    if ( !drop ) line.setAttribute( 'y1', start.y );
    //Define its end position
    line.setAttribute( 'x2', end.x );
    line.setAttribute( 'y2', end.y );
	svg.insertBefore( line, App.draggableElements[0] );
	App.lineTools.lineArray.push( line );
    // svg.prepend( line );
}

class Vector2D {
    constructor( x, y ) {
        this.x = x;
        this.y = y;
    }
}