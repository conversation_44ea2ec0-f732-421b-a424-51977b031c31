"use strict";
/*Svg Capture Main Script*/
document.addEventListener( 'DOMContentLoaded', init );
const App = {};
let s = Snap( '#svg' )
.attr({ 
    margin: 0, 
    padding: 0
});
console.log('Loading...')

async function init() {
    if ( sessionStorage.getItem( 'svgData' ) !== null || localStorage.getItem( 'svgData' ) !== null ) {
        /*Download event listener*/
        document.getElementById( 'download' ).addEventListener( 'click', function( e ) {
            let svg = document.getElementById( 'svg' );
            let clone = svg.cloneNode( true );
            let svgDocType = document.implementation.createDocumentType( 'svg', "-//W3C//DTD SVG 1.1//EN", "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" );
            let svgDoc = document.implementation.createDocument( 'http://www.w3.org/2000/svg', 'svg', svgDocType );
            svgDoc.replaceChild( clone, svgDoc.documentElement );
            let svgData = ( new XMLSerializer() ).serializeToString( svgDoc );
            // httpRequest( svgData.replace( /></g, '>\n\r<' ) );
            httpRequest( svgData );
        } );
        App.svgData = (sessionStorage.getItem( 'svgData' ) !== null) ? JSON.parse(sessionStorage.getItem( 'svgData' )) : JSON.parse( localStorage.getItem( 'svgData' ) );
        let svgElement = document.getElementById( 'svg' );
        svgElement.setAttribute( 'width', window.innerWidth - 3 );
        svgElement.setAttribute( 'height', window.innerHeight - 3 );
        svgElement.setAttribute( 'viewBox', `-${window.innerWidth / 2} -${window.innerHeight / 2} ${window.innerWidth} ${window.innerHeight}` );
        svgElement.style.width = window.innerWidth;
        svgElement.style.height = window.innerHeight;
        svgElement.style.fontFamily = "Arial,Helvetica,sans-serif";
        svgElement = null;
        let boxViewActive = App.svgData.boxViewActive;
        //Set array for draggable elements
        App.draggableElements = [];
        //Coordinate (viewBox) Offset
        App.coordinateOffset = {
            x: -Math.abs(window.innerWidth / 2),
            y: -Math.abs(window.innerHeight / 2)
        };
        // console.log( App.svgData.themeLabelTexts );
        //Set body background
        if ( App.svgData.styles.bodyBackground.includes('radial-gradient(farthest-side at 80% 45%, rgb(66, 8, 8) 40%, rgb(19, 19, 19))') ) {
            console.log("1")
            let backgroundGradient = s.gradient( "r(0.8,0.5,0.80)#420808-#131313" );
            let backgroundRect = s.rect( `-${window.innerWidth / 2}`, `-${window.innerHeight / 2}`, '100%', '100%' ).attr( { id: 'backgroundRect', fill: backgroundGradient } );
        } else if ( App.svgData.styles.bodyBackground.includes( 'images/' ) === true ) {
            console.log("2")
            let linkArray = App.svgData.styles.bodyBackgroundImage.split('/');
            // console.log(linkArray)
            let bgImage = linkArray[linkArray.length - 1].slice(0, -2);
            // console.log(bgImage)
            let image64 = await getBase64Image( `${window.location.href}images/${bgImage}` );
            let backgroundImage = s.image( image64, `-${window.innerWidth / 2}`, `-${window.innerHeight / 2}`, window.innerWidth, window.innerHeight ).attr( { id: 'background-image' } );
        } else {
            console.log("3")
            let backgroundRect = s.rect( `-${window.innerWidth / 2}`, `-${window.innerHeight / 2}`, window.innerWidth, window.innerHeight ).attr( { id: 'backgroundRect', fill: App.svgData.styles.bodyBackgroundColor } );
        }
        // debugger;
        const svgSpheresLoaded = await loadSvgGroups( App.svgData.groupElements );
        console.log(svgSpheresLoaded);
        // const greatestRadialValue = await returnGreatestRadialValue( svgSpheresLoaded );
        const themeInnerTextCreated = (boxViewActive) ? await createThemeBoxEl( App.svgData.boxElements,App.svgData.boxBCR ) : await createThemeInnerText( App.svgData.themeSphereTexts );
        const themeLabels = await createThemeLabels( (boxViewActive) ? Array.from(document.querySelectorAll('.theme-boxes')) : svgSpheresLoaded,App.svgData.themeLabelTexts.reverse() );
        console.log(themeLabels);
        // const labelNames = await createThemeTextLabels( svgSpheresLoaded,App.svgData.themeLabelTexts.reverse() );
        // const themeBackgroundsCreated = await createLabelBackground( labelNames );
        
        // svgSpheresLoaded = null;
        // themeInnerTextCreated = null;
        // labelNames = null;
        // themeBackgroundsCreated = null;

        const dragHandlerBound = await attachDragHandler();
        const dblClickHandlerBound = await attachDblClickHandler();
        //Load line drawing
        // const lineToolsLoaded = await loadLineTools( App.svgElement );

        // Object.keys(App).forEach(key => delete App[key]);
        // s = null;

        // Final SVG cleanup
        // Remove Polygon edges
        let grps = Array.from(document.querySelectorAll("[data-geom-type=sphere]"));
        grps.forEach((g) => {
            let paths = Array.from(g.children);
            paths.forEach((p) => {
                let f = p.style.fill;
                if(f !== 'none') {
                    p.style.stroke = f;
                }
            });
            let targetid = `inner-text=${g.id}`;
            let txt = document.getElementById(targetid);
            let tsp = Array.from(txt.children);
            tsp.forEach((t)=>{ 
                if(t.getBBox().width > g.getBBox().width) {
                    let tfs = t.parentElement.style.fontSize.replace('px','');
                    while ( t.getBBox().width > g.getBBox().width + 1 ) {
                        tfs = tfs - 1;
                        t.style.fontSize = `${tfs}px`;
                    }
                    // t.setAttribute('textLength',g.getBBox().width - 5);
                    // t.setAttribute('lengthAdjust','spacingAndGlyphs');
                }
            });
            g.appendChild(document.getElementById(targetid));
        });
        grps = Array.from(document.querySelectorAll("[data-geom-type=tube]"));
        grps.forEach((g) => {
            let paths = Array.from(g.children);
            paths.forEach((p) => {
                let f = p.style.fill;
                if(f !== 'none') {
                    p.style.stroke = f;
                }
            });
        });
        // Fix Multi-Line Text Tspan Group Bug
        let labs = Array.from(document.querySelectorAll('.sphere-label'));
        labs.forEach((l) => {
            if(l.innerHTML !== '') {
                let tid = l.id.replace('label-', '');
                let tg = document.getElementById(`label-grp-${tid}`);
                if(tg !== null) {
                    let tsps = tg.querySelectorAll('TSPAN');
                    tsps.forEach((tsp) => {
                        l.appendChild(tsp);
                    });
                    l.parentElement.appendChild(l);
                }
            }
        });
    } else {
        console.log('!Session Storage Error!\nNo SVG Data available...')
        document.getElementById( 'content' ).innerText = 'Error!!! You have reached this page in error or there has been an error in the application.  Try reloading the page or contacting support to help with the problem.';
    }
}

/* Updated V3 */
function createThemeBoxEl( array,bcrArray ) {
    return new Promise((resolve) => {
        console.log(bcrArray)
        let bb,boxEls = [];
        let cc = document.createElement('DIV');
        cc.id = 'clone-container';
        document.body.appendChild(cc);
        array.forEach(function(el,i) {
            cc.innerHTML = el;
            let elNode = cc.children[0];
            let grp = s.g()
            .attr({
                id: elNode.id,
                class: 'theme-boxes draggable-element'
            })
            let org = cc.children[0];
            let orgTabCont = org.querySelector('.themebox-table-cont');
            bb = bcrArray[i].orgTabCont;
            console.log(bb)
            let tabContRect = s.rect(bb.x-(window.innerWidth/2), bb.y-(window.innerHeight/2), bb.width, bb.height, 4)
            .attr({
                fill: orgTabCont.style.backgroundColor,
                stroke: '#ffffff',
                class: 'themebox-table-cont'
            });
            console.log(tabContRect);
            if(typeof bcrArray[i].custBorder != 'undefined') {
                let bColor = bcrArray[i].custBorder.replace('10px solid ','');
                tabContRect.attr({
                    stroke: bColor,
                    strokeWidth: 10,
                    'paint-order': 'stroke'
                });
            }
            grp.add(tabContRect);
            let h1 = orgTabCont.querySelector('th.themebox-con');
            bb = bcrArray[i].h1;
            let h1Text = s.text(bb.x-(window.innerWidth/2), bb.bottom - 4-(window.innerHeight/2), "Concepts")
            .attr({
                fill: '#00ffff',
                fontWeight: 'bold',
                class: 'th-themebox-con',
                // dominantBaseline: 'text-after-edge'
            });
            let h1BB = s.rect(bb.x-(window.innerWidth/2), bb.bottom -(window.innerHeight/2), bb.width, 1)
            .attr({
                fill: '#00ffff'
            });
            grp.add(h1Text,h1BB);
            let h2 = orgTabCont.querySelector('th.themebox-ent');
            bb = bcrArray[i].h2
            let h2Text = s.text(bb.x-(window.innerWidth/2), bb.bottom - 4 -(window.innerHeight/2), "Entities")
            .attr({
                fill: '#00ffff',
                fontWeight: 'bold',
                class: 'th-themebox-ent',
                // dominantBaseline: 'text-after-edge'
            });
            let h2BB = s.rect(bb.x-(window.innerWidth/2), bb.bottom -(window.innerHeight/2), bb.width, 1)
            .attr({
                fill: '#00ffff'
            });
            grp.add(h2Text,h2BB);
            let conCol = orgTabCont.querySelectorAll('td.themebox-con');
            let tmpIndx = 0;
            for( let td of conCol ) {
                bb = bcrArray[i].conCol[tmpIndx];
                let tdText = s.text(bb.x-(window.innerWidth/2), bb.bottom-(window.innerHeight/2), td.innerText)
                .attr({
                    fill: '#ffffff',
                    class: 'td-themebox-con',
                    // dominantBaseline: 'central'
                });
                tmpIndx++;
                grp.add(tdText);
            }
            tmpIndx = 0;
            let entCol = orgTabCont.querySelectorAll('td.themebox-ent');
            for( let td of entCol ) {
                bb = bcrArray[i].entCol[tmpIndx];
                let tdText = s.text(bb.x-(window.innerWidth/2), bb.bottom-(window.innerHeight/2), td.innerText)
                .attr({
                    fill: '#ffffff',
                    class: 'td-themebox-ent'
                });
                tmpIndx++;
                grp.add(tdText);
            }
            cc.innerHTML = '';
            console.log(grp);
            let fs = 13;
            // Concepts Header
            while( tabContRect.getBBox().y > h1Text.getBBox().y && fs > 1
                || h1Text.getBBox().x2 > h2Text.getBBox().x && fs > 1 ) {
                    h1Text.node.style.fontSize = `${fs}px`;
                    fs = fs - 1;
            }
            // Entity Header
            fs = 13;
            while( tabContRect.getBBox().y > h2Text.getBBox().y && fs > 1
                || h2Text.getBBox().x2 > tabContRect.getBBox().x2 && fs > 1 ) {
                    h2Text.node.style.fontSize = `${fs}px`;
                    fs = fs - 1;
            }
            // Concepts Column
            let cons = grp.selectAll('.td-themebox-con');
            let idx = 0;
            for(let c of cons.items) {
                fs = 13;
                if( idx === 0 ) {
                    while( c.getBBox().x2 > h2Text.getBBox().x && fs > 1
                        || h1BB.getBBox().y2 > c.getBBox().y && fs > 1 ) {
                            c.node.style.fontSize = `${fs}px`;
                            fs = fs - 1;
                    }
                    c.node.style.fontSize = `${fs + 1}px`;
                } else {
                    while( c.getBBox().x2 > h2Text.getBBox().x && fs > 1
                        || cons[idx - 1].getBBox().y2 > c.getBBox().y && fs > 1 ) {
                            c.node.style.fontSize = `${fs}px`;
                            fs = fs - 1;
                    }
                    // c.node.style.fontSize = `${fs + 2}px`;
                }
                idx++;
            }
            // Entities Column
            let ents = grp.selectAll('.td-themebox-ent');
            idx = 0;
            for(let c of ents.items) {
                fs = 13;
                if( idx === 0 ) {
                    while( c.getBBox().x2 > tabContRect.getBBox().x2 && fs > 1
                        || h1BB.getBBox().y2 > c.getBBox().y && fs > 1 ) {
                            c.node.style.fontSize = `${fs}px`;
                            fs = fs - 1;
                    }
                    c.node.style.fontSize = `${fs + 1}px`;
                } else {
                    while( c.getBBox().x2 > tabContRect.getBBox().x2 && fs > 1
                        || ents[idx - 1].getBBox().y2 > c.getBBox().y && fs > 1 ) {
                            c.node.style.fontSize = `${fs}px`;
                            fs = fs - 1;
                    }
                    // c.node.style.fontSize = `${fs + 2}px`;
                }
                idx++;
            }
        });
        document.body.removeChild(cc);
        resolve(true);
    });
}

function createThemeLabels( array,arrayTexts ) {
    return new Promise((resolve) => {
        let labelSvgs = [];
        let array3 = App.svgData.labelCoordinates.reverse();
        array.forEach( function( sphere,index ) {
            let isEmpty = Object.values( array3[ index ] ).every( x => x === 0 || x === null );
            if(!isEmpty) {
                let labelRect = s.rect(array3[index].x-(window.innerWidth/2),array3[index].y-(window.innerHeight/2),array3[index].width,array3[index].height,4)
                .attr({
                    fill: App.svgData.styles.labelBackground,
                    stroke: '#ffffff',
                    strokeWidth: 1,
                    id: `label-rect-${sphere.getAttribute('id')}`,
                    class: ''
                });
                let bBox = labelRect.getBBox();
                console.log(bBox);
                let text = Object.values( arrayTexts[ index ] )[0];
                text = (text.includes("\n")) ? text.split("\n") : text;
                let fs = 24;
                let tLength,tOffsets,labelText;
                if(Array.isArray(text)) {
                    console.log('IS ARRAY')
                    tLength = text.length;
                    console.log(text)
                    console.log(text.length)
                    tOffsets = ( tLength > 2 ) ? ['-1.2em','1.1em','1.1em'] : ['-0.7em','1em'];
                    if(tLength == 4) {
                        tOffsets =['-1.5em','1.1em','1.1em'];
                    } else if(tLength > 4) {
                        tOffsets =['-1.75em','1.1em','1.1em'];
                    }
                    labelText = s.text(bBox.cx,bBox.cy,text)
                    .attr({
                            fill: App.svgData.styles.labelColor,
                            // fontFamily: App.svgData.styles.labelFont,
                            fontFamily: "Arial,Helvetica,sans-serif",
                            fontSize: fs + 'px',
                            dominantBaseline: 'middle',
                            textAnchor: 'middle',
                            id: `label-${sphere.getAttribute('id')}`,
                            class: 'sphere-label draggable-element',
                    })
                    .selectAll( "TSPAN" )
                    .forEach( function( tspan,i ) {
                        tspan.attr({
                            x: tspan.node.parentNode.attributes.x.nodeValue,
                            dy: tOffsets[i] ?? '1.1em'
                        });
                    })
                } else {
                    console.log('IS NOT ARRAY')
                    labelText = s.text(bBox.cx,bBox.cy,text)
                    .attr({
                            fill: App.svgData.styles.labelColor,
                            // fontFamily: App.svgData.styles.labelFont,
                            fontFamily: "Arial,Helvetica,sans-serif",
                            fontSize: fs + 'px',
                            dominantBaseline: 'middle',
                            textAnchor: 'middle',
                            id: `label-${sphere.getAttribute('id')}`,
                            class: 'sphere-label draggable-element',
                    });
                }
                while((labelText.getBBox().width + 16) > labelRect.getBBox().width) {
                    fs = fs - 1;
                    labelText.attr({
                       fontSize: fs + 'px' 
                    });
                
                }
                labelSvgs.push( `label-${sphere.getAttribute('id')}` );
                console.log(labelRect)
                console.log(labelText)
                let grp = s.g()
                .attr({
                    id: `label-grp-${sphere.getAttribute('id')}`,
                    class: 'label-grp draggable-element'
                });
                grp.add(labelRect);
                console.log(grp);
            } else {
                labelSvgs.push( null );
            }
        });
        resolve(labelSvgs);
    });
}

function createLabelBackground( array ) {
    return new Promise(( resolve ) => {
        let array3 = App.svgData.labelCoordinates;
        // let ctx = document.getElementById("svg");
        array.forEach(( labelName,index ) => {
                // console.log( labelName );
                // console.log( array3[ index ]);
                // debugger;                   
            let isEmpty = Object.values( array3[ index ] ).every( x => x === 0 || x === null );
            if( !isEmpty ) {
                let targetWidth = array3[index].width,
                targetHeight = array3[index].height,
                textObj = s.select( `#${CSS.escape( labelName )}` );
                // console.log(textObj);
                let rect = s.rect( array3[index].x - ( window.innerWidth / 2 ),array3[index].y - ( window.innerHeight / 2 ),array3[index].width,array3[index].height )
                    .attr({
                        fill: App.svgData.styles.labelBackground,
                        stroke: 'none'
                    });
                // Resize the label text to fit the label rect
                if( textObj.node.childElementCount > 0 ) {

                    // if( textObj.getBBox().w < targetWidth - 5 || textObj.getBBox().h < targetHeight ) {
                        // console.log( 'resize up!' );
                        let fSize = parseInt( textObj.attr( 'fontSize' ));
                        while ( textObj.getBBox().w < targetWidth - 5 || textObj.getBBox().h < targetHeight - 5 ) {
                            fSize = fSize + 0.1;
                            textObj.attr({ 
                                fontSize: `${fSize}px`,
                                x: array3[index].x - ( window.innerWidth / 2 ) + ( targetWidth / 2 ),
                                y: array3[index].y - ( window.innerHeight / 2 ) + targetHeight + 5
                            });
                        }
                    // }
                    // if( textObj.getBBox().w > targetWidth - 5 || textObj.getBBox().h > targetHeight ) {
                        // console.log( 'resize down!' );
                        // let fSize = parseInt( textObj.attr( 'fontSize' ));
                        while ( textObj.getBBox().w > targetWidth - 5 || textObj.getBBox().h > targetHeight - 5 ) {
                            fSize = fSize - 0.1;
                            textObj.attr({ 
                                fontSize: `${fSize}px`,
                                x: array3[index].x - ( window.innerWidth / 2 ) + ( targetWidth / 2 ),
                                y: array3[index].y - ( window.innerHeight / 2 ) + targetHeight + 5
                            });
                        }
                    // }
                    let tspans = Array.from( textObj.node.children );
                    tspans.forEach(( span ) => {
                        span.setAttribute( 'x',textObj.attr( 'x' ));
                    });
                } else {
                    // if( textObj.getBBox().w < targetWidth - 5 || textObj.getBBox().h < targetHeight ) {
                        // console.log( 'resize up!' );
                        let fSize = parseInt( textObj.attr( 'fontSize' ));
                        while ( textObj.getBBox().w < targetWidth - 5 || textObj.getBBox().h < targetHeight ) {
                            fSize = fSize + 0.1;
                            textObj.attr({ 
                                fontSize: `${fSize}px`,
                                x: array3[index].x - ( window.innerWidth / 2 ) + 2.5,
                                y: array3[index].y - ( window.innerHeight / 2 ) + targetHeight - 5
                            });
                        }
                    // }
                    // if( textObj.getBBox().w > targetWidth - 5 || textObj.getBBox().h > targetHeight ) {
                        // console.log( 'resize down!' );
                        // let fSize = parseInt( textObj.attr( 'fontSize' ));
                        while ( textObj.getBBox().w > targetWidth - 5 || textObj.getBBox().h > targetHeight ) {
                            fSize = fSize - 0.1;
                            textObj.attr({ 
                                fontSize: `${fSize}px`,
                                x: array3[index].x - ( window.innerWidth / 2 ) + 2.5,
                                y: array3[index].y - ( window.innerHeight / 2 ) + targetHeight - 5
                            });
                        }
                    // }                
                }
                let g = s.group( rect,textObj )
                    .attr({
                        class: 'label-group draggable-element',
                        id: `g-label-${labelName}`
                    });
            }
        });
        resolve( true );
    });
}

// font-size algo 
// n = normalized distance ( 500 )
// d = distance from label to camera
// 1.5em = normalized font-sized 
// ( n/d ) * 1.5em

function createThemeTextLabels( array,arrayTexts ) {
    return new Promise( ( resolve ) => {
        let labelSvgs = [];
        // let array2 = App.svgData.labelStyles.reverse();
        let array3 = App.svgData.labelCoordinates.reverse();
        array.forEach( function( sphere,index ) {
                // console.log( sphere );
                // console.log( array3[ index ]);
                // debugger;            
            let isEmpty = Object.values( array3[ index ] ).every( x => x === 0 || x === null );
            if( !isEmpty ) {
                // let font = App.svgData.styles.labelFont.slice( App.svgData.styles.labelFont.indexOf( 'px' ) + 2 ).trim();
                let font = "Arial,Helvetica,sans-serif";
                // console.log( array3[index ]);
                let sphX = array3[ index ].x - ( window.innerWidth / 2 ) + 5;
                let sphY = array3[ index ].y - ( window.innerHeight / 2 ) + ( array3[ index ].height );
                let textVal = Object.values( arrayTexts[ index ] )[0].replaceAll( '*','');
                let labelText = ( textVal.includes( '\n' )) ? textVal.split( '\n' ) : textVal;
                let label = ( Array.isArray( labelText )) ? s.text(
                    sphX,
                    sphY,
                    labelText.reverse(),
                )
                .attr({ 
                    fill: App.svgData.styles.labelColor, 
                    fontFamily: font, 
                    id: `label-${sphere.getAttribute('name')}`, 
                    class: 'sphere-label',
                    fontSize: 12 + 'px', 
                    // fontSize: '0.9em',
                    // fontSize: ( 500 / array2[ index ] ) * 0.7 + 'em',
                    // fontSize: '1em',
                    textAnchor: 'middle',
                    // dominantBaseline: 'ideographic',
                })
                .selectAll( "TSPAN" )
                .forEach( function( tspan,i ) {
                    tspan.attr({
                        dy: "-1em"
                    });
                }) : s.text( sphX,sphY, labelText )
                .attr({ 
                    fill: App.svgData.styles.labelColor, 
                    fontFamily: font, 
                    id: `label-${sphere.getAttribute('name')}`, 
                    class: 'sphere-label',
                    fontSize: 24 + 'px'
                    // fontSize: ( 500 / array2[ index ] ) * 1.5 + 'em'
                    // fontSize: '0.5em'
                });
                let labelBB = label.getBBox();
                if( label.type === 'set') {
                    let labelBB2 = label.getBBox();
                    label
                    .items
                    .forEach( function( tspan,i ) {
                        tspan.attr({
                            x: tspan.node.parentNode.attributes.x.nodeValue
                        });
                    });
                }

                labelSvgs.push( `label-${sphere.getAttribute('name')}` );
            } else {
                labelSvgs.push( null );
            }
        });
        resolve( labelSvgs );
    } );
}

function createThemeInnerText( array ) {
    return new Promise( ( resolve ) => {
        array.forEach( function( item ) {
            let id = Object.keys( item );
            let list = item[ id ];
            let listLen = list.length;
            let element = s.select( `#${CSS.escape( id )}` );
            let coords = element.getBBox();
            // let font = App.svgData.styles.sphereFont.slice( App.svgData.styles.sphereFont.indexOf( 'px' ) + 2 ).trim();
            // if ( font.includes( 'Arial Black' ) === true ) {
            //     font = "Arial Black";
            // }
            let font = "Arial,Helvetica,sans-serif";
            let attributes = {
                fontFamily: "Arial,Helvetica,sans-serif",
                fill: 'white',
                id: `inner-text=${id}`,
                class: 'sphere-text',
                textAnchor: 'middle',
                // dominantBaseline: 'middle',
                width: coords.w,
                height: coords.h,
                // stroke: '#000',
                // strokeWidth: '0.25px',
                // strokeMiterlimit: '10',
                fontSize: '32px'
                // letterSpacing: -0.5
            };
            /* 
                stroke: #000;
                stroke-miterlimit: 10;
                stroke-width: 0.25px;
            */
            if( list.includes('') === true ) {
                list = list.map((str) => {
                    return ( str === "" ) ? "\u00A0" : str;
                });
            }
            let textItem = s.text( coords.cx, coords.cy, list ).attr( attributes );
            textItem.selectAll( "tspan:nth-child(n+2)" ).attr( {
                dy: "1em",
                x: coords.cx,
            } );
            //Adjust font-size
            if ( textItem.getBBox().w > coords.w + 40 || textItem.getBBox().h > coords.h ) {
                let fSize = 32;
                while ( textItem.getBBox().w > coords.w + 40 || textItem.getBBox().h > coords.h ) {
                    fSize = fSize - 1;
                    textItem.attr( { fontSize: `${fSize}px` } );
                    // if ( fSize == 11 ) {
                    //     textItem.attr( { fontFamily: 'sans-serif', letterSpacing: '-1em' } );
                    // }
                }
            }
            // let ts = Array.from(textItem.node.children());
            // ts.forEach((t) => {
            //     if(t.getBBox().w > coords.w) {
            //         t.setAttribute('textLength',coords.w);
            //     }
            // });
            console.log(textItem)
            // if ( textItem.getBBox().w > coords.w || textItem.getBBox().h > coords.h ) {
            //     let fSize = 32;
            //     while ( textItem.getBBox().w > coords.w || textItem.getBBox().h > coords.h ) {
            //         fSize = fSize - 1;
            //         textItem.attr( { fontSize: `${fSize}px` } );
            //         // if ( fSize == 11 ) {
            //         //     textItem.attr( { fontFamily: 'sans-serif', letterSpacing: '-1em' } );
            //         // }
            //     }
            // }
            //Center text element
            let fSize = ( Number.isNaN( parseFloat( textItem.node.style.getPropertyValue( 'font-size' ) ) ) === true ) ? 16 : parseFloat( textItem.node.style.getPropertyValue( 'font-size' ) );
            fSize = (fSize / 16) * 8;
            let yPos = textItem.attr( 'y' );
            let eC = textItem.children().length - 1.5;
            let finalY = yPos - ( fSize * eC );
            textItem.attr( { y: finalY } );
            // let dY = Number( parseFloat(textItem.attr('fontSize').replace(/em/g, '')));
        } );
        resolve( true );
    } );
}

// function createOuterSpheres( array, radVal ) {
//     return new Promise( ( resolve ) => {
//         let clearColor = Snap.getRGB( 'rgba(0,0,0,0)' );
//         let color = Snap.getRGB( 'rgba(0,0,0,0)' );
//         let rGradient = s.gradient( `r(0.5,0.5,0.9)${clearColor.toString()}-${color.toString}` );
//         array.forEach( function( sphere ) {
//             let id = sphere.id;
//             let coords = { x: s.select( `#${CSS.escape( id )}` ).getBBox().cx, y: s.select( `#${CSS.escape( id )}` ).getBBox().cy };
//             let c = s.circle( coords.x, coords.y, radVal - 1 ).attr( { fill: rGradient } ).prepend( s.children[ 2 ] );
//         } );
//     } );
// }

// function returnGreatestRadialValue( array ) {
//     return new Promise( ( resolve ) => {
//         let gValue = 0;
//         array.forEach( function( sphere ) {
//             let id = sphere.id;
//             gValue = ( s.select( `#${CSS.escape( id )}` ).getBBox().r1 > gValue ) ? s.select( `#${CSS.escape( id )}` ).getBBox().r1 : gValue;
//         } );
//         resolve( gValue );
//     } );
// }

function loadSvgGroups( array, isConceptMap = false ) {
    return new Promise( ( resolve ) => {
        let nodeArray = [];
        let spheres = [];
        array.reverse();
        array.forEach( function( string ) {
            if(string.includes('data-geom-type="sphere"')){
                spheres.push(string);
            } else {
                let groupNode = Snap.parse( string );
                s.append( groupNode );
                nodeArray.push( groupNode );
            }
        });
        spheres.forEach( function(string) {
            let groupNode = Snap.parse( string );
            s.append( groupNode );
            nodeArray.push( groupNode );
        });
        if( isConceptMap === true ) {
            resolve( nodeArray );
        } else {
            let sphereElements = [].slice.call( document.querySelectorAll( '[data-geom-type="sphere"' ) );
            resolve( sphereElements );
        }
    } );
}

/*Attach Drag Handler*/
function attachDragHandler() {
    return new Promise( ( resolve ) => {
        console.log( 'Attaching element drag handler...' )
        let gElements = [].slice.call( document.getElementsByClassName( 'draggable-element' ) );
        gElements.forEach( function( element ) {
            App.draggableElements.push( element );
            let snapSelected = s.select( `#${CSS.escape(element.id)}` );
            snapSelected.drag();
        } );
        resolve( true );
    } );
}

/*Serialize SVG/XML Data*/
function xmlToString( xmlData ) {
    let xmlString;
    // IE
    if ( window.ActiveXObject ) {
        xmlString = xmlData.xml;
    }
    // Mozilla, Firefox, Opera, etc.
    else {
        xmlString = ( new XMLSerializer() ).serializeToString( xmlData );
    }
    return xmlString;
}

/*Send Svg Data*/
function httpRequest( data, type = 'c' ) {
    if ( type === 'c' || type === 'r' ) {
        let header = ( type === 'r' ) ? 'Remove=' : 'PostRequest=';
        let request = header + encodeURIComponent( data );
        console.log( request )
        let url = 'php/download.php';
        let xhr = new XMLHttpRequest();
        xhr.open( "POST", url, true );
        xhr.setRequestHeader( "Content-type", "application/x-www-form-urlencoded" );
        xhr.onreadystatechange = function() {
            if ( xhr.readyState == 4 && xhr.status == 200 ) {
                let response = decodeURIComponent( xhr.responseText );
                if ( response.includes( 'download' ) === true ) {
                    let file = response;
                    let url = window.location.href + file;
                    let linkElement = document.createElement( 'a' );
                    linkElement.href = url;
                    linkElement.addEventListener( 'click', function( e ) {
                        console.log( 'Download Started' )
                        let fArray = file.split( '/' );
                        let fileName = fArray[ 1 ];
                        /*Delete File*/
                        setTimeout( function() {
                            httpRequest( fileName, 'r' );
                            console.log( 'File Removal Requested' );
                            document.body.removeChild(linkElement);
                        }, 10000 )
                    } );
                    let downloadAttribute = document.createAttribute( 'download' );
                    linkElement.setAttributeNode( downloadAttribute );
                    document.body.appendChild( linkElement );
                    /*Download File*/
                    linkElement.click();

                } else {
                    console.log( response )
                }
            }
        }
        xhr.send( request );
    }
}

/*Convert Image to base64 for embedding*/
function getBase64Image( imgSrc ) {
    return new Promise( ( resolve ) => {
        let canvas = document.createElement( "canvas" );
        let ctx = canvas.getContext( "2d" );
        let img = new Image();
        img.src = imgSrc;
        img.onload = function() {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage( img, 0, 0 );
            let dataURL = canvas.toDataURL();
            resolve( dataURL );
            // resolve( dataURL.replace( /^data:image\/(png|jpg);base64,/, "" ) );
        };
    } );
}