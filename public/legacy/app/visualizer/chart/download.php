<?php

if(isset( $_POST['PostRequest'] ))
{
	$jsonData = json_decode($_POST['PostRequest']);
	$data = rawurldecode( $jsonData->svgData );
	$name = ( $jsonData->name == 'noname' ) ? uniqid() : $jsonData->name;
	$success = file_put_contents( "./download/{$name}.svg", $data, LOCK_EX );
	if( $success !== false )
	{
		echo "./download/{$name}.svg";
	} else
	{
		die( 'File Export Failure' );
	}
} else if( isset( $_POST['Remove'] ))
{
	$data = urldecode( $_POST['Remove'] );
	sleep( 3 );
	$success = unlink( "./download/{$data}" );
	if( $success !== false )
	{
		echo 'File Removed';
	} else
	{
		die( 'File Removal Failure!' );
	}
} else
{
	echo 'Else Statement';
}

?>