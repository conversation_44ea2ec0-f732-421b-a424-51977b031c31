// "use strict";
var graph,data,themeDataLength,bars,annotes;
// // Load the Visualization API and the corechart package.
google.charts.load('current', {'packages':['corechart']});
// // Set a callback to run when the Google Visualization API is loaded.
google.charts.setOnLoadCallback(drawThemeChart);
function drawThemeChart() {
	const themeData = JSON.parse(sessionStorage.getItem('graphData'));
   themeDataLength = themeData.length;
	const title = (sessionStorage.getItem('Project') !== null) ? sessionStorage.getItem('Project') : 'CompBio-Theme Graph Export';
// set inner height to 30 pixels per row
   let chartAreaHeight = themeData.length * 50;
   // add padding to outer height to accomodate title, axis labels, etc
   let chartHeight = chartAreaHeight + 200;   
	document.title = title;
   options = {
		'title': title,
		// 'width':window.innerWidth,
		'height': chartHeight,
		// 'width':'auto',
      'legend': { position: 'none' },
		// 'height': ( themeData.length * 100 ),
      'chartArea': { 
         'top': 100,
         'height': chartAreaHeight,
         'left':'20%'
      },
		'colors':['red'],
		'textStyle':{'bold':true},
		'hAxis':{
         'title': 'Normalized Enrichment Score',
         'titleTextStyle': {
            'bold':true,
            'italic':false,
            'fontSize':32
         },
         'textStyle':{
            // 'bold':true
         },
         'ticks': [ 0,0.5,1,1.5,2,2.5,3,3.5,4,4.5,5 ]
         // 'viewWindow':{'min':0,'max':Math.ceil(themeData[0][1] / 0.9)}
      },
		'vAxis':{
         'title': 'Themes',
         'titleTextStyle': {
            'bold':true,
            'italic':false,
            'fontSize':32
         },
         'textStyle':{
            fontSize:16,
            textAlign: "right"
         }
      },
      axisTitlesPosition: 'out',
      tooltip: {
         trigger: 'selection',
         textStyle: { 
            fontSize: 14,
            bold: true
         },
         // ignoreBounds: true
         // isHtml: true
         
      },
      selectionMode: 'multiple',
      aggregationTarget: 'none'
      // 'is3D':true,
		// 'series':{
		// 	0:{'color':'red'}
		// }
	}
	graph = new google.visualization.BarChart(document.getElementById('theme_chart_div'));
	data = new google.visualization.DataTable();
	data.addColumn('string','Theme Name',);
	// data.addColumn('number','Enrichment Score');
	data.addColumn('number','Enrichment Score');
	data.addColumn({role:'annotation'});
	window.opener.console.log(`Graph Rows = ${themeData.length}`);
	data.addRows(themeData);
	graph.draw(data,options);
   console.log(graph);
   let setSelections = [];
   for(let i=0;i<themeDataLength;i++) {
      setSelections.push({row:i,column:1});
   }
   graph.setSelection(setSelections);
   bars = Array.from($("svg").children()[3].children[1].children[1].children);
   annotes = Array.from( $("svg").children()[3].children[4].children );
   console.log(bars);
   let ttps = Array.from($(".google-visualization-tooltip"));
   ttps.forEach(function(ttp,index) {
      let bDim = bars[index].getBoundingClientRect();
      let pth = ttp.children[0];
      let nameGrp = ttp.children[1];
      let scoreGrp = ttp.children[2];
      let ogX,ogY,nwX,nwY
      // Move Theme Name
      let nameTxt = nameGrp.children[0];
      // $(nameTxt).remove();
      ogX = parseInt($(nameTxt).attr('x'));
      ogY = parseInt($(nameTxt).attr('y'));
      nwX = bDim.right;
      nwY = ogY + 55;
      $(nameTxt).attr('x', nwX);
      $(nameTxt).attr('y', nwY);
      // Move Score Label
      let esTxt = scoreGrp.children[0];
      
      ogX = parseInt($(esTxt).attr('x'));
      ogY = parseInt($(esTxt).attr('y'));
      nwX = bDim.right;
      nwY = nameTxt.getBoundingClientRect().height + nwY;
      $(esTxt).attr('x', nwX);
      $(esTxt).attr('y', nwY);
      // Move Score
      // let lDim = $(esTxt).get(0).getBoundingClientRect();
      let scoreTxt = scoreGrp.children[1];
      // ogX = parseInt($(scoreTxt).attr('x'));
      // ogY = parseInt($(scoreTxt).attr('y'));
      nwX = esTxt.getBoundingClientRect().right;
      // nwY = ogY + 45;
      $(scoreTxt).attr('x', nwX);
      $(scoreTxt).attr('y', nwY);
      // $(esTxt).remove();
      $(pth).remove();
      // console.log(nameTxt);
      // console.log(esTxt);
      // console.log(scoreTxt);
      let gClone = ttp.cloneNode();
      // gClone.classList.remove('google-visualization-tooltip');
      // gClone.classList.add('popup-clone');
      let nameClone = nameTxt.cloneNode();
      $(nameClone).text($(nameTxt).text());
      let esClone = esTxt.cloneNode();
      $(esClone).text($(esTxt).text());
      let scoreClone = scoreTxt.cloneNode();
      $(scoreClone).text($(scoreTxt).text());
      gClone.appendChild(nameClone);
      gClone.appendChild(esClone);
      gClone.appendChild(scoreClone);
      $(gClone).hide();
      ttp.parentElement.appendChild(gClone);
      let bClone = bars[index].cloneNode();
      let rClone = bars[index].firstChild.cloneNode();
      bClone.append(rClone);
      $(bClone).click(function() {
         $(gClone).toggle();
      });
      bars[index].parentElement.append(bClone);
      
      let agClone = annotes[index].firstChild.cloneNode();
      let atClone = annotes[index].firstChild.firstChild.cloneNode();
      $(atClone).text($(annotes[index].firstChild.firstChild).text());
      agClone.append(atClone);
      annotes[index].append(agClone);
      console.log(agClone);
      $(annotes[index].firstChild).hide();
      $(ttp,nameTxt,esTxt,scoreTxt).remove();
   });
   
	// initDownloadProcess();
   if( title != 'CompBio-Theme Graph Export' ) {
      document.getElementById('dl_name').value = title;
   }
   let outerG = Array.from( document.querySelectorAll("g"))[1];
   let innerG = outerG.children[3];
   let gArray = Array.from( innerG.children );
   gArray.forEach((g, index) => {
      if( g.children.length > 1 ) {
         let gText = Array.from( g.querySelectorAll("text"));
         if( gText.length > 1 ) {
            let yAdjst = ( gText.length - 1 ) * 12;
            let t1 = gText[0];
            for( let i = 1; i < gText.length; i++ ) {
               gText[i].remove();
            }
            let origY = parseFloat( t1.getAttribute('y'));
            let newY = origY + yAdjst;
            t1.setAttribute('y', newY);
         }
      } else if( g.children.length < 1 ) {
            console.log(index);
            console.log(g);
      }
   });
   let fD = $($("#theme_chart_div").get(0).children).get(0);
   let dims = fD.getBoundingClientRect();
   console.log('COMPLETE!!!');
}

$(function() {
   document.getElementById('init-download-btn').addEventListener( 'click',function() {
      initDownloadProcess();
   });
});

function moveSection( elmnt, xOffset, yOffset) {
   if (elmnt) {
      var transformAttr = ' translate(' + xOffset + ',' + yOffset + ')';
      elmnt.setAttribute('transform', transformAttr);
   }
}
async function initDownloadProcess() {
   var svg = document.getElementsByTagName("svg")[0].cloneNode(true);
   svg.removeAttribute('aria-label');
   let clip = svg.querySelectorAll('[clip-path]')[0];
   clip.replaceWith(...clip.childNodes);
   console.log(svg);
   let docName = ( document.getElementById('dl_name').value === '' ) ? 'noname' : document.getElementById('dl_name').value.replace(/[^a-z0-9_-]/gi,'');
   let docTypeHead = '<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">';
   var svgData = ( new XMLSerializer() ).serializeToString( svg );
   var allData = JSON.stringify({ name: docName, svgData: docTypeHead + svgData });
   console.log(svgData);
   console.log(allData);
   httpRequest(allData);
}
function httpRequest( data, type = 'c' ) {
    if ( type === 'c' || type === 'r' ) {
        let header = ( type === 'r' ) ? 'Remove=' : 'PostRequest=';
        let request = header + encodeURIComponent( data );
        console.log( request )
        let url = 'download.php';
        let xhr = new XMLHttpRequest();
        xhr.open( "POST", url, true );
        xhr.setRequestHeader( "Content-type", "application/x-www-form-urlencoded" );
        xhr.onreadystatechange = function() {
            if ( xhr.readyState == 4 && xhr.status == 200 ) {
                let response = decodeURIComponent( xhr.responseText );
                if ( response.includes( 'download' ) === true ) {
                	downloadSvgGraphFile(response);
                	setTimeout(function() {
                		let fArray = response.split( '/' );
        				   let fileName = fArray[fArray.length - 1];
                		httpRequest( fileName, 'r' );
                	},1000);
                } else {
                    console.log( response )
                }
            }
        }
        xhr.send( request );
    }
}
function downloadSvgGraphFile(resp) {
    let linkElement = document.createElement( 'a' );
    linkElement.href = resp;
    linkElement.addEventListener( 'click', function( e ) {
        console.log( 'Graph Downloading' );
        /*Delete Link Element*/
        setTimeout(function() {
			document.body.removeChild(linkElement);
        },2000);
    } );
	let downloadAttribute = document.createAttribute( 'download' );
	linkElement.setAttributeNode( downloadAttribute );
	document.body.appendChild( linkElement );
	/*Trigger File Downalod Via Link Click*/
	linkElement.click();
}