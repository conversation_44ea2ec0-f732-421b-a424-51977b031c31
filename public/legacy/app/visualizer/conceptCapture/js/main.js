/*Svg Capture Main Script*/
document.addEventListener( 'DOMContentLoaded', init );
const App = {};
var s = Snap( '#svg' ).attr( { margin: 0, padding: 0 } );
console.log('Loading...')

async function init() {
    if ( typeof( sessionStorage.getItem( 'svgData' ) ) !== 'undefined' ) {
        /*Download event listener*/
        document.getElementById( 'download' ).addEventListener( 'click', function( e ) {
            var svg = document.getElementById( 'svg' );
            var clone = svg.cloneNode( true );
            var svgDocType = document.implementation.createDocumentType( 'svg', "-//W3C//DTD SVG 1.1//EN", "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" );
            var svgDoc = document.implementation.createDocument( 'http://www.w3.org/2000/svg', 'svg', svgDocType );
            svgDoc.replaceChild( clone, svgDoc.documentElement );
            var svgData = ( new XMLSerializer() ).serializeToString( svgDoc );
            httpRequest( svgData );
        } );
        App.svgData = JSON.parse( sessionStorage.getItem( 'svgData' ) );
        App.svgData.tubes = window.opener.svgTubes;
        App.svgData.text = window.opener.svgText;
        App.svgElement = document.getElementById( 'svg' ); 
        App.svgElement.setAttribute( 'width', window.innerWidth - 3 );
        App.svgElement.setAttribute( 'height', window.innerHeight - 3 );
        App.svgElement.setAttribute( 'viewBox', `-${window.innerWidth / 2} -${window.innerHeight / 2} ${window.innerWidth} ${window.innerHeight}` );
        App.svgElement.style.width = window.innerWidth;
        App.svgElement.style.height = window.innerHeight;
        //Set array for draggable elements
        App.draggableElements = [];
        //Set body background
        let backgroundRect = s.rect( `-${window.innerWidth / 2}`, `-${window.innerHeight / 2}`, window.innerWidth, window.innerHeight ).attr( { id: 'backgroundRect', fill: App.svgData.background } );
        const tubesAdded = await loadSvgGroups( App.svgData.tubes, true );
        const textAdded = await loadSvgGroups( App.svgData.text, true );
        const dragHandlerBound = await attachDragHandler();
    } else {
        console.log('!Session Storage Error!\nNo SVG Data available...')
        document.getElementById( 'content' ).innerText = 'Error!!! You have reached this page in error or there has been an error in the application.  Try reloading the page or contacting support to help with the problem.';
    }
}

/*Attach Drag Handler*/
function attachDragHandler() {
    return new Promise( ( resolve ) => {
        console.log( 'Attaching element drag handler...' )
        let gElements = [].slice.call( document.getElementsByClassName( 'draggable-element' ) );
        gElements.forEach( function( element ) {
            let snapSelected = s.select( `#${CSS.escape(element.id)}` );
            snapSelected.drag();
        } );
        resolve( true );
    } );
}

function loadSvgGroups( array, isConceptMap = false ) {
    return new Promise( ( resolve ) => {
        let nodeArray = [];
        array.reverse();
        array.forEach( function( string ) {
            let groupNode = Snap.parse( string );
            s.append( groupNode );
            nodeArray.push( groupNode );
        } );
        if( isConceptMap === true ) {
            resolve( nodeArray );
        } else {
            let sphereElements = [].slice.call( document.querySelectorAll( '[data-geom-type="sphere"' ) );
            resolve( sphereElements );
        }
    } );
}

/*Serialize SVG/XML Data*/
function xmlToString( xmlData ) {
    var xmlString;
    // IE
    if ( window.ActiveXObject ) {
        xmlString = xmlData.xml;
    }
    // Mozilla, Firefox, Opera, etc.
    else {
        xmlString = ( new XMLSerializer() ).serializeToString( xmlData );
    }
    return xmlString;
}

/*Send Svg Data*/
function httpRequest( data, type = 'c' ) {
    if ( type === 'c' || type === 'r' ) {
        let header = ( type === 'r' ) ? 'Remove=' : 'PostRequest=';
        let request = header + encodeURIComponent( data );
        let url = 'php/download.php';
        let xhr = new XMLHttpRequest();
        xhr.open( "POST", url, true );
        xhr.setRequestHeader( "Content-type", "application/x-www-form-urlencoded" );
        xhr.onreadystatechange = function() {
            if ( xhr.readyState == 4 && xhr.status == 200 ) {
                let response = decodeURIComponent( xhr.responseText );
                if ( response.includes( 'download' ) === true ) {
                    let file = response;
                    let url = window.location.href + file;
                    let linkElement = document.createElement( 'a' );
                    linkElement.href = url;
                    linkElement.addEventListener( 'click', function( e ) {
                        console.log( 'Download Started' )
                        let fArray = file.split( '/' );
                        let fileName = fArray[ 1 ];
                        /*Delete File*/
                        setTimeout( function() {
                            httpRequest( fileName, 'r' );
                            console.log( 'File Removal Requested' );
                            document.body.removeChild(linkElement);                            
                        }, 10000 )
                    } );
                    let downloadAttribute = document.createAttribute( 'download' );
                    linkElement.setAttributeNode( downloadAttribute );
                    document.body.appendChild( linkElement );
                    /*Download File*/
                    linkElement.click();
                } else {
                    console.log( response )
                }
            }
        }
        xhr.send( request );
    }
}