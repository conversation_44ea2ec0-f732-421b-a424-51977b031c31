/*Bootstrap Overrides*/
div.no-gutter-left {
    padding-left: 0;
}

button.btn-primary:hover {
    /* background-color: #820606; */
}
button.btn-primary:focus {
    /* box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5); */
}
/*.form-control:focus {
    border-color: #820606;
    box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5);
}*/
table.table {
    color: #ffffff;   
    height: 99.9%;
    height: fit-content;
    height: -webkit-fill-available;
    margin-bottom: 0;
}
table.table th {
    width: 25%;
}
table.table th, table.table td {
    border: 1px solid #ffffff;
    text-align: left;
    padding: 0;
    padding-left: 8px;
    vertical-align: middle;
}
/*Overrides End*/

/*Global Scrollbars*/
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-thumb {
    background: #ffffff;
}
::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
}
/*Scrollbars End*/

html {
    /*    background: linear-gradient(135deg, #212121, #540a0a) !important;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;*/
}

body {
/*    background: linear-gradient(135deg, #212121, #540a0a) !important;*/
    /* background: #3c0808 !important; */

    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    color: #ffffff;
    font-family: Raleway;
    cursor: pointer;
    overflow: hidden;
    height: 100vh;
}

nav.navbar-dark {
    background: #191c21;
}

section.nav-panel {
    /*    position: fixed;
    top: 0;
    left: 0;*/
    height: 100vh;
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

nav.sidebar-sticky {
    background: #191c21;
    height: 100%;
    width: 100%;
    padding: 15px;
}

p.panel-title {
    position: absolute;
    width: 100%;
    background: #fff;
    top: 0;
    left: 0;
    padding: 10px 0;
    color: #191c21;
    text-align: center;
    font-weight: bold;
}

p.acct-name {
    text-align: center;
    margin: 15px;
    margin-top: 50px;
    font-size: 1.5em;
}

section.nav-sub {
    border-top: 1px solid #fff;
    padding-top: 1em;
    padding-bottom: 1em;
}

ul.nav {
    margin-left: 15px;
}

li.nav-item {
    font-size: 0.9em;
    padding: 10px;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}

li.nav-item:hover {
    font-size: 1em;
    background-color: #0d0e11;
    border: 1px groove #ffffff;
    margin-right: -10px;
}

li.active-nav-item, li.active-project {
    border: 1px solid #540a0a !important;
    background-color: #0d0e11;
    margin-right: -10px;
}

button#log-out-btn {
    width: 100%;
    background: #540a0a;
    border: 3px outset #fff;
}

input#current-email {
    color: #000;
    font-weight: bold;
}

/*Mobile Small Screen Menu*/

div.account-name-mobile {
    margin: 15px 0;
    padding: 10px 0;
    border-color: #fff;
    border-top: 1px groove rgba(255,255,255,0.5);
    border-bottom: 1px groove rgba(255,255,255,0.5);
    font-size: 1.2em;
}

li.mobile-nav-section-title {
    color: #fff !important;
    border-bottom: 1px groove rgba(255,255,255,0.5);
}

li.mobile-nav-link {
    margin-left: 15px;
}

/*mobile end*/

.page-title {
    color: #0a0a0a;
    font-family: 'Audiowide', cursive;
    text-shadow: 0 0 15px #ffffff, 0 0 5px #d80000;
    font-variant: small-caps;
    /*font-weight: bold;*/
}

section.tab {
    /*    margin: 15px;
    padding: 15px;*/
}

section.tab-content {
    background: #191c21;
    width: 100%;
    min-height: 80vh;
    padding: 15px;
    border: 1px solid #ffffff;
    overflow-y: auto;
    box-shadow: 0 0 25px #fff, 0 0 5px #d80000;
    margin-bottom: 25px;
}

div.tab-content-row {
    min-height: 35vh;
}

div.btn-panel-group {}

button.btn-standard {
    background: #fff;
    border: none;
    color: #000;
    font-weight: bold;
    font-variant: small-caps;
    margin-top: 5px;
    margin-bottom: 5px;
}

div.box-outline-wrap {
    border: 2px solid #ffffff;
    height: 85%;
    max-height: 275px;
    overflow-x: hidden;
    overflow-y: auto;
}

ul#projects-list, ul#filters-list {
    margin-bottom: 5px;
}

div#announcements {
    padding: 15px;
}

tr:nth-child(even) {
    background-color: #8484848a;
}

hr.section-seperate {
    border: 1px solid #ffffff;
}

form.input-form {
    height: 100%;
}

textarea.entity-textarea {
    width: 90%;
    height: 100%;
    resize: none;
    margin-bottom: 5em;
}
ul {
    padding: 0;
}
li.list-item {
    font-size: 0.9em;
    padding: 15px;
    border-bottom: 1px dotted #4a4a4a;
    list-style: none;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;    
}

li.list-item:hover {
    font-size: 1em;
    background-color: #0d0e11;
    border: 1px groove #ffffff;
    padding: 17px;
}

div#new-folder-error {
    color: red;
}

i.folder-icon {
    margin-right: 15px;
}

ul.folder-list {
    /*padding-left: 15px;*/
}

li.folder-list-item {
    font-size: 0.8em;
}

li.folder-list-item:hover {
    font-size: 0.9em;
}

li.folder-list-item::before {
    content: '\2BA1';
    color: #8b0b0b;
    font-size: 1em;
    margin-right: 10px;
    margin-left: 15px;
}

/*Dialog Popups*/
div#list-display {
    font-family: monospace;
    line-height: 1em;
}
span.ui-dialog-title, span.ui-button-icon , button.ui-button {
    font-size: 1.3em;
}

div#modal-help-content {
    margin: 25px 15px;
}

div#modal-help-content h3 {
    text-decoration: underline;
}

/*Sudo Tabs*/

button.account-create-btn-group {
    border: 1px solid #191c21;
}

textarea#anon-edit {
    width: 100%;
    width: -webkit-fill-available;
    height: 75%;
}
/*sudo end*/
div#mobile-log-out {
    font-size: 1.5em;
    font-weight: bold;
}

div#mobile-log-out::before, div#mobile-log-out::after {
    content: '-';
}

/*Dialog End*/
/*Comparison Btns Wrap*/
div.special-btn-wrap {
    background: #1b80f3;
    padding: 5px;
    border-radius: 10px;
    text-align: center;
    transition: 0.15s;
}
div.special-btn-wrap > p {
    margin-bottom: 0;
}
/*Compare Btn End*/
/*Optimization Check*/
div#optimization-check-wrap {
    position: absolute;
    bottom: 0;
}
/*Optimization End*/

@media screen and (min-width: 992px) {

}

@media screen and (max-width: 991px) {
    body {
        overflow-y: auto;
    }
    section.tab-content {
        height: 100%;
        width: 100%;
        overflow: unset;
    }
}

@media screen and (max-width: 769px) { 
    textarea.entity-textarea {
        width: 100%;
    }
}

@media screen and (max-width: 575.98px) {
    body {
        background-position: center top;
    }
    div.form-dark { 
        padding: 10px;
    }
    div.box-outline-wrap {
        overflow-x: auto;
    }
    iframe {
        position: absolute;
        left: 0 !important;
    }
    div#help-tab {
        position: absolute;
        /*right: 0 !important;*/
    }  
    div.main-content-col {
        padding-left: 1em !important;
        padding-right: 1em !important;
    }
}
