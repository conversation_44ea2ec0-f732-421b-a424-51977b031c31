/* Remove UserSanp Btn */
div#us_report_button {
    display: none !important;
}
/* End */
html {
    height: 100%;
}
body {
    /* font-family: 'Exo 2',sans-serif; */
    /* font-family: 'Roboto',sans-serif; */
    font-family: sans-serif;
    font-size: 14px;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #000000;
    margin: 0;
    padding: 0;
    overflow: hidden;
    line-height: 1;
    text-rendering: optimizeLegibility;
}
/*Cursor Wait*/
body.wait * {
    cursor: wait !important;
}
/*Popup Display Filter Class*/
.v-none {
    visibility: hidden!important;
}
/*WUSTL Red Background*/
body {
    /* background: radial-gradient(farthest-side at 80% 45%,#420808 40%,#131313); */
    background: #ffffff;
}
.title i.fa.fa-question-circle.fa-pull-right {
    padding-top: 10px;
}
/*GuiMenu prevent text highlighting*/
.dg ul li {
    user-select: none;
}
/*end*/
#canvas1 {
    position: absolute;
    z-index: -4;
}
#stats {
    top: calc(100% - 48px) !important;
    visibility: visible;
}
#hud {
    /* background: #676767; */
    background: #27215f;
    position: fixed;
    top: 0;
    left: 0;
    width: 20%;
    min-width: 250px;
    height: 100%;
    max-height: 100%;
    border: 1px groove #000000;
    border-top: none;
    color: #ffffff;
    overflow-wrap: break-word;
    word-wrap: break-word;
    /* cursor: pointer; */
    z-index: -2;
    display: flex;
    flex-flow: column;
}
.hud-views {
    background: #eeecff;
    color:#27215f;
    font-weight: bold;
}
span.indent-value {
    margin-left: 5px;
}
span.geneTotal {
/*    margin-left: 5px;*/
    margin-top: 5px;
}
p.conceptTotal {
    margin: 0;
}
span.hide-default-count {
    display: none;
}
/*Added for HUD P-Value and NESScore*/
p.score-pVal-nes {
    margin: 0;
}
/*P-Value and NESScore End*/
#concept-map {
    height: 100%;
    width: 100%;
}
/*HUD2*/
#hud2 {
    position: absolute;
    top: 2px;
    left: 5px;
    width: 20%;
    height: 98%;
    border: 1px groove #00FFFF;
    border-radius: 5px;
    padding: 5px;
    color: #00FFFF;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    z-index: 1;
    overflow: hidden;
}
/*HUD2 Header*/
#hud2-header {
    height: auto;
    font-size: 1.5em;
    text-align: center;
    border: 1px solid #00ffff;
    border-radius: 5px;
    padding-bottom: 15px;
}
#hud2-title {
    margin: 5px 0;
    margin-top: 10px;
}
div#hud2-header div.custom-search-notice {
    font-size: 0.5em;
    margin-bottom: 10px;
}
#hud2-header .form {
    display: block;
    margin-top: 0em;
    font-size: 0.5em;
    padding: 5px;
}
div#add-on-term {
    padding-top: 5px;
}
#customTermTextBox {
    background-color: rgba(255, 255, 255, 0.1);
    color: #00ffff;
    padding: 1px;
    margin: 5px;
}
/*HUD2 InnerWrap scroll*/
#hud2-inner-wrap {
    overflow-y: auto;
}
/*width */
#hud2-inner-wrap::-webkit-scrollbar {
    width: 10px;
}
/*Track */
#hud2-inner-wrap::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}
/*Handle */
#hud2-inner-wrap::-webkit-scrollbar-thumb {
    background: #00FFFF;
    border-radius: 10px;
}
/*Handle on hover */
#hud2-inner-wrap::-webkit-scrollbar-thumb:hover {
    background: #FF1493;
}
/*HUD2 header*/
#hud2 .concept-item {
    padding: 7px 10px;
    border-bottom: 1px solid #00FFFF;
}
button#custom-search-btn {
    border: 2px solid #00ffff;
    border-radius: 4px;
    padding: 2px 10px;
    background-color: rgba(0, 0, 0, 0);
    color: #00ffff;
}
/*HUD2 Selected Item*/
#hud2 .concept-item.selected-concept {
    background-color: #00ffff;
    color: #000000;
    padding-left: 20px;
    margin-bottom: 2px;
}
/*HUD2 Custom Query*/
#add-on-term {
    visibility: hidden;
    display: none;
}
.hudCluster {
    border-bottom: 2px solid #fff;
    padding: 5px;
}
.hudSelectedCluster,.hudSelectedGene {
    background-color: #27215f;
    color:#fff;
    padding: 10px;
    font-weight: bold;
    text-shadow: none;
    border: 2px solid #007bff;
}
.boxViewSelected {
    border: 5px solid lime !important;
    filter: hue-rotate(180deg) !important;
}
.boxViewGroupSelected {
    border: 5px solid yellow !important;
} 
.themebox-table-cont:hover {
    cursor: pointer;
    border: 5px solid lime !important;
    filter: hue-rotate(270deg) !important;
}
.clusterName {
    /* font-size: 1.3em; */
    margin: 0;
    text-align: center;
}
.theme-index {
    margin: 1px;
    margin-bottom: 0;
}
.score-conceptTotal {
    margin: 0;
}
.concepts {
    margin-top: 5px;
    margin-bottom: 5px;
}
.genes {
    margin-bottom: 10px;
}
#clusterInfo {
    visibility: hidden;
    position: absolute;
    width: auto;
    height: 100%;
    max-width: 450px;
    max-height: 400px;
    border: 2px groove #ffffff;
    border-radius: 4px;
    padding: 5px 10px 5px 10px;
    color: #ffffff;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(0, 0, 0, 0.9);
    font-weight: 300;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    overflow: hidden;
    font-size: 16px;
    font-weight: lighter;
    line-height: 105%;
    cursor: pointer;
    z-index: -2;
    resize: vertical;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    min-height: 160px;
}
table.popup-table{
  height:100%;  
}
table.popup-table tbody{
  display:block;
  overflow-x: hidden;
  overflow-y:auto;
  min-height: 100px;
  height: calc(100% - 5px);
  width:100%;
}
table.popup-table tbody tr {
  width:100%;
}
table.popup-table thead tr{
  display:table;
}
.concept-table-row th {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 175px;
}
#popUpHeader {
    color: #FFFF00;
    padding: 5px;
    /* padding-bottom: 0; */
    margin-right: 35px;
}
#popUpBody {
    display: flex;
    height: 100%;
}
table.popup-table > tbody::-webkit-scrollbar {
    width: 3px;
}
table.popup-table > tbody::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
}
table.popup-table > tbody::-webkit-scrollbar-thumb {
    background: #00FFFF;
    border-radius: 10px;
}
table.popup-table > tbody::-webkit-scrollbar-thumb:hover {
    background: #FF1493;
}
.t-head th {
    border-bottom: 1px solid #00ffff;
    padding-bottom: 2px !important;
    color: #00ffff;
    font-size: 0.7em;
    white-space: nowrap;
}
#selectedClusters {
    visibility: hidden;
    position: fixed;
    bottom: 30px;
    left: 27%;
    max-width: 73%;
    width: auto;
    height: auto;
    cursor: pointer;
    z-index: -2;
}
#selectedClusters button {
    padding: 5px;
    font-weight: 300;
    font-size: 14px;
    font-weight: lighter;
    line-height: 105%;
    cursor: pointer;
    opacity: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 20vw;
    margin-left: 5px;
    margin-bottom: 5px;
}
input#deselectAll {
    visibility: hidden;
    position: fixed;
    bottom: 30px;
    left: 22%;
    width: auto;
    height: auto;
    padding: 5px;
    font-weight: 300;
    font-size: 14px;
    font-weight: lighter;
    line-height: 105%;
    cursor: pointer;
    z-index: -2;
    opacity: 90%;
    margin-bottom: 5px;
}
#scale {
    /* color: #000000; */
    background: #27215f;
    /* border: 3px solid #262626; */
    border-bottom: 1px solid #fff;
    border-left: none;
    width: auto;
    padding: 5px;
    /* font-weight: bold; */
    text-align: center;
    font-variant-caps: small-caps;
    user-select: none;
}
div#scale-file,
div#visible-gene-wrap,
div#exp-pval {
    font-size: 1.1rem;
}

div#exp-pval {
    margin-top: 3px;
    margin-bottom: 2px;
    font-size: 1.2rem;
}

div#exp-pval span {
    color: #82be42;
    font-weight: bold;
    padding-left: 2px;
    padding-right: 2px;
}

#exp-pval>span:nth-child(1) {
    border: 1px solid white;
    border-right: none;
}

#exp-pval>span:nth-child(2) {
    border: 1px solid white;
    border-left: none;
}
#scale-file span {
    margin-right: 10px;
    font-variant-caps: petite-caps;
    font-weight: bold;
}
#view, #hud-btns, #group-tools {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    padding: 0;
    margin: 5px 0;
    /*font-variant-caps: petite-caps;*/
    user-select: none; 
}
#view-title {
    font-variant-caps: petite-caps;
    user-select: none;
}
.view-btn {
    /* border-radius: 2px; */
    /* background: #676767;    
    border: 1px solid #8d8d8d;
    color: #fff; */
    width: 45%;
    min-width: 100px;
    padding: 5px 10px;
    margin: 2px 5px;
    /* margin: 5px;
    padding: 5px; */
    user-select: none;
    background: #fff;
    /* border: 1px solid #e4deef; */
    border: none;
    color: #27215f;
    font-variant: small-caps;
}
.hud-btn-btns {
    width: 45%;
    border: none;

}
/*HUD GeneView*/
#gene-view {
    display: none;
}
.active-view-btn {
    /* background: #c7c7c7;
    color: #000; */
    background-color: #82be42 !important;
    color: #ffffff;
}
.active-draglock-btn {
    /* background: #c7c7c7 !important;
    color: #000 !important;     */
    background: #82be42 !important;
    color: #fff !important;
}
.hud-btn-btns:hover,.view-btn:hover {
    background: #007bff !important; 
    color: #fff !important;
}
.gene-container {
    border-bottom: 2px solid #fff;
    padding: 5px;
}
.gene-container-title {
    font-size: 1.3em;
    margin: 0;
}
.gene-theme-list {
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 5px;
    height: 75px;
    overflow: hidden;
}
.gene-theme-list-selected {
    height: auto;
    max-height: 300px;
    overflow-y: auto;
}
.hudSelectedGene{
    background-color: #e4deef;
    color: #27215f;
    padding: 10px;
    font-weight: bold;
    text-shadow: none;
    border: 2px solid #007bff;
}
.gene-theme-list::-webkit-scrollbar {
    width: 2px;
}
.gene-theme-list::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}
.gene-theme-list::-webkit-scrollbar-thumb {
    background: #000000;
    border-radius: 10px;
}
.gene-theme-list::-webkit-scrollbar-thumb:hover {
    background: #343434;
}
#genesPopupDisplay {
    visibility: hidden;
    position: absolute;
    width: auto;
    max-width: 250px;
    height: 250px;
    max-height: 400px;
    /* border: 1px groove #FF1493;
    border-radius: 5px; */
    padding: 5px 10px 5px 10px;
    color: #ffffff;
    font-weight: 300;
    font-size: 16px;
    font-weight: lighter;
    line-height: 105%;
    background-color: rgba(0, 0, 0, 0.9);
    overflow: hidden;
    word-wrap: break-word;
    hyphens: auto;
    cursor: pointer;
    z-index: -2;
    resize: vertical;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    min-height: 160px;

}
#popUpGeneHeader {
    color: #FFFF00;
    padding: 5px;
    margin-right: 35px;
}
#popUpGeneBody {
    overflow-y: auto;
}
.shared-thead th {
    border-bottom: 1px solid #ff1493;
    padding-bottom: 2px !important;
    color: #ff1493;
    font-size: 0.7em;
    white-space: nowrap;
}
.shared-thead th[colspan] {
    text-align: center !important;
}
#popUpGeneBody tr {
    width: 100%;
}
#popUpGeneBody th {
    text-align: left;
    padding: 0 5px;
}
#popUpGeneBody td {
    text-align: right;
}
#popUpGeneBody::-webkit-scrollbar {
    width: 3px;
}
#popUpGeneBody::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}
#popUpGeneBody::-webkit-scrollbar-thumb {
    background: #FF1493;
    border-radius: 10px;
}
#popUpGeneBody::-webkit-scrollbar-thumb:hover {
    background: #00ffff;
}

/*#clusterInfoHideBtn {
    float: right;
    margin: 5px;
    height: 25px;
    width: 25px;
    position: absolute;
    margin: 0;
    top: 3px;
    right: 3px;
    color: #00ffff;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(0, 255, 255, 0.4);
    padding: 5px;
    padding-top: 3px;
    padding-bottom: 17px;
    border-radius: 100%;
    text-align: center;
    font-weight: bold;
}
*/
div.popup-btns {
    display: flex;
    flex-direction: row;
}
/*Class for Theme Info Panel Btns*/
/*Concept Map Btn*/
.conceptMapOpenBtn,.entityScoreGraphBtn {
    /* background-color: rgba(0, 255, 255, 0.4); */
    padding: 2px;
    font-size: 0.7em;
}
/*Image Search Btn*/
.imageSearchBtn {
    /* background-color: rgba(0, 255, 255, 0.4); */
    padding: 2px;
    font-size: 0.7em;
}
#groupListWrap {
    visibility: hidden;
    position: fixed;
    right: 0;
    bottom: 15%;
    width: 125px;
    color: #00FFFF;
    font-size: 16px;
    /*wrap text and overflow*/
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    z-index: -3;
}
#groupList div:first-of-type {
    border-top: 1px solid #000000;
}
/*#groupList {
    height: 100px;
    max-height: 100px;
    border-radius: 5px;
    overflow-y: scroll;
}
*/
#listHead {
    text-align: center;
    padding: 5px;
    border: 2px groove #00ffff;
    border-radius: 5px 0 0 0;
    border-bottom: none;
}
/*GroupEdit*/
#group-tools {
    /*display: flex;
    justify-content: center;
    text-align: center;
    font-variant-caps: petite-caps;*/
    user-select: none;
    padding: 10px;
    margin: 0;
    color: #000000;
    background: #dfdfdf;
    visibility: hidden;
}
.group-tools-btn {
    background: #676767;
    border: 2px outset #8d8d8d;
    margin: 0 5px;
    padding: 5px;
    font-variant-caps: petite-caps;
    user-select: none;
    width: 40%;
}
/*HUD groups*/
.group-container {
    border-bottom: 2px solid #000000;
    padding: 5px;
}
#groupListItems {
    padding: 10px;
    margin: 0;
    font-size: 0.8em;
    font-weight: 300;
    border-bottom: 2px solid #000000;
    color: #000000;
    background: #dfdfdf;
    text-align: center;
    visibility: hidden;
}
#groupListItems span {
    margin: 0;
    padding-bottom: 2px;
    cursor: pointer;
    display: inline-block;
}
.group-name {
    font-size: 20px;
}
.listItemSelected {
    background-color: #ffffff;
    color: #000000;
    padding: 10px;
    font-weight: bold;
    text-shadow: none;
}
.group-hidden:after {
    content: '(hidden)';
}
/*End*/
/*Canvas2*/
#canvas2 {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 0;
}
/*Canvas3*/
#canvas3 {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100% !important;
    height: 100%;
    z-index: 0;

}
#gui3 {
    position: absolute;
    top: 2px;
    right: 0;
    z-index: 1;
}
#canvas3CloseBtn {
    position: fixed;
    top: 10px;
    right: 40px;
    padding: 5px;
    border-radius: 6px;
    text-align: center;
    width: 30px;
}
#canvas3CloseBtn:active {
    background-color: #FF69B4;
    box-shadow: 0 1px #FF1493;
    transform: translateY(2px);
    color: #000000;
}
/*Modal Stuff*/
/*Test Button*/
#modalTestBtn {
    position: fixed;
    right: 15%;
    top: 5%;
}
/*Informational Modal*/
#info-popout {
    display: none;
    position: fixed;
    z-index: 1;
    left: 40%;
    top: 7%;
    width: 20%;
    height: auto;
    overflow: auto;
    background-color: rgba(228, 222, 239, 0.5);
    border: 1px solid #27215f;
    margin: auto;
    text-align: center;
}
.popout-content {

    color: #27215f;
    padding: 2rem;
    font-weight: bold;
}
/*Regular Modal*/
.modal {
    display: none;
    /*Hidden by default */
    position: fixed;
    /*Stay in place */
    z-index: 1;
    /*Sit on top */
    padding-top: 100px;
    /*Location of the box */
    left: 0;
    top: 0;
    width: 100%;
    /*Full width */
    height: 100%;
    /*Full height */
    overflow: auto;
    /*Enable scroll if needed */
    background-color: rgb(0, 0, 0);
    /*Fallback color */
    background-color: rgba(0, 0, 0, 0);
    /*Black w/ opacity */
}
/*Modal Content */
.modal-content {
    color: #aaaaaa;
    background-color: #000000;
    margin: auto;
    padding: 20px;
    border: 1px solid #00ffff;
    width: 50%;
    height: 30%;
}
/*The Close Button */
.close {
    color: #00ffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
}
.close:hover,
.close:focus {
    color: #ff6600;
    text-decoration: none;
    cursor: pointer;
}
/*HUD Close*/
#hudClose, #capture-btn {
    /* background: #676767; */
    /* border: 1px solid #8d8d8d; */
    /* color: #fff; */
    margin: 5px;
    padding: 5px;
    user-select: none;
    background: #fff;
    /* border: 1px solid #e4deef; */
    color: #27215f;
    font-variant: small-caps;
}
#hudClose:active, #capture-btn:active {
    background: #c7c7c7;
    /* border: 2px inset #8d8d8d; */
}
/*HUD Open*/
#hudOpen {
    /* color: #00ffff;
    border: 1px solid #00ffff; */
    display: none;
    position: absolute;
    padding: 5px;
    margin: 5px;
    cursor: pointer;
    font-variant-caps: small-caps;    
    user-select: none;
    background: #27215f;
    color: #fff;
    border: none;
}
#hudOpen:hover {
    background: #007bff;
}
/*Gene list Scores*/
.geneScore {
    padding-right: 5px;
    color: lightpink;
    font-size: 0.8em;
    font-family: monospace;
    text-align: right;
    border-right: 1px solid #2d2d2d;
}
td.concept-score {
    padding-right: 5px;
    color: cornflowerblue;
    font-size: 0.8em;
    font-family: monospace;
    text-align: right;
    border-right: 1px solid #2d2d2d;
}
.exp-val {
    border: none;
}
/*Genecentric View*/
#geneViewSelection {
    color: white;
    cursor: pointer;
    z-index: 101;
}
.geneViewSelectionHeader {
    padding: 2px 5px 13px;
    text-align: right;
    font-size: 0.8em;
    color: white;
    background: #212b31;
    border: 1px solid white;
}
.geneViewSelectionHeader span {
    padding: 1px;
}
.geneViewSelectionHeader span:hover {
    color: #212b31;
    background: white;
    border-radius: 5px;
    padding: 2px;
}
.geneViewSelectionItem {
    background: #212b31;
    padding: 10px;
    border: 1px solid white;
}
.geneViewSelectionItem:hover {
    color: #212b31;
    background: white;
    border-style: inset;
}
/*VisLockPanel*/
#visLockPanel {
    position: absolute;
    color: #00ffff;
    border: 1px solid #00ffff;
    border-radius: 5px;
    padding: 5px;
    bottom: 50px;
    right: 15px;
    cursor: pointer;
    user-select: none;
    z-index: -1;
}
#visLockPanel div {
    padding: 2px;
}
#visLockPanel div:hover {
    background-color: #00ffff;
    color: #000000;
}
#dialog-confirm {
    text-align: center;

}
#dialog-confirm i {
    margin-bottom: 10px;
}
span#dialog-name {
    line-height: 2;
}
/*ImageSearch CSS*/
#content-all-wrap {
    text-align: center;
    background-color: #333333;
    height: 100%;
}
#page-header {
    margin: 0;
    padding: 25px;
}
#results {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
}
.img-container {
    width: 300px;
    height: 300px;
    margin: 10px 5px;
    border: 1px solid black;
}
#results-wrap img {
    object-fit: fill;
    width: 300px;
    height: 300px;
}
/*Modal Content Focus Info Item*/
#outer-item-modal {
    display: none;
}
#item-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 98%;
    height: 85%;
    background-color: grey;
    display: inline-flex;
    flex-flow: row wrap;
    justify-content: space-between;
    padding: 25px;
}
#panel-close {
    position: absolute;
    top: 25px;
    left: 95%;
}
#selectedImg-wrapper {
    width: 30%;
    margin: auto;
}
#selectedImg-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}
#info-wrapper {
    text-align: left;
    word-wrap: break-word;
    overflow-y: auto;
    width: 55%;
}
#info-wrapper p {
    margin-right: 5px;
    padding-right: 5px;
}
#info-wrapper p span {
    font-weight: bold;
    font-size: 1.2em;
}
#info-wrapper p b {
    color: darkred;
}
.link-btn {
    background-color: #333333;
    color: white;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
}
.link-btn a:link,
.link-btn a:visited {
    text-decoration: none;
    color: white;
    cursor: pointer;
}
/*ImageSearch End*/
#dialog {
    font-size: 0.8em;
}
#canvas2CloseBtn {
    visibility: hidden;
}
/*#canvas3CloseBtn {
    visibility: hidden;
}
*/
#quickSaveBtn {
    visibility: visible;
}
#visLockPanel {
    visibility: hidden;
}
.clusterInfoBtns {
    flex-grow: 0;
    flex-shrink: 0;
    width: 50%;
    margin: 1px;
    color: #000000;
    /* text-shadow: 1px 1px 1px #000000; */
}
.cluster-info-header {
    cursor: move !important;
    border: 1px solid #333;
    color: #fff;
    font-weight: bold;
    /* min-height: 30px; */
    margin: -5px -11px 10px -11px;
    /* background-color: rgba(0, 255, 255, 0.4); */
    text-shadow: 2px 2px 1px #000000;
    
}
input.info-panel-clsbtn {
    position: absolute;
    top: 0;
    right: 0;
    font-weight: bold;
}
div.info-panel-title {
    position: absolute;
    top: 10px;
    left: 10px;
    max-width: 150px;
    text-shadow: 1px 1px 1px #000;
    font-weight: normal;
    color: #fff;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    padding-bottom: 5px;
}
p.info-panel-txt {
    margin: 0;
}
p.info-panel-disp-name {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;    
}
#clusterInfoHideBtn {
    float: right;
    margin: 5px;
    border-radius: 5px;
}
div.gene-info-header {
    cursor: move !important;
    border: 1px solid #333;
    color: #fff;
    font-weight: bold;
    /* width: 109%; */
    /* height: 35px; */
    margin: -5px -11px 5px -11px;
    /* border-radius: 5px; */
    /* background-color: rgba(255,20,147,0.5); */
    text-shadow: 2px 2px 1px #000000;
    /* cursor: move;
    border: 1px solid #333;
    color: #fff;
    font-weight: bold;
    width: 109%;
    margin: -9px -9px 5px -9px;
    border-radius: 5px; */
}
div#popUpGeneHeader hr {
    margin: 1px;
    border: none;
    border-top: 1px dotted rgba(255,20,147,0.5);
}
span.shared-popup-separator {
    font-size: 0.75em;
    padding-left: 15px;
}
.popUpGeneSubHeader {
    font-weight: normal;
    font-size: 0.9em;
    margin: 0 1px 2px 5px;
}
#closeBtn { 
    float: right;
    margin: 5px;
    border-radius: 5px;
    font-weight: bold;
}
#popUpGeneBody {
    border-collapse: collapse;
}
#popUpBody tr {
    width: 100%;
}
#popUpBody th {
    text-align: left;
    padding: 0 5px;
}
#popUpBody th:nth-child(2) {
    text-align: right;
}
#popUpBody td {
    text-align: right;
}
#auth-msg {
    visibility: hidden;
    color: red;
}
.no-close .ui-dialog-titlebar-close {
    display: none;
}
.ui-widget {
    font-size: 18px;
    font-family: Raleway;
}
.th-line-br {
    font-size: 14px;
    padding-top: 2px !important;
    padding-bottom: 2px !important;
}
span.ui-dialog-title, span.ui-button-icon , button.ui-button {
    font-size: 1.3em;
}
#auth-msg {
    visibility: hidden;
    color: red;
}
.no-close .ui-dialog-titlebar-close {
    display: none;
}
/*HUD Views scrollable*/
/* div.hud-views, #groupList {
    text-shadow: -1px -1px 0 #454545, 1px -1px 0 #454545, -1px 1px 0 #454545, 1px 1px 0 #454545;
} */
#group-view, #gene-view, #theme-view {
    overflow-y: auto;
}
#group-view::-webkit-scrollbar, #gene-view::-webkit-scrollbar, #theme-view::-webkit-scrollbar {
    width: 15px;
    background: #e0e0e0;
}
#group-view::-webkit-scrollbar-track, #gene-view::-webkit-scrollbar-track, #theme-view::-webkit-scrollbar-track {
    box-shadow: inset 0 0 15px #27215f;
    border: 1px #000000;
    border-style: ridge;
}
#group-view::-webkit-scrollbar-thumb, #gene-view::-webkit-scrollbar-thumb, #theme-view::-webkit-scrollbar-thumb {
    background: #ffffff;
    box-shadow: inset 0 0 2px #27215f;
    border: 1px #a9a7a7;
}
#group-view::-webkit-scrollbar-thumb:hover, #gene-view::-webkit-scrollbar-thumb:hover, #theme-view::-webkit-scrollbar-thumb:hover {
    background: #007bff;
}
#group-view {
    text-shadow: none;
}
#gene-view-key span {
    margin: 0 10px;
}
#gene-view-key div {
    display: flex;
    justify-content: space-between;
    border: 1px solid #27215f;
    padding: 1px 5px;
    font-size: 16px;
    background: #e4deef;
    color: #27215f;
}
#gene-view-key {
    display: none;
}
#gene-view-key div:nth-child(1) {
    justify-content: center;
}
#gene-view-key div:nth-child(2) {
    justify-content: center;
}
#gene-view-key > div:nth-child(2) span:nth-child(2) {
    color: #0034f1;
}
#gene-view-key > div:nth-child(3) span:nth-child(2) {
    color: #ffa500;
}
#gene-view-key > div:nth-child(2) span:nth-child(4) {
    color: #00ff00;
}
#gene-view-key > div:nth-child(3) span:nth-child(4) {
    color: #ff0000;
}
/*CSS3D domElement*/
.glow-element, .filter-halo-element {
    border-radius: 50%;
}
#dialog-notes {
    overflow: hidden;
}
#dialog-notes p {
    font-size: 0.75em;
}
#notes-text-area {
    width: 100%;
    height: 90%;
    resize: none;
    margin-bottom: 15px;
    overflow-y: auto;
}
.no-user-select {
  -webkit-touch-callout: none; /*iOS Safari */
    -webkit-user-select: none; /*Safari */
     -khtml-user-select: none; /*Konqueror HTML */
       -moz-user-select: none; /*Firefox */
        -ms-user-select: none; /*Internet Explorer/Edge */
            user-select: none; /*Non-prefixed version, currently supported by Chrome and Opera */
}
.allow-user-select {
  -webkit-touch-callout: text; /*iOS Safari */
    -webkit-user-select: text; /*Safari */
     -khtml-user-select: text; /*Konqueror HTML */
       -moz-user-select: text; /*Firefox */
        -ms-user-select: text; /*Internet Explorer/Edge */
            user-select: text; /*Non-prefixed version, currently supported by Chrome and Opera */
}
div.theme-labels {
    cursor: pointer;
    border: 1.5px solid #ffffff;
    padding: 5px 8px;
    border-radius: 4px;
    /* background-color: #6f7b91; */
}
div.theme-labels > span.flo-hidden {
    display: none;
}
span.annotation-flag {
    margin-left: 2px;
    font-family: none;
}
/*Filter Styles*/
span.filter-text {
    visibility: hidden;
}
div.filter-parent-hide {
    display: none;
}
/*dat gui overide width for longer text*/
ul > li.cr.function > div > span.property-name {
    width: 100%;
}
/*Annotations Menu*/
div#annotation-menu-wrap {
    width: auto;
    position: absolute;    
    background: black;
    color: white;
    padding: 10px;
    padding-top: 5px;
}
div#annotation-menu-wrap:hover {
    cursor: move;
}
table#annotation-menu {
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    margin-bottom: auto;
    border-collapse: collapse;
}
tr#header-row {
    padding-bottom: 5px;
}
tr.annotation-table-row:hover {
    background: white;
    color: black;
}
td.annotation-score {
    padding: 2px 5px;
    border: 1px white;
    border-style: solid;
}
td.annotation-score:hover {
    cursor: pointer;
    padding-top: 5px;
    padding-bottom: 5px;
}
td.annotation-selection {
    padding: 2px 5px;
    border: 1px white;
    border-style: solid;
}
td.annotation-selection:hover {
    cursor: pointer;
    background: white;
    color: black;
    padding-top: 5px;
    padding-bottom: 5px;
}
span.label-concept-count {
    color: red;
    pointer-events: none;
}
span.hud-concept-count {
    color: white;
}
div.hudCluster.hudSelectedCluster > p.conceptTotal > span.label-concept-count.hud-concept-count {
    color: black;   
}
button.wheel-table-btns {
    font-size: 0.6em;
}
/* BoxView */
div.theme-boxes {
    text-align: center;
    padding: 0;
    max-width: 400px;
    background: none !important;
    border-radius: 9px;
    /* border: 1px solid #243248;
    border-radius: 4px; */
    /* border: 3px outset #b3bdf2;
    border-radius: 4px; */
}
div.themebox-name {
    padding: 5px;
}
div.themebox-name > span:not(:first-child) {
    display: none;
}
/* div.themebox-name > span {
    white-space: nowrap;
} */
div.themebox-table-cont {
    /* background-color: #243248; */
    color: #ffffff;
    border: 1.5px solid #ffffff;
    border-radius: 4px;
    padding-bottom: 4px;
}
table.themebox-table {
    /* font-weight: bold; */
    width: 100%;
}
div.themebox-table-cont th, div.themebox-table-cont td {
    text-align: left;
    /* font-size: 0.8em; */
    padding: 3px;
}
div.themebox-table-cont th {
    border-bottom: 1px solid #00ffff;
    padding-bottom: 2px !important;
    color: #00ffff;
    /* font-size: 0.8em; */
    white-space: nowrap;
}
div.themebox-table-cont td {
    padding: 0 0 0 2px;
}
div.themebox-name {
    display: none;
}
.popup-table {
    flex: 50%;
}
.entity-table-row th,.concept-table-row th {
    width: 100%
}
/* th.themebox-con {
    border-right: 1px solid white;
}
th.themebox-ent {
    border-left: 2px solid white;
} */
div.themebox-table-cont td.themebox-ent {
    /* background-color: #008000; */
    border-left: 1px solid rgba(255,255,255,0.15);
}
div.themebox-table-cont-hidden {
    display: none;
}
#ent-bargraph-content {
    height: 100%;
    width: 100%;
}
/* BoxView End */
/* Test HUD Colors */
    /* #scale {
    background: #e4deef !important;
    color: #27215f !important;
background: #007bff !important; */
    /* color: #fff !important;
    font-weight: bold;
} 
#scale button {
    background: #27215f;
    color: #fff !important;
}*/
/* Test End */
/*Dev ListGroup*/
.list-element {
    color: hotpink;
    list-style: none;
    font-size: 0;
    text-align: center;
}
/*DevEnd*/
.hidden {
    display: none !important;
}
/*On screens that are 992px wide or less, go from four columns to two columns */
@media screen and (max-width: 992px) {
  .view-btn, .hud-btn-btns, .group-tools-btn {
    flex: 50%;
  }
}
/*On screens that are 600px wide or less, make the columns stack on top of each other instead of next to each other */
@media screen and (max-width: 600px) {
  #view, #hud-btns, #group-tools {
    flex-direction: column;
  }
}