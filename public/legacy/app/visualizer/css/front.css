@font-face {
    font-family: 'Verdana';
    src: url('../../Fonts/ttf/verdana.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanab.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanai.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanaz.ttf') format('truetype');
}

html {
    height: 100%;
    font-family: 'Verdana';
    font-size: 24px;
}

body {
    margin: 15px;
    background: linear-gradient(135deg, #212121, #540a0a);
}

#page-wrap {
    height: 100%;
    margin: 15px;
}

h2 {
    text-shadow: 0 0 25px #fff, 0 0 5px #d80000;
    margin: 15px 2px 5px 15px;
}

button,
#uploadfiles {
    font-variant: small-caps;
    font-weight: bold;
}

.title-wrap {
    margin: auto 0;
    font-size: 1.2rem;
    text-align: center;
    font-variant: small-caps;
    font-weight: bold;
    text-shadow: 0 0 25px #ffffff, 0 0 5px #d80000;
}

#form-wrap {

    width: 85%;
    height: 100%;
    margin: 15px auto;
    background: rgba(255, 255, 255, 0.5);
    padding: 25px 50px 75px;
    border: 7px groove darkgrey;
}

#heading {
    text-align: center;
    width: 75%;
    margin: auto;
}

#inner-form-wrap {
    display: flex;
}

#form-wrap label {}

#form-wrap input,
#form-wrap textarea {
    width: 100%;
    padding: 5px;
    margin: 5px 0;
}

#input-wrap {
    width: 50%;
    margin-left: 15px;
    padding-left: 15px;
}

#info-wrap input,
#input-wrap textarea {
    margin-left: 5px;
}

#info-wrap label,
#input-wrap label {
    font-weight: bold;
}

#copypaste {

}
#file-upload-innerwrap input {}

#uploadfiles {}

#filetype-error {
    color: red;
    font-size: 0.5em;
}


.invalid-input {
    outline: 1px solid #f00;
}

#dialog {
    font-size: 0.8em;
}

/* Accts CSS */ 

#info-wrap1 {
    width: 80%;
}
#info-wrap2 {
    width: 20%;
    background: #222222;
}
.error-message {
    color: red;
    font-size: 0.5em;
}
/*Added for admin page*/
#panel-wrap {
    width: 100%;    
}
#heading-wrap {
    display: flex;
}
#controls-title {
    width: 50%;
}
#project-title {
    width: 50%;
}
#panel-inner-wrap {
    display: flex;
    flex-direction: row;
}
#project-wrap {
    width: 50%;
}
#btn-wrap {
    text-align: center;
    margin: 5px;
    width: 50%;
    height: 300px;
}
#btn-wrap button {
    height: 50px;
    width: 90%;
    background: darkgrey;
    margin-bottom: 5px;
}
#scroll-list {
    margin: 5px;
    background: white;
    height: 300px;
    overflow-y: auto;
    border: 1px solid grey;
}
#scroll-list p {
    border-bottom: 1px solid lightgrey;
    margin: 2px;
    padding: 5px;
    cursor: pointer;
}

#dialog {
    font-size: 0.8em;
}

/*Accts End */ 

/* Submit CSS */ 

#info-wrap {
    width: 50%;
    margin-right: 15px;
    padding-right: 15px;
}
#file-upload-wrap {
    border: 1px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 25%;
}

#item-modal {
    height: 75%;
}

#modal-content {
    position: relative;
    height: 100%;
    text-align: center;
    font-size: 0.8em;
}

#cp-btn {}

#form-submit-wrap {
    text-align: center;
    margin-bottom: 50px;
}

#form-submit {
    width: 250px;
    height: 50px;
    margin-top: 25px;
}

#form-submit:focus {}

#tips {
    width: 80%;
    margin: auto;
}

#footer-imgs img {
    height: 75px;
}
#file-warning {
    text-align: center;
    font-size: 0.5em;
}
#modal-content button {
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 1.4em;   
}
#input-warning {
    font-variant-caps: all-petite-caps;
    font-weight: bold;
}

/*Submit End */

