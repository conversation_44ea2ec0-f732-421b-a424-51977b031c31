body {
    background: #3c0808 !important;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    height: 100vh;
    color: #ffffff;
    font-family: Ralew<PERSON>;
}

.background-model-wrapper {
    position: absolute;
}

.page-title {
    color: #0a0a0a;
    font-family: 'Audiowide', cursive;
    text-shadow: 0 0 15px #ffffff, 0 0 5px #d80000;
    font-variant: small-caps;
    font-weight: bold;
}

div.form-dark {
    background: #191c21;
    padding: 25px 50px;
    border: 1px solid #ffffff;
    box-shadow: 0 0 25px #fff, 0 0 5px #d80000;
}

.login-header-img {
    max-height: 100px;
    max-width: 100%;
}

button.btn-standard {
    background: #fff;
    border: none;
    color: #000;
    font-weight: bold;
    font-variant: small-caps;
    margin-top: 5px;
    margin-bottom: 5px;
}

button.btn-primary:hover {
    background-color: #820606;
}
button.btn-primary:focus {
    box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5);
}
.form-control:focus {
    border-color: #820606;
    box-shadow: 0 0 0 0.2rem rgba(255, 38, 38, 0.5);
}

button#form-submit, button#new-account-submit {
    font-family: 'Exo 2',sans-serif;
    font-variant-caps: petite-caps;
    font-weight: bold;
}
.alt-links {
    color: #ffffff !important;
    -webkit-transition: all 0.25s ease-out;
    -moz-transition: all 0.25s ease-out;
    -o-transition: all 0.25s ease-out;
    transition: all 0.25s ease-out;
}
.alt-links:hover {
    color: #c10000 !important;
    text-decoration: none;
}
#alert-message {
    color: red;
    text-align: center;
}
.alert-hidden {
    visibility: hidden;
}

.invalid-input {
    outline: 1px solid #f00;
}

hr.section-seperate {
    border: 1px solid #ffffff;
}

/*Dialog Popups*/
span.ui-dialog-title, span.ui-button-icon , button.ui-button {
    font-size: 1.3em;
}
/*dialog end*/

@media screen and (max-width: 575.98px) {
    body {
        background-position: center top;
    }
    div.form-dark { 
        padding: 10px;
    }
    iframe {
        position: absolute;
        left: 0 !important;
    }
    div#help-tab {
            position: absolute;
            right: 0 !important;
    }
}

