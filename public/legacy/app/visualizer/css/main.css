html {
    height: 100%;


}

body {
    font-family: 'Exo 2', sans-serif;
    font-size: 14px;
    background: url(../images/back1-min.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #000000;
    margin: 0;
    padding: 0;
    overflow: hidden;
    line-height: 1;
}



/*GuiMenu prevent text highlighting*/

.dg ul li {
    user-select: none;
}

/*end*/

#canvas1 {
    position: absolute;
    z-index: -4;
}

#stats {
    top: calc(100% - 48px) !important;
    visibility: visible;
}

#hud {
    /*    font-family: 'Exo 2',sans-serif;*/
    /*    font-weight: 100;*/
    background: #676767;
    position: fixed;
    top: 0;
    left: 0;
    width: 375px;
    min-width: 250px;
    height: 98%;
    max-height: 98%;
    border: 1px groove #000000;
    border-top: none;
    color: #ffffff;
    overflow-wrap: break-word;
    word-wrap: break-word;
    overflow-y: scroll;
    cursor: pointer;
    resize: both;
    z-index: -2;
}



/*#hud-btn {
    position: fixed;
    top: 0;
    border: 1px groove #00FFFF;
    border-bottom: 1px groove #00FFFF;
    border-radius: 5px 5px 0 0;
    width: 20%;
    padding: 5px;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 300;
    color: #00FFFF;
    cursor: pointer;
}

*/

#hud::-webkit-scrollbar {
    width: 15px;
    background: #e0e0e0;
}

#hud::-webkit-scrollbar-track {
    box-shadow: inset 0 0 15px #000000;
    border: 1px #000000;
    border-style: ridge;
}

#hud::-webkit-scrollbar-thumb {
    background: #444444;
    box-shadow: inset 0 0 5px #000000;
    border: 2px #a9a7a7;
    border-style: ridge;
    border-radius: 5px;
}

#hud::-webkit-scrollbar-thumb:hover {
    background: #585858;
}






/*HUD2*/

#hud2 {
    visibility: hidden;
    position: fixed;
    top: 0;
    left: 0;
    width: 20%;
    height: 98%;
    border: 1px groove #00FFFF;
    border-top: none;
    border-radius: 5px;
    padding: 5px;
    color: #00FFFF;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    font-size: 14px;
    cursor: pointer;
    user-select: none;
    /*line-height: 105%;*/
    z-index: 0;
    overflow: hidden;
}



/*HUD2 Header*/

#hud2-header {
    height: auto;
    font-size: 1.5em;
    text-align: center;
    border: 1px solid #00ffff;
    border-radius: 5px;
    padding-bottom: 15px;
}

#hud2-title {
    margin: 15px 0;

}

#hud2-header .form {
    display: block;
    margin-top: 0em;
    font-size: 0.5em;
    padding: 5px;
}

#customTermTextBox {
    background-color: rgba(255, 255, 255, 0.1);
    color: #00ffff;
    padding: 1px;
}



/*HUD2 InnerWrap scroll*/

#hud2-inner-wrap {
    overflow-y: auto;
}



/* width */

#hud2-inner-wrap::-webkit-scrollbar {
    width: 10px;
}




/* Track */

#hud2-inner-wrap::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}




/* Handle */

#hud2-inner-wrap::-webkit-scrollbar-thumb {
    background: #00FFFF;
    border-radius: 10px;
}



/* Handle on hover */

#hud2-inner-wrap::-webkit-scrollbar-thumb:hover {
    background: #FF1493;
}



/*HUD2 header*/

#hud2 .concept-item {
    padding: 7px 10px;
    border-bottom: 1px solid #00FFFF;
}

button#custom-search-btn {
    border: 2px solid #00ffff;
    border-radius: 4px;
    padding: 2px 10px;
    background-color: rgba(0, 0, 0, 0);
    color: #00ffff;
}



/*HUD2 Selected Item*/

#hud2 .concept-item.selected-concept {
    background-color: #00ffff;
    color: #000000;
    padding-left: 20px;
    margin-bottom: 2px;
}



/*HUD2 Custom Query*/

#add-on-term {
    visibility: hidden;
    display: none;
}


.hudCluster {
    border-bottom: 2px solid #000000;
    padding: 5px;
}

.hudSelectedCluster {
    background-color: #ffffff;
    color: #000000;
    padding: 10px;
    font-weight: bold;
}

.clusterName {
    font-size: 1.3em;
    margin: 0;
}

.score-conceptTotal {
    margin: 1px;
}

.concepts {
    margin-top: 5px;
    margin-bottom: 5px;
}

.genes {
    margin-bottom: 10px;
}

#clusterInfo {
    visibility: hidden;
    position: absolute;
    width: auto;
    max-width: 250px;
    height: 250px;
    max-height: 400px;
    border: 1px groove #00FFFF;
    border-radius: 5px;
    padding: 5px 10px 5px 10px;
    color: #00FFFF;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(0, 0, 0, 0.9);
    font-weight: 300;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    overflow-y: hidden;
    font-size: 16px;
    font-weight: lighter;
    line-height: 105%;
    cursor: pointer;
    z-index: -2;
    resize: vertical;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    min-height: 160px;
}

#popUpHeader {
    color: #FFFF00;
    border-bottom: 2px solid #FFFF00;
    padding: 5px;
    margin-bottom: 10px;
    margin-right: 20px;
}

#popUpBody {
    overflow-y: scroll;
    padding-left: 10px;
}



/* width */

#popUpBody::-webkit-scrollbar {
    width: 2px;
}




/* Track */

#popUpBody::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}




/* Handle */

#popUpBody::-webkit-scrollbar-thumb {
    background: #00FFFF;
    border-radius: 10px;
}




/* Handle on hover */

#popUpBody::-webkit-scrollbar-thumb:hover {
    background: #FF1493;
}

#selectedClusters {
    visibility: hidden;
    position: fixed;
    bottom: 0;
    left: 30%;
    max-width: 50%;
    width: auto;
    height: auto;
    margin-bottom: 15px;
    cursor: pointer;
    z-index: -2;
}

#selectedClusters button {
    margin: 2px;
    padding: 5px;
    border: 3px outset #00FFFF;
    border-radius: 5px;
    background-color: rgba(0, 255, 255, 0.2);
    color: #00FFFF;
    text-shadow: 2px 2px 1px #000000;
    font-weight: 300;
    font-size: 14px;
    font-weight: lighter;
    line-height: 105%;
    cursor: pointer;
}

input#deselectAll {
    visibility: hidden;
    position: fixed;
    bottom: 17px;
    left: 22%;
    width: auto;
    height: auto;
    padding: 5px;
    border: 5px outset #00FFFF;
    border-radius: 5px;
    background-color: rgba(0, 255, 255, 0.2);
    color: #00FFFF;
    text-shadow: 2px 2px 1px #000000;
    font-weight: 300;
    font-size: 14px;
    font-weight: lighter;
    line-height: 105%;
    cursor: pointer;
    z-index: -2;
}

#scale {
    color: #000000;
    background: #dfdfdf;
    border: 3px solid #262626;
    width: auto;
    padding: 5px;
    font-weight: bold;
    text-align: center;
}

#scale-file {
    font-weight: normal;
    font-size: 16px;
}

#scale-file span {
    margin-right: 10px;
    font-variant-caps: petite-caps;
    font-weight: bold;
}

#view {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    padding: 0;
    margin: 5px;
    font-variant-caps: petite-caps;
}

#view-title {
    font-variant-caps: petite-caps;
}

.view-btn {
    background: #676767;
    border: 5px outset #8d8d8d;
    width: 40%;
    min-width: 100px;
    margin: 0;
    padding: 5px;
}

/*HUD GeneView*/
#gene-view {
    display: none;
}

.active-view-btn {
    background: #c7c7c7;
    border: 5px inset #8d8d8d;
}

.gene-container {
    border-bottom: 2px solid #000000;
    padding: 5px;
}

.gene-container-title {
    font-size: 1.3em;
    margin: 0;
}

.gene-theme-list {
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 5px;
    height: 75px;
    overflow: hidden;
}

.gene-theme-list-selected {
    height: auto;
    max-height: 300px;
    overflow-y: auto;
}

.hudSelectedGene {
    background-color: #ffffff;
    color: #000000;
    padding: 10px;
    font-weight: bold;
    text-shadow: none;
}

.gene-theme-list::-webkit-scrollbar {
    width: 2px;
}

.gene-theme-list::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}

.gene-theme-list::-webkit-scrollbar-thumb {
    background: #000000;
    border-radius: 10px;
}

.gene-theme-list::-webkit-scrollbar-thumb:hover {
    background: #343434;
}

#genesPopupDisplay {
    visibility: hidden;
    position: absolute;
    width: 250px;
    max-width: 250px;
    height: 200px;
    max-height: 400px;
    border: 1px groove #FF1493;
    border-radius: 5px;
    padding: 10px 10px 5px 10px;
    color: #FF1493;
    font-weight: 300;
    font-size: 16px;
    font-weight: lighter;
    line-height: 105%;
    background-color: rgba(0, 0, 0, 0.9);
    /*wrap text and overflow*/
    overflow-y: hidden;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    cursor: pointer;
    z-index: -2;
    resize: vertical;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    min-height: 160px;
}

#popUpGeneHeader {
    color: #FFFF00;
    border-bottom: 2px solid #FFFF00;
    padding: 5px;
    margin-bottom: 20px;
    width: 80%;
}

#popUpGeneBody {
    overflow-y: scroll;
    /*    height: auto;*/
    /*    max-height: 110px;*/
    padding-left: 10px;
}



/* width */

#popUpGeneBody::-webkit-scrollbar {
    width: 2px;
}




/* Track */

#popUpGeneBody::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}




/* Handle */

#popUpGeneBody::-webkit-scrollbar-thumb {
    background: #FF1493;
    border-radius: 10px;
}




/* Handle on hover */

#popUpGeneBody::-webkit-scrollbar-thumb:hover {
    background: #00ffff;
}

#closeBtn {
    height: 25px;
    width: 25px;
    position: absolute;
    margin: 0;
    top: 3px;
    right: 3px;
    color: #FF1493;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(255, 20, 147, 0.5);
    padding: 5px;
    padding-top: 3px;
    padding-bottom: 17px;
    border-radius: 100%;
    text-align: center;
    font-weight: bold;
}

#clusterInfoHideBtn {
    height: 25px;
    width: 25px;
    position: absolute;
    margin: 0;
    top: 3px;
    right: 3px;
    color: #00ffff;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(0, 255, 255, 0.4);
    padding: 5px;
    padding-top: 3px;
    padding-bottom: 17px;
    border-radius: 100%;
    text-align: center;
    font-weight: bold;
}



/*Class for Theme Info Panel Btns*/


/*Concept Map Btn*/

.conceptMapOpenBtn {
    color: #00ffff;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(0, 255, 255, 0.4);
    padding: 2px;
    font-size: 0.7em;

}



/*Image Search Btn*/

.imageSearchBtn {
    color: #00ffff;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(0, 255, 255, 0.4);
    padding: 2px;
    font-size: 0.7em;
    margin-top: 2px;
}

#groupListWrap {
    visibility: hidden;
    position: fixed;
    right: 0;
    bottom: 15%;
    width: 125px;
    color: #00FFFF;
    font-size: 16px;
    /*wrap text and overflow*/
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    z-index: -3;
}

#groupList {
    height: 100px;
    max-height: 100px;
    border-radius: 5px;
    overflow-y: scroll;
}

#listHead {
    text-align: center;
    padding: 5px;
    border: 2px groove #00ffff;
    border-radius: 5px 0 0 0;
    border-bottom: none;
}



/*GroupEdit*/

#groupEdit {
    position: absolute;
    right: 0;
    bottom: 6%;
    color: #00ffff;
    cursor: pointer;
    visibility: hidden;
    z-index: -3;
}

#groupEdit div {
    border: 1px solid #00ffff;
    padding: 5px;
    margin-bottom: 2px;
}

#groupEdit div:hover {
    background-color: #00ffff;
    color: #000000;
}

#groupListItems {
    position: absolute;
    visibility: hidden;
    right: 15%;
    border: 1px groove #00FFFF;
    border-radius: 5px;
    width: auto;
    padding: 5px;
    font-size: 0.8em;
    font-weight: 300;
    color: #00FFFF;
    text-align: center;
    width: auto;
    max-width: 25%;
    height: auto;
    max-height: 20%;
    /*wrap text and overflow*/
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;
    overflow-y: auto;
    z-index: -3;
}

#groupListItems p {
    margin: 0;
    padding-bottom: 2px;
    cursor: pointer;
}

#groupList .listItem {
    display: block;
    color: inherit;
    width: 100%;
    border: 3px outset #00FFFF;
    border-radius: 5px 0 0 5px;
    margin-top: 2px;
    padding: 5px 10px;
    background-color: rgba(0, 255, 255, 0.2);
    text-shadow: 2px 2px 2px #000000;
}

#groupList .listItem:hover {
    background-color: #009999;
    color: #000000;
    font-weight: bold;
    text-shadow: none;
}




/* width */

#groupList::-webkit-scrollbar {
    width: 2px;
}




/* Track */

#groupList::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}




/* Handle */

#groupList::-webkit-scrollbar-thumb {
    background: #00FFFF;
    border-radius: 10px;
}




/* Handle on hover */

#groupList::-webkit-scrollbar-thumb:hover {
    background: #FF1493;
}

.listItemSelected {
    background-color: #00FFFF !important;
    color: #000000 !important;
    font-weight: bold !important;
    text-shadow: none !important;
}

/*End*/


/*Canvas2*/

#canvas2 {
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: -1;
    border: 1px outset #cc0066;
}

#canvas2CloseBtn {
    position: fixed;
    bottom: 5%;
    right: 1%;
    color: #FF1493;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(255, 20, 147, 0.5);
    padding: 10px;
    border-radius: 5%;
    text-align: center;
    font-weight: bold;
}

#canvas2CloseBtn:active {
    background-color: #FF69B4;
    box-shadow: 0 1px #FF1493;
    transform: translateY(2px);
    color: #000000;
}



/*Canvas3*/

#canvas3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100%;

    z-index: -1;
    /*    border: 2px outset #cc0066;*/
}









#canvas3CloseBtn {
    position: fixed;
    bottom: 5%;
    right: 1%;
    color: #FF1493;
    text-shadow: 2px 2px 1px #000000;
    background-color: rgba(255, 20, 147, 0.5);
    padding: 10px;
    border-radius: 5%;
    text-align: center;
    font-weight: bold;
}


#canvas3CloseBtn:active {
    background-color: #FF69B4;
    box-shadow: 0 1px #FF1493;
    transform: translateY(2px);
    color: #000000;
}



/*Modal Stuff*/


/*Test Button*/

#modalTestBtn {
    position: fixed;
    right: 15%;
    top: 5%;
}



/*Informational Modal*/

#info-popout {
    display: none;
    position: fixed;
    z-index: 1;
    left: 40%;
    top: 7%;
    width: 20%;
    height: auto;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    border: 1px solid #00ffff;
    margin: auto;

    text-align: center;

}

.popout-content {
    /*    color: #aaaaaa;*/
    color: #00ffff;
    padding: 20px;
}



/*Regular Modal*/

.modal {
    display: none;
    /* Hidden by default */
    position: fixed;
    /* Stay in place */
    z-index: 1;
    /* Sit on top */
    padding-top: 100px;
    /* Location of the box */
    left: 0;
    top: 0;
    width: 100%;
    /* Full width */
    height: 100%;
    /* Full height */
    overflow: auto;
    /* Enable scroll if needed */
    background-color: rgb(0, 0, 0);
    /* Fallback color */
    background-color: rgba(0, 0, 0, 0);
    /* Black w/ opacity */
}



/* Modal Content */

.modal-content {
    color: #aaaaaa;
    background-color: #000000;
    margin: auto;
    padding: 20px;
    border: 1px solid #00ffff;
    width: 50%;
    height: 30%;
}



/* The Close Button */

.close {
    color: #00ffff;
    float: right;
    font-size: 28px;
    font-weight: bold;

}


.close:hover,
.close:focus {
    color: #ff6600;
    text-decoration: none;
    cursor: pointer;
}




/*HUD Close*/

#hudClose {
    background: #676767;
    border: 5px outset #8d8d8d;
    margin: 5px;
    padding: 5px;
    font-variant-caps: petite-caps;
}

#hudClose:active {
    background: #c7c7c7;
    border: 5px inset #8d8d8d;
}



/*HUD Open*/

#hudOpen {
    display: none;
    position: absolute;
    color: #00ffff;
    border: 1px solid #00ffff;
    padding: 5px;
    margin: 5px;
    cursor: pointer;
    font-variant-caps: petite-caps;
}



/*Gene list Scores*/

.geneScore {
    float: right;
    padding-right: 15px;
    color: lightpink;
}

/*Genecentric View*/

#geneViewSelection {
    color: white;
    cursor: pointer;
    z-index: -2;
}

.geneViewSelectionHeader {
    padding: 2px 5px 13px;
    text-align: right;
    font-size: 0.8em;
    color: white;
    background: #212b31;
    border: 1px solid white;
}

.geneViewSelectionHeader span {
    padding: 1px;
}

.geneViewSelectionHeader span:hover {
    color: #212b31;
    background: white;
    border-radius: 5px;
    padding: 2px;
}

.geneViewSelectionItem {
    background: #212b31;
    padding: 10px;
    border: 1px solid white;
}

.geneViewSelectionItem:hover {
    color: #212b31;
    background: white;
    border-style: inset;
}



/*VisLockPanel*/

#visLockPanel {
    position: absolute;
    color: #00ffff;
    border: 1px solid #00ffff;
    border-radius: 5px;
    padding: 5px;
    bottom: 0;
    right: 10%;
    cursor: pointer;
}

#visLockPanel div {
    padding: 2px;
}

#visLockPanel div:hover {
    background-color: #00ffff;
    color: #000000;
}

div#quickSaveBtn {
    display: none;
    position: fixed;
    left: 21%;
    color: #00ffff;
    border: 5px outset #00ffff;
    border-radius: 5px;
    padding: 10px;
    cursor: pointer;
}

div#quickSaveBtn:hover {
    color: #000000;
    background-color: #00ffff;
    font-weight: bold;
}

div#quickSaveBtn:active {
    background-color: #009999;
    color: #000000;
    font-weight: bold;
    border: 6px inset #00ffff;
}

#dialog-confirm {
    text-align: center;
    /*    color: #00ffff;*/
}

#dialog-confirm i {
    margin-bottom: 10px;
}

span#dialog-name {
    line-height: 2;
}







/*ImageSearch CSS*/

#content-all-wrap {
    text-align: center;
    background-color: #333333;
    height: 100%;
}

#page-header {
    margin: 0;
    padding: 25px;
}

#results {
    display: flex;
    flex-flow: row wrap;
    justify-content: center;
}

.img-container {
    width: 300px;
    height: 300px;
    margin: 10px 5px;
    border: 1px solid black;
}

#results-wrap img {
    object-fit: fill;
    width: 300px;
    height: 300px;
}


/*Modal Content Focus Info Item*/

#outer-item-modal {
    display: none;
}

#item-modal {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 98%;
    height: 85%;
    background-color: grey;
    display: inline-flex;
    flex-flow: row wrap;
    justify-content: space-between;
    padding: 25px;
}

#panel-close {
    position: absolute;
    top: 25px;
    left: 95%;
}

#selectedImg-wrapper {
    width: 30%;
    margin: auto;



}


#selectedImg-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

#info-wrapper {
    text-align: left;
    word-wrap: break-word;
    overflow-y: auto;
    width: 55%;
}

#info-wrapper p {
    margin-right: 5px;
    padding-right: 5px;
}

#info-wrapper p span {
    font-weight: bold;
    font-size: 1.2em;
}

#info-wrapper p b {
    color: darkred;
}

.link-btn {
    background-color: #333333;
    color: white;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;














    margin: 4px 2px;
    cursor: pointer;
}





.link-btn a:link,
.link-btn a:visited {
    text-decoration: none;
    color: white;
    cursor: pointer;
}






/*ImageSearch End*/























#dialog {
    font-size: 0.8em;
}


#canvas2CloseBtn {
    visibility: hidden;
}


#canvas3CloseBtn {
    visibility: hidden;
}

#quickSaveBtn {
    visibility: visible;
}

#visLockPanel {
    visibility: hidden;
}


.clusterInfoBtns {
    flex-grow: 0;
    flex-shrink: 0;
    width: 85%;
}

.popup-drag-handle {
    position: absolute;
    margin: 0;
    top: 35px;
    right: 3px;






    font-size: 1.5em;
    border: groove 1px #00ffff;
    background-color: rgba(0, 255, 255, 0.35);











































    padding: 2px;

}

.gene-popup-drag-handle {
    position: absolute;


















































    margin: 0;
    top: 35px;
    right: 3px;







    font-size: 1.5em;
    border: groove 1px #FF1493;
    background-color: rgba(255, 20, 147, 0.25);
    padding: 2px;
    color: #FF1493;
    text-shadow: 2px 2px 1px #000000;
}












#popUpGeneBody span.geneScore {
    margin-left: 21px;
}