@font-face {
    font-family: 'Verdana';
    src: url('../../Fonts/ttf/verdana.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanab.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanai.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanaz.ttf') format('truetype');
}

html {
    font-family: 'Verdana';
    font-size: 24px;
}

body {
    margin: 15px;
    background: url(../images/gradient.gif);
    background-size: cover;
}

#page-wrap {
    height: 100%;
    margin: 15px;
}

#title-wrap {
    margin: auto 0;
    font-size: 1.2rem;
    text-align: center;
    font-variant: small-caps;
    font-weight: bold;
    text-shadow: 0 0 25px #ffffff, 0 0 5px #d80000;
}

#form-wrap {
    width: 85%;
    height: 100%;
    margin: 15px auto;
    background: rgba(255, 255, 255, 0.5);
    padding: 25px 50px 75px;
    border: 7px groove darkgrey;
}
#heading {
    text-align: center;
    width: 90%%;
    margin: auto;
}
#inner-form-wrap {
    display: flex;
}


#form-wrap input {
    width: 100%;
    padding: 5px;
    margin: 5px 0;
}


.invalid-input {
    outline: 1px solid #f00;
}

#form-submit-wrap {
    text-align: center;
    margin-bottom: 50px;
}
#form-submit {
    width: 250px;
    height: 50px;
    margin-top: 25px;

}
#info-wrap, #tips {
    width: 80%;
    margin: auto;
}
#info-wrap input {
    margin-left: 5px;
}
#info-wrap label {
    font-weight: bold;
}
#info-wrap li {
    margin: 15px;
}
button {
    font-variant: small-caps;
    font-weight: bold;
}
#modal-title {
    text-align: center;
    font-weight: bold;
}
#modal-content {
    text-align: center;
    font-size: 0.8em;
}
button.ui-button.ui-corner-all.ui-widget {
    font-variant: normal;
}

/*Error Message*/
.error-message {
    color: red;
    font-size: 0.5em;
}
#alert-message {
    color: red;
    text-align: center;
}
#dialog {
    font-size: 0.8em;
}
#name-warning {
    text-align: center !important;
    font-variant-caps: all-petite-caps;
    font-weight: bold;
}


/*New*/
.tabbed-content-inner {
    width: auto;
    height: auto;
    margin: auto;
    padding: 15px 50px;
}
.head-notes {
    margin: auto;
    width: 85%;
    min-width: 300px;
    flex-flow: row wrap;
    justify-content: space-evenly;
}
.note-pair {
    width: 50%;
    margin: 5px;
}
#new-form-wrap {
    margin: auto;
    width: 70%;
    min-width: 300px;
}
#new-form-wrap textarea {
    width: 100%;
    margin-left: 5px;
}
header {
    margin: 0;
}
.tabbed-content {
    padding: 0 50px;
}
body {
    height: 100%;
    background: url(../images/sphere-model.png);
    background-repeat: no-repeat;
    background-position: top center;
    background-size: contain;
}
#new-account-submit-wrap {
    text-align: center;
}
.register-header {
    width: 75%;
    text-align: center;
    margin: auto;
}
.register-header p {
    margin: 5px;
}
#new-info-wrap {
    width: 100%
}
#alternate-links {
    font-size: 0.8em;
}

body {
    overflow-y: auto;
}
.tab-heading {
    margin-bottom: 10px;
}
#notes {
    max-width: 100%;
    height: 2em;
    min-height: 1em;
}