@font-face {
    font-family: 'Verdana';
    src: url('../../Fonts/ttf/verdana.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanab.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanai.ttf') format('truetype'),
    src: url('../../Fonts/ttf/verdanaz.ttf') format('truetype');
}

html {
    font-family: 'Verdana';
    font-size: 24px;
}

body {
    margin: 15px;
    background: url(../images/gradient.gif);
    background-size: cover;
}
#page-wrap {
    height: 100%;
    margin: 15px;
}

h2 {
    text-shadow: 0 0 25px #fff, 0 0 5px #d80000;
    margin: 15px 2px 5px 15px;
}

button,
#uploadfiles {
    font-variant: small-caps;
    font-weight: bold;
}

#title-wrap {
    margin: auto 0;
    font-size: 1.2rem;
    text-align: center;
    font-variant: small-caps;
    font-weight: bold;
    text-shadow: 0 0 25px #ffffff, 0 0 5px #d80000;
}

#form-wrap {

    width: 85%;
    height: 100%;
    margin: 15px auto;
    background: rgba(255, 255, 255, 0.5);
    padding: 25px 50px 75px;
    border: 7px groove darkgrey;
}

#heading {
    text-align: center;
    width: 75%;
    margin: auto;
}

#inner-form-wrap {
    display: flex;
}

#form-wrap label {}

#info-wrap {
    width: 50%;
    margin-right: 15px;
    padding-right: 15px;
}

#form-wrap input,
#form-wrap textarea {
    width: 100%;
    padding: 5px;
    margin: 5px 0;
}

#input-wrap {
    width: 50%;
    margin-left: 15px;
    padding-left: 15px;
}

#info-wrap input,
#input-wrap textarea {
    margin-left: 5px;
}

#info-wrap label,
#input-wrap label {
    font-weight: bold;
}

#copypaste {

}

.invalid-input {
    outline: 1px solid #f00;
}


/*#input-wrap input {
    width: 100%;
    padding: 5px;
    margin: 5px 0;
}*/

#file-upload-wrap {
    border: 1px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 25%;
}

#file-upload-innerwrap input {}

#uploadfiles {}

#filetype-error {
    color: red;
    font-size: 0.5em;
}


/*Modal*/

#item-modal {
    height: 75%;
}

#modal-content {
    position: relative;
    height: 100%;
    text-align: center;
    font-size: 0.8em;
}

#cp-btn {}

#form-submit-wrap {
    text-align: center;
    margin-bottom: 50px;
}

#form-submit {
    width: 250px;
    height: 50px;
    margin-top: 25px;
}

#form-submit:focus {}

#tips {
    width: 80%;
    margin: auto;
}

#footer-imgs img {
    height: 75px;
}
#file-warning {
    text-align: center;
    font-size: 0.5em;
}
#modal-content button {
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 1.4em;   
}
#input-warning {
    font-variant-caps: all-petite-caps;
    font-weight: bold;
}