/*jquery-ui override*/
div.ui-dialog .ui-dialog-titlebar {
	padding: 0 0  0.1em 1em;
}
/*overrride end*/

div#dialog-wheel {
	padding: 5px;
	height: auto;
	width: 300px;
	overflow-y: auto;
	max-height: 600px !important;
	overflow-x: hidden;
}
button.wheel-table-btns {
    cursor: pointer;
    background-color: #373070;
    color: #fff;
}
button.wheel-table-btns:hover {
    cursor: pointer;
    background-color: #82be42;
    color: #fff;
}
#color-wheel-table > tbody > tr.table-th:hover {
  background-color: initial;
}
table#color-wheel-table tr#gene-head span {
	font-weight: normal;
} 
table#color-wheel-table {
    background-color: #eeecff;
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
}

table#color-wheel-table td,
table#color-wheel-table th,
tr#gene-head {
    border: 1px solid #ddd;
    padding: 2px 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
table#color-wheel-table td {
	font-family: monospace;
	font-size: 0.9em
}
table#color-wheel-table tr:nth-child(odd) {
    background-color: #ffffff;
}

table#color-wheel-table tr#gene-head {
	/* background-color: #000; */
  cursor: pointer;
}

table#color-wheel-table tr:hover,
table#color-wheel-table tr#gene-head:hover {
    background-color: #1b80f3;
}

/* table#color-wheel-table tr.table-th:hover {
	background-color: #000;
} */
 
table#color-wheel-table th,
tr#gene-head {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    /* color: white; */
}

/*table#color-wheel-table td.table-theme-name {
	text-overflow: ellipsis;
	white-space: nowrap;
}*/

/*dialog-wheel scrollbar*/

div#dialog-wheel::-webkit-scrollbar {
  width: 5px;
  border-radius: 15px;
}

div#dialog-wheel::-webkit-scrollbar-track {
  background: #000; 
}
 
div#dialog-wheel::-webkit-scrollbar-thumb {
  background: #fff; 
  border-radius: 15px
}

div#dialog-wheel::-webkit-scrollbar-thumb:hover {
  background: #bababa; 
}

/*scrollbar end*/