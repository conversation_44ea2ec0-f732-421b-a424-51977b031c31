<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="../../../app/assets/images/sphere.ico" type="image/ico" sizes="16x16">
	<title>CompBio - % Entities Mapped</title>
    <!--Load the AJAX API-->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
  
  <style type="text/css">
    #theme_chart_div  {
      margin: 0;
      padding: 0;
      page-break-inside: avoid;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <!--Div that will hold the bar chart-->
  <canvas id="myChart"></canvas>
<script>
document.addEventListener('DOMContentLoaded', init, false);
function init() {
  let gData = JSON.parse(sessionStorage.getItem('curveData'));
  console.log(gData);
  plotDataCurve(gData.clusterData, gData.project, gData.validTotal);
}
</script>
<script>
let ctx, myChart,debug = {};

function plotDataCurve(themeData, dataName, validTotal) {
   if(myChart !== undefined) {
      myChart.destroy();
   }
   console.log( "ready!" );

   // let total = 0;
   let allEnts = themeData.map(item => Object.keys(item.Genes)).flat();
   let total = allEnts.length;
   // themeData.forEach((item) => allEnts = allEnts.concat(Object.keys(item.Genes)));
   let allUniqueEnts = [...new Set(allEnts)];
   let totalValid = allUniqueEnts.length;
   let uniqueTotal = 0;
   let accum = [];
   let graphData = [];
   let tempArr = [];
   let highestPoint = 0;
   themeData.forEach((item, index) => {
      if(Object.keys(item.Genes).length > 0) {
         let themeGenes = Object.keys(item.Genes);
         accum = [...new Set([...accum,...themeGenes])];
         // accum = accum.concat(themeGenes);
         // console.log(accum);
         // accum = [...new Set(accum)];
         let tvc = ( accum.length/totalValid ) * 100;
         let tc = ( accum.length/validTotal ) * 100;
         // console.log(accum.length);
         // console.log(tvc)
         // console.log(tc)
         let tmp = {
            name:item.Name,
            genes:themeGenes,
            count:themeGenes.length,
            accum:accum,
            accumCount:accum.length,
            calc: tvc,
            calcAll: tc
         }
         tempArr.push(tmp);
         highestPoint = tc;
         debug = {
            "accumLen":accum.length,
            "totalValCal":tvc,
            "totalVal":tc,
            "highestPoint":highestPoint
         }
         console.log(debug);
      } else {
         console.log(themeData.length);
         console.log(index);
         console.log(highestPoint);
         let tmp = {
            name:item.Name,
            genes:null,
            count:null,
            accum:null,
            accumCount:null,
            calc: (themeData.length == index + 1) ? highestPoint : null,
            calcAll: highestPoint
         }
         tempArr.push(tmp);
      }
   });
   graphData.labels = [];
   graphData.values = [];
   graphData.valuesAll = [];
   tempArr.forEach((item,index) => {
      graphData.labels.push(item.name);
      graphData.values.push(item.calc);
      graphData.valuesAll.push(item.calcAll);
   });

   console.log(graphData.values);
   ctx = document.getElementById('myChart').getContext('2d');
   myChart = new Chart(ctx, {
      type:'line',
      data: {
         labels: graphData.labels,
         datasets: [
            {
            label: dataName,
            data: graphData.valuesAll,
            backgroundColor: 'red',
            borderColor: 'blue'
            }   
         ]
      },
      options: {
         scales: {
            y: {
               beginAtZero: 1,
               style: 'percent',
               max: 100,
               min: 0,
               title: {
                  display: true,
                  text: '% of Valid Entities Mapped' // The desired y-axis title
               }
            }
         },
         spanGaps: true,
         vAxis: {
            title: '% of Valid Entities Mapped'
         }
      }
   });
   console.log('Set! Go!');
}
function initDownloadProcess() {
    var svg = document.getElementById( 'myChart' );
    var clone = svg.cloneNode( true );
    var svgDocType = document.implementation.createDocumentType( 'svg', "-//W3C//DTD SVG 1.1//EN", "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" );
    var svgDoc = document.implementation.createDocument( 'http://www.w3.org/2000/svg', 'svg', svgDocType );
    svgDoc.replaceChild( clone, svgDoc.documentElement );
    var svgData = ( new XMLSerializer() ).serializeToString( svgDoc );
    window.opener.console.log(svgData);
    httpRequest(svgData);
}
function httpRequest( data, type = 'c' ) {
    if ( type === 'c' || type === 'r' ) {
        let header = ( type === 'r' ) ? 'Remove=' : 'PostRequest=';
        let request = header + encodeURIComponent( data );
        window.opener.console.log( request )
        let url = 'download.php';
        let xhr = new XMLHttpRequest();
        xhr.open( "POST", url, true );
        xhr.setRequestHeader( "Content-type", "application/x-www-form-urlencoded" );
        xhr.onreadystatechange = function() {
            if ( xhr.readyState == 4 && xhr.status == 200 ) {
                let response = decodeURIComponent( xhr.responseText );
                if ( response.includes( 'download' ) === true ) {
                	window.opener.downloadSvgGraphFile(response);
                	setTimeout(function() {
                		let fArray = response.split( '/' );
        				let fileName = fArray[fArray.length - 1];
                		httpRequest( fileName, 'r' );
                	},100);
                } else {
                    window.opener.console.log( response )
                }
	            /*Close Window*/
	            setTimeout( function() {
	                window.close();
	            },1000 );
            }
        }
        xhr.send( request );
    }
}
</script>
</body>
</html>