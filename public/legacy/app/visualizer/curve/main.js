"use strict";
// Load the Visualization API and the corechart package.
google.charts.load('current', {'packages':['corechart']});
// Set a callback to run when the Google Visualization API is loaded.
google.charts.setOnLoadCallback(drawThemeChart);
function drawThemeChart() {
	const themeData = JSON.parse(sessionStorage.getItem('graphData'));
	console.log(themeData);
	const title = (sessionStorage.getItem('Project') !== null) ? sessionStorage.getItem('Project') : 'CompBio-Theme Graph Export';
	document.title = title;
    var options = {
		'title': title,
		// 'width':window.innerWidth,
		// 'height':window.innerHeight,
		// 'width':'auto',
		'height':themeData.length * 100,
		'colors':['red'],
		'textStyle':{'bold':true},
		'hAxis':{'textStyle':{'bold':true},'viewWindow':{'min':0,'max':<PERSON>.ceil(themeData[0][1] / 0.9)}},
		'vAxis':{'textStyle':{'bold':true}}
		// 'is3D':true,
		// 'series':{
		// 	0:{'color':'red'}
		// }
	}
	var graph = new google.visualization.BarChart(document.getElementById('theme_chart_div'));
	var data = new google.visualization.DataTable();
	data.addColumn('string','Theme Name',);
	data.addColumn('number','Enrichment Score');
	window.opener.console.log(`Graph Rows = ${themeData.length}`);
	data.addRows(themeData);
	graph.draw(data,options);
	initDownloadProcess();
}
function initDownloadProcess() {
    var svg = document.getElementById( 'theme_chart_div' );
    var clone = svg.cloneNode( true );
    var svgDocType = document.implementation.createDocumentType( 'svg', "-//W3C//DTD SVG 1.1//EN", "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" );
    var svgDoc = document.implementation.createDocument( 'http://www.w3.org/2000/svg', 'svg', svgDocType );
    svgDoc.replaceChild( clone, svgDoc.documentElement );
    var svgData = ( new XMLSerializer() ).serializeToString( svgDoc );
    window.opener.console.log(svgData);
    httpRequest(svgData);
}
function httpRequest( data, type = 'c' ) {
    if ( type === 'c' || type === 'r' ) {
        let header = ( type === 'r' ) ? 'Remove=' : 'PostRequest=';
        let request = header + encodeURIComponent( data );
        window.opener.console.log( request )
        let url = 'download.php';
        let xhr = new XMLHttpRequest();
        xhr.open( "POST", url, true );
        xhr.setRequestHeader( "Content-type", "application/x-www-form-urlencoded" );
        xhr.onreadystatechange = function() {
            if ( xhr.readyState == 4 && xhr.status == 200 ) {
                let response = decodeURIComponent( xhr.responseText );
                if ( response.includes( 'download' ) === true ) {
                	window.opener.downloadSvgGraphFile(response);
                	setTimeout(function() {
                		let fArray = response.split( '/' );
        				let fileName = fArray[fArray.length - 1];
                		httpRequest( fileName, 'r' );
                	},100);
                } else {
                    window.opener.console.log( response )
                }
	            /*Close Window*/
	            setTimeout( function() {
	                window.close();
	            },1000 );
            }
        }
        xhr.send( request );
    }
}