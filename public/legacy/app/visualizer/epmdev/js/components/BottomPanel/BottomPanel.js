import utils from "../../../../../../src/js/utils/utils";
import router from "../../../../../../src/js/router";

const BottomPanel = (function() {
  "use strict";

  class BottomPanel {
    constructor(cfg = {}) {
      this._cfg = cfg;
      this._model = {
        hidden: false,
        totalEntitiesNum: "",
        visibleEntitiesNum: ""
      };
      this._DOM = undefined;
      this.document = cfg.document || document;
      this._handlers = utils.bindMethods([this._handleHide], this, {});

      this.init();
    }

    generateHTML() {
      let html = "";
      let info = this.generateInfo();
      html += `
        <div class="bottom-bar">
            <span class="js-bottom-bar-text bottom-bar-text">${info}</span> 
            <div class="js-hide-button hide-button">
                <span class="hide-button-text">Hide</span>
                <img class="arrow-image" src="/${router.prefix}imgs/icons/arrow-down.svg">
            </div>
        </div>
      `;
      return html;
    }

    generateInfo() {
      const {
        totalEntitiesNum,
        visibleEntitiesNum,
        filterName,
        commonConceptsNum,
        uniqueFilterConceptsNum,
        uniqueProjectConceptsNum
      } = this._model;
      let text = `Total Entities: ${totalEntitiesNum} &emsp;
       Visible Entities: ${visibleEntitiesNum}`;
      if (filterName) {
        text += ` &emsp; | &emsp;
         Filter "${filterName}"&emsp;
         #concepts in common: ${commonConceptsNum} &emsp;
         #unique project concepts: ${uniqueProjectConceptsNum} &emsp;
         #unique filter concepts: ${uniqueFilterConceptsNum}`;
      }
      return text;
    }

    _handleHide() {
      this._model.hidden = !this._model.hidden;
      const hideButtonText = this._model.hidden ? "Show" : "Hide";
      const offset = this._model.hidden ? "0" : "32px";
      this._DOM.querySelector(".hide-button-text").textContent = hideButtonText;
      this._DOM
        .querySelector(".hide-button .arrow-image")
        .classList.toggle("inverted");
      this._DOM.classList.toggle("animated-hidden");

      const notesTab = this.document.getElementById("notes-tab");
      if (notesTab) {
        notesTab.style.bottom = offset;
      }
      const helpTab = this.document.getElementById("help-tab");
      if (helpTab) {
        helpTab.style.bottom = offset;
      }
    }

    set entitiesInfo({ totalEntitiesNum, visibleEntitiesNum }) {
      this._model = Object.assign(this._model, {
        totalEntitiesNum,
        visibleEntitiesNum
      });
      this._DOM.querySelector(
        ".js-bottom-bar-text"
      ).innerHTML = this.generateInfo();
    }

    set filterInfo({
      filterName,
      commonConceptsNum,
      uniqueProjectConceptsNum,
      uniqueFilterConceptsNum
    }) {
      this._model = Object.assign(this._model, {
        filterName,
        commonConceptsNum,
        uniqueProjectConceptsNum,
        uniqueFilterConceptsNum
      });
      this._DOM.querySelector(
        ".js-bottom-bar-text"
      ).innerHTML = this.generateInfo();
    }

    init() {
      const bottomPanel = this.generateHTML();
      this._cfg.container.insertAdjacentHTML("beforeend", bottomPanel);
      this._DOM = this._cfg.container.querySelector(".bottom-bar");
      utils.addOrRemoveListeners(
        {
          ".js-hide-button": {
            click: [this._handlers.handleHide]
          }
        },
        true,
        this._DOM
      );
    }
  }

  return BottomPanel;
})();

export default BottomPanel;
