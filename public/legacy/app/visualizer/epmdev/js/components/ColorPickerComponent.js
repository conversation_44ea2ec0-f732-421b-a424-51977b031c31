const DEFAULT_THEME_COLOR = '#000015';

class ColorPickerComponent {
  constructor(cfg) {
    this._cfg = cfg;

    this.wasSelected = false;
    this._currentValue = DEFAULT_THEME_COLOR;
    this.clickElement = null;

    this._componentUpdateTimeout = null;
    this._componentOnChangeTimeout = null;

    this.init();
  }

  init() {
    this._component = new dat.controllers.ColorController(this, 'currentValue');
    this._component.domElement.classList.add('color-picker');
    this._component.domElement.classList.add('hidden');

    this._cfg.container.appendChild(this._component.domElement);
  }

  scheduleComponentUpdate() {
    clearTimeout(this._componentUpdateTimeout);
    this._componentUpdateTimeout = setTimeout(() => {
      this.component.updateDisplay();
    })
  }

  scheduleOnChangeCallback() {
    clearTimeout(this._componentOnChangeTimeout);
    this._componentOnChangeTimeout = setTimeout(() => {
      if (typeof this.onChange === 'function') {
        this.onChange(this.value);
      }
    })
  }

  get component() {
    return this._component;
  }

  set visible(value) {
    if (!value) {
      this._component.domElement.classList.add('hidden');
    } else {
      this._component.domElement.classList.remove('hidden');
    }
  }

  set position({x, y}) {
    this._component.domElement.style.left = `${x}px`;
    this._component.domElement.style.top = `${y}px`;
  }

  set onChange(callBack) {
    this._onChangeCb = callBack;
    this._component.onChange(callBack);
  }

  get value() {
    return this._currentValue;
  }

  set value(value) {
    this._currentValue = value;
    this.scheduleComponentUpdate();
    return true;
  }

  get currentValue() {
    return this._currentValue;
  }

  set currentValue(value) {
    this._currentValue = value;
    this.scheduleOnChangeCallback();
    return true;
  }
}

export default ColorPickerComponent;
