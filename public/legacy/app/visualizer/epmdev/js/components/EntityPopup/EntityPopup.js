import utils from "../../../../../../src/js/utils/utils";
import Popup from "../Popup/Popup";

const ICONS = {
  NEW_WINDOW: `<i class="icon-new-window js-new-window" title="View on NCBI"></i>`
};

const EntityPopup = (function() {
  "use strict";

  class EntityPopup extends Popup {
    constructor(cfg) {
      super(cfg);
      this._model.filter = [];
      this.entityRows = [];
      this._handlers = utils.bindMethods(
        [
          this._handleCloseBtn,
          this._handleEntityToThemeBtn,
          this._handleEntityToAllBtn,
          this._handleEntityRowClick,
          this._handleOpenNCBI
        ],
        this
      );
      this.init();
    }

    generateHTML({ entity }) {
      let html = "";
      html += `
          <div class="popup-header gene-popup-drag-handle">
              <div class="popup-title allow-user-select">
                  ${entity}
                  ${ICONS.NEW_WINDOW}
              </div>
              <input 
                  type="button" 
                  value="X" 
                  class="close-btn js-close-btn" 
              />
          </div>
          <div class="button-wrapper">
              <button class="entity-button js-entity-to-theme selected">Entity to Theme</button>
              <button class="entity-button js-entity-to-all">Entity to All</button>
          </div>
          <div class="popup-table allow-user-select js-entity-table">
          </div>
      `;

      return html;
    }

    set position({ x, y }) {
      this._model.position = { x, y };
      this._cfg.container.style.left = x + "px";
      this._cfg.container.style.top = y + "px";
    }

    get position() {
      return this._model.position;
    }

    set visible(v) {
      this._model.visible = v;
      if (v) {
        this._cfg.container.classList.remove("hidden");
      } else {
        this._cfg.container.classList.add("hidden");
      }
    }

    get visible() {
      return this._model.visible;
    }

    _handleCloseBtn(e) {
      e.stopPropagation();
      this.hide({ invokeGlobalCb: true });
    }

    set filter({ concepts, mode }) {
      this._model.filter = { concepts, mode };
      switch (mode) {
        case this._cfg.FILTER_MODES.DIFFERENCE: {
          this.entityRows.forEach(row => {
            if (concepts.indexOf(row.dataset.entity) === -1) {
              row.classList.remove("disabled");
            } else {
              row.classList.add("disabled");
            }
          });
          break;
        }
        case this._cfg.FILTER_MODES.INTERSECTION: {
          this.entityRows.forEach(row => {
            if (concepts.indexOf(row.dataset.entity) !== -1) {
              row.classList.remove("disabled");
            } else {
              row.classList.add("disabled");
            }
          });
          break;
        }
      }
    }

    selectBtn(button) {
      this.buttons.forEach(btn => btn.classList.remove("selected"));
      button.classList.add("selected");
    }

    _handleEntityToThemeBtn(e) {
      if (e) {
        this.selectBtn(e.target);
      }
      const { entity, themes, selectedThemes } = this._data;
      let tableData = [];
      if (selectedThemes instanceof Array) {
        const themeIds = [];
        selectedThemes.forEach(themeName => {
          themeIds.push(themes.findIndex(theme => theme.Name === themeName));
        });
        themeIds.forEach(themeId => {
          const conceptsMap = themes[themeId].Genes[entity].Concepts;
          const conceptsNames = Object.keys(conceptsMap);
          const conceptsResult = conceptsNames.map(concept => ({
            name: concept,
            Score: Number(conceptsMap[concept]).toFixed(2),
            themeName: themes[themeId].Name
          }));
          tableData = [...tableData, ...conceptsResult];
        });
      } else {
        const conceptsMap = themes[selectedThemes].Genes[entity].Concepts;
        const conceptsNames = Object.keys(conceptsMap);
        tableData = conceptsNames.map(concept => ({
          name: concept,
          Score: Number(conceptsMap[concept]).toFixed(2)
        }));
      }
      tableData = tableData.sort((a, b) => {
        return b.Score - a.Score;
      });

      this.removeEventListeners();
      this._cfg.container.querySelector(
        ".js-entity-table"
      ).innerHTML = this._generateTableHTML(tableData);
      this.entityRows = [
        ...this._cfg.container.querySelectorAll(".popup-table tr")
      ];
      this.addEventListeners();
    }

    _handleEntityRowClick(e) {
      const concept = e.currentTarget.dataset.entity;
      const { selectedCluster, entity } = this._data;
      let selectedObjConcepts = Object.prototype.hasOwnProperty.call(
        selectedCluster.userData,
        "combinedConcepts"
      )
        ? selectedCluster.userData.combinedConcepts
        : selectedCluster.userData.Concepts;
      let abstractData = {
        Key: this._cfg.key,
        a: entity,
        b: concept,
        ThemeConcepts: selectedObjConcepts
      };
      localStorage.setItem("abstractData", JSON.stringify(abstractData));
      const link = this._cfg.composeAbstractLink({
        geneId: entity,
        conceptName: concept
      });
      window.open(link);
    }

    _handleEntityToAllBtn(e) {
      this.selectBtn(e.target);
      const { entity, entities } = this._data;
      let obj = entities.find(o => o.Gene_ID === entity);
      if (obj === undefined) {
        // log("gene cannot be found");
      } else {
        obj = Object.values(obj);
        obj = obj.reduce((acc, item) => {
          if (Array.isArray(item)) {
            acc.push({
              name: item[0],
              Score: Number(item[1]).toFixed(2)
            });
          }
          return acc;
        }, []);

        this.removeEventListeners();
        this._cfg.container.querySelector(
          ".js-entity-table"
        ).innerHTML = this._generateTableHTML(obj);
        this.entityRows = Array.from(
          this._cfg.container.querySelectorAll(".popup-table tr")
        );
        this.addEventListeners();
      }
    }

    _handleOpenNCBI() {
      window.open(
        `https://www.ncbi.nlm.nih.gov/gene?term=(${encodeURIComponent(
          this._data.entity.toUpperCase()
        )}%5BGene%20Name%5D)%20AND%20homo%20sapience%5BOrganism%5D`
      );
    }

    _generateTableHTML(data) {
      let tbody = "";
      const { mode, concepts } = this._model.filter;
      let disabled;
      for (let ent of data) {
        switch (mode) {
          case this._cfg.FILTER_MODES.DIFFERENCE: {
            disabled = concepts.indexOf(ent.name) !== -1 ? "disabled" : "";
            break;
          }
          case this._cfg.FILTER_MODES.INTERSECTION: {
            disabled = concepts.indexOf(ent.name) === -1 ? "disabled" : "";
            break;
          }
          default: {
            disabled = "";
          }
        }
        tbody += `
          <tr data-entity="${ent.name}" class="js-concept-row ${disabled}">
                <td>${ent.name}</td>
                <td class="concept-score score-number">${ent.Score}</td>
                ${ent.themeName ? `<td>${ent.themeName}</td>` : ""}
          </tr>
        `;
      }

      let html = `
          <table class="popup-table allow-user-select ${
            data[0].themeName ? "col-3" : ""
          }">
              <thead>
                  <th>Concept</th>
                  <th class="concept-score">Score</th>
                  ${data[0].themeName ? `<th>Theme</th>` : ""}
              </thead>
              <tbody>
                  ${tbody}
              </tbody>
          </table>
      `;
      return html;
    }

    show(data) {
      this._data = data;
      this.removeEventListeners();
      this._cfg.container.innerHTML = this.generateHTML(data);
      this.hideCb = data.hideCb;
      this.buttons = Array.from(
        this._cfg.container.querySelectorAll(".button-wrapper .entity-button")
      );
      this.position = data.position;
      this._handlers.handleEntityToThemeBtn();
      this.addEventListeners();
      this.minHeight = data.minHeight;
      this.visible = true;
    }

    addEventListeners() {
      utils.addOrRemoveListeners(
        {
          ".js-close-btn": {
            click: [this._handlers.handleCloseBtn]
          },
          ".js-entity-to-theme": {
            click: [this._handlers.handleEntityToThemeBtn]
          },
          ".js-entity-to-all": {
            click: [this._handlers.handleEntityToAllBtn]
          },
          ".js-concept-row": {
            click: [this._handlers.handleEntityRowClick]
          },
          ".js-new-window": {
            click: [this._handlers.handleOpenNCBI]
          }
        },
        true,
        this._cfg.container
      );
    }

    removeEventListeners() {
      utils.addOrRemoveListeners(
        {
          ".js-close-btn": {
            click: [this._handlers.handleCloseBtn]
          },
          ".js-entity-to-theme": {
            click: [this._handlers.handleEntityToThemeBtn]
          },
          ".js-entity-to-all": {
            click: [this._handlers.handleEntityToAllBtn]
          },
          ".js-concept-row": {
            click: [this._handlers.handleEntityRowClick]
          },
          ".js-new-window": {
            click: [this._handlers.handleOpenNCBI]
          }
        },
        false,
        this._cfg.container
      );
    }

    init() {
      this.visible = this._model.visible;
    }
  }

  return EntityPopup;
})();

export default EntityPopup;
