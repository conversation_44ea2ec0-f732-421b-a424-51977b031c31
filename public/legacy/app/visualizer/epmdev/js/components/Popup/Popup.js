const Popup = (function () {
  'use strict';

  class Popup {
    constructor (cfg) {
      this._cfg = cfg;
      this._model = {
        visible: false,
        position: undefined
      };
      this._DOM = undefined;
      this.hideCb = undefined;

      this.hide = this.hide.bind(this);
    }

    set position ({x, y}) {
      if (x < 0) {
        x = 0;
      }
      if (y < 0) {
        y = 0;
      }
      this._model.position = {x, y};
      this._cfg.container.style.left = x + 'px';
      this._cfg.container.style.top = y + 'px';
    }

    get position () {
      const x = this._cfg.container.style.left.slice(0, -2);
      const y = this._cfg.container.style.top.slice(0, -2);
      const position = {x, y};
      this._model.position = position;
      return position;
    }

    set visible (v) {
      this._model.visible = v;
      if (v) {
        this._cfg.container.classList.remove('hidden')
      } else {
        this._cfg.container.classList.add('hidden')
      }
    }

    get visible () {
      return this._model.visible;
    }

    get height () {
      return getComputedStyle(this._cfg.container, null).height.slice(0, -2);
    }

    set minHeight (minHeight) {
      this._cfg.container.style.minHeight = `${minHeight}px`;
    }

    hide ({invokeGlobalCb} = {invokeGlobalCb: false}) {
      if (this.visible) {
        this.visible = false;
        if (invokeGlobalCb) {
          this.hideCb();
        }
      }
    }
  }

  return Popup;
})();

export default Popup;
