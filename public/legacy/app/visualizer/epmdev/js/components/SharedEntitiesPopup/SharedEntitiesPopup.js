import utils from "../../../../../../src/js/utils/utils";
import Popup from "../Popup/Popup";

const SharedEntitiesPopup = (function() {
  "use strict";

  class SharedEntitiesPopup extends Popup {
    constructor(cfg) {
      super(cfg);

      this._handlers = utils.bindMethods(
        [this._handleCloseBtn, this._handleEntityClick],
        this
      );
      this.init();
    }

    generateHTML({ selectedCluster, clusterGroup }) {
      let html = "",
        list = "";
      let sharedGenesTemp = [];
      let listLen = selectedCluster.userData.sharedGenes.length;
      html += `
          <div class="popup-header gene-popup-drag-handle">
              <div class="popup-title allow-user-select">
                  ${
                    clusterGroup.getObjectByName(
                      selectedCluster.userData.endpoints[0]
                    ).userData.Name
                  } & 
                  ${
                    clusterGroup.getObjectByName(
                      selectedCluster.userData.endpoints[1]
                    ).userData.Name
                  }
              </div>
              <input 
                  type="button" 
                  value="X" 
                  class="close-btn js-close-btn" 
              />
          </div>
          <div class="popup-info">
              <p class="info-panel-txt">
                <span class="info-panel-description">Shared Entities: </span>${listLen}
              </p> 
          </div>
          <div class="popup-table allow-user-select">
      `;
      if (listLen <= 0) {
        list += "No Shared Entities";
      } else {
        for (let i = 0; i < selectedCluster.userData.sharedGenes.length; i++) {
          let tempObj = {
            Gene: selectedCluster.userData.sharedGenes[i],
            Score1: selectedCluster.userData.sharedGeneScores[i].score1,
            Score2: selectedCluster.userData.sharedGeneScores[i].score2,
            LowScore:
              Number(selectedCluster.userData.sharedGeneScores[i].score1) <
              Number(selectedCluster.userData.sharedGeneScores[i].score2)
                ? selectedCluster.userData.sharedGeneScores[i].score1
                : selectedCluster.userData.sharedGeneScores[i].score2
          };
          sharedGenesTemp.push(tempObj);
        }
        sharedGenesTemp.sort(function(a, b) {
          return b.LowScore - a.LowScore;
        });
      }
      list += `
            <table>
                <thead>
                    <tr>
                        <th>Entity</th>
                        <th class="score-number">
                            ${
                              clusterGroup.getObjectByName(
                                selectedCluster.userData.endpoints[0]
                              ).userData.Name
                            }</th>
                        <th class="score-number">
                            ${
                              clusterGroup.getObjectByName(
                                selectedCluster.userData.endpoints[1]
                              ).userData.Name
                            }</th>
                    </tr>
                </thead>
                <tbody>`;
      for (let i = 0; i < listLen; i++) {
        list += `
            <tr data-entity="${sharedGenesTemp[i].Gene}" class="js-entity-row">
                <td>${sharedGenesTemp[i].Gene}</td>
                <td class="score-number">${parseFloat(
                  sharedGenesTemp[i].Score1
                ).toFixed(1)}</td>
                <td class="score-number">${parseFloat(
                  sharedGenesTemp[i].Score2
                ).toFixed(1)}</td>
            </tr>
        `;
      }
      list += "</tbody></table>";
      html += `
              ${list}
          </div>
      `;
      return html;
    }

    get theme() {
      return [];
    }

    _handleCloseBtn(e) {
      e.stopPropagation();
      this.hide({ invokeGlobalCb: true });
    }

    _handleEntityClick(e) {
      const entity = e.currentTarget.dataset.entity;
      if (typeof this._cfg.onEntityClick === "function") {
        this._cfg.onEntityClick(entity, this);
      }
    }

    show(data) {
      this._data = data;
      this.removeEventListeners();
      this._cfg.container.innerHTML = this.generateHTML(data);
      this.hideCb = data.hideCb;
      this.themes = [
        data.selectedCluster.userData.endpoints[0],
        data.selectedCluster.userData.endpoints[1]
      ];
      this.addEventListeners(data);
      this.position = data.position;
      this.visible = true;
    }

    addEventListeners() {
      utils.addOrRemoveListeners(
        {
          ".js-close-btn": {
            click: [this._handlers.handleCloseBtn]
          },
          ".js-entity-row": {
            click: [this._handlers.handleEntityClick]
          }
        },
        true,
        this._cfg.container
      );
    }

    removeEventListeners() {
      utils.addOrRemoveListeners(
        {
          ".js-close-btn": {
            click: [this._handlers.handleCloseBtn]
          },
          ".js-entity-row": {
            click: [this._handlers.handleEntityClick]
          }
        },
        false,
        this._cfg.container
      );
    }

    init() {
      this.visible = this._model.visible;
    }
  }

  return SharedEntitiesPopup;
})();

export default SharedEntitiesPopup;
