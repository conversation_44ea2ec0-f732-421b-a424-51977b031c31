import utils from "../../../../../../src/js/utils/utils";
import Popup from "../Popup/Popup";

/* global eventFilterController */

const ThemePopup = (function() {
  "use strict";

  class ThemePopup extends Popup {
    constructor(cfg) {
      super(cfg);
      this._handlers = utils.bindMethods(
        [
          this._handleCloseBtn,
          this._handleImageSearch,
          this._handleOpenConcepts,
          this._handleEntityRowClick
        ],
        this
      );
      this.init();
    }

    generateHTML({ selectedCluster }) {
      let html = "";
      let geneList = this._generateTableHTML();
      const shouldDisableConceptMap =
        selectedCluster.userData.Concepts.length < this._cfg.minimumConceptsNum;
      const conceptMapBtnDisabled = shouldDisableConceptMap ? "disabled" : "";
      const conceptMapBtnTitle = conceptMapBtnDisabled
        ? `Theme should have minimum ${this._cfg.minimumConceptsNum} concepts`
        : "";
      html += `
        <div class="popup-header">
            <div class="popup-title">
                ${selectedCluster.userData.Alias ||
                  selectedCluster.userData.Name}
            </div>
            <input 
                type="button" 
                value="X"
                class="close-btn js-close-btn"
            >
        </div>
        <div class="theme-popup-buttons-wrapper">
            <input 
                type="button"
                value="Concept Map"
                title="${conceptMapBtnTitle}"
                ${conceptMapBtnDisabled}
                class="theme-popup-button js-open-concept-map"
            />
            <input 
                type="button" 
                value="Image Search" 
                class="theme-popup-button js-image-search" 
            />
        </div>
        <div class="popup-info">
            <p class="info-panel-txt">
                <span class="info-panel-description">Score: </span>${
                  selectedCluster.userData.Score
                }
            </p>
            <p class="info-panel-txt">
                <span class="info-panel-description">Concept Total: </span>${
                  selectedCluster.userData.Concept_Total
                }
            </p>
            <p class="info-panel-txt">
                <span class="info-panel-description">Entity Total: </span>${
                  selectedCluster.userData.Gene_Total
                }
            </p>
        </div>
        <div class="popup-table allow-user-select">
            <table>${geneList}</table>
        </div>
      `;
      return html;
    }

    _handleCloseBtn(e) {
      e.stopPropagation();
      this.hide({ invokeGlobalCb: true });
    }

    _handleOpenConcepts() {
      const { openConceptMap, selectedCluster } = this._data;
      openConceptMap(selectedCluster.name);
    }

    _handleImageSearch() {
      const { imageSearch, selectedCluster } = this._data;
      imageSearch(selectedCluster.userData.Concepts);
    }

    _handleEntityRowClick(e) {
      const entity = e.currentTarget.dataset.entity;
      eventFilterController(entity, this);
    }

    _generateTableHTML() {
      let i;
      let gene,
        expVal,
        geneScore,
        geneList = "";
      const { selectedCluster } = this._data;
      geneList +=
        '<thead class="t-head"><th>Entity</th><th class="entity-score">Score</th><th>Exp Value</th></thead><tbody>';
      for (i = 0; i < selectedCluster.userData.Genes.length; i++) {
        gene = selectedCluster.userData.Genes[i];
        expVal = window.projectDescription.entitiesMap[gene];
        geneScore = (
          parseFloat(selectedCluster.userData.Gene_Scores[i]) || 0
        ).toFixed(2);
        geneList += `
            <tr data-entity="${gene}" class="js-entity-row">
                <td>${gene}</td>
                <td class="score-number">${geneScore}</td>
                <td>${expVal}</td>
            </tr>`;
      }
      geneList += "</tbody>";
      return geneList;
    }

    set title(name) {
      this._cfg.container.querySelector(".popup-title").textContent = name;
    }

    show(data) {
      this._data = data;
      this.removeEventListeners();
      this._cfg.container.innerHTML = this.generateHTML(data);
      this.hideCb = data.popupClusterInfoHide;
      this.themes = data.selectedCluster.userData.indexKey;
      this.addEventListeners(data);
      this.position = data.position;
      this.visible = true;
    }

    addEventListeners() {
      utils.addOrRemoveListeners(
        {
          ".js-close-btn": {
            click: [this._handlers.handleCloseBtn]
          },
          ".js-open-concept-map": {
            click: [this._handlers.handleOpenConcepts]
          },
          ".js-image-search": {
            click: [this._handlers.handleImageSearch]
          },
          ".js-entity-row": {
            click: [this._handlers.handleEntityRowClick]
          }
        },
        true,
        this._cfg.container
      );
    }

    removeEventListeners() {
      utils.addOrRemoveListeners(
        {
          ".js-close-btn": {
            click: [this._handlers.handleCloseBtn]
          },
          ".js-open-concept-map": {
            click: [this._handlers.handleOpenConcepts]
          },
          ".js-image-search": {
            click: [this._handlers.handleImageSearch]
          },
          ".js-entity-row": {
            click: [this._handlers.handleEntityRowClick]
          }
        },
        false,
        this._cfg.container
      );
    }

    init() {
      this.visible = this._model.visible;
    }
  }

  return ThemePopup;
})();

export default ThemePopup;
