import utils from '../../../../../../src/js/utils/utils.js';
import UIPanelTabs from './UIPanelTabs.js';

const UIPanel = (function () {
  'use strict';

  const EDGE_SNAP_DIST = 10;
  const THROW_GESTURE_THRESHOLD = 8.3;
  const FRAME_TIME = 16;
  const DRAG = .9;
  const MAX_V = 60;

  const COMPONENTS = {
    tabs: UIPanelTabs
  };

  class UIPanel {
    constructor (cfg) {
      this._cfg = cfg;
      this._model = {
        dragging: false,
        vx: 0,
        vy: 0,
        prevTime: 0,
        time: 0,
        mouse: {
          x: 0,
          y: 0,
          prevX: 0,
          prevY: 0
        },
        dragOffset: {
          x: 0,
          y: 0
        }
      };
      this._components = [];
      this._DOM = undefined;
      this._handlers = utils.bindMethods([
        this._mouseUp,
        this._mouseDown,
        this._mouseMove,
        this._animateInertia,
        this._panelMdown
      ], this, undefined, {});
      this.init();
    }

    _animateInertia () {
      if (Math.abs(this._model.vx) > 0 || Math.abs(this._model.vy) > 0) {
        requestAnimationFrame(this._handlers.animateInertia);
      } else {
        return;
      }
      let bcr = this._DOM.getBoundingClientRect();
      this._model.vx *= DRAG;
      this._model.vy *= DRAG;
      let x = bcr.left + this._model.vx;
      let y = bcr.top + this._model.vy;
      this.x = x;
      this.y = y;
      if (Math.abs(this._model.vx) <= .1) {
        this._model.vx = 0;
      }
      if (Math.abs(this._model.vy) <= .1) {
        this._model.vy = 0;
      }
    }

    _initComponents () {
      let i;
      for (i in this._cfg.components) {
        if (typeof COMPONENTS[i].init === 'function') {
          COMPONENTS[i].init(this._DOM, this._cfg.components[i]);
        }
      }
    }

    _buildPanel () {
      let i;
      let cid = 0;
      let component;
      let html = `<div class="ui-panel ${this._cfg.classString}">
      <div class="ui-panel__drag-header"></div>
      <div class="ui-panel__header">${this._cfg.header|| ''}</div>`;
      for (i in this._cfg.components) {
        component = new COMPONENTS[i](this._cfg.components[i], cid);
        this._components.push(component);
        html += component.bodyConstructor();
        cid++;
      }
      html += '</div>';
      let host = document.createElement('div');
      host.innerHTML = html;
      this._DOM = host.firstElementChild;
      this._cfg.container.appendChild(this._DOM);
      for (i = 0; i < this._components.length; i++) {
        this._components[i].init(this._DOM);
      }
    }

    _mouseDown (e) {
      if (e.target !== e.currentTarget) {
        return;
      }
      let bcr = this._DOM.getBoundingClientRect();
      this._model.dragging = true;
      this._model.dragOffset.x = e.clientX - bcr.left;
      this._model.dragOffset.y = e.clientY - bcr.top;
      this._model.vx = 0;
      this._model.vy = 0;
      this._model.mouse.prevX = e.clientX;
      this._model.mouse.prevY = e.clientX;
      this._model.mouse.x = e.clientX;
      this._model.mouse.y = e.clientY;
      this._model.prevTime = performance.now();
      this._model.time = this._model.prevTime;
    }

    _mouseUp (e) {
      let dt = this._model.time - this._model.prevTime;
      let motionReleaseTime = performance.now() - this._model.time;
      this._model.dragging = false;
      if (motionReleaseTime === 0 || motionReleaseTime > THROW_GESTURE_THRESHOLD) {
        return;
      }
      let dx = this._model.mouse.x - this._model.mouse.prevX;
      let dy = this._model.mouse.y - this._model.mouse.prevY;
      this._model.mouse.x = 0;
      this._model.mouse.y = 0;
      this._model.mouse.prevX = 0;
      this._model.mouse.prevY = 0;
      this._model.vx = Math.max(-MAX_V, Math.min(MAX_V, dx * FRAME_TIME / dt));
      this._model.vy = Math.max(-MAX_V, Math.min(MAX_V, dy * FRAME_TIME / dt));
      this._model.time = 0;
      this._model.prevTime = 0;
      this._animateInertia();
    }

    _mouseMove (e) {
      if (!this._model.dragging) {
        return;
      }
      e.preventDefault();
      this._model.mouse.prevX = this._model.mouse.x;
      this._model.mouse.prevY = this._model.mouse.y;
      this._model.mouse.x = e.clientX;
      this._model.mouse.y = e.clientY;
      this._model.prevTime = this._model.time;
      this._model.time = performance.now();
      this.x = e.clientX - this._model.dragOffset.x;
      this.y = e.clientY - this._model.dragOffset.y;
    }

    _panelMdown (e) {
      // visualizer listens clicks on document
      // in order to prevent click-through-panel effect
      // catch every click and prevent further propagation
      // can be removed once visualizer starts handling document clicks properly
      // or moves away from such approach completely.
      e.stopPropagation();
    }

    set x (x) {
      let bcr = this._DOM.getBoundingClientRect();
      let rightEdge = window.innerWidth - bcr.width;
      x = Math.max(0, Math.min(x, rightEdge));
      if (x <= EDGE_SNAP_DIST) {
        x = 0;
      }
      if (x >= rightEdge - EDGE_SNAP_DIST) {
        x = rightEdge;
      }
      this._DOM.style.left = `${x}px`;
    }

    set y (y) {
      let bcr = this._DOM.getBoundingClientRect();
      let bottomEdge = window.innerHeight - bcr.height;
      y = Math.max(0, Math.min(y, bottomEdge));
      if (y <= EDGE_SNAP_DIST) {
        y = 0;
      }
      if (y >= bottomEdge - EDGE_SNAP_DIST) {
        y = bottomEdge;
      }
      this._DOM.style.top = `${y}px`;
    }

    init () {
      this._buildPanel();
      if (this._cfg.noDragHeader) {
        this._DOM.querySelector('.ui-panel__drag-header').classList.add('hidden');
      }
      this.bindListeners();
    }

    getComponent (idx) {
      return this._components[idx];
    }

    setHeader (header = '') {
      let headerDOM = this._DOM.querySelector('.ui-panel__header');
      header = utils.escapeHTML(header);
      headerDOM.textContent = header;
      headerDOM.title = header;
    }

    bindListeners () {
      if (!this._cfg.noDragHeader) {
        window.addEventListener('mouseup', this._handlers.mouseUp);
        window.addEventListener('mousemove', this._handlers.mouseMove);
        this._DOM.querySelector('.ui-panel__drag-header').addEventListener('mousedown', this._handlers.mouseDown);
      }
      this._DOM.addEventListener('mousedown', this._handlers.panelMdown);
    }

    unbindListeners () {
      if (!this._cfg.noDragHeader) {
        window.removeEventListener('mousemove', this._handlers.mouseMove);
        window.removeEventListener('mouseup', this._handlers.mouseUp);
        this._DOM.querySelector('.ui-panel__drag-header').removeEventListener('mousedown', this._handlers.mouseDown);
      }
      this._DOM.removeEventListener('mousedown', this._handlers.panelMdown);
    }
  }

  return UIPanel;
})();

export default UIPanel;
