import utils from '../../../../../../src/js/utils/utils.js';
import UIPanelComponent from './UIPanelComponent.js';

const UIPanelTabs = (function () {
  'use strict';

  class UIPanelTabs extends UIPanelComponent{
    constructor (cfg, cid) {
      super(cfg, cid);
      this._tabsContainerDOM = undefined;
      this._tabsContentContainerDOM = undefined;
      this._handlers = utils.bindMethods([
        this._tabClick
      ], this, {});
    }

    _tabClick (e) {
      let tabIdx = parseInt(e.currentTarget.dataset.tabIdx, 10);
      let tabs = this._tabsContainerDOM.querySelectorAll('.tab');
      let tabsContent = this._tabsContentContainerDOM.querySelectorAll('.ui-panel__tabs-content');
      let i;
      for (i = 0; i < tabs.length; i++) {
        if (i === tabIdx) {
          continue;
        }
        tabs[i].classList.remove('tab--active');
        tabsContent[i].classList.add('hidden');
      }
      tabs[tabIdx].classList.toggle('tab--active');
      tabsContent[tabIdx].classList.toggle('hidden');
    }

    get tabsContentContainer () {
      return this._tabsContentContainerDOM;
    }

    bodyConstructor () {
      let i;
      let html = `<div class="ui-panel__tabs-container" data-component-id="${this._cid}">`;
      let tabsContentHtml = `<div class="ui-panel__tabs-content-container" data-component-id="${this._cid}">`;
      let tabClassModifier;
      let contentClassModifier;
      let activeTab = this._cfg.activeTab || 0;
      for (i = 0; i < this._cfg.tabs.length; i++) {
        if (i === activeTab) {
          tabClassModifier = 'tab--active';
          contentClassModifier = '';
        } else {
          tabClassModifier = '';
          contentClassModifier = 'hidden';
        }
        html += `<div class="tab ${tabClassModifier}" data-tab-idx="${i}">
        <span class="tab-name">${this._cfg.tabs[i].name}</span>
        <span class="show-hide-btn"></span>
        </div>`;
        tabsContentHtml += `<div class="ui-panel__tabs-content ${contentClassModifier}" data-tab-idx="${i}"></div>`;
      }
      html += `</div>${tabsContentHtml}</div>`;
      return html;
    }

    init (panelDOM) {
      this._panelDOM = panelDOM;
      this._tabsContainerDOM = this._panelDOM.querySelector(`.ui-panel__tabs-container[data-component-id="${this._cid}"]`);
      this._tabsContentContainerDOM = this._panelDOM.querySelector(`.ui-panel__tabs-content-container[data-component-id="${this._cid}"]`);
      this.bindListeners();
    }

    setTabData (tabIdx, data) {
      if (tabIdx > this._cfg.tabs.length - 1) {
        return;
      }
      let html;
      let tabContentContainer = this._tabsContentContainerDOM.querySelector(`[data-tab-idx="${tabIdx}"]`);
      if (tabContentContainer.innerHTML.length > 0) {
        this._cfg.tabs[tabIdx].beforeContentBuild(tabContentContainer);
      }
      html = this._cfg.tabs[tabIdx].contentBuilder(data);
      this._tabsContentContainerDOM.querySelector(`[data-tab-idx="${tabIdx}"]`).innerHTML = html;
      this._cfg.tabs[tabIdx].afterContentBuild(tabContentContainer);
    }

    bindListeners () {
      utils.addOrRemoveListeners({
        '.tab': {
          click: [this._handlers.tabClick]
        }
      }, true, this._tabsContainerDOM);
    }
  }

  return UIPanelTabs;
})();

export default UIPanelTabs;
