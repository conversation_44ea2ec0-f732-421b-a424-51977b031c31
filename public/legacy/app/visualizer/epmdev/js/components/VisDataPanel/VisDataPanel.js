/* global selected, hiddenGroups */

import utils from "../../../../../../src/js/utils/utils.js";
import UIPanel from "../UIPanel/UIPanel.js";
import ColorPicker from "../ColorPickerComponent";

function eventPath(event) {
  return event.path || (event.composedPath && event.composedPath()) || [];
}

const VisDataPanel = (function() {
  "use strict";

  const NO_GROUP_CONTENT_TEXT =
    'Create new Group by selecting several Themes and using "Create Group" button.';

  class VisDataPanel {
    constructor(cfg) {
      this._cfg = cfg;
      this._uiPanel;
      this.themesUiSyncTimeout = null;
      this.groupsVisibilityUiSyncTimeout = null;
      this.colorPicker = null;
      this._model = {
        themes: undefined,
        entitiesData: undefined,
        groups: undefined
      };
      this._handlers = utils.bindMethods(
        [
          this._themeContentBuilder,
          this._beforeThemesBuild,
          this._afterThemesBuild,
          this._groupCreateButtonClick,
          this._themeShowRelationButtonClick,
          this._themeMassSelectionButtonClick,
          this._themeItemClick,
          this._entitiesContentBuilder,
          this._beforeEntitiesBuild,
          this._afterEntitiesBuild,
          this._entitiesItemClick,
          this._groupsContentBuilder,
          this._beforeGroupsBuild,
          this._afterGroupsBuild,
          this._groupsItemClick,
          this._groupVisibilityChangeClick,
          this._addToGroupClick,
          this._removeFromGroupClick,
          this._removeGroupClick,
          this._renameThemeClick,
          this._handleColorIndicatorClick,
          this._handleThemeListScroll,
          this._saveThemeColor
        ],
        this,
        {}
      );

      this.init();
    }
    get visualizerSelectedGroupName() {
      // eslint-disable-next-line no-undef
      return selectedGroupName;
    }
    _groupVisibilityChangeClick(e) {
      e.stopPropagation();
      const groupNode = e.target;
      const groupName = atob(groupNode.dataset.name);
      if (groupName !== this.visualizerSelectedGroupName) {
        return;
      }
      this.toggleGroupVisibility(groupNode);
      if (typeof this._cfg.setGroupVisibility === "function") {
        this._cfg.setGroupVisibility(
          groupName,
          this.hiddenGroups.includes(groupName)
        );
      }
    }
    toggleGroupVisibility(node) {
      node.classList.toggle("visibility--visible");
      if (node.classList.contains("visibility--visible")) {
        node.title = "Hide Group";
      } else {
        node.title = "Unhide Group";
      }
    }
    addEventListeners() {
      // don't need to remove
      this._tabClick = this._tabClick.bind(this);
      this._uiPanel._DOM.addEventListener("click", this._tabClick);
    }
    get selectedThemes() {
      return selected;
    }
    get hiddenGroups() {
      return hiddenGroups;
    }
    _tabClick(e) {
      if (
        !eventPath(e).some(
          el => "classList" in el && el.classList.contains("tab")
        )
      ) {
        return;
      }
      const rootNode = this._uiPanel._DOM;
      if (!rootNode.querySelector(".tab--active")) {
        rootNode.classList.add("toggled");
      } else {
        rootNode.classList.remove("toggled");
      }
    }
    _themeButtons() {
      return `
			<div class="theme-list-header">
			<div class="theme-list__actions">
				<div class="selection-indicator"></div>
				<div class="button button--show-relation" title="Select two Themes to show relation between them constantly" ctrl-class="button--disabled">Show Relation</div>
				<div class="button button--export-selected" ctrl-class="button--disabled" style="display:none">Export Selected</div>
			</div>
			</div>
		`;
    }
    _themeContentBuilder({ themes, settingsRegistry }) {
      let i;
      let html = this._themeButtons() + '<div class="theme-list">';
      let concepts, genes;
      for (i = 0; i < themes.length; i++) {
        concepts = Object.keys(themes[i].Concepts);
        genes = Object.keys(themes[i].Genes);
        html += `<div class="theme-item" data-idx="${i}">
          <div class="selection-indicator"></div>
          <div class="theme-item__body">
            <div class="theme-item__header">
              ${settingsRegistry[i].Alias}
              <div class="rename-theme js-theme-controls hidden">
                <div class="color-indicator-wrapper js-color-indicator-wrapper" title="Set Theme Color">
                  <div data-idx="${i}" class="color-indicator js-color-indicator"></div>
                </div>
                <i data-idx="${i}" class="icon-rename-theme js-rename-theme" title="Rename Theme"></i>
              </div>
            </div>
            <div class="theme-item__score">
              <span class="theme-item__score-key">Score:</span>
              <span class="theme-item__score-value">${themes[i].Score}</span>
            </div>
            <div class="theme-item__data">
              <div class="theme-item__data-column">
                <div class="theme-item__data-column-header">Concepts (${
                  concepts.length
                })</div>
                <div class="theme-item__data-column-content">${concepts.join(
                  ", "
                )}</div>
              </div>
              <div class="theme-item__data-column">
                <div class="theme-item__data-column-header">Entities (${
                  genes.length
                })</div>
                <div class="theme-item__data-column-content">${genes.join(
                  ", "
                )}</div>
              </div>
            </div>
          </div>
        </div>`;
      }
      html += "</div>";
      return html;
    }

    _selectThemeItem(themeItemDOM) {
      themeItemDOM.classList.add("theme-item--active");
      themeItemDOM
        .querySelector(".selection-indicator")
        .classList.add("selection-indicator--active");
      themeItemDOM
        .querySelector(".js-theme-controls")
        .classList.remove("hidden");
      themeItemDOM.scrollIntoView();
      this.selectedThemesCountChanged();
    }

    _deselectThemeItem(themeItemDOM) {
      themeItemDOM.classList.remove("theme-item--active");
      themeItemDOM
        .querySelector(".selection-indicator")
        .classList.remove("selection-indicator--active");
      themeItemDOM.querySelector(".js-theme-controls").classList.add("hidden");
      themeItemDOM.querySelector(".js-theme-controls").classList.add("hidden");
      this.selectedThemesCountChanged();
    }

    _toggleThemeItem(themeItemDOM) {
      this._saveThemeColor(themeItemDOM);
      if (themeItemDOM.classList.contains("theme-item--active")) {
        this._deselectThemeItem(themeItemDOM);
      } else {
        this._selectThemeItem(themeItemDOM);
      }
    }

    toggleThemeByName(themeName) {
      let themeIndex = -1;
      this._model.themes.forEach(({ Name }, index) => {
        if (Name === themeName) {
          themeIndex = index;
        }
      });
      if (themeIndex !== -1) {
        this._toggleThemeItemByIdx(themeIndex);
      }
    }
    _themeDOMNodeByIndex(idx) {
      return this._uiPanel
        .getComponent(0)
        .tabsContentContainer.querySelector(`.theme-item[data-idx="${idx}"]`);
    }
    _toggleThemeItemByIdx(idx) {
      const themeItem = this._themeDOMNodeByIndex(idx);
      if (!themeItem) {
        return;
      }
      this._toggleThemeItem(themeItem);
    }

    _themeItemClick(e) {
      let idx = parseInt(e.currentTarget.dataset.idx, 10);
      this._toggleThemeItem(e.currentTarget);
      if (typeof this._cfg.themeItemClick === "function") {
        this._cfg.themeItemClick(this._model.themes[idx]);
      }
    }

    _saveThemeColor(node) {
      const colorIndicator = node.querySelector(".js-color-indicator-wrapper");
      if (this.colorPicker.wasSelected) {
        this._cfg.saveColor();
        this._uiPanel._DOM
          .querySelector(".theme-list")
          .removeEventListener("scroll", this._handlers.handleThemeListScroll);
        if (this.colorPicker.clickElement !== colorIndicator) {
          this.colorPicker.clickElement.classList.remove("selected");
        }
        this.colorPicker.visible = false;
        this.colorPicker.wasSelected = false;
      }
      if (colorIndicator.classList.contains("selected")) {
        colorIndicator.classList.remove("selected");
        return;
      }
    }

    _groupCreateButtonClick() {
      if (this.selectedThemes.length === 0) {
        return;
      }
      if (typeof this._cfg.groupCreateButtonClick === "function") {
        this._cfg.groupCreateButtonClick();
      }
    }

    _themeShowRelationButtonClick() {
      if (this.selectedThemes.length !== 2) {
        return;
      }
      if (typeof this._cfg.themeShowRelationButtonClick === "function") {
        this._cfg.themeShowRelationButtonClick(this.selectedThemes);
      }
    }
    _themeMassSelectionButtonClick() {
      if (typeof this._cfg.themeItemClick === "function") {
        const actionName =
          this.selectedThemes.length === this._model.themes.length
            ? "_deselectThemeItem"
            : "_selectThemeItem";
        this._model.themes.forEach((el, idx) => {
          this[actionName](this._themeDOMNodeByIndex(idx));
          this._cfg.themeItemClick(el);
        });
      }
    }

    _beforeThemesBuild(tabContentContainer) {
      let i;
      let themeItems = tabContentContainer.querySelectorAll(".theme-item");
      for (i = 0; i < themeItems.length; i++) {
        themeItems[i].removeEventListener(
          "click",
          this._handlers.themeItemClick
        );
        themeItems[i]
          .querySelector(".js-rename-theme")
          .removeEventListener("click", this._handlers.renameThemeClick);
        themeItems[i]
          .querySelector(".js-color-indicator-wrapper")
          .addEventListener("click", this._handlers._handleColorIndicatorClick);
      }
      tabContentContainer
        .querySelector(".button--show-relation")
        .removeEventListener(
          "click",
          this._handlers.themeShowRelationButtonClick
        );
      tabContentContainer
        .querySelector(".theme-list__actions .selection-indicator")
        .removeEventListener(
          "click",
          this._handlers.themeMassSelectionButtonClick
        );
    }

    _afterThemesBuild(tabContentContainer) {
      let themeItems = tabContentContainer.querySelectorAll(".theme-item");
      for (let i = 0; i < themeItems.length; i++) {
        themeItems[i].addEventListener("click", this._handlers.themeItemClick);
        themeItems[i]
          .querySelector(".js-rename-theme")
          .addEventListener("click", this._handlers.renameThemeClick);
        themeItems[i]
          .querySelector(".js-color-indicator-wrapper")
          .addEventListener("click", this._handlers.handleColorIndicatorClick);

        const colorIndicator = themeItems[i].querySelector(
          ".js-color-indicator"
        );
        const color = `#${this._model.settingsRegistry[i].Color}`;
        colorIndicator.style.backgroundColor = color;
      }

      tabContentContainer
        .querySelector(".button--show-relation")
        .addEventListener("click", this._handlers.themeShowRelationButtonClick);

      tabContentContainer
        .querySelector(".theme-list__actions .selection-indicator")
        .addEventListener(
          "click",
          this._handlers.themeMassSelectionButtonClick
        );
    }

    _entitiesContentBuilder(data) {
      let i, j;
      let entities = data.entitiesList;
      let expressions = data.expressionsMap;
      let concepts;
      let html = `<div class="entities-legend">
      <span class="entities-legend__halo entities-legend__halo--9"></span>
      <span class="entities-legend__halo entities-legend__halo--99"></span>
      <span class="entities-legend__halo entities-legend__halo--999"></span>
      <span class="entities-legend__halo entities-legend__halo--1000"></span>
      <span class="entities-legend-info" title="Entity haloing color is based on ranged enrichment score for an entity in a specific theme."></span>
      </div><div class="entities-list">`;
      for (i = 0; i < entities.length; i++) {
        const entityName = entities[i].name;
        html += `<div class="entities-item" data-idx="${i}">
          <div class="selection-indicator"></div>
          <div class="entities-item__body">
            <div class="entities-item__header">${entityName}</div>
            <div>
              <span class="entities-item__sub-header">Theme Total:</span>
              <span class="">${entities[i].themes.length}</span>
            </div>
            <div>
              <span class="entities-item__sub-header">Highest Score:</span>
              <span class="">${(entities[i].maxScore || 0).toFixed(2)}</span>
            </div>
            <div>
              <span class="entities-item__sub-header">Exp Value:</span>
              <span class="">${expressions[entities[i].name] ||
                "&ndash;"}</span>
            </div>
            <span class="entities-item__concepts">Concepts (${
              Object.keys(entities[i].concepts).length
            })</span>
            <div class="entities-item__theme-list">`;
        for (j = 0; j < entities[i].themes.length; j++) {
          const conceptMap = entities[i].themes[j].Genes[entityName].Concepts;
          concepts = Object.keys(conceptMap).sort(
            (a, b) => conceptMap[a] > conceptMap[b]
          );
          html += `<div class="entities-item__theme-list-item">
            <div class="entities-item__sub-header entities-item__sub-header">${
              entities[i].themes[j].Name
            }</div>
            <div class="entities-item__theme-list-content">${concepts.join(
              ", "
            )}</div>
          </div>`;
        }
        html += "</div></div></div>";
      }
      html += "</div>";
      return html;
    }

    _beforeEntitiesBuild(tabContentContainer) {
      let i;
      let entitiesItems = tabContentContainer.querySelectorAll(
        ".entities-item"
      );
      for (i = 0; i < entitiesItems.length; i++) {
        entitiesItems[i].removeEventListener(
          "click",
          this._handlers.entitiesItemClick
        );
      }
    }

    _afterEntitiesBuild(tabContentContainer) {
      let i;
      let entitiesItems = tabContentContainer.querySelectorAll(
        ".entities-item"
      );
      for (i = 0; i < entitiesItems.length; i++) {
        entitiesItems[i].addEventListener(
          "click",
          this._handlers.entitiesItemClick
        );
      }
    }

    _entitiesItemClick(e) {
      let i;
      let idx = parseInt(e.currentTarget.dataset.idx, 10);
      this._model.entitiesData.entitiesList[idx].selected = !this._model
        .entitiesData.entitiesList[idx].selected;
      this._model.entitiesData.entitiesList.forEach((item, index) => {
        if (index !== idx) {
          item.selected = false;
        }
      });
      let tabsContentContainer = this._uiPanel.getComponent(0)
        .tabsContentContainer;
      let entityItems = tabsContentContainer.querySelectorAll(".entities-item");
      let selectionIndicators = tabsContentContainer.querySelectorAll(
        ".entities-item .selection-indicator"
      );
      for (i = 0; i < entityItems.length; i++) {
        if (i === idx) {
          continue;
        }
        entityItems[i].classList.remove("entities-item--active");
        selectionIndicators[i].classList.remove("selection-indicator--active");
      }
      e.currentTarget.classList.toggle("entities-item--active");
      e.currentTarget
        .querySelector(".selection-indicator")
        .classList.toggle("selection-indicator--active");
      e.currentTarget.scrollIntoView();
      if (typeof this._cfg.entityItemClick === "function") {
        this._cfg.entityItemClick(this._model.entitiesData.entitiesList[idx]);
      }
    }
    resolveThemeName(originalName) {
      // @todo figure out how to bring reference access to fix cases on profile init/restore
      // restore profile can trigger some UI actions, but UI at this point is not configured
      if (!Array.isArray(this._model.settingsRegistry)) {
        return originalName;
      }
      return (
        (
          this._model.settingsRegistry.find(el => el.Name === originalName) ||
          {}
        ).Alias || originalName
      );
    }
    _getGroupHTML(group, groupIdx) {
      let i;
      let themesHtml = "";
      let groupActiveClass = "";
      let selectionIndicatorActiveClass = "";
      if (group.selected) {
        groupActiveClass = "group-item--active";
        selectionIndicatorActiveClass = "selection-indicator--active";
      }
      for (i = 0; i < group.themes.length; i++) {
        themesHtml += `<div class="group-theme-item" data-group-idx="${groupIdx}" data-theme-idx="${i}" title="Remove Theme from Group">
          <span class="group-theme-item__name">${utils.escapeHTML(
            group.themes[i].Name
          )}</span>
          <span class="group-theme-item__remove"></span>
        </div>`;
      }
      return `<div class="group-item ${groupActiveClass}" data-group-idx="${groupIdx}">
        <div class="selection-indicator ${selectionIndicatorActiveClass}"></div>
        <div class="group-item__body">
		  <div class="group-item__header" title="${utils.escapeHTML(group.name)}">
			  <div class="text">${utils.escapeHTML(group.name)}</div>
			  <div class="visibility button--visibility ${
          !this.hiddenGroups.includes(group.name) ? " visibility--visible" : ""
        }" data-name="${btoa(group.name)}" title="${
        !this.hiddenGroups.includes(group.name) ? "Hide Group" : "Unhide Group"
      }"></div>
		  </div>
          <div class="group-item__actions">
            <div class="button button--add ${
              this.selectedThemes.length === 0 ? " button--disabled" : ""
            }" data-group-idx="${groupIdx}">Add to Group</div>
            <div class="button button--remove" data-group-idx="${groupIdx}" data-group-name="${utils.escapeHTML(
        group.name
      )}">Delete Group</div>
          </div>
          <div class="group-item__themes">${themesHtml}</div>
        </div>
      </div>`;
    }

    _groupsHeader() {
      return `
			<div class="group-list-header">
			<div class="group-list__actions">
				<div class="button button--group-creation" ctrl-class="button--disabled">Create Group</div>
			</div>
			</div>
		`;
    }

    _groupsContentBuilder(rawGroups) {
      const groups = rawGroups.sort((a, b) => a.name - b.name);
      let i;
      let html = '<div class="groups-list">';
      for (i = 0; i < groups.length; i++) {
        html += this._getGroupHTML(groups[i], i);
      }
      html += "</div>";
      if (groups.length === 0) {
        html = `<div class="groups-list">${NO_GROUP_CONTENT_TEXT}</div>`;
      }
      return this._groupsHeader() + html;
    }

    _unbindGroupListeners(groupDOM) {
      let i;
      let groupThemesRemoveBtns = groupDOM.querySelectorAll(
        ".group-theme-item__remove"
      );
      let addToGroupBtn = groupDOM.querySelector(
        ".group-item__actions .button--add"
      );
      let removeGroupBtn = groupDOM.querySelector(
        ".group-item__actions .button--remove"
      );
      groupDOM
        .querySelector(".group-item__header .button--visibility")
        .removeEventListener(
          "click",
          this._handlers.groupVisibilityChangeClick
        );
      groupDOM.removeEventListener("click", this._handlers.groupsItemClick);
      addToGroupBtn.removeEventListener(
        "click",
        this._handlers.addToGroupClick
      );
      removeGroupBtn.removeEventListener(
        "click",
        this._handlers.removeGroupClick
      );
      for (i = 0; i < groupThemesRemoveBtns.length; i++) {
        groupThemesRemoveBtns[i].removeEventListener(
          "click",
          this._handlers.removeFromGroupClick
        );
      }
    }

    _bindGroupListeners(groupDOM) {
      let i;
      let groupThemesRemoveBtns = groupDOM.querySelectorAll(
        ".group-theme-item__remove"
      );
      let addToGroupBtn = groupDOM.querySelector(
        ".group-item__actions .button--add"
      );
      let removeGroupBtn = groupDOM.querySelector(
        ".group-item__actions .button--remove"
      );
      groupDOM
        .querySelector(".group-item__header .button--visibility")
        .addEventListener("click", this._handlers.groupVisibilityChangeClick);
      groupDOM.addEventListener("click", this._handlers.groupsItemClick);
      addToGroupBtn.addEventListener("click", this._handlers.addToGroupClick);
      removeGroupBtn.addEventListener("click", this._handlers.removeGroupClick);
      for (i = 0; i < groupThemesRemoveBtns.length; i++) {
        groupThemesRemoveBtns[i].addEventListener(
          "click",
          this._handlers.removeFromGroupClick
        );
      }
    }

    _rebuildGroupItemHTML(groupItem, groupDOM) {
      let newGroupDOM;
      let host = document.createElement("div");
      host.innerHTML = this._getGroupHTML(
        groupItem,
        parseInt(groupDOM.dataset.groupIdx, 10)
      );
      newGroupDOM = host.firstElementChild;
      this._unbindGroupListeners(groupDOM);
      groupDOM.replaceWith(newGroupDOM);
      this._bindGroupListeners(newGroupDOM);
    }

    _removeGroupItem(groupIdx) {
      this._model.groups.splice(groupIdx, 1);
      this.setGroups(this._model.groups);
      this.selectedThemesCountChanged();
    }

    _beforeGroupsBuild(tabContentContainer) {
      let i;
      const groupCreateButton = tabContentContainer.querySelector(
        ".button--group-creation"
      );
      groupCreateButton.removeEventListener(
        "click",
        this._handlers.groupCreateButtonClick
      );
      let groupItems = tabContentContainer.querySelectorAll(".group-item");
      let groupThemesRemoveBtns = tabContentContainer.querySelectorAll(
        ".group-theme-item__remove"
      );
      let addToGroupBtns = tabContentContainer.querySelectorAll(
        ".group-item__actions .button--add"
      );
      let removeGroupBtns = tabContentContainer.querySelectorAll(
        ".group-item__actions .button--remove"
      );
      let visibilityChangeBtns = tabContentContainer.querySelectorAll(
        ".group-item__header .button--visibility"
      );
      for (i = 0; i < visibilityChangeBtns.length; i++) {
        visibilityChangeBtns[i].removeEventListener(
          "click",
          this._handlers.groupVisibilityChangeClick
        );
      }
      for (i = 0; i < groupItems.length; i++) {
        groupItems[i].removeEventListener(
          "click",
          this._handlers.groupsItemClick
        );
      }
      for (i = 0; i < groupThemesRemoveBtns.length; i++) {
        groupThemesRemoveBtns[i].removeEventListener(
          "click",
          this._handlers.removeFromGroupClick
        );
      }
      for (i = 0; i < addToGroupBtns.length; i++) {
        addToGroupBtns[i].removeEventListener(
          "click",
          this._handlers.addToGroupClick
        );
        removeGroupBtns[i].removeEventListener(
          "click",
          this._handlers.removeGroupClick
        );
      }
    }

    _afterGroupsBuild(tabContentContainer) {
      let i;
      const groupCreateButton = tabContentContainer.querySelector(
        ".button--group-creation"
      );
      groupCreateButton.addEventListener(
        "click",
        this._handlers.groupCreateButtonClick
      );
      let visibilityChangeBtns = tabContentContainer.querySelectorAll(
        ".group-item__header .button--visibility"
      );
      for (i = 0; i < visibilityChangeBtns.length; i++) {
        visibilityChangeBtns[i].addEventListener(
          "click",
          this._handlers.groupVisibilityChangeClick
        );
      }
      let groupItems = tabContentContainer.querySelectorAll(".group-item");
      let groupThemesRemoveBtns = tabContentContainer.querySelectorAll(
        ".group-theme-item__remove"
      );
      let addToGroupBtns = tabContentContainer.querySelectorAll(
        ".group-item__actions .button--add"
      );
      let removeGroupBtns = tabContentContainer.querySelectorAll(
        ".group-item__actions .button--remove"
      );
      for (i = 0; i < groupItems.length; i++) {
        groupItems[i].addEventListener("click", this._handlers.groupsItemClick);
      }
      for (i = 0; i < groupThemesRemoveBtns.length; i++) {
        groupThemesRemoveBtns[i].addEventListener(
          "click",
          this._handlers.removeFromGroupClick
        );
      }
      for (i = 0; i < addToGroupBtns.length; i++) {
        addToGroupBtns[i].addEventListener(
          "click",
          this._handlers.addToGroupClick
        );
        removeGroupBtns[i].addEventListener(
          "click",
          this._handlers.removeGroupClick
        );
      }
    }

    get selectedGroup() {
      const groups = this._model.groups.filter(({ selected }) => {
        return selected === true;
      });
      return groups.length ? groups[0] : undefined;
    }

    _selectGroupItem(groupDOM) {
      let i;
      let groupIdx = parseInt(groupDOM.dataset.groupIdx, 10);
      let tabContentContainer = this._uiPanel.getComponent(0)
        .tabsContentContainer;
      let groupItems = tabContentContainer.querySelectorAll(".group-item");
      let selectionIndicators = tabContentContainer.querySelectorAll(
        ".group-item .selection-indicator"
      );
      this._model.groups[groupIdx].selected = true;
      for (i = 0; i < groupItems.length; i++) {
        if (i === groupIdx) {
          continue;
        }
        this._model.groups[i].selected = false;
        groupItems[i].classList.remove("group-item--active");
        selectionIndicators[i].classList.remove("selection-indicator--active");
      }
      groupDOM.classList.add("group-item--active");
      groupDOM
        .querySelector(".selection-indicator")
        .classList.add("selection-indicator--active");
      groupDOM.scrollIntoView();
    }

    _deselectGroupItem(groupDOM) {
      let i;
      let groupIdx = parseInt(groupDOM.dataset.groupIdx, 10);
      let tabContentContainer = this._uiPanel.getComponent(0)
        .tabsContentContainer;
      let groupItems = tabContentContainer.querySelectorAll(".group-item");
      let selectionIndicators = tabContentContainer.querySelectorAll(
        ".group-item .selection-indicator"
      );
      this._model.groups[groupIdx].selected = false;
      for (i = 0; i < groupItems.length; i++) {
        if (i === groupIdx) {
          continue;
        }
        this._model.groups[i].selected = false;
        groupItems[i].classList.remove("group-item--active");
        selectionIndicators[i].classList.remove("selection-indicator--active");
      }
      groupDOM.classList.remove("group-item--active");
      groupDOM
        .querySelector(".selection-indicator")
        .classList.remove("selection-indicator--active");
    }

    _toggleGroupItem(groupItem) {
      if (groupItem.classList.contains("group-item--active")) {
        this._deselectGroupItem(groupItem);
      } else {
        this._selectGroupItem(groupItem);
      }
    }

    _selectGroupByIdx(idx) {
      let groupDOM = this._uiPanel
        .getComponent(0)
        .tabsContentContainer.querySelector(
          `.group-item[data-group-idx="${idx}"]`
        );
      if (!groupDOM) {
        return;
      }
      this._selectGroupItem(groupDOM);
    }

    _toggleGroupByIdx(idx) {
      if (idx < 0) {
        return;
      }
      let groupDOM = this._uiPanel
        .getComponent(0)
        .tabsContentContainer.querySelector(
          `.group-item[data-group-idx="${idx}"]`
        );
      if (!groupDOM) {
        return;
      }
      this._toggleGroupItem(groupDOM);
    }

    _groupsItemClick(e) {
      // relevant on 2019/08/19
      // because of how current visualizer is built
      // toggling groupItem will have to happen
      // outside of this component and handled in groupSelect() in vis code.

      // this._toggleGroupItem(e.currentTarget);
      if (
        eventPath(e).some(
          item =>
            item.classList &&
            (item.classList.contains("group-item__themes") ||
              item.classList.contains("button--visibility"))
        )
      ) {
        return;
      }
      let groupIdx = parseInt(e.currentTarget.dataset.groupIdx, 10);
      if (typeof this._cfg.entityItemClick === "function") {
        this._cfg.groupItemClick(this._model.groups[groupIdx]);
      }
    }

    _removeFromGroupClick(e) {
      e.stopPropagation();
      let tabsContentDOM = this._uiPanel.getComponent(0).tabsContentContainer;
      let groupIdx = parseInt(
        e.currentTarget.parentElement.dataset.groupIdx,
        10
      );
      let themeIdx = parseInt(
        e.currentTarget.parentElement.dataset.themeIdx,
        10
      );
      if (this._model.groups[groupIdx] !== this.selectedGroup) {
        return;
      }
      let themeItem = this._model.groups[groupIdx].themes[themeIdx];
      let groupDOM = tabsContentDOM.querySelector(
        `.group-item[data-group-idx="${groupIdx}"]`
      );
      this._model.groups[groupIdx].themes.splice(themeIdx, 1);
      this._rebuildGroupItemHTML(this._model.groups[groupIdx], groupDOM);
      if (typeof this._cfg.removeFromGroupClick === "function") {
        this._cfg.removeFromGroupClick(themeItem);
      }
    }

    _addToGroupClick(e) {
      e.stopPropagation();
      let groupIdx = parseInt(e.currentTarget.dataset.groupIdx, 10);
      if (typeof this._cfg.addToGroupClick === "function") {
        this._cfg.addToGroupClick(groupIdx);
        this.selectGroup(groupIdx);
      }
    }

    _removeGroupClick(e) {
      e.stopPropagation();
      let groupIdx = parseInt(e.currentTarget.dataset.groupIdx, 10);
      let groupName = e.currentTarget.dataset.groupName;
      if (typeof this._cfg.removeGroupClick === "function") {
        this._cfg.removeGroupClick(groupIdx, groupName, () => {
          this._removeGroupItem(groupIdx);
        });
      } else {
        this._removeGroupItem(groupIdx);
      }
    }

    _renameThemeClick(e) {
      e.stopPropagation();
      let idx = parseInt(e.currentTarget.dataset.idx, 10);
      const namesToCheck = this._model.themes.map(el => {
        return {
          name: el.Name
        };
      });
      const aliasesToCheck = this._model.settingsRegistry.map(el => {
        return {
          name: el.Alias
        };
      });
      const originalName = this._model.themes[idx].Name;
      const originalAlias =
        this._model.settingsRegistry[idx].Alias || originalName;
      const itemsToCheck = []
        .concat(aliasesToCheck, namesToCheck)
        .filter(el => {
          return (
            el.name && el.name !== originalName && el.name !== originalAlias
          );
        });
      Object.assign(this._cfg.components.renameModal, {
        visible: true,
        items: itemsToCheck,
        itemKey: "name",
        name: originalAlias,
        onRename: this._cfg.renameThemeClick,
        allowInitialName: true,
        title: "Rename Theme",
        label: "theme name",
        mainActionName: "Rename",
        validationErrorDuplicate: "A theme with this name already exists.",
        validationErrorFresh: "You need to enter a new name before setting."
      });
    }

    _handleColorIndicatorClick(e) {
      if (this.colorPicker.wasSelected) {
        this._cfg.saveColor();
        this.colorPicker.clickElement.classList.remove("selected");
      }
      this.colorPicker.wasSelected = true;
      e.stopPropagation();
      e.currentTarget.classList.add("selected");
      this.colorPicker.clickElement = e.currentTarget;
      const colorIndicator = this.colorPicker.clickElement.querySelector(
        ".js-color-indicator"
      );
      let idx = parseInt(colorIndicator.dataset.idx, 10);
      if (typeof this._cfg.themeItemClick === "function") {
        // need to set selectedCluster to the current theme
        this._cfg.themeItemClick(this._model.themes[idx]);
        this._cfg.themeItemClick(this._model.themes[idx]);
      }
      this._handlers.handleThemeListScroll();
      this._uiPanel._DOM
        .querySelector(".theme-list")
        .addEventListener("scroll", this._handlers.handleThemeListScroll);
      this.colorPicker.onChange = tempVal => {
        if (this.selectedThemes.length > 0) {
          colorIndicator.style.backgroundColor = tempVal;
          this._cfg.onColorChange(tempVal);
        }
      };

      this.colorPicker.visible = true;
    }

    _handleThemeListScroll() {
      let bcr = this._uiPanel._DOM
        .querySelector(".theme-list")
        .getBoundingClientRect();
      let offset = $(this.colorPicker.clickElement).offset();
      if (offset.top < bcr.top || offset.top > bcr.bottom) {
        this.colorPicker.visible = false;
      } else {
        this.colorPicker.visible = true;
      }
      this.colorPicker.position = {
        x: offset.left - 50,
        y: offset.top + 5
      };
    }

    init() {
      this._uiPanel = new UIPanel({
        container: this._cfg.container,
        noDragHeader: this._cfg.noDragHeader,
        header: "Panel Header",
        classString: "vis-data-panel",
        components: {
          tabs: {
            activeTab: 0,
            tabs: [
              {
                name: "Themes",
                contentBuilder: this._handlers.themeContentBuilder,
                beforeContentBuild: this._handlers.beforeThemesBuild,
                afterContentBuild: this._handlers.afterThemesBuild
              },
              {
                name: "Entities",
                contentBuilder: this._handlers.entitiesContentBuilder,
                beforeContentBuild: this._handlers.beforeEntitiesBuild,
                afterContentBuild: this._handlers.afterEntitiesBuild
              },
              {
                name: "Groups",
                contentBuilder: this._handlers.groupsContentBuilder,
                beforeContentBuild: this._handlers.beforeGroupsBuild,
                afterContentBuild: this._handlers.afterGroupsBuild
              }
            ]
          }
        }
      });

      this.colorPicker = new ColorPicker({
        container: document.body
      });

      this.addEventListeners();
    }

    onThemeRename(oldValue, newValue) {
      const registryItems = [
        this._model.settingsRegistry.find(theme => theme.Name === oldValue),
        this._model.settingsRegistry.find(theme => theme.Alias === oldValue)
      ];
      registryItems
        .filter(el => el)
        .forEach(item => {
          item.Alias = newValue;
        });
      this.setThemes(this._model.themes, this._model.settingsRegistry);
      this.selectedThemes.forEach(theme => this.toggleThemeByName(theme));
    }

    getBoundingClientRect() {
      return this._uiPanel._DOM.getBoundingClientRect();
    }

    setHeader(header = "") {
      this._uiPanel.setHeader(header);
    }

    setThemes(themes = [], settingsRegistry = []) {
      this._model.themes = themes;
      this._model.settingsRegistry = settingsRegistry;
      this._uiPanel.getComponent(0).setTabData(0, {
        themes,
        settingsRegistry
      });
      this.selectedThemesCountChanged();
    }

    setEntities(entities = [], themes = [], expressionsMap = {}) {
      // actually entities param is absolutely unnecessary
      // just like in the original visualizer
      // kept here just in case.
      let i, j;
      let entitiesList = [];
      for (i = 0; i < entities.length; i++) {
        entitiesList.push({
          name: entities[i].Gene_ID,
          maxScore: 0,
          concepts: {},
          themes: [],
          selected: false
        });
        for (j = 0; j < themes.length; j++) {
          if (themes[j].Genes[entities[i].Gene_ID]) {
            Object.assign(
              entitiesList[i].concepts,
              themes[j].Genes[entities[i].Gene_ID].Concepts
            );
            entitiesList[i].themes.push(themes[j]);
            entitiesList[i].maxScore = Math.max(
              entitiesList[i].maxScore,
              themes[j].Genes[entities[i].Gene_ID].Score
            );
          }
        }
      }
      entitiesList.sort((a, b) => {
        return b.themes.length - a.themes.length;
      });
      this._model.entitiesData = {
        entitiesList,
        expressionsMap
      };
      this._uiPanel.getComponent(0).setTabData(1, {
        entitiesList,
        expressionsMap
      });
    }

    setGroups(groups = []) {
      this._model.groups = groups;
      this._uiPanel.getComponent(0).setTabData(2, groups);
      this.groupsVisibilityChanged();
    }

    toggleTheme(idx) {
      this._toggleThemeItemByIdx(idx);
    }

    setValidThemesSelectionStatus() {
      const checkBoxNode = this._uiPanel._DOM.querySelector(
        ".theme-list__actions .selection-indicator"
      );
      const nodeClasses = ["selection-indicator"];
      if (this.selectedThemes.length) {
        nodeClasses.push("selection-indicator--active");
        if (this.selectedThemes.length !== this._model.themes.length) {
          nodeClasses.push("selection-indicator--intermediate");
        }
      }
      checkBoxNode.classList = nodeClasses.join(" ");
    }

    setRelationButtonStatus() {
      const relationButtonNode = this._uiPanel._DOM.querySelector(
        ".theme-list__actions .button--show-relation"
      );
      const nodeClasses = ["button", "button--show-relation"];
      if (this.selectedThemes.length !== 2) {
        nodeClasses.push("button--disabled");
      }
      relationButtonNode.classList = nodeClasses.join(" ");
    }
    setCreateGroupButtonStatus() {
      const groupCreationButtonNode = this._uiPanel._DOM.querySelector(
        ".group-list__actions .button--group-creation"
      );
      const nodeClasses = ["button", "button--group-creation"];
      if (!this.selectedThemes.length) {
        nodeClasses.push("button--disabled");
      }
      groupCreationButtonNode.classList = nodeClasses.join(" ");
    }
    selectedThemesCountChanged() {
      // we need to get next runloop, after all related html will be rendered
      clearTimeout(this.themesUiSyncTimeout);
      this.themesUiSyncTimeout = setTimeout(() => {
        const allGroupButtons = Array.from(
          this._uiPanel._DOM.querySelectorAll(
            ".group-item__actions .button--add"
          )
        );
        const hasSelectedThemes = this.selectedThemes.length > 0;
        allGroupButtons.forEach(buttonNode => {
          if (hasSelectedThemes) {
            buttonNode.classList.remove("button--disabled");
          } else {
            buttonNode.classList.add("button--disabled");
          }
        });
        this.setValidThemesSelectionStatus();
        this.setRelationButtonStatus();
        this.setCreateGroupButtonStatus();
      }, 10);
    }

    groupsVisibilityChanged() {
      // we need to get next runloop, after all related html will be rendered
      clearTimeout(this.groupsVisibilityUiSyncTimeout);
      this.groupsVisibilityUiSyncTimeout = setTimeout(() => {
        const groups = this.hiddenGroups;
        const allGroupVisibilityNodes = Array.from(
          this._uiPanel._DOM.querySelectorAll(
            ".group-item__header .button--visibility"
          )
        );
        allGroupVisibilityNodes.forEach(node => {
          if (groups.includes(atob(node.dataset.name))) {
            node.classList.remove("visibility--visible");
            node.title = "Unhide Group";
          } else {
            node.classList.add("visibility--visible");
            node.title = "Hide Group";
          }
        });
      }, 10);
      // hiddenGroups
    }

    deselectAllThemeItems() {
      if (!this._model.themes) {
        return;
      }
      let i;
      let tabsContentDOM = this._uiPanel.getComponent(0).tabsContentContainer;
      let themeItems = tabsContentDOM.querySelectorAll(".theme-item");
      let selectionIndicators = tabsContentDOM.querySelectorAll(
        ".theme-item .selection-indicator"
      );
      for (i = 0; i < themeItems.length; i++) {
        themeItems[i].classList.remove("theme-item--active");
        selectionIndicators[i].classList.remove("selection-indicator--active");
      }
      themeItems[0].scrollIntoView();
      this.selectedThemesCountChanged();
    }

    getSelectedEntities() {
      if (
        this._model.entitiesData &&
        this._model.entitiesData.entitiesList instanceof Array
      ) {
        return this._model.entitiesData.entitiesList.filter(e => e.selected);
      }
      return [];
    }

    selectGroup(groupIdx) {
      this._selectGroupByIdx(groupIdx);
    }

    toggleGroupByName(groupName) {
      this._toggleGroupByIdx(
        this._model.groups.findIndex(g => g.name === groupName)
      );
    }

    addThemesToGroup(themeItems, groupIdx) {
      const group = this._model.groups[groupIdx];
      const existingThemes = (group.themes || []).map(({ Name }) => Name);
      const newThemes = themeItems.filter(
        ({ Name }) => !existingThemes.includes(Name)
      );
      if (newThemes.length === 0) {
        return;
      }
      group.themes = group.themes.concat(newThemes);
      let tabsContentDOM = this._uiPanel.getComponent(0).tabsContentContainer;
      let groupDOM = tabsContentDOM.querySelector(
        `.group-item[data-group-idx="${groupIdx}"]`
      );
      this._rebuildGroupItemHTML(group, groupDOM);
    }
  }

  return VisDataPanel;
})();

export default VisDataPanel;
