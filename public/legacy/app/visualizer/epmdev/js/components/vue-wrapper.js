import Vue from "vue";

function registerGlobalVueModules(modules) {
  if (modules && Array.isArray(modules)) {
    modules.forEach(([mod, config]) => {
      if (typeof config === "object") {
        Vue.use(mod, config);
      } else {
        Vue.use(mod);
      }
    });
  }
}

function vueComponentWrapper(
  Component,
  { container, data = {}, modules = [] }
) {
  const mountNode = document.createElement("div");
  container.appendChild(mountNode);
  registerGlobalVueModules(modules);
  var ComponentClass = Vue.extend(Component);
  var instance = new ComponentClass({
    data: data
  });
  instance.$mount(mountNode);
  instance._DOM = instance.$el;
  function preventEventLeaking(e) {
    //   e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();
  }
  instance.addListeners = function() {
    instance.$el.addEventListener("click", preventEventLeaking, false);
    instance.$el.addEventListener("mousedown", preventEventLeaking, false);
    instance.$el.addEventListener("touchstart", preventEventLeaking, false);
    instance.$el.addEventListener("mousemove", preventEventLeaking, false);
  };
  instance.addListeners();
  instance.removeListeners = function() {
    instance.$el.removeEventListener("click", preventEventLeaking);
    instance.$el.removeEventListener("mousedown", preventEventLeaking);
    instance.$el.removeEventListener("touchstart", preventEventLeaking);
    instance.$el.removeEventListener("mousemove", preventEventLeaking);
  };
  instance.remove = function() {
    instance.removeListeners();
    instance.$destroy();
    container.removeChild(instance._DOM);
  };
  Object.defineProperty(instance, "data", {
    get: function() {
      return this.$data;
    },
    set: function(data) {
      this.$data = data;
    },
    enumerable: true,
    configurable: false
  });
  Object.defineProperty(instance, "_model", {
    get: function() {
      return this.$data;
    },
    set: function(data) {
      this.$data = data;
    },
    enumerable: true,
    configurable: false
  });
  return instance;
}

export default class VueComponentWrapper {
  constructor() {
    return vueComponentWrapper(...arguments);
  }
}
