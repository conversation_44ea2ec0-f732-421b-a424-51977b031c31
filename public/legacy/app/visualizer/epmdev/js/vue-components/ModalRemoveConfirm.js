export default {
  data() {
    return {
	  visible: false,
	  title: 'Delete Group',
	  message: 'Are you sure you want to delete "GROUP_NAME"?'
    };
  },
  template: `
<div data-test="modal-remove-confirm">
	<modal name="hello-world" :classes="['popup', 'popup-relative', 'abstract-popup']" :width="'380px'" :height="'220px'" :clickToClose="false">
		<div class="popup-header">
			<div class="popup-title">
				{{title}}
			</div>
			<div @click="cancel" title="Close" class="close-btn js-close-btn"> X </div>
		</div>
		<div class="popup-content">
			<p>{{message}}</p>
		</div>
		<div class="theme-popup-buttons-wrapper">
			<button type="button" @click="confirm" class="theme-popup-button theme-popup-button--remove">Delete</button>
			<button type="button" @click="cancel" class="theme-popup-button">Cancel</button>
		</div>
	</modal>
</div>
	`,
  methods: {
    show() {
      this.$modal.show('hello-world', { clickToClose: false });
    },
    hide() {
      	this.$modal.hide('hello-world');
	},
	cancel() {
		if (this.onCancel) {
			this.onCancel();
		}
		this.visible = false;
	},
	confirm() {
		if (this.onConfirm) {
			this.onConfirm();
		}
		this.visible = false;
	}
  },
  watch: {
    visible(val) {
      if (val) {
        this.show();
      } else {
        this.hide();
      }
    }
  }
};
