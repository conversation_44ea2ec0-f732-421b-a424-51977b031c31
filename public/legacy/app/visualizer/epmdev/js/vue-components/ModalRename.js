export default {
  data() {
    return {
      visible: false,
      validationError: "",
      title: "",
      items: [],
      itemKey: "Name",
      label: "theme name",
      mainActionName: "Rename",
      validationErrorDuplicate: "A theme with this name already exists.",
      validationErrorFresh: "You need to enter a new name before setting.",
      allowInitialName: true,
      name: undefined,
      onRename: function() {}
    };
  },
  template: `
    <div data-test="modal-rename">
      <modal 
          name="modal-rename" 
          :classes="['popup', 'popup-relative', 'abstract-popup']" 
          :width="'380px'" 
          :height="'200px'" 
          :clickToClose="false"
          @before-open="beforeOpen"
          @before-close="beforeClose"
      >
        <div class="popup-header">
          <div class="popup-title">
            {{title}}
          </div>
          <div @click="close" title="Close" class="close-btn js-close-btn"> X </div>
        </div>
        <div class="popup-content">
          <div class="popup-control popup-control__input">
            <label>{{label}}</label>
            <input autofocus type="text" v-bind:class="{ error: validationError }" v-model="name"/>
            <div class="validation-error" v-if="validationError">{{validationError}}</div>
          </div>
        </div>
        <div class="theme-popup-buttons-wrapper">
          <button type="button" @click="rename" class="theme-popup-button">&#10003; {{mainActionName}}</button>
          <button type="button" @click="close" title="Theme should have minimum 4 concepts"
            class="theme-popup-button theme-popup-button--cancel">Cancel</button>
        </div>
      </modal>
    </div>
  `,
  methods: {
    show() {
      this.$modal.show("modal-rename", {
        clickToClose: false
      });
    },
    close() {
      this.visible = false;
    },
    hide() {
      this.$modal.hide("modal-rename");
    },
    rename() {
      this.validateInput();
      if (this.validationError != "") {
        return;
      }
      const { name, initialName } = this;
      this.onRename(initialName, this.normalizeName(name));
      this.visible = false;
    },
    normalizeName(name) {
      return (name || "").trim();
    },
    validateInput() {
      const { initialName, items, name, itemKey } = this;
      const normalizedName = this.normalizeName(name);
      if (normalizedName === "") {
        this.validationError = " ";
        return;
      }
      if (
        !this.allowInitialName &&
        initialName &&
        normalizedName === initialName
      ) {
        this.validationError = this.validationErrorFresh;
        return;
      }
      if (items.filter(theme => theme[itemKey] === normalizedName).length) {
        this.validationError = this.validationErrorDuplicate;
        return;
      }
      this.validationError = "";
    },
    beforeOpen() {
      this.initialName = this.data.name;
      this.validationError = "";
    },
    beforeClose() {
      this.validationError = "";
      this.name = undefined;
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.show();
      } else {
        this.hide();
      }
    },
    name(name, oldVal) {
      if (oldVal === undefined) {
        return;
      }
      this.validateInput();
    }
  }
};
