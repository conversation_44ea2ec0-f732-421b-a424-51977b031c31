import SelectedTheme from "./SelectedTheme";

export default {
  components: { SelectedTheme },
  data() {
    return {
      themes: [],
      onClick() {}
    };
  },
  template: `
		<div class="selected-themes-panel">
		    <div v-if="themes.length" class="selected-theme" @click="deselectAll">Deselect All</div>
		    <SelectedTheme 
		        class="selected-theme" 
		        v-for="theme in themes"
		        :key="theme.id"
		        :theme="theme" 
		        :onClick="externalAction"
        ></SelectedTheme>
		</div>
	`,
  watch: {
    themes() {
      if (this.themes.length === 0) {
        this.$el.style.display = "none";
      } else {
        this.$el.style.display = "flex";
      }
    }
  },
  methods: {
    deselectAll() {
      this.onDeselectAll();
    },
    externalAction(data) {
      this.onClick(data.id);
    },
    renameTheme(oldName, newName) {
      const theme = this.themes.find(theme => theme.name === oldName);
      if (theme) {
        theme.name = newName;
      }
    }
  }
};
