import ThemeRelation from "./ThemeRelation";

export default {
  components: { ThemeRelation },
  data() {
    return {
      relations: [],
      onClick() {}
    };
  },
  template: `
		<ul class="themes-relation-display">
			<ThemeRelation v-for="relation in relations" :key="relation.id" :relation="relation" :onClick="externalAction" />
		</ul>
	`,
  watch: {
    relations() {
      if (this.relations.length === 0) {
        this.$el.style.display = "none";
      } else {
        this.$el.style.display = "block";
      }
    }
  },
  methods: {
    renameTheme(oldName, newName) {
      this.relations = this.relations.map(relation => {
        if (relation.left.id === oldName || relation.left.name === oldName) {
          relation.left.name = newName;
        }
        if (relation.right.id === oldName || relation.right.name === oldName) {
          relation.right.name = newName;
        }
        return relation;
      });
    },
    externalAction(data) {
      this.onClick(data);
    },
    addRelation(relation) {
      this.relations.unshift(relation);
    },
    deleteRelationById(id) {
      this.relations = this.relations.filter(rel => rel.id !== id);
    },
    clear() {
      this.relations = [];
    }
  }
};
