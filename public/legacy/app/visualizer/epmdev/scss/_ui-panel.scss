$uip-base-padding: 16px;

.ui-panel {
  position: absolute;
  max-height: 100%;
  overflow: auto;
  font-family: "Maison Neue";
  font-size: 14px;
  display: flex;
  flex-flow: column;
  background: $uip-base-dark;
  color: $uip-gray;

  & > * {
    padding: $uip-base-padding;
  }

  &__drag-header {
    background: $uip-gray-05;
    border-bottom: 1px solid $uip-dark-gray;
  }

  &__header {
    font-size: 24px;
    font-weight: bold;
    border-bottom: 1px solid $uip-dark-gray;
    overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
    flex-shrink: 0;
  }

  &__tabs-container {
    display: flex;
    flex-shrink: 0;
    padding: 0;

    .tab {
      display: flex;
      align-items: center;
      flex-grow: 1;
      min-width: 106px;
      cursor: pointer;
      padding: $uip-base-padding * .25 $uip-base-padding;
      line-height: 24px;
      background: $uip-gray-05;

      &:not(:last-child) {
        border-right: 1px solid $uip-dark-gray;
      }

      .show-hide-btn {
        padding: 0 8px;
        &:before {
          content: '';
          margin: -5px 0 0 0;
          display: block;
          width: 5px;
          height: 5px;
          border: 1px solid white;
          border-left-color: transparent;
          border-bottom-color: transparent;
          transform: rotate(135deg);
        }
      }

      &--active {
        background: initial;

        .show-hide-btn {
          &:before {
            margin: 5px 0 0 0;
            transform: rotate(-45deg);
          }
        }
      }
    }
  }

  .selection-indicator {
    width: 14px;
    height: 14px;
    border: 1px solid #9BA5B2;
    border-radius: 1px;
    margin: 4px 13px 0 0;
    padding: 1px;

    &--active {
      &:after {
        content: '';
        display: block;
        height: 100%;
        background: $uip-blue;
      }
    }
  }

  .button {
    padding: 8px;
    cursor: pointer;
    border: 1px solid $uip-gray-02;
    font-family: 'Maison Neue Light';
    line-height: 14px;
    font-size: 12px;
    background-repeat: no-repeat;
    background-position: 10px center;

    &:hover {
      background-color: rgba(216,216,216,0.28);
    }

    &--disabled {
      opacity: .3;
    }

    &--add {
      padding-left: 34px;
      background-image: url('../imgs/icons/plus.svg');
    }

    &--remove {
      padding-left: 30px;
      background-image: url('../imgs/icons/bucket-small.svg');
    }
  }

  &__tabs-content-container {
    overflow: auto;
    padding: 0;
  }

  &::-webkit-scrollbar, & *::-webkit-scrollbar {
    width: 14px;
    height: 14px;
  }

  &::-webkit-scrollbar-track, & *::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb {
    background: $uip-gray-05;
    border-radius: 14px;
    border: 3px solid transparent;
    background-clip: content-box;
  }
}
