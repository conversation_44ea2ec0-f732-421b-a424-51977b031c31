$vis-data-panel-scroll-container-size: 100vh;

.vis-data-panel {
  width: 320px;
  height: 100%;
  z-index: -1;
  transition: height 0.25s ease-in;
}

.vis-data-panel.toggled {
	height: 122px;
}

.ui-panel__tabs-content-container {

  overflow: auto;
  .theme-list-header, .group-list-header {
	border-bottom: 1px solid #4d4d4d;
	.theme-list__actions, .group-list__actions {
		padding: 16px;
		.button   {
			display: inline-block;
		}
		.selection-indicator {
			display: inline-block;
			position: relative;
			cursor: pointer;
			&--intermediate {
				&::before {
					content: "";
					position: absolute;
					display: block;
					position: absolute;
					background: #121111;
					top: 5px;
					left: 3px;
					width: 6px;
					height: 2px;
				}
			}
		}


	  }
  }
  .rename-theme {
    display: inline-block;
    float: right;
    .color-indicator-wrapper {
      display: inline-block;
      position: relative;
      vertical-align: middle;
      height: 24px;
      width: 24px;
      padding-left: 5px;
      cursor: pointer;
      &:hover {
        background-color: rgba(216,216,216,0.28);
      }

      &.selected {
        background: none;
        .color-indicator > div {
          display: block;
          position: relative;
          right: 75px;
        }
      }
    }
    .color-indicator {
      display: inline-block;
      position: relative;
      vertical-align: middle;
      height: 14px;
      width: 14px;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      border-radius: 50%;
      border: 1px solid #ffffff;
    }
    .icon-rename-theme {
      display: inline-block;
      vertical-align: middle;
      height: 24px;
      width: 24px;
      margin-left: 5px;
      cursor: pointer;
      background: url('../imgs/icons/edit.svg') no-repeat center;

      &:hover {
        background-color: rgba(216,216,216,0.28);
      }
    }
  }



  .button--group-creation {
	background-image: url(../imgs/icons/add.svg);
	background-repeat: no-repeat;
	background-position: 7px 7px;
    padding-left: 28px;
  }


  .theme-list {
    line-height: 24px;
	padding: $uip-base-padding;
	overflow-x: hidden;
    overflow-y: scroll;
	max-height: calc(#{$vis-data-panel-scroll-container-size} - 187px);
	height: 100%;
    .theme-item {
      border-bottom: 1px solid $uip-dark-gray;
      padding: 16px 0;
      display: flex;
      max-height: 200px;
      cursor: pointer;

      &:first-child {
        padding-top: 0px;
      }
      &:last-child {
        border: none;
      }

      &__body {
        flex-basis: 0;
        flex-grow: 1;
        overflow: hidden;
      }

      &__header {
        font-family: 'Maison Neue Demi';
        font-size: 16px;
      }

      &__score {
        font-family: 'Maison Neue Light';

        &-key {
          color: $uip-gray-05;
        }

        &-value {

        }
      }

      &__data {
        display: flex;

        &-column {
          width: 50%;
          margin: 0 16px 0 0;

          &-header {
            font-family: 'Maison Neue Demi';
            margin: 0 0 4px 0;
          }

          &-content {
            font-family: 'Maison Neue Light';
            line-height: 18px;
            word-break: break-word;
          }
        }
      }

      &__header, &__score {
        margin: 0 0 4px 0;
      }

      &--active {
        max-height: initial;

        .theme-item__header {
          color: $uip-blue;
        }
      }
    }
  }

  .entities-legend {
    display: flex;
    justify-content: space-around;
    padding: $uip-base-padding;
    border-bottom: 1px solid $uip-gray-02;

    &__halo {
      display: flex;
      align-items: center;

      &:before {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 100%;
        border: 3px solid white;
      }

      &:after {
        color: $uip-gray-05;
        font-family: 'Maison Neue Light';
        font-size: 12px;
        margin: 0 0 0 8px;
      }

      &--9 {
        &:before {
          border-color: $uip-dark-blue;
        }

        &:after {
          content: '< 9';
        }
      }

      &--99 {
        &:before {
          border-color: $uip-dark-green;
        }

        &:after {
          content: '< 99';
        }
      }

      &--999 {
        &:before {
          border-color: $uip-dark-orange;
        }

        &:after {
          content: '< 999';
        }
      }

      &--1000 {
        &:before {
          border-color: $uip-dark-red;
        }

        &:after {
          content: '1 000+';
        }
      }
	}

	.entities-legend-info {
		cursor: pointer;
		width: 18px;
		margin-top: -4px;
		margin-left: 10px;
		height: 16px;
		background-image: url(../imgs/icons/info.svg);
		background-repeat: no-repeat;
		&:hover {
			opacity: 0.8;
			transition: opacity 0.5s ease-in-out;
		}
		&:active {
			opacity: 1;
		}
	  }
  }

  .entities-list {
    line-height: 24px;
    padding: $uip-base-padding;
    font-family: 'Maison Neue Light';
	overflow-x: hidden;
    overflow-y: scroll;
	max-height: calc(#{$vis-data-panel-scroll-container-size} - 167px);
	height: 100%;

    .entities-item {
      border-bottom: 1px solid $uip-dark-gray;
      padding: 16px 0;
      display: flex;
      max-height: 220px;
      cursor: pointer;

      &:last-child {
        border: none;
      }

      &__body {
        flex-basis: 0;
        flex-grow: 1;
        overflow: hidden;
      }

      &__header {
        font-family: 'Maison Neue Demi';
        font-size: 16px;
        margin: 0 0 4px 0;
      }

      &__sub-header {
        color: $uip-gray-05;
      }

      &__concepts {
        font-family: 'Maison Neue Demi';
        margin: 4px 0 4px 0;
      }

      &__theme-list {
        &-item {
          margin: 4px 0 0 0;
        }

        &-content {
          line-height: 18px;
        }
      }

      &--active {
        max-height: initial;

        .entities-item__header {
          color: $uip-blue;
        }
      }
    }
  }

  .groups-list {
    line-height: 24px;
    padding: $uip-base-padding;
    font-family: 'Maison Neue Light';
	overflow-x: hidden;
    overflow-y: scroll;
	max-height: calc(#{$vis-data-panel-scroll-container-size} - 187px);
	height: 100%;

    .group-item {
      border-bottom: 1px solid $uip-dark-gray;
      padding: 16px 0;
      display: flex;
      max-height: 220px;
      cursor: pointer;

      &:last-child {
        border: none;
      }

      &__body {
        flex-basis: 0;
        flex-grow: 1;
        overflow: hidden;
      }

      &__header {
        font-family: 'Maison Neue Demi';
        font-size: 16px;
        margin: 0 0 4px 0;
      }

      &__actions {
        display: none;
        & > * {
          margin: 0 8px 0 0;
        }
      }

      &__themes {
        display: flex;
        flex-flow: row wrap;
        margin: 12px 0 0 0;
      }

      &--active {
        max-height: initial;

		.group-item__header {
          .text {
            color: $uip-blue;
          }
		}

        .group-item__actions {
          display: flex;
		}

		.group-theme-item {
			&__remove {
				visibility: visible;
			}
		}

	  }

	  .group-item__header {
		position: relative;
		.text {
		  text-overflow: ellipsis;
		  max-width: 100%;
		  overflow: hidden;
		  white-space: nowrap;
		  padding-right: 26px;
		}
		.visibility {
		  position: absolute;
		  right: -8px;
		  top: 6px;
		  width: 26px;
		  height: 12px;
		  background-image: url('../imgs/icons/cross-eye.svg');
		  background-repeat: no-repeat;

		  &--visible {
			background-image: url('../imgs/icons/eye.svg');
		  }
		}
	  }
    }

    .group-theme-item {
      margin: 0 16px 0 0;

      &__remove {
        border-radius: 100%;
        background: white;
        width: 10px;
        height: 10px;
        display: inline-flex;
        justify-content: center;
		align-items: center;
		visibility: hidden;

        &:before {
          content: '\2A2F';
          color: black;
          font-family: initial;
          line-height: 10px;
          height: 10px;
        }
      }
    }
  }
}
