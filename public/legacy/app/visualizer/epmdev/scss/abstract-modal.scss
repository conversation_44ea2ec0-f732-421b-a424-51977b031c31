.abstract-popup {
    box-shadow: 0px 0px 3px #fff3;
	z-index: 10;
	.popup-control__input {
		label {
			text-transform: uppercase;
			font-weight: 400;
			font-size: 10px;
			font-family: "Maison Neue", sans-serif;
			letter-spacing: 1px;
		}
		input {

			height: 32px;
			min-width: 343px;
			border: 1px solid #C6CBD4;
			border-radius: 2px;
			background-color: #FFFFFF;
			font-size: 14px;
			font-family: "Maison Neue", sans-serif;
			font-weight: 400;
			padding-left: 5px;

          &.error {
            border: 1px solid red;
            outline: none;
          }

		}

        .validation-error {
          position: absolute;
          color: red;
          font-size: 12px;
        }
	}
	.theme-popup-buttons-wrapper {
	  margin: 25px 9px 0 0;

	  .theme-popup-button {
		background-repeat: no-repeat;
		float: right;
		margin-right: 10px;
		width: 95px;
		height: 32px;
		color: #EEEEEE;
		background-color: rgb(17,17,17, 0.9);
		font-family: "<PERSON>son Neue";
		font-size: 12px;
		font-weight: 300;
		letter-spacing: 0.1px;
		line-height: 14px;
		border: 1px solid rgba(238, 238, 238, 0.2);
		cursor: pointer;

		&:hover {
			background-color: rgba(216,216,216,0.28);
		}

		&--disabled {
			opacity: .3;
		}

		&--remove {
			padding-left: 22px;
			background-position: 20px 8px;
			background-image: url(../imgs/icons/bucket-small.svg);
		}

		&[disabled] {
		  color: #EEEEEE;
		  border: 1px solid #EEEEEE;
		  opacity: 0.2;
		  cursor: not-allowed;
		}

        &--cancel {
          border: none;
        }
	  }
	}

	.popup-content {
	  margin: 9px 16px 0 16px;
	}

  }

