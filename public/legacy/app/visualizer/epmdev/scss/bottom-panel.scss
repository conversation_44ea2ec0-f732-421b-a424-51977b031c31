.bottom-bar {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 32px;
  transition: height 0.5s ease-in;
  background-color: #838080;
  align-items: center;
  color: #111111;
  font-family: "Maison Neue";
  font-size: 14px;
  font-weight: 500;
  line-height: 32px;
  z-index: 10;
  &.animated-hidden {
    height: 0;
  }

  &-text {
    margin-left: 16px;
    display: inline-block;
    vertical-align: middle;
    line-height: normal;
  }

  .hide-button {
    position: absolute;
    top: -24px;
    width: 71px;
    height: 24px;
    line-height: 24px;
    padding-left: 10px;
    cursor: pointer;
    background-color: rgb(238,238,238, 0.4);

    .hide-button-text {
      margin-right: 5px;
    }

    .arrow-image {
      position: relative;
      top: -2px;
    }

    .arrow-image.inverted {
      transform: rotateZ(180deg);
    }
  }
}
