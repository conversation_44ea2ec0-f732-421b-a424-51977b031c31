#new-entity-popup {

  .entity-button {
    height: 32px;
    width: calc(50% - 2px);
    text-align: left;
    padding-left: 16px;
    background-color: rgb(238,238,238, 0.5);
    vertical-align: middle;
    color: #111111;
    white-space: nowrap;
    border: none;
    cursor: pointer;

    &.selected {
      background-color: rgb(17,17,17, 0.9);
      color: #EEEEEE;
      user-select: none;
    }
  }
  .popup-table {
    margin-top: 16px;

    thead th {
      min-width: 200px;

      &.concept-score {
        min-width: 100px;
      }
    }

    tbody {
      max-height: 440px;
    }

    tbody tr {
      cursor: pointer;

      td {
        min-width: 200px;

        &.concept-score {
          min-width: 89px;
        }
      }

      &.disabled td {
        color: #727171;
        pointer-events: none;
      }
    }

    &.col-3 {
      thead th {
        text-align: center;
      }

      td, th {
        min-width: 89px;
      }
    }
  }

  .icon-new-window {
    display: inline-block;
    vertical-align: middle;
    height: 18px;
    width: 18px;
    margin-left: 15px;
    cursor: pointer;
    background: url('../imgs/icons/new-window.svg') no-repeat right;
  }
}
