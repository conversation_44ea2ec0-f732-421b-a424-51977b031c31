.popup-relative {
	position: relative !important;
}

.ui-draggable .popup-header {
    cursor: move !important;
}

.popup {
  position: absolute;
  color: #EEEEEE;
  background-color: rgb(17,17,17, 0.9);
  font-family: "Maison Neue";
  font-size: 14px;
  font-weight: 300;
  line-height: 24px;
  z-index: -2;
  min-width: 300px;
  max-width: 650px;
  overflow: auto;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;


  .popup-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px 0 16px;
    color: #EEEEEE;
    background-color: rgb(17,17,17, 0.9);
    min-height: 56px;
    border-bottom: 1px solid #4D4D4D;

    .popup-title {
      font-size: 20px;
      font-weight: bold;
      line-height: 24px;
      margin-right: 20px;
    }

    .close-btn {
      cursor: pointer;
      font-weight: bold;
      color: #EEEEEE;
      background-color: rgb(17,17,17, 0.9);
      border: none;
    }
  }

  .popup-info {
    padding: 16px;
    .info-panel-txt {
      color: #EEEEEE;
      font-family: "Maison Neue";
      font-size: 14px;
      font-weight: 300;
      line-height: 24px;
    }
    .info-panel-description {
      opacity: 0.5;
    }
  }

  .popup-table {

    padding: 0 16px 16px 16px;

    table {
      table-layout: fixed;
      border: 1px solid #4D4D4D;
      border-radius: 2px;
      background-color: #191919;
      border-collapse: collapse;
      min-width: 268px;

      thead tr {
        display: table;
        width: 100%;
        white-space: nowrap;

        th {
          border-bottom: 1px solid #4D4D4D;
          text-align: left;
          color: #EEEEEE;
          font-family: "Maison Neue";
          font-size: 14px;
          font-weight: 500;
          line-height: 24px;
          padding: 3px 10px;
        }
      }

      tbody {
        display: block;
        max-height: 350px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          height: 230px;
          width: 7px;
        }
        &::-webkit-scrollbar-track {
          box-shadow: inset 0 0 5px grey;
          border-radius: 10px;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 3.5px;
          background-color: #858585;
        }

        tr:hover {
          background-color: #3E3E3E;
        }

        td {
          border-bottom: 1px solid #4D4D4D;
          color: #D8D8D8;
          font-family: "Maison Neue";
          font-size: 14px;
          font-weight: 500;
          line-height: 24px;
          padding: 3px 10px;
        }
      }

      .score-number {
        text-align: right;
        padding-right: 20px;
      }
    }
  }
}
