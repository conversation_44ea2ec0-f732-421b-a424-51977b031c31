.selected-themes-panel {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  position: fixed;
  bottom: 40px;
  left: 340px;
  color: #EEEEEE;
  font-family: <PERSON>son Neue Light;
  font-size: 12px;
  letter-spacing: 0.1px;
  line-height: 14px;

  .selected-theme {
    cursor: pointer;
    height: 30px;
    margin-left: 16px;
    margin-bottom: 10px;
    border: solid 1px rgba(238, 238, 238, 0.5);
    padding: 8px;
    background-color: rgba(21, 19, 19, 0.5);

    .icon {
      background-image: url(../imgs/icons/close.svg);
      background-repeat: no-repeat;
      background-position: 10px 0;
      padding-left: 20px;
    }
  }
}
