#new-theme-popup {
  min-width: 300px;
  max-height: 700px;

  .theme-popup-buttons-wrapper {
    margin: 17px 0 17px 0;
    padding: 0 16px 0 16px;

    .theme-popup-button {
      margin-right: 10px;
      width: 95px;
      height: 32px;
      color: #EEEEEE;
      background-color: rgb(17,17,17, 0.9);
      font-family: "Maison Neue";
      font-size: 12px;
      font-weight: 300;
      letter-spacing: 0.1px;
      line-height: 14px;
      border: 1px solid rgba(238, 238, 238, 0.2);
      cursor: pointer;

      &[disabled] {
        color: #EEEEEE;
        border: 1px solid #EEEEEE;
        opacity: 0.2;
        cursor: not-allowed;
      }
    }
  }
  .popup-info {
    padding: 0 16px 16px 16px;
  }

  .popup-table tr {
      cursor: pointer;

      .entity-score {
        width: 75px;
      }

      td {
        min-width: 89px;
      }
  }
}

