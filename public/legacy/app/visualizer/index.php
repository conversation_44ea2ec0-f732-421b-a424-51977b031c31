<?php
include('../../config/config.php');
session_start();

if (isset($_SESSION['LoggedIn'])) {
    if (isset($_SESSION['ProjectKeys'])) {
        $projectkey = $_SERVER['QUERY_STRING'];
        $labname = $_SESSION['ProjectKeys'][$projectkey]['Account'];
        $expname = $_SESSION['ProjectKeys'][$projectkey]['Project'];
        if(isset($_SESSION['ProjectKeys'][$projectkey]['ProjectId'])) {
            $projectId = $_SESSION['ProjectKeys'][$projectkey]['ProjectId'];
        }
        if(isset($_SESSION['ProjectKeys'][$projectkey]['Directory'])) {
            $directory = $_SESSION['ProjectKeys'][$projectkey]['Directory'];
        }
        $id = $_SESSION['Id'];
        $email = $_SESSION['Email'];
        $token = $_SESSION['_token'];
        
    }
    include(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'cpv_header.php');
} else {
    session_destroy();
    header('Location: ' . WEBROOT);
    exit;
}
echo '<title>' . urlencode($expname) . '</title>';
if (file_exists(CSS_VIZ_ROOT . DIRECTORY_SEPARATOR . 'cpv.min.css')) {
    echo '<link rel="stylesheet" type="text/css" href="css/cpv.min.css?ver=' . filemtime(CSS_VIZ_ROOT . DIRECTORY_SEPARATOR . 'cpv.min.css') . '">';
} else {
    echo '<link rel="stylesheet" type="text/css" href="css/cpv.css?ver=' . filemtime(CSS_VIZ_ROOT . DIRECTORY_SEPARATOR . 'cpv.css') . '">';
}

/*Dev Color Wheel Remove*/
echo '<link rel="stylesheet" type="text/css" href="css/wheel-remove.css?ver=' . filemtime(CSS_VIZ_ROOT . DIRECTORY_SEPARATOR . 'wheel-remove.css') . '">';
/*Dev End*/
?>
<style>
    #loadingScreen {
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        background-color: #000000;
        z-index: 999999;
    }

    #loadingScreen img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
</style>
<style>
    #hud {
        height:100%;
    }

    .hud-btn-btns,
    .view-btn {
        cursor: pointer;
    }

    #auto_annotate,
    #drag_lock,
    #idea-header-visibility,
    #expt-unmapped-ent {
        /* border-radius: 2px; */
        /* background: #676767;
        border: 1px solid #8d8d8d;
        margin: 5px;
        padding: 5px;
        user-select: none;
        color: #fff; */
        margin: 5px;
        padding: 5px;
        user-select: none;
        background: #fff;
        /* border: 1px solid #e4deef; */
        color: #27215f;
        font-variant: small-caps;
    }

    .theme-labels {
        border: 3px inset #b3bdf2;
        padding: 5px;
        border-radius: 4px;
    }

    .theme-label-bold {
        font-weight: bold;
    }

    div#auto-annote-header {
        position: fixed;
        padding: 5px;
        display: none;
        cursor: -webkit-grab;
        cursor: grab;
        /* width: 20vw; */
        /* border: 1px solid #262626; */
        /* resize: both; */
    }

    div#auto-annote-header h3 {
        text-align: center;
        margin: 5px 0;
    }

    div#auto-annote-header p {
        margin: 5px 0;
    }

    button#idea-header-visibility {
        display: none;
        width: 90%;
    }

    button#expt-unmapped-ent {
        width: 90%;
        margin: 10px;
    }

    #graph-warning {
        color: red;
        margin: 10px 5px;
        overflow-wrap: break-word;
        word-wrap: break-word;
        font-size: 75%;
    }

    .chat-container {
        z-index: 5;
        position: fixed;
        bottom: 0;
        height: 15%;
        width: calc(100% - 23%);
        right: 0;
        border: solid black 1px;
        background-color: #eeecff;
        /* transition: width 0.1s ease-out; */
    }

    .chat-box-area {
        background-color: white;
        object-position: top;
        width: 100%;
        overflow-y: scroll;
        height: 50%;
    }

    .warning-log {
        padding-top: 10px;
        padding-left: 10px;
        font-style: italic;
        font-size: 0.7em;
        color: red;
        padding-bottom: 1em;
    }

    .chat-log {
        padding: 10px;
        font-family: consolas, sans-serif;
        font-size: 0.8em;
    }

    .chat-bar {
        padding-left: 10px;
        object-position: bottom;
        margin-top: 10px;
        width: 100%;
    }

    .chat-input {
        width: 40%;
        padding-left: 10px;
        padding-right: 10px;
    }

    .chat-button {  
        width: 10%;
        padding-right: 10px;
    }

    i#chat-container-min {
        position: absolute;
        top: 7px;
        right: 10px;
        font-size: 1rem;
        color: #6f7b91;
    }

    #loadingoverlay {
    position: absolute;
    top: 0;
    z-index: 999;
    width: 100%;
    height: 100%;
    display: none;
    background: rgba(0,0,0,0.6);
    }

    .cv-spinner {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px #ddd solid;
        border-top: 4px #2e93e6 solid;
        border-radius: 50%;
        animation: sp-anime 0.8s infinite linear;
    }

    /**
    Dark Chat and HUD trial
    */
#chat-box-area::-webkit-scrollbar,#group-view::-webkit-scrollbar, #gene-view::-webkit-scrollbar, #theme-view::-webkit-scrollbar {
    /* width: 15px; */
    background: #343a40;
}
#chat-box-area::-webkit-scrollbar-thumb,#group-view::-webkit-scrollbar-thumb, #gene-view::-webkit-scrollbar-thumb, #theme-view::-webkit-scrollbar-thumb {
    background: #f0f0f0;
    box-shadow: inset 0 0 2px #383838;
    border: 1px #a9a7a7;
    border-radius: 1px;
}
#chat-box-area::-webkit-scrollbar-thumb:hover,#group-view::-webkit-scrollbar-thumb:hover, #gene-view::-webkit-scrollbar-thumb:hover, #theme-view::-webkit-scrollbar-thumb:hover {
    background: #125487
    box-shadow: inset 0 0 2px #383838;
    border: 1px #a9a7a7;
    border-radius: 1px;
}
#chat-box-area::-webkit-scrollbar-track,#group-view::-webkit-scrollbar-track, #gene-view::-webkit-scrollbar-track, #theme-view::-webkit-scrollbar-track {
    /* box-shadow: inset 0 0 15px #27215f; */
    /* background-image: linear-gradient(#712cf980, #712cf91a); */
}
i#chat-container-min:hover {
    cursor: pointer;
    color: #125487;
}
button#chat-button {
    border-style: none;
    padding: 5px;
    border-radius: 5px;
}
button#chat-button:hover {
    background-color: #125487;
    color: #372762;
    border: 1px solid #372762;
}
.chat-input {
    width: 40%;
    padding-left: 10px;
    padding-right: 10px;
    background-color: #343a40;
    border-style: none;
    padding: .375rem .75rem;
    font-size: .9rem;
    font-weight: 400;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-clip: padding-box;
    border-radius: 5px;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
    color: #ffffff;
}
input#chat-input:active {
    border: #4602ff 1px solid;
}
input#chat-input:focus-visible {
    border: #4602ff 1px solid;
}
.chat-bar {
    padding-left: 10px;
    object-position: bottom;
    margin-top: 10px;
    width: 100%;
    position: absolute;
    bottom: 50px;
}
.chat-box-area {
    color: white;
    object-position: top;
    width: 100%;
    overflow-y: scroll;
    height: 50%;
    background-color: #212529;
    font-size: 1rem;
}
.chat-log {
    padding: 10px;
    font-family: consolas, sans-serif;
    font-size: 0.85rem;
}
.warning-log {
    padding-top: 10px;
    padding-left: 10px;
    font-style: italic;
    font-size: 0.7em;
    color: white;
    padding-bottom: 1em;
    background-image: linear-gradient(#712cf980, #712cf91a);
}
.chat-container {
    z-index: 5;
    position: fixed;
    bottom: 0;
    height: 15%;
    width: calc(100% - 23%);
    right: 0;
    border: solid black 1px;
    background-color: #212529;
}
div#hud {
    background: #212529;
    background-image: linear-gradient(#27215f, #712cf91a);
}
.hud-views {
    background: #212529;
    font-weight: bold;
    color: #ffffff;
}
#scale {
    border-bottom: 1px solid #fff;
    border-left: none;
    width: auto;
    padding: 5px;
    text-align: center;
    font-variant-caps: small-caps;
    user-select: none;
}


    @keyframes sp-anime {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(359deg);
        }
    }

</style>
    <script>
        /* Get laravel CRSF token for legacy app AJAX calls */
        document.addEventListener('DOMContentLoaded', () => {
            // Create a <meta> element
            const metaElement = document.createElement('meta');
            metaElement.setAttribute('name', 'csrf-token');
            metaElement.setAttribute('content', '<?php echo $token; ?>');

            // Append the <meta> element to the <head>
            document.head.appendChild(metaElement);
            console.log('CSRF meta tag added successfully.');

        });
        const csrfToken = '<?php echo $token; ?>';
    </script>
</head>
<!-- <body class="no-user-select"> -->

<body>
    <div id="loadingScreen">
        <img src="<?php echo IMAGES_WEBROOT; ?>/ajax-loader.gif" id="loadingImage" alt="loading image">
    </div>
    <div id="hud">
        <div id="scale">
            <div id="scale-file" class="allow-user-select"></div>
            <div id="visible-gene-wrap">Visible Entity Total: <span id="visible-gene-total"></span>
                <br>
                Total Entities: <span id="all-gene-total"></span>
            </div>
            <div id="exp-pval" class="allow-user-select"></div>
            <div id="hud-btns">
                <button id="hudClose" class="hud-btn-btns">Hide Display</button>
                <button id="capture-btn" class="hud-btn-btns">Capture Scene</button>
            </div>
            <div>
                <button id="auto_annotate" class="hud-btn-btns">Auto-Annotate</button>
                <button id="drag_lock" class="hud-btn-btns">Label Drag Mode</button>
            </div>
            <div>
                <button id="idea-header-visibility" class="hud-btn-btns">Show/Hide Central Ideas</button>
            </div>
            <div id="view-title">View Type</div>
            <div id="view">
                <button id="theme-view-button" class="view-btn active-view-btn" onclick="hudListViewToggle(event)">Theme-Sphere</button>
                <button id="gene-view-button" class="view-btn" onclick="hudListViewToggle(event)">Entity</button>
                <button id="box-view-button" class="view-btn" onclick="hudListViewToggle(event)">Theme-Box</button>
                <button id="group-view-button" class="view-btn" onclick="hudListViewToggle(event)">Groups</button>
            </div>
            <div id="gene-view-key">
                <div>Score Color Key</div>
                <div><span>0-9:</span><span>&#9673;</span><span>10-99:</span><span>&#9673;</span></div>
                <div><span>100-999:</span><span>&#9673;</span><span>1000+:</span><span>&#9673;</span></div>
            </div>
        </div>
        <div id="theme-view" class="hud-views allow-user-select"></div>
        <div id="gene-view" class="hud-views allow-user-select"></div>
        <div id="group-view" class="hud-views allow-user-select">
            <div id="group-tools">
                <div onclick="groupAdd()" class="group-tools-btn"><i class="fa fa-plus" aria-hidden="true"></i> Add To Group</div>
                <div onclick="deleteGroup()" class="group-tools-btn"><i class="fa fa-trash" aria-hidden="true"></i> Delete Group</div>
            </div>
            <div id="groupListItems"></div>
            <div id="groupList"></div>
        </div>
    </div>
    <div id="auto-annote-header">
        <h3>Central Ideas In Project</h3>
        <div id="auto-annote-header-content"></div>
    </div>
    <input type="button" id="deselectAll" value="Deselect All" onclick="deselectAll()">
    <div id="selectedClusters"></div>
    <div id="clusterInfo"></div>
    <div id="genesPopupDisplay"></div>

    <div id="hiddenClusters"></div>
    <div id="theModal" class="modal">
        <!-- Modal content -->
        <div class="modal-content-info">
            <span class="close">&times;</span>
            <div id="inner-modal"></div>
        </div>
    </div>
    <!--Modal End-->
    <!-- <button id="canvas2CloseBtn" type="button" onclick="closeCanvas2()">CLOSE</button> -->

    <button id="hudOpen">Open Display</button>
    <!--     <div id="quickSaveBtn">Save Profile Progress</div> -->

    <div id="info-popout">
        <div class="popout-content">
            <div id="inner-popout"></div>
        </div>
    </div>
    <div id="visLockPanel"></div>
    <div id="dialog-confirm"></div>
    <div id="dialog-wheel"></div>
    <div class="chat-container" id="chat-box">
        <i id="chat-container-min" class="fa fa-minus-square" aria-hidden="true"></i>
        <div class="warning-log">
            Disclaimer: ChatGPT responses may not be accurate and should only be used as a method of research and discovery, not fact. ChatGPT has been known to create hallucinations and other bad information at times. You have been warned!
        </div>
        <div class="object-top chat-box-area" id="chat-box-area">
        <div class="chat-log" id="chat-log">
            Chatbot: Welcome to the Wash U Chat GPT Bot! Please let me know what questions you have.
        </div>
        </div>
        <div class="chat-bar" id="chat-bar">
            <input type="text" class="chat-input" id="chat-input" placeholder="Enter Your Question Here..." />
            <button type="button" class="chat-button" id="chat-button">Send
            </button>
        </div>
        <div id="loadingoverlay">
            <div class="cv-spinner">
                <span class="spinner"></span>
            </div>
        </div>
    </div>
    
    <div id="dialog-notes" title="Notes">
        <p><i class="fa fa-exclamation-circle" aria-hidden="true"></i> Notes must be saved to a profile to be accessible at another time.</p>
        <textarea id="notes-text-area"></textarea>
    </div>
    <div id="concept-map" title="Concept Map">
        <div id="concept-map-header"></div>
        <div id="hud2">
            <div id="hud2-header">
                <div id="hud2-title">Custom Image Search</div>
                <div class="custom-search-notice">- Notice -<br>Must select two or more terms when using custom search</div>
                <button id="custom-search-btn" onclick="customSearchConstruct()">Begin Search</button>
                <div class="form">
                    <input type="checkbox" name="term" id="customTermCheckBox">Add Custom Term to the Query?
                    <br>
                    <div id="add-on-term"><label for="customTermTextBox">Additional Query Term(s):</label>
                        <br>
                        <input type="text" name="customTerm" id="customTermTextBox">
                        <br>
                        <label for="customTermTextBox">( Comma Seperate Multiple Terms )</label>
                    </div>
                </div>
            </div>
            <div id="hud2-inner-wrap"></div>
        </div>

    </div>
    <?php
    if (file_exists(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'reauthenticate.html')) {
        echo file_get_contents(TEMPLATES_ROOT . DIRECTORY_SEPARATOR . 'reauthenticate.html');
    }
    ?>
    <!--!!!!!!!!!!!!!!!!!!!!!!!!!!!!-->
    <!--!!!!!!DataFolder Start!!!!!!-->
    <script>
        var dataQuery = location.search;
    </script>
    <!--Program Stats-->
    <script type="text/javascript">
        javascript: (function() {
            var script = document.createElement('script');
            script.onload = function() {
                var stats = new Stats();
                document.body.appendChild(stats.dom).setAttribute('id', 'stats');
                requestAnimationFrame(function loop() {
                    stats.update();
                    requestAnimationFrame(loop)
                });
            };
            script.src = 'https://rawgit.com/mrdoob/stats.js/master/build/stats.min.js';
            document.head.appendChild(script);
        })()
    </script>
    <!--Main Data File for Initial Loading Ref:Data-->
    <?php
    $relativePath = str_replace(ACCOUNT_STORAGE_ROOT, '', $directory);
    $relativePath = ltrim($relativePath, '/'); // Ensure no leading slash
    $expname = urlencode($expname);
    $relativePathEncoded = implode('/', array_map('urlencode', explode('/', $relativePath)));

    $scriptUrl = WEBROOT . "legacy/data/{$relativePathEncoded}/{$expname}_Theme_Coordinates.js";
    // echo "<script id='dataSrc' src='" . WEBROOT . "legacy/data/$labname/" . urlencode($expname) . '/' . urlencode($expname) . "_Theme_Coordinates.js'></script>";
    echo "<script id='dataSrc' src='" . $scriptUrl . "'></script>";
    $scriptUrl = WEBROOT . "legacy/data/{$relativePathEncoded}/{$expname}_Genes.js";
    // echo "<script src='" . WEBROOT . "legacy/data/$labname/" . urlencode($expname) . '/' . urlencode($expname) . "_Genes.js'></script>";
    echo "<script src='" . $scriptUrl . "'></script>";
    // $scriptUrl = WEBROOT . "legacy/data/{$relativePathEncoded}/{$expname}_Auto_Annotation.json";
    $scriptUrl = "{$directory}/{$expname}_Auto_Annotation.json";
    ?>

    <?php if (file_exists($scriptUrl)) : ?>
        <!-- Load Auto-Annotations -->
        <?php $autoAnnotationJsonData = str_replace('&','and',file_get_contents($scriptUrl)); ?>
        <script>
            var autoAnnotationDataPreLoad = <?php echo $autoAnnotationJsonData; ?>;
            var autoAnnotationDataPreLoadHeader = (autoAnnotationDataPreLoad[0].name === 'header') ? autoAnnotationDataPreLoad.shift() : null;
            console.log('Auto-Annotations Data Pre-Loaded!');
        </script>
    <?php else : ?>
        <!-- Auto-Annotations JSON file does not exist -->
        <script>
            var autoAnnotationDataPreLoad = null;
            console.log('Auto-Annotations Data NOT Pre-Loaded!');
        </script>
    <?php endif; ?>
    <?php if (file_exists(ABS_ROOT . "/data/{$labname}/{$expname}/{$expname}_EXP_PValue.json")) : ?>
        <!-- Load EXP PValue -->
        <?php 
		$expPValJson = json_decode(file_get_contents(ABS_ROOT . "/data/{$labname}/{$expname}/{$expname}_EXP_PValue.json"));
		if (json_last_error() === JSON_ERROR_NONE) {
			$expPVal = $expPValJson->ExperimentPValue;
		} else {
			$expPVal = null;
		}
	?>
        <script>
            const ExpPVal = <?php echo '"'.$expPVal.'"'; ?>;
            console.log('ExpPVal Loaded Successfully!');
        </script>
    <?php else : ?>
        <!-- ExpPVal file does not exists -->
        <script>
            const ExpPVal = null;
            console.log('ExpPVal file missing, assigning null value');
        </script>
    <?php endif; ?>
    <script src="js/refs.js"></script>
    <script src="js/eventBus.js"></script>
    <script src="js/mainCfg_wu.js"></script>
    <!--Main Script-->
    <?php if (file_exists('js/main.min.js')) : ?>
        <script src="js/main.min.js?ver=<?php echo filemtime('js/main.min.js'); ?>"></script>
    <?php else :
        include(TEMPLATES_ROOT .'/libs_fallback.php') ?>
        <script src="js/main.js?ver=<?php echo filemtime('js/main.js'); ?>"></script>
    <?php endif;
    echo file_get_contents(TEMPLATES_ROOT.'/help_tab.html');
    echo file_get_contents(TEMPLATES_ROOT.'/notes_tab.html'); ?>
    <!--Chat script-->
    <script src="js/chat.js"></script>

    <div id="version-display">Version: v2.7</div>
    <style type="text/css">
        #version-display {
            /* color: white; */
            text-align: center;
            font-size: 12px;
            color: #8c8c8c;
            position: absolute;
            bottom: 0;
            right: 35%;
            padding: 10px;
            font-weight: 300;
            /* font-family: monospace; */
        }
    </style>
    <div id="dialog-graph-options" title="Graph Options" class="dialog-load-hidden" style="display:none;">
        <div class="input-form">
            <div class="row">
                <div id="graph-warning"></div>
                <div class="form-group col-12">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="all">
                        <label class="form-check-label" for="inlineRadio1">All Themes</label>
                        <!-- </div>
                    <div class="form-check form-check-inline"> -->
                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="selected">
                        <label class="form-check-label" for="inlineRadio2">Selected Themes</label>
                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="theme_range">
                        <label class="form-check-label" for="inlineRadio3">Theme Range</label>
                    </div>
                    <!-- <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="inlineRadioOptions" id="inlineRadio3" value="option3">
                        <label class="form-check-label" for="inlineRadio3">PVal Range Only</label>
                    </div> -->
                </div>
                <div class="form-group col-12" id="pval-range-container">
                    <p>
                        <label for="amount1">PVal Range:</label>
                        <input type="text" id="amount1" readonly style="border:0; font-weight:bold;">
                    </p>

                    <div id="slider-range-1"></div>
                </div>
                <div class="form-group col-12" id="nesscore-range-container">
                    <p>
                        <label for="amount2">NES Range:</label>
                        <input type="text" id="amount2" readonly style="border:0; font-weight:bold;">
                    </p>

                    <div id="slider-range-2"></div>
                </div>
                <div class="form-group col-12" id="theme-range-container">
                    <p>
                        <label for="amount3">Theme Range:</label>
                        <input type="text" id="amount3" readonly style="border:0; font-weight:bold;">
                    </p>

                    <div id="slider-range-3"></div>
                </div>
            </div>
        </div>
    </div>
    <div id="dialog-ent-bargarph">
        <div id="ent-bargraph-content"></div>
    </div>
    <!--     /*Dev Color Wheel Remove*/ -->
    <script src="js/wheel-remove.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/wheel-remove.js'); ?>"></script>
    <!--     /*Dev Export Publication Table*/ -->
    <script src="js/publication.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/publication.js'); ?>"></script>
    <!--     /*Dev Create SVG of Scene*/ -->
    <script src="js/capture.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/capture.js'); ?>"></script>
    <script src="js/conceptCapture.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/capture.js') ?>"></script>
    <!--     /*Dev Suggested Annotations*/ -->
    <script src="js/annotation.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/annotation.js'); ?>"></script>
    <?php $scriptUrl = WEBROOT . "legacy/data/{$relativePathEncoded}/{$expname}_Theme_Annotations.json"; ?>
    <?php if (file_exists($scriptUrl)) : ?>
        <script>
            window.addEventListener('DOMContentLoaded', event => getAnnotationData(<?php echo $scriptUrl; ?>));
        </script>
    <?php endif; ?>

    <!--     /*Export to Bar-Graph*/ -->
    <?php if (file_exists(JS_VIZ_ROOT . '/chart.js')) : ?>
        <script src="js/chart.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/chart.js'); ?>"></script>
    <?php endif; ?>

    <!--     /*RBARVE Macro*/ -->
    <?php if (file_exists(JS_VIZ_ROOT . '/theme_concept_entity_macro.js')) : ?>
        <script src="js/theme_concept_entity_macro.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/theme_concept_entity_macro.js'); ?>"></script>
    <?php endif; ?>


    <!-- Full Annotation -->
    <?php if (file_exists(JS_VIZ_ROOT . '/full_annotation.js')) : ?>
        <script src="js/full_annotation.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/full_annotation.js'); ?>"></script>
    <?php endif; ?>

    <!-- Label DragControls -->
    <?php if (file_exists(JS_VIZ_ROOT . '/object_drag.js')) : ?>
        <script src="js/object_drag.js?ver=<?php echo filemtime(JS_VIZ_ROOT . '/object_drag.js'); ?>"></script>
    <?php endif; ?>

    <!-- Entity Bar Graphs -->
    <?php if (file_exists(JS_VIZ_ROOT . '/ent_bargraph.js')) : ?>
        <script src="js/ent_bargraph.js"></script>
    <?php endif; ?>

    <?php if (isset($_SESSION['Sudo'])) : ?>
        <script>
            const isSudo = true;
            console.log('!!!!!!!! IS SUDO !!!!!!!!');
            // $(function() {
            //     $('#expt-unmapped-ent').click(function() {
            //         getProjectInputList(true);
            //     });
            // });
        </script>
    <?php else : ?>
        <script>
            const isSudo = false;
        </script>
    <?php endif; ?>
    <?php $qpPath = "$directory/{$expname}_query_params.json"; ?>
    <?php if (file_exists($qpPath)) : ?>
        <script>
            /* Query Params File Exists */
            var ValidGeneCount = <?php echo json_decode(file_get_contents($qpPath))->ValidGeneCount; ?>;
        </script>
    <?php else : ?>
        <script>
            /* Query Params File DOES NOT Exist */
            var ValidGeneCount = null;
        </script>
    <?php endif; ?>
    <?php echo file_get_contents(TEMPLATES_ROOT . '/footer.html'); ?>
    
