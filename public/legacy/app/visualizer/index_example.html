<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/ico" sizes="16x16" href="../../imgs/favicon.ico">
    <link rel="stylesheet" href="../libs/font-awesome-4.7.0/css/font-awesome.min.css">
    <link href="https://fonts.googleapis.com/css?family=Audiowide|Exo+2:400,700,900|Raleway:400,700,900" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="../libs/jquery-ui/jquery-ui.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../visualizer/css/wheel-remove.css">
    <link rel="stylesheet" type="text/css" href="../visualizer/css/cpv.css">
    <style>
    #loadingScreen {
        position: fixed;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        background-color: #000000;
        z-index: 999999;
    }

    #loadingScreen img {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
    </style>
</head>
<body class="no-user-select">
    <div id="loadingScreen">
        <img src="../visualizer/images/ajax-loader.gif" id="loadingImage" alt="loading image">
    </div>
    <div id="hud">
        <div id="scale">
            <div id="scale-file" class="no-user-select"></div>
            <div id="visible-gene-wrap">Visible Entity Total: <span id="visible-gene-total"></span>
                <br>
                Total Entities: <span id="all-gene-total"></span>
            </div>
            <div id="hudClose">Hide Display</div>
            <div id="view-title">View Type</div>
            <div id="view">
                <div id="theme-view-button" class="view-btn active-view-btn" onclick="hudListViewToggle(event)">Theme</div>
                <div id="gene-view-button" class="view-btn" onclick="hudListViewToggle(event)">Entity</div>
                <div id="group-view-button" class="view-btn" onclick="hudListViewToggle(event)">Groups</div>
            </div>
            <div id="gene-view-key">
                <div>Score Color Key</div>
                <div><span>0-9:</span><span>&#9673;</span><span>10-99:</span><span>&#9673;</span></div>
                <div><span>100-999:</span><span>&#9673;</span><span>1000+:</span><span>&#9673;</span></div>
            </div>
        </div>
        <div id="theme-view" class="hud-views allow-user-select"></div>
        <div id="gene-view" class="hud-views allow-user-select"></div>
        <div id="group-view" class="hud-views allow-user-select">
            <div id="group-tools">
                <div onclick="groupAdd()" class="group-tools-btn"><i class="fa fa-plus" aria-hidden="true"></i> Add To Group</div>
                <div onclick="deleteGroup()" class="group-tools-btn"><i class="fa fa-trash" aria-hidden="true"></i> Delete Group</div>
            </div>
            <div id="groupListItems"></div>
            <div id="groupList"></div>
        </div>
    </div>
    <input type="button" id="deselectAll" value="Deselect All" onclick="deselectAll()">
    <div id="selectedClusters"></div>
    <div id="clusterInfo"></div>
    <div id="genesPopupDisplay"></div>

    <div id="hiddenClusters"></div>
    <div id="theModal" class="modal">
        <!-- Modal content -->
        <div class="modal-content-info">
            <span class="close">&times;</span>
            <div id="inner-modal"></div>
        </div>
    </div>
    <!--Modal End-->
    <!-- <button id="canvas2CloseBtn" type="button" onclick="closeCanvas2()">CLOSE</button> -->
    <div id="hudOpen">Open Display</div>
<!--     <div id="quickSaveBtn">Save Profile Progress</div> -->

    <div id="info-popout">
        <div class="popout-content">
            <div id="inner-popout"></div>
        </div>
    </div>
    <div id="visLockPanel"></div>
    <div id="dialog-confirm"></div>
    <div id="dialog-wheel"></div>
    <div id="dialog-notes" title="Notes">
        <p><i class="fa fa-exclamation-circle" aria-hidden="true"></i> Notes must be saved to a profile to be accessible at another time.</p>
        <textarea id="notes-text-area"></textarea>
    </div>
    <div id="concept-map" title="Concept Map">
        <div id="concept-map-header"></div>
        <div id="hud2">
            <div id="hud2-header">
                <div id="hud2-title">Custom Image Search</div>
                <div class="custom-search-notice">- Notice -<br>Must select two or more terms when using custom search</div>
                <button id="custom-search-btn" onclick="customSearchConstruct()">Begin Search</button>
                <div class="form">
                    <input type="checkbox" name="term" id="customTermCheckBox">Add Custom Term to the Query?
                    <br>
                    <div id="add-on-term">Additional Query Term:
                        <br>
                        <input type="text" name="customTerm" id="customTermTextBox">
                    </div>
                </div>
            </div>
            <div id="hud2-inner-wrap"></div>
        </div>
    </div>
    <div id="dialog-session-confirm" title="Please Log In">
        <h4><b>!!Warning!!</b></h4>
        <p>Due to inactivity, you must re-login to continue.</p>
        <p>If you do not login, any unsaved changes will be lost.</p>
        <p id="auth-msg">The submitted information is incorrect.</p>
        <label>Account Name:</label><br>
        <input type="text" id="inactive-acct-name" placeholder="Account Name" required><br>
        <label>Password:</label><br>
        <input type="password" id="inactive-password" placeholder="Password" required>
    </div>
    <!--!!!!!!!!!!!!!!!!!!!!!!!!!!!!-->
    <!--!!!!!!DataFolder Start!!!!!!-->
    <script>
    var dataQuery = location.search;
    </script>
    <!--Program Stats-->
    <script type="text/javascript">
    // javascript: (function() {
    //     var script = document.createElement('script');
    //     script.onload = function() {
    //         var stats = new Stats();
    //         document.body.appendChild(stats.dom).setAttribute('id', 'stats');
    //         requestAnimationFrame(function loop() {
    //             stats.update();
    //             requestAnimationFrame(loop)
    //         });
    //     };
    //     script.src = 'https://rawgit.com/mrdoob/stats.js/master/build/stats.min.js';
    //     document.head.appendChild(script);
    // })()
    </script>
    <script src="../libs/three.js"></script>
    <script src="../libs/Detector.js"></script>
    <script src="../libs/OrbitControls.js"></script>
    <script src="../libs/dat.gui.js"></script>
    <script src="../libs/THREE.MeshLine.js"></script>
    <script src="../libs/geometry.js"></script>
    <script src="../libs/THREE.TextTexture.js"></script>
    <script src="../libs/THREE.TextSprite.js"></script>
    <script src="../libs/CSS3DRenderer.js"></script>
    <script src="../libs/SubdivisionModifier.js"></script>
    <script type="text/javascript" src="../../js/mainCfg.js"></script>
    <script type="text/javascript" src="../visualizer/js/main.js"></script>
    <script type="text/javascript" src="../visualizer/js/wheel-remove.js"></script>

    <div id="version-display"></div>
    <style type="text/css">
        #version-display {
            color: white;
            text-align: center;
            font-size: 12px;
            position: absolute;
            bottom: 5px;
            margin: auto;
            left: 0;
            right: 0;
            font-family: monospace;
        }
    </style>
    <style type="text/css">
        #help-tab {
            font-family: Roboto, sans-serif !important;
            font-size: 14px !important;
            text-transform: uppercase;
            bottom: 38px;
            right: calc(3em + 124px);
            position: fixed !important;
            background-color: #ccc !important;
            border-right: 1px solid #000000;
            border-radius: 5px 5px 0 0 !important;
            padding: 0 17px !important;
            height: 0;
            line-height: 30px !important;
            cursor: pointer !important;
            text-shadow: none !important;
            width: auto !important;
            margin: 0 !important;
            color: #58595b;
            box-sizing: content-box;
            z-index: 3147483637;
            -webkit-transition: height 0.8s;
            transition: height 0.8s;
        }

        .help-tab-inner {
            position: relative;
            bottom: -3px;
        }

        @media (max-width: 575px) {
           #help-tab {
               right: 0px;
           }
       }
    </style>
    <div id="help-tab">
        <div class="help-tab-inner">
            <i class="fa fa-question-circle" aria-hidden="true"></i> Help
        </div>
    </div>
    <style type="text/css">
        #notes-tab {
            font-family: Roboto, sans-serif !important;
            font-size: 14px !important;
            text-transform: uppercase;
            bottom: 38px;
            right: 17.65em;
            position: fixed !important;
            background-color: #ccc !important;
            border-right: 1px solid #000000;
            border-radius: 5px 5px 0 0 !important;
            padding: 0 17px !important;
            height: 0;
            line-height: 30px !important;
            cursor: pointer !important;
            text-shadow: none !important;
            width: auto !important;
            margin: 0 !important;
            color: #58595b;
            box-sizing: content-box;
            z-index: 3147483637;
            -webkit-transition: 0.5s;
            transition: 0.5s;
        }
        .notes-tab-inner {
            position: relative;
            bottom: -3px;
        }
    </style>
    <div id="notes-tab">
        <div class="notes-tab-inner">
            <i class="fa fa-sticky-note" aria-hidden="true"></i> Notes
        </div>
    </div>
</body>
</html>

