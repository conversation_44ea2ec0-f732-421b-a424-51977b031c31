"use strict";
let annotations = null;
let selectedLabel = null;

function getAnnotationData($path = null) {
    console.log('Getting Annotation Data')
    fetch( `${Webroot}data/${ProjectAcct.toUpperCase()}/${Project}/${Project}_Theme_Annotations.json` )
        .then( response => {
            if( !response.ok ) { throw new Error( `HTTP error: ${response.status}` ) };
            return response.json();
        })
        .then( response => {
            annotations = response;
            console.log(annotations)
            console.log('Annotation Data Loaded');
        })
        .catch( error => {
            console.log( `FETCH error: ${error}` );
        });
}

function createAnnotationSelection( object ) {
    object.userData.referenceName = object.name.replace(/spriteText=/g, '');
    object.userData.objectAnnotationData = annotations[ object.userData.referenceName ].Annotations;
    object.userData.objectAnnotations = object.userData.objectAnnotationData.map( obj => `${obj.Score},${encodeURIComponent( obj.Entry )},${obj.Source}` );
}

async function labelSelect( event ) {
    if(typeof autoAnnotationActive == 'undefined' || autoAnnotationActive != true) {
        if( document.body.contains( document.getElementById('annotation-menu') ) ) {
            destroyAnnotationMenu();
        }
        selectedLabel = (event.target.classList.contains('annotation-flag')) ? spriteTextGroup.getObjectByName( event.target.parentElement.id ) : spriteTextGroup.getObjectByName( event.target.id );
        if( selectedLabel.userData.hasOwnProperty( 'objectAnnotationData' ) ) {
            const menuElement = await createAnnotationMenu({ x:event.clientX, y:event.clientY });
            const menuAnnotations = await getElementAnnotationsArray( labelIntersects[0] );
            const menuItems = await createMenuItems( menuAnnotations );
            const table = await appendItemsToMenu( menuElement.firstChild, menuItems );
            // menu.appendChild( menu );
            document.body.appendChild( menuElement );
            menuElement.style.width = `${table.clientWidth}px`;
            menuElement.style.height = `${table.clientHeight}px`;
            await attachMenuCloseEvent( menuElement ); 
        } else {
            selectedLabel = null;
            alertInformation('Theme does not have any suggested annotations...');
        }
    }
}

function attachMenuCloseEvent( element ) {
    return new Promise( resolve => {
        document.getElementById('label-renderer').addEventListener( 'mousedown', offMenuEventClose );
        resolve();
    });
}

function appendItemsToMenu( parent, children ) {
    return new Promise( resolve => {
        children.forEach( child => {
            child.addEventListener('click', function( event ) { 
                if( dragKeyDown === false ) {
                    annotationSelected( child.children[1].innerText.split('[')[0] );
                }
            });
            parent.appendChild( child );
        });
        resolve( parent );
    });
}

function createMenuItems( array ) {
    return new Promise( resolve => {
        const elementsArray = array.map(( item, index ) => {
            console.log(item)
            const score = Number( item.split(',')[0] ).toFixed( 2 ).toString();
            const source = item.split(',')[2] === '"' ? '?' : item.split(',')[2];
            item = decodeURIComponent( item.split(',')[1] );
            const row = document.createElement('TR')
            row.classList.add('annotation-table-row');
            const scoreElement = document.createElement('TD');
            const element = document.createElement('TD');
            scoreElement.innerText = score;
            scoreElement.classList.add('annotation-score');
            row.appendChild( scoreElement );
            // element.innerText = item;
            //Temp Fix - Wayne Needs To Fix In Exe
            element.innerText = item.replace(/\+/g, ' ').replace(/%26/g, '&').replace(/\[/g, '(').replace(/]/g, ')');
            element.innerText += `[${source}]`;
            element.classList.add('annotation-selection');
            row.appendChild( element );
            return row;
        });
        resolve( elementsArray );
    });
}

function getElementAnnotationsArray( label ) {
    return new Promise( resolve => {
        const name = label.id;
        const labelObject = spriteTextGroup.getObjectByName( name );
        const labelAnnotations = [ ...labelObject.userData.objectAnnotations ];
        resolve( labelAnnotations );
    });
}

function createAnnotationMenu( coordinates ) {
    return new Promise( resolve => {
        let element = document.createElement('TABLE');
        element.id = 'annotation-menu';
        let headerRow = document.createElement('TR');
        headerRow.id = 'header-row';
        let headerScore = document.createElement('TH');
        headerScore.id = 'header-score';
        headerScore.innerText = 'Score';
        headerRow.appendChild( headerScore );
        let headerCell = document.createElement('TH');
        headerCell.id = 'header-cell';
        headerCell.innerText = 'Potential Theme Annotations';
        headerRow.appendChild( headerCell );
        element.appendChild( headerRow );
        let wrap = document.createElement('DIV');
        wrap.id = 'annotation-menu-wrap';
        wrap.appendChild( element );
        wrap.style.left = `${coordinates.x}px`;
        wrap.style.top = `${coordinates.y}px`;
        $( wrap ).draggable({ cancel: ['.annotation-selection', '.annotation-score' ]});
        resolve( wrap );
    });
}

function annotationSelected( name ) {
    console.log('flag! SELECTED')
    const previousSelectedCluster = selectedCluster;
    parameters.a = name;
    console.log(name)
    selectedCluster = clusterGroup.getObjectByName( selectedLabel.userData.referenceName );
    console.log(selectedLabel)
    console.log(selectedCluster)
    selected.push( selectedCluster.name );
    console.log(selected)
    guiSaveClusterName();
    destroyAnnotationMenu( event );
    selected.pop();
    selectedCluster = previousSelectedCluster;
    console.log(selectedLabel)
    appendFlagToText( selectedLabel );
    selectedLabel = null;
}

function destroyAnnotationMenu() {
    console.log('destroy')
    let element = document.getElementById('annotation-menu-wrap');
    element.parentNode.removeChild( element );
    document.getElementById('label-renderer').removeEventListener( 'mousedown', offMenuEventClose );
}

function offMenuEventClose( event ) {
    console.log('handler')
    const target = event.target;
    const menu = document.getElementById('annotation-menu-wrap');
    console.log(target)
    console.log(menu)
    if( target !== menu || !menu.contains( target )) {
        destroyAnnotationMenu();
    }
}

function appendFlagToText( object ) {
    const flag = document.createElement('SPAN');
    flag.classList.add('annotation-flag');
    flag.innerText = '*';
    if(object.element.childElementCount === 0 ) {
        object.element.appendChild(flag);
    }
}