/*Convert Scene To SVG*/
const capBtn = document.getElementById( 'capture-btn' );
// let parentTab;
// let childTab;
const hudOffset = document.getElementById( 'hud' ).clientWidth;
capBtn.addEventListener( 'click', function() {

    initCapture();

} );
//Create SVG Renderer
async function initCapture() {
    //Declare Variables
    let svgRenderer, svgScene, svgGroup, svgData = {};
    if(boxViewActive) {
        // alertInformation("Currently, only Theme-Sphere capture is available.  Theme-Box view capturing is coming soon but is still under development at this time...");
        // return;
        svgData.boxViewActive = true;
    } else {
        svgData.boxViewActive = false;
    }
    const svgCreated = await createSvgScene();
    const visibleSceneObjs = await getVisibleObjs( clusterGroup );
    const filteredObjs = await getInViewObjs( visibleSceneObjs, clusterGroup, camera1 );
    const svgComplete = await loopConvertObjs( filteredObjs, clusterGroup, camera1 );
    const themeLabelTexts = await getLabelText( filteredObjs );
    const labelCoordinates = await getLabelCoords( filteredObjs );
    const labelStyles = await getCaptureThemeLabels( themeLabelTexts );
    const themeSphereTexts = await getThemeSphereTexts( filteredObjs );
    const boxElements = (boxViewActive) ? await getBoxElements(filteredObjs) : null;
    const boxBCR = (boxViewActive) ? await getBoxBoundingClientRect(filteredObjs) : null;
    const sceneStyles = await getSceneStyles();
    svgData.labelCoordinates = labelCoordinates;
    svgData.filteredObjs = filteredObjs;
    svgData.themeLabelTexts = themeLabelTexts;
    svgData.themeSphereTexts = themeSphereTexts;
    svgData.groupElements = svgComplete;
    svgData.styles = sceneStyles;
    svgData.labelStyles = labelStyles;
    svgData.boxElements = boxElements;
    svgData.boxBCR = boxBCR;
    const childWindow = await createNewWindow( svgData );
    const cleanRenderer = await clearSvgRenderBuffer(svgData);
    const captureComplete = await logCompletion();
}

/*Capture Promises*/
function getBoxBoundingClientRect(array) {
    return new Promise((resolve) => {
        let elBCR = [];
        for ( let i = 0; i < array.length; i++ ) {
            if(clusterGroup.getObjectByName(array[i]).geometry.type === 'SphereBufferGeometry' 
                || clusterGroup.getObjectByName(array[i]).geometry.type === 'SphereGeometry') {
                    if( typeof boxViewGroup.getObjectByName(`themeBoxEl=${array[i]}`) !== 'undefined' 
                        && boxViewGroup.getObjectByName(`themeBoxEl=${array[i]}`).element.style.display !== 'none' ) {
                            let tmpObj = {};
                            let org = boxViewGroup.getObjectByName(`themeBoxEl=${array[i]}`).element;
                            tmpObj.org = org.getBoundingClientRect();
                            if(org.style.border != '') {
                                tmpObj.custBorder = org.style.border;
                            }
                            let orgTabCont = org.querySelector('.themebox-table-cont');
                            tmpObj.orgTabCont = orgTabCont.getBoundingClientRect();
                            let h1 = orgTabCont.querySelector('th.themebox-con');
                            tmpObj.h1 = h1.getBoundingClientRect();
                            let h2 = orgTabCont.querySelector('th.themebox-ent');
                            tmpObj.h2 = h2.getBoundingClientRect();
                            let conCol = orgTabCont.querySelectorAll('td.themebox-con');
                            tmpObj.conCol = [];
                            for( let td of conCol ) {
                                tmpObj.conCol.push(td.getBoundingClientRect());
                            }
                            let entCol = orgTabCont.querySelectorAll('td.themebox-ent');
                            tmpObj.entCol = [];
                            for( let td of entCol ) {
                                tmpObj.entCol.push(td.getBoundingClientRect());
                            }
                            elBCR.push(tmpObj);
                    }
            }
        }
        resolve(elBCR);
    });
}

function getBoxElements(array) {
    return new Promise((resolve) => {
        let elms = [];
        for ( let i = 0; i < array.length; i++ ) {
            if(clusterGroup.getObjectByName(array[i]).geometry.type === 'SphereBufferGeometry' 
                || clusterGroup.getObjectByName(array[i]).geometry.type === 'SphereGeometry') {
                    if( typeof boxViewGroup.getObjectByName(`themeBoxEl=${array[i]}`) !== 'undefined' 
                        && boxViewGroup.getObjectByName(`themeBoxEl=${array[i]}`).element.style.display !== 'none' ) {
                            elms.push(boxViewGroup.getObjectByName(`themeBoxEl=${array[i]}`).element.outerHTML);
                    }
            }
        }
        resolve( elms );
    });
}

function getLabelCoords( array ) {
    return new Promise( ( resolve ) => {
        let labelCoords = [];
        for ( let i = 0; i < array.length; i++ ) {
            if ( clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereBufferGeometry' || clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereGeometry' ) {
                let coords = spriteTextGroup.getObjectByName( 'spriteText=' + array[ i ]).element.getBoundingClientRect();
                labelCoords.push( coords );
            }
        }
        resolve( labelCoords );
    } );
}
function getCaptureThemeLabels( array ) {
    return new Promise(( resolve ) => {
        let labels = [];
        array.forEach((label) => {
            let cssObj = camera1.position.distanceTo( spriteTextGroup.getObjectByName('spriteText=' + Object.keys( label )[0]).position );
            labels.push( cssObj );
        });
        resolve( labels );
    });
}

function logCompletion() {
    return new Promise( ( resolve ) => {
        console.log( 'Final Memory Usage:' )
        try {
            console.log( window.performance.memory )
        } catch ( error ) {
            console.log( 'Memory Usage Statics Are Not Supported On This Device' )
            console.log( error )
        }
        console.log( 'Capture Process Complete!' )
        resolve( true );
    } );
}

function clearSvgRenderBuffer(svgData) {
    return new Promise( ( resolve ) => {
        while( svgGroup.children.length > 0 ) {
            svgGroup.remove( svgGroup.children[0] );
        }
        while( svgScene.children.length > 0 ) {
            svgScene.remove( svgScene.children[0] );
        }
        svgData = null;
        svgGroup = null;
        svgScene = null;
        svgRenderer.domElement.remove();
        svgRenderer = null;

        resolve( true );
    } );
}

function createNewWindow( object ) {
    return new Promise( ( resolve ) => {
        let captureWindow = window.open();
        try {
            captureWindow.localStorage.setItem( 'svgData', JSON.stringify( object ) );
        } catch (error) {
            console.log(error);
            captureWindow.localStorage.setItem( 'svgData', JSON.stringify( object ) );
        }
        captureWindow.location = `${Webroot}legacy/app/visualizer/capture/`;
        resolve( true );
    } );
}

function getSceneStyles() {
    return new Promise( ( resolve ) => {
        let bG = window.getComputedStyle(document.body, null).background;
        let bgColor = window.getComputedStyle(document.body,null).backgroundColor;
        let bgImage = window.getComputedStyle(document.body,null).backgroundImage;
        let styles = {
            bodyBackground: bG,
            bodyBackgroundColor: bgColor,
            bodyBackgroundImage: bgImage,            
            labelFont: spriteTextGroup.children[ 0 ].element.style.fontFamily,
            labelColor: parameters.d3,
            labelBackground: parameters.d5,
            sphereFont: cubeGroup.children[ 0 ].material[ 4 ].map.image.getContext( '2d' ).font
        };        
        resolve( styles );
    } );
}

function getThemeSphereTexts( array ) {
    return new Promise( ( resolve ) => {
        let sphereTexts = [];
        for ( let i = 0; i < array.length; i++ ) {
            if ( clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereBufferGeometry' || clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereGeometry' ) {
                let index = clusterGroup.getObjectByName( array[ i ] ).userData.indexKey;
                let obj = {};
                obj[ array[ i ] ] = ( parameters.j.toLowerCase() === 'none' ) ? cubeGroup.children[ index ].userData.concepts : cubeGroup.children[ index ].userData.visibleConcepts;
                sphereTexts.push( obj );
            }
        }
        resolve( sphereTexts );
    } );
}

function getDisplayText( array ) {
    return new Promise( ( resolve ) => {
        let displayNames = [];
        for ( let i = 0; i < array.length; i++ ) {
            if ( clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereBufferGeometry' || clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereGeometry' ) {
                let obj = {};
                obj[ array[ i ] ] = clusterGroup.getObjectByName( array[ i ] ).userData.Name;
                displayNames.push( obj );
            }
        }
        resolve( displayNames );
    } );
}

function getLabelText( array ) {
    return new Promise( ( resolve ) => {
        let labelTexts = [];
        for ( let i = 0; i < array.length; i++ ) {
            if ( clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereBufferGeometry' || clusterGroup.getObjectByName( array[ i ] ).geometry.type === 'SphereGeometry' ) {
                let obj = {};
                obj[ array[ i ] ] = spriteTextGroup.getObjectByName( 'spriteText=' + array[ i ] ).element.innerText;
                labelTexts.push( obj );
            }
        }
        resolve( labelTexts );
    } );
}


function loopConvertObjs( array, group, camera ) {
    return new Promise( ( resolve ) => {
        let svgArray = [];
        for ( let i = 0; i < array.length; i++ ) {
            let displayName = ( typeof( group.getObjectByName( array[ i ] ).userData.Name ) !== 'undefined' ) ? group.getObjectByName( array[ i ] ).userData.Name : group.getObjectByName( array[ i ] ).name;
            let geomType = ( group.getObjectByName( array[ i ] ).geometry.type === 'SphereBufferGeometry' || group.getObjectByName( array[ i ] ).geometry.type === 'SphereGeometry' ) ? 'sphere' : 'tube';
            if( geomType === 'sphere' ) {
                if (dragLines.visible === true) {
                    svgGroup.add(dragLines.getObjectByName(array[i]).clone());
                    svgRenderer.render(svgScene, camera);
                    svgArray.push(`<g id="dragLine_${array[i]}" name="dragLine_${array[i]}" data-geom-type="line" class="converted-svg-elements draggable-element">${document.getElementById('svg-renderer').innerHTML}</g>`);
                    svgGroup.remove(svgGroup.children[0]);
                    svgRenderer.render(svgScene, camera);
                }
            }
            if( boxViewActive === false || boxViewActive && geomType === 'tube' ) {
                svgGroup.add( group.getObjectByName( array[ i ] ).clone());
                svgRenderer.render( svgScene, camera );
                svgArray.push( `<g id="${array[i]}" name="${displayName}" data-geom-type="${geomType}" class="converted-svg-elements draggable-element">${document.getElementById( 'svg-renderer' ).innerHTML}</g>` );
                svgGroup.remove( svgGroup.children[ 0 ] );
                svgRenderer.render( svgScene, camera );
            }
        }
        resolve( svgArray );
    } );
}


function getInViewObjs( array, group, camera ) {
    return new Promise( ( resolve ) => {
        let viewObjs = [];
        for ( let i = 0; i < array.length; i++ ) {
            if ( cameraViewCheck( group.getObjectByName( array[ i ] ), camera ) === true ) {
                viewObjs.push( array[ i ] );
            }
        }
        resolve( viewObjs );
    } );
}

function getVisibleObjs( group ) {
    return new Promise( ( resolve ) => {
        let visObjs = [];
        //Loop and Gather Objs with Visible Property Set True
        if( boxViewActive ) {
            for ( let i = 0; i < group.children.length; i++ ) {
                if( group.children[ i ].geometry.type === 'SphereBufferGeometry' || group.children[ i ].geometry.type === 'SphereGeometry') {
                    let n = group.children[ i ].name;
                    if( typeof boxViewGroup.getObjectByName(`themeBoxEl=${n}`) !== 'undefined' 
                        && boxViewGroup.getObjectByName(`themeBoxEl=${n}`).element.style.display !== 'none' ) {
                            visObjs.push( group.children[ i ].name );
                        }
                } else if ( group.children[ i ].visible === true ) {
                    visObjs.push( group.children[ i ].name );
                }
            }
        } else {
            for ( let i = 0; i < group.children.length; i++ ) {
                if ( group.children[ i ].visible === true ) {
                    visObjs.push( group.children[ i ].name );
                }
            }
        }
        resolve( visObjs );
    } );
}

function createSvgScene( bool = false ) {
    return new Promise( ( resolve ) => {
        let success;
        try {
            //Define and Append Renderer
            svgRenderer = new THREE.SVGRenderer();
            svgRenderer.domElement.id = 'svg-renderer';
            svgRenderer.setClearColor( 0x000000, 0 );
            svgRenderer.setPixelRatio( window.devicePixelRatio );
            svgRenderer.setQuality('high');
            svgRenderer.overdraw = 0;
            // svgRenderer.domElement.style.zIndex = '9999999'; //Only needed to view end result when developing
            document.body.appendChild( svgRenderer.domElement );
            //Define Scene
            svgScene = new THREE.Scene();
            if ( bool === false ) {
                svgRenderer.setSize( window.innerWidth, window.innerHeight );
                svgRenderer.domElement.style.width = `${window.innerWidth}px`;
                svgRenderer.domElement.style.height = `${window.innerHeight}px`;                
                //Define Light
                let svgAmbientLight = new THREE.AmbientLight( 0xffffff, 0.5 );
                // let svgTopDirectionalLight = new THREE.DirectionalLight( 0xffffff, 0.8 );
                // let svgBottomDirectionLight = new THREE.DirectionalLight( 0xffffff, 0.8 );
                // let svgPointLight = new THREE.PointLight( 0xffffff, 0.7 );
                //Position Light
                // svgTopDirectionalLight.position.set( 0, 200, 200 );
                // svgBottomDirectionLight.position.set( 0, -200, -200 );
                // svgPointLight.position.set( 0, 0, 0 );
                //Define Group
                svgGroup = new THREE.Group();
                //Add Light and Group to Scene
                // svgScene.add( svgAmbientLight, svgTopDirectionalLight, svgBottomDirectionLight, svgPointLight, svgGroup );
                // svgScene.add( svgAmbientLight, svgTopDirectionalLight, svgBottomDirectionLight, svgGroup );
                svgScene.add( svgAmbientLight, svgGroup );
                svgRenderer.render( svgScene, camera1 );
            } else if ( bool === true ) {
                svgRenderer.setSize( document.getElementById('canvas3').clientWidth, document.getElementById('canvas3').clientHeight );
                svgRenderer.domElement.style.width = `${document.getElementById('canvas3').clientWidth}px`;
                svgRenderer.domElement.style.height = `${document.getElementById('canvas3').clientHeight}px`;                   
                // let svgPointLight1 = new THREE.PointLight( 0xffffff, 1, 0 );
                // let svgPointLight2 = new THREE.PointLight( 0xffffff, 1, 0 );
                // let svgPointLight3 = new THREE.PointLight( 0xffffff, 1, 0 );
                // svgPointLight1.position.set( 0, 0, 50 );
                // svgPointLight2.position.set( 50, 50, 50 );
                // svgPointLight3.position.set( -50, 50, 50 );
                //Define Group
                svgGroup = new THREE.Group();
                //Add Light and Group to Scene
                // svgScene.add( svgPointLight1, svgPointLight2, svgPointLight3, svgGroup );
                svgScene.add( svgGroup );
                svgRenderer.render( svgScene, camera3 );
            }

            //Resolve Promise
            resolve( true );
        } catch ( err ) {
            console.log( err )
        }
    } );
}

/*Promises End*/

/*Helper Functions*/

//Return bool of if object in camera view

function isInViewport( obj ) {
    let td = toScreenPosition( obj, viewer );
    let vpd = { x: window.innerWidth, y: window.innerHeight };
    if ( ( ( td.x > 0 && td.x < vpd.x ) && ( td.y > 0 && td.y < vpd.y ) ) && ( obj.visible === true ) ) {
        return true;
    } else {
        return false;
    }
}
//Get 2d coordinates
function toScreenPosition( obj ) {
    var vector = new THREE.Vector3();
    var widthHalf = 0.5 * renderer.context.canvas.width;
    var heightHalf = 0.5 * renderer.context.canvas.height;
    obj.updateMatrixWorld();
    vector.setFromMatrixPosition( obj.matrixWorld );
    vector.project( camera );

    vector.x = ( vector.x * widthHalf ) + widthHalf;
    vector.y = -( vector.y * heightHalf ) + heightHalf;

    return {
        x: vector.x - document.getElementById( 'hud' ).clientWidth,
        y: vector.y
    };
};

function cameraViewCheck( obj, camera ) {
    var frustum = new THREE.Frustum();
    camera.updateMatrix();
    camera.updateMatrixWorld();
    frustum.setFromMatrix( new THREE.Matrix4().multiplyMatrices( camera.projectionMatrix, camera.matrixWorldInverse ) );
    if ( typeof(obj.geometry) !== "undefined" && obj.geometry.type === "TubeBufferGeometry" && frustum.containsPoint( obj.geometry.boundingSphere.center ) ) {
        return true;
    } else if ( frustum.containsPoint( obj.position ) ) {
        return true;
    } else {
        return false;
    }
}
/*Helper End*/

/* // Testing Purposes Only - Renderers finished svg over canvas 
// Notice: Requires reload/refresh after each capture
function viewSvgObjs( svgGroups ) {
    return new Promise((resolve) => {
        $('#canvas1').toggle();
        svgGroups.reverse();
        var svgElem = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svgElem.id = 'testSvg';
        svgElem.setAttribute( 'viewBox', document.getElementById('svg-renderer').getAttribute('viewBox') );
        document.body.appendChild(svgElem);
        svgElem.height = window.innerHeight;
        svgElem.width = window.innerWidth;
        svgElem.style.height = window.innerHeight + 'px';
        svgElem.style.width = window.innerWidth + 'px';
        svgElem.style.zIndex = '9999999999';
        svgElem.style.position = 'absolute';
        svgElem.style.top = 0;
        svgElem.style.left = 0;
        for( let i=0; i<svgGroups.length; i++ ) {
            svgElem.innerHTML += svgGroups[i];
        }
        resolve(true);
    });
}
*/

