"use strict";
// function gatherGraphData() {
// 	let selectedDataArray = [];
// 	selected.forEach(name => {
// 		let target = clusterGroup.getObjectByName(name).userData;
// 		let itemData = [];
// 		itemData.push(target.Name,target.Score);
// 		selectedDataArray.push(itemData);
// 	});
// 	selectedDataArray.sort((a,b) => {
// 		return b[1]-a[1];
// 	})
// 	dataToNewTab(selectedDataArray);
// }
function dataToNewTab(array) {
	console.log(array);
	let params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,
width=${window.innerWidth},height=${window.innerHeight},top=0`;
	let path = './chart/';
	let graphTab = window.open(path,'_blank',params);
	graphTab.sessionStorage.setItem('graphData',JSON.stringify(array));
	// graphTab.blur();
	// window.focus();
	// graphTab.print();
	// graphTab.addEventListener('afterprint',(event) => {
	// 	console.log('tab printed');
	// 	graphTab.close();
	// 	window.focus();
	// });
}
function testParentFunction() {
	let str = 'parent function called from child';
	console.log(str);
	return str;
}

/** Entity Curve **/
function prepareEntityCurve() {
	let curveData = [];
	let insignificantCount = 0;
	for(let i=0; i<optimalItemsSize(); i++) {
		let colName = ( clusterGroup.children[i].userData.Name.includes( 'Theme' )) ? clusterGroup.children[i].userData.Name : clusterGroup.children[i].userData.Name.split('<br>')[0];
		if( colName.length >= 30 ) {
			colName = colName.slice( 0,27 ) + '...';
		}
		let tmp = {
			Name: colName,
			Genes: data[i].Genes,
			Pval: clusterGroup.children[i].userData.PValue ?? null
		}
		if(tmp.Pval !== null && tmp.Pval > 0.1) {
			tmp.Genes = {};
			insignificantCount++;
		}
		curveData.push(tmp);
	}
	let significantDiff = optimalItemsSize() - insignificantCount;
	if(insignificantCount < optimalItemsSize() && significantDiff > 2) {
		openNewCurveTab(curveData)
	} else {
		let str = 'No Significant Themes Found';
		alertInformation(str);
	}
}

function openNewCurveTab(array) {
	let valTot = [...new Set(data.map(item => Object.keys(item.Genes)).flat())].length
	let params = `scrollbars=no,resizable=no,status=no,location=no,toolbar=no,menubar=no,
width=${window.innerWidth/1.5},height=${window.innerHeight/1.5},left=0,top=0`;
	let path = './curve/';
	let graphTab = window.open(path,'_blank',params);
	let curveDataObj = {
		project:sessionStorage.Project || 'Compbio_Entity_Curve',
		validTotal:parseInt(document.getElementById('all-gene-total').innerText),
		hasPVal:clusterGroup.children[0].userData.hasOwnProperty('PValue'),
		clusterData:array
	};
	graphTab.sessionStorage.setItem('curveData',JSON.stringify(curveDataObj));

	graphTab.blur();
}

// New Graph Init
function gatherGraphData() {
	console.log( barGraphSelectType );
	let selectedDataArray = [];
	let recheckLabelSores = false;
	if( gui.__folders['Visual Setting'].__controllers[4].domElement.firstChild.checked === true ) {
		recheckLabelSores = true;
		gui.__folders['Visual Setting'].__controllers[4].domElement.firstChild.click();
	}
	if( barGraphSelectType == 'theme_range' ) {
			let pvalRange = {
				min: $('#amount1').val().split(' - ')[0],
				max: $('#amount1').val().split(' - ')[1]
			};
			let nesRange = {
				min: $('#amount2').val().split(' - ')[0],
				max: $('#amount2').val().split(' - ')[1]
			};
			let thmRange = {
				min: $('#amount3').val().split(' - ')[0],
				max: $('#amount3').val().split(' - ')[1]
			};			
			console.log(pvalRange);
			console.log(nesRange);
			console.log(thmRange);
			let tmpSelected = [];
			for( let i=thmRange.min-1;i<thmRange.max;i++ ) {
				tmpSelected.push( clusterGroup.children[i].name );
			}
			if(data[0].hasOwnProperty('PValue') && data[0].PValue !== 'N/A' && data[0].hasOwnProperty('NEScore') && data[0].NEScore !== 'N/A') {
				tmpSelected.forEach(name => {
					let target = clusterGroup.getObjectByName(name).userData;
					let tPval = ( target.PValue.includes('<') ) ? 0 : target.PValue;
					let pValAnnote = ( target.PValue.includes('<') ) ? 'p' + target.PValue : 'p=' + target.PValue;
					let tNes = target.NEScore;
					if( tPval + 0.001 >= pvalRange.min && tPval - 0.001 <= pvalRange.max && tNes + 0.001 >= nesRange.min && tNes - 0.001 <= nesRange.max ) {
						let itemData = [];
						// let colName = ( target.Name.includes( 'Theme' )) ? target.Name : target.Name.split('<br>')[0];
						let colName = spriteTextGroup.getObjectByName('spriteText=' + name).element.innerText.split('\n')[0];
						colName = colName.replace(/\(\d\.\d\)$/gm, '');
						// if( colName.length >= 50 ) {
						// 	colName = colName.slice( 0,47 ) + '...';
						// }
						itemData.push(colName,Number( target.NEScore ),pValAnnote);
						selectedDataArray.push(itemData);
					}
				});
				// selectedDataArray.sort((a,b) => {
				// 	return b[1]-a[1];
				// });
			} else {
				tmpSelected.forEach(name => {
					let target = clusterGroup.getObjectByName(name).userData;
					let itemData = [];
					// let colName = ( target.Name.includes( 'Theme' )) ? target.Name : target.Name.split('<br>')[0];
					let colName = spriteTextGroup.getObjectByName('spriteText=' + name).element.innerText.split('\n')[0];
					// if( colName.length >= 50 ) {
					// 	colName = colName.slice( 0,47 ) + '...';
					// }
					itemData.push(colName,target.Score,'');
					selectedDataArray.push(itemData);
				});
				// selectedDataArray.sort((a,b) => {
				// 	return b[1]-a[1];
				// });
			}
			clearGraphWarning();
			// $( "#dialog-graph-options" ).dialog('close');
	} else if( barGraphSelectType == 'selected' ) {
		if( selected.length > 0 ) {
			// let pvalRange = {
			// 	min: $('#amount1').val().split(' - ')[0],
			// 	max: $('#amount1').val().split(' - ')[1]
			// };
			// let nesRange = {
			// 	min: $('#amount2').val().split(' - ')[0],
			// 	max: $('#amount2').val().split(' - ')[1]
			// };
			// console.log(pvalRange);
			// console.log(nesRange);
			if(data[0].hasOwnProperty('PValue') && data[0].PValue !== 'N/A' && data[0].hasOwnProperty('NEScore') && data[0].NEScore !== 'N/A') {
				selected.forEach(name => {
					let target = clusterGroup.getObjectByName(name).userData;
					let tPval = ( target.PValue.includes('<') ) ? 0 : target.PValue;
					let pValAnnote = ( target.PValue.includes('<') ) ? 'p' + target.PValue : 'p=' + target.PValue;
					let tNes = target.NEScore;
					// if( tPval >= pvalRange.min && tPval <= pvalRange.max && tNes >= nesRange.min && tNes <= nesRange.max ) {
						let itemData = [];
						let colName = spriteTextGroup.getObjectByName('spriteText=' + name).element.innerText.split('\n')[0];
						// let colName = ( target.Name.includes( 'Theme' )) ? target.Name : target.Name.split('<br>')[0];
						// if( colName.length >= 50 ) {
						// 	colName = colName.slice( 0,47 ) + '...';
						// }
						itemData.push(colName,Number( target.NEScore ),pValAnnote);
						selectedDataArray.push(itemData);
					// }
				});
				// selectedDataArray.sort((a,b) => {
				// 	return b[1]-a[1];
				// });
			} else {
				selected.forEach(name => {
					let target = clusterGroup.getObjectByName(name).userData;
					let itemData = [];
					// let colName = ( target.Name.includes( 'Theme' )) ? target.Name : target.Name.split('<br>')[0];
					let colName = spriteTextGroup.getObjectByName('spriteText=' + name).element.innerText.split('\n')[0];
					// if( colName.length >= 50 ) {
					// 	colName = colName.slice( 0,47 ) + '...';
					// }
					itemData.push(colName,target.Score,'');
					selectedDataArray.push(itemData);
				});
				// selectedDataArray.sort((a,b) => {
				// 	return b[1]-a[1];
				// });
			}
			clearGraphWarning();
			// $( "#dialog-graph-options" ).dialog('close');
		} else {
			console.log('inside else');
			alertInformation( 'No Themes Meet Option Filter Requirements...' );
		}
	} else {
		let allThemeNames = [];
		for( let i=0;i<optiLength;i++ ) {
			allThemeNames.push( clusterGroup.children[i].name );
		}
		console.log('allThemeNames');
		console.log(allThemeNames);
		let pvalRange = {
			min: $('#amount1').val().split(' - ')[0],
			max: $('#amount1').val().split(' - ')[1]
		};
		let nesRange = {
			min: $('#amount2').val().split(' - ')[0],
			max: $('#amount2').val().split(' - ')[1]
		};
		console.log(pvalRange);
		console.log(nesRange);
		if(data[0].hasOwnProperty('PValue') && data[0].PValue !== 'N/A' && data[0].hasOwnProperty('NEScore') && data[0].NEScore !== 'N/A') {
			allThemeNames.forEach(name => {
				let target = clusterGroup.getObjectByName(name).userData;
				let tPval = ( target.PValue.includes('<') ) ? 0 : target.PValue;
				let pValAnnote = ( target.PValue.includes('<') ) ? 'p' + target.PValue : 'p=' + target.PValue;
				let tNes = target.NEScore;
				if( tPval + 0.001 >= pvalRange.min && tPval - 0.001 <= pvalRange.max && tNes + 0.001 >= nesRange.min && tNes - 0.001 <= nesRange.max ) {
					let itemData = [];
					// let colName = ( target.Name.includes( 'Theme' )) ? target.Name : target.Name.split('<br>')[0];
					let colName = spriteTextGroup.getObjectByName('spriteText=' + name).element.innerText.split('\n')[0];
					// if( colName.length >= 50 ) {
					// 	colName = colName.slice( 0,47 ) + '...';
					// }
					itemData.push(colName,Number( target.NEScore ),pValAnnote);
					selectedDataArray.push(itemData);
				}
			});
			// selectedDataArray.sort((a,b) => {
			// 	return b[1]-a[1];
			// });
		} else {
			allThemeNames.forEach(name => {
				let target = clusterGroup.getObjectByName(name).userData;
				let itemData = [];
				// let colName = ( target.Name.includes( 'Theme' )) ? target.Name : target.Name.split('<br>')[0];
				let colName = spriteTextGroup.getObjectByName('spriteText=' + name).element.innerText.split('\n')[0];
				// if( colName.length >= 50 ) {
				// 	colName = colName.slice( 0,47 ) + '...';
				// }
				itemData.push(colName,target.Score,'');
				selectedDataArray.push(itemData);
			});
			// selectedDataArray.sort((a,b) => {
			// 	return b[1]-a[1];
			// });
		}
		clearGraphWarning();
		// $( "#dialog-graph-options" ).dialog('close');
	}
	// let selectedDataArray = [];
	// selected.forEach(name => {
	// 	let target = clusterGroup.getObjectByName(name).userData;
	// 	let itemData = [];
	// 	itemData.push(target.Name,target.Score);
	// 	selectedDataArray.push(itemData);
	// });
	if( recheckLabelSores === true ) {
		gui.__folders['Visual Setting'].__controllers[4].domElement.firstChild.click();
	}	
	if( selectedDataArray.length ) {
		selectedDataArray.sort((a,b) => {
			return b[1]-a[1];
		});
		$( "#dialog-graph-options" ).dialog('close');
		console.log( selectedDataArray );
		dataToNewTab(selectedDataArray);
	} else {
		console.log( selectedDataArray );
		displayGraphWarning( 'No Themes Have Been Selected.<br>Please Select Theme(s) or <br>select "All" in the Graph Options...' );
	}
}
var barGraphSelectType = 'all';
// Graph Pre-Load Options
$( function() {
	$('#theme-range-container').hide();
	$( "#dialog-graph-options" ).dialog({
	autoOpen: false,
	resizable: false,
	height: "auto",
	width: "auto",
	modal: true,
	buttons: {
		"Confirm": function() {
			console.log('Confirming Graph Options!');
			gatherGraphData();
		},
		Cancel: function() {
			clearGraphWarning();
			$( this ).dialog( "close" );
		}
	}
	});
	Array.from( $('input[name=inlineRadioOptions]') ).forEach((rad) => {
		$( rad ).change(function() {
			barGraphSelectType = $( rad ).val();
			if( barGraphSelectType == 'theme_range' ) {
				$('#pval-range-container').show();
				$('#nesscore-range-container').show();
				$('#theme-range-container').show();
			} else if( barGraphSelectType == 'selected' ) {
				// $('#theme-range-container').show();
				$('#pval-range-container').hide();
				$('#nesscore-range-container').hide();
				$('#theme-range-container').hide();		
			} else {
				$('#pval-range-container').show();
				$('#nesscore-range-container').show();
				$('#theme-range-container').hide();
			}
		});
	});
	if(data[0].hasOwnProperty('PValue') && data[0].PValue !== 'N/A' && data[0].hasOwnProperty('NEScore') && data[0].NEScore !== 'N/A') {
		$( "#slider-range-1" ).slider({
			range: true,
			min: 0,
			max: Math.max(...data.map(i => i.PValue).map(i => i.replace(/\<|N\/A/,'')).filter(Number).map(i => Number(i))) + 0.1,
			values: [ 0,Math.max(...data.map(i => i.PValue).map(i => i.replace(/\<|N\/A/,'')).filter(Number).map(i => Number(i))) ],
			slide: function( event, ui ) {
				$( "#amount1" ).val( ui.values[ 0 ] + " - " + ui.values[ 1 ] );
			},
			step: 0.001
		});
		$( "#amount1" ).val( $( "#slider-range-1" ).slider( "values", 0 ) +
		" - " + $( "#slider-range-1" ).slider( "values", 1 ) );

		$( "#slider-range-2" ).slider({
			range: true,
			min: 1,
			max: getMaxNeScore() + 0.1,
			values: [ 1.2, getMaxNeScore() ],
			slide: function( event, ui ) {
				$( "#amount2" ).val( ui.values[ 0 ] + " - " + ui.values[ 1 ] );
			},
			step: 0.001
		});
		$( "#amount2" ).val( $( "#slider-range-2" ).slider( "values", 0 ) +
		" - " + $( "#slider-range-2" ).slider( "values", 1 ) );

	} else {
		$('#pval-range-container').hide();
		$('#nesscore-range-container').hide();
	}
	$( "#slider-range-3" ).slider({
		range: true,
		min: 1,
		max: optimalItemsSize(),
		values: [ 1,optimalItemsSize() ],
		slide: function( event, ui ) {
			$( "#amount3" ).val( ui.values[ 0 ] + " - " + ui.values[ 1 ] );
		},
		step: 1
	});
	$( "#amount3" ).val( $( "#slider-range-3" ).slider( "values", 0 ) +
		" - " + $( "#slider-range-3" ).slider( "values", 1 ) );
});

function displayGraphWarning( msg ) {
	console.log('inside displayGraphWarning');
	console.log(msg);
	$('#graph-warning').html( msg );
}
function clearGraphWarning() {
	console.log('inside clearGraphWarning');
	$('#graph-warning').html('');
}
function getMaxNeScore() {
	return Math.max(...data.slice(0,optimalItemsSize()).map(i => i.NEScore).map(i => i.replace(/\<|N\/A/,'')).filter(Number).map(i => Number(i)));
}
// Graph Options
// $( function() {
// 	$('#inlineRadio1')
// });