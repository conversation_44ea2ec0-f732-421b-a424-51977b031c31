"use strict";
const hostname = getHostNameRoot();
// const apiEndpoint = hostname + ":8080/api/washu-chatgpt-query";
const apiEndpoint = getChatApiEndPoint();
  /* Sends chat message to chat GPT, and the response is appended to the current chat log string and returned */

const scrollSmoothlyToBottom = (id) => {
  const element = $(`#${id}`);
  element.animate({
      scrollTop: element.prop("scrollHeight")
  }, 500);
}

async function sendChatMessage(chatMsg, chatLog) {
    /* structure data object */
    let apiOptions = {  
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': btoa('admin:password')
        },
        body: JSON.stringify({
            "messages": [
                {"role":"user", "content": chatMsg}
            ]
        })
      }
    $("#loadingoverlay").fadeIn();
    document.getElementById('chat-input').value = 'Getting response...';
    document.getElementById('chat-input').disabled = true;
    document.getElementById('chat-button').disabled = true;
    /* send api call to chat GPT */
    const response = await fetch(apiEndpoint, apiOptions).then(t => t.text()).then(x => JSON.parse(x)).catch(err => {
      console.log("Chat message api error: " + err);
    });;

    /* append question and response to chat log and return the new string */
    let responseMsg = response["choices"][0]["message"]["content"];
    console.log("Response message: " + responseMsg);
    let updatedLog = chatLog + '\n' + 'User: ' + chatMsg + '\n' + 'Chatbot: ' + responseMsg;
    console.log("Updated log: " + updatedLog);
    return updatedLog;

}

document.getElementById('chat-button').addEventListener('click', function() {
  RunChatEvent();
});

document.getElementById('chat-input').addEventListener('keypress', function(event) {
  if (event.key === "Enter") {
    event.preventDefault();
    RunChatEvent();
  }
});

function RunChatEvent() {
  console.log('triggered send chat message: ' + document.getElementById('chat-input').value);

  sendChatMessage(document.getElementById('chat-input').value, document.getElementById('chat-log').innerText).then(response => {
    document.getElementById('chat-log').innerText = response;
    document.getElementById('chat-input').value = '';
    document.getElementById('chat-input').disabled = false;
    document.getElementById('chat-button').disabled = false;
    const chatBox = document.getElementById('chat-log');
    scrollSmoothlyToBottom('chat-box-area');
    document.getElementById('chat-input').focus();
    $("#loadingoverlay").fadeOut();
  }); 
}

/* Chat Box Resize */
const chatContainer = document.getElementById('chat-box');
const warningLog = document.querySelector('.warning-log');
const chatBar = document.getElementById('chat-bar');
const chatBoxArea = document.getElementById('chat-box-area');

let isResizing = false;
let startY = 0;
let startHeight = 0;

warningLog.addEventListener('mousedown', (e) => {
  isResizing = true;
  startY = e.clientY;
  startHeight = chatContainer.offsetHeight;
  e.preventDefault();
});

warningLog.addEventListener('mouseover', (e) => {
  document.body.style.cursor = 'ns-resize';
});
warningLog.addEventListener('mouseout', (e) => {
  document.body.style.cursor = 'default';
});
document.addEventListener('mousemove', (e) => {
  if (!isResizing) return;

  const dy = startY - e.clientY;
  const newHeight = startHeight + dy;

  // Minimum height constraint
  if (newHeight > 150) {
    chatContainer.style.height = `${newHeight}px`;
  }

  const chatAreaHeight = chatContainer.clientHeight - warningLog.clientHeight - chatBar.clientHeight - 60;
  // Resize Chat Area
  chatBoxArea.style.height = `${chatAreaHeight}px`;
});

document.addEventListener('mouseup', () => {
  if (isResizing) {
    isResizing = false;
  }
});

/* 
 * Selected Clusters and Deselect All
 * Reposition based on chat-box position
*/
const chatBoxEl = document.getElementById('chat-box');
const deselectAllEl = document.getElementById('deselectAll');
const selectedClustersEl = document.getElementById('selectedClusters');

function updateFloatingUIPosition() {
  // Return them to normal position if chatbox is hidden
  const isChatBoxVisible = getComputedStyle(chatBoxEl).visibility === 'visible';
  if (!isChatBoxVisible) {
    deselectAllEl.style.bottom = '30px';
    selectedClustersEl.style.bottom = '30px';
    return;
  }

  // Don't move them if they're hidden
  const isDeselectVisible = getComputedStyle(deselectAllEl).visibility === 'visible';
  const isSelectedClustersVisible = getComputedStyle(selectedClustersEl).visibility === 'visible';

  if (!isDeselectVisible && !isSelectedClustersVisible) return;

  const chatBoxRect = chatBoxEl.getBoundingClientRect();

  // Distance from the bottom of the window to top of chat-box
  const newBottom = window.innerHeight - chatBoxRect.top + 10;

  if (isDeselectVisible) {
    deselectAllEl.style.bottom = `${newBottom}px`;
    deselectAllEl.style.zIndex = 1000;
  } else {
    deselectAllEl.style.zIndex = -2;
    deselectAllEl.style.bottom = '30px';
  }

  if (isSelectedClustersVisible) {
    // Position selectedClustersEl 5px to the right of deselectAllEl
    const deselectRect = deselectAllEl.getBoundingClientRect();
    const newLeft = deselectRect.right + 5;
    selectedClustersEl.style.left = `${newLeft}px`;
    selectedClustersEl.style.bottom = `${newBottom}px`;
    selectedClustersEl.style.zIndex = 1000;
  } else {
    deselectAllEl.style.zIndex = -2;
    deselectAllEl.style.bottom = '30px';
  }
}

// Attach a resize observer for #chat-box height changes
const chatBoxResizeObserver = new ResizeObserver(() => {
  updateFloatingUIPosition();
});
chatBoxResizeObserver.observe(document.getElementById('chat-box'));

// Also run this on window resize or layout changes
window.addEventListener('resize', updateFloatingUIPosition);

// Observer config for visibility-related attributes
const observerConfig = {
  attributes: true,
  attributeFilter: ['style', 'class']
};

// Create observer for both elements
const visibilityObserver = new MutationObserver(() => {
  updateFloatingUIPosition();
});

visibilityObserver.observe(deselectAllEl, observerConfig);
visibilityObserver.observe(selectedClustersEl, observerConfig);
visibilityObserver.observe(chatBoxEl, observerConfig);
