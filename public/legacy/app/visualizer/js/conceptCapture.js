/*Concept Map Capture*/
async function initConceptCapture() {
    var svgRenderer, svgScene, svgGroup, svgData = {};
    const svgCreated = await createSvgScene( true );
    const visibleLines = await getVisibleObjs( lineConceptGroup );
    const filteredLines = await getInViewObjs( visibleLines, lineConceptGroup, camera3 );
    const visibleText = await getVisibleObjs( meshConceptGroup );
    const filteredText = await getInViewObjs( visibleText, meshConceptGroup, camera3 );
    const svgTubes = await loopConvertObjs( filteredLines, lineConceptGroup, camera3 );
    const svgText = await svgConvertText( filteredLines, filteredText, lineConceptGroup, meshConceptGroup, camera3 );
    const svgViewBox = await getSvgViewBox();
    svgData.type = 'concept-map';
    svgData.viewBox = svgViewBox;
    svgData.background = GLOBAL_STATE.scene3.background.getHexString();
    svgData.canvasWidth = document.getElementById('canvas3').width;
    svgData.canvasHeight = document.getElementById('canvas3').height;
    window.svgTubes = svgTubes;
    window.svgText = svgText;
    const childWindow = await createCaptureWindow( svgData );
    const cleanRenderer = await clearSvgRenderBuffer();
    const captureComplete = await logCompletion();
}

function createCaptureWindow( object ) {
    return new Promise( ( resolve ) => {
        console.log( 'Setting Svg SessionStorage!' )
        let captureWindow = window.open();
        captureWindow.location = `${Webroot}app/visualizer/conceptCapture/`;
        captureWindow.sessionStorage.setItem( 'svgData', JSON.stringify( object ) );
        resolve( captureWindow );
    } );
}

function chunkArray( array ) {
    return new Promise( ( resolve ) => {
        let max = 2250000;
        let arrString = JSON.stringify( array );
        let arrStringLength = arrString.length;
        let numChunks = Math.ceil( arrStringLength / max );
        let chunks = new Array( numChunks )

        for ( let i = 0, o = 0; i < numChunks; ++i, o += max ) {
            chunks[ i ] = arrString.substr( o, max )
        }
        resolve( chunks );
    } );
}

function testSvg( array ) {
    return new Promise( ( resolve ) => {
        let svgEl = document.getElementById( 'svg-renderer' );
        array.forEach( function( gEl ) {
            svgEl.innerHTML += gEl;
        } );
        $( '#concept-map' ).dialog( 'close' );
        $( '#canvas1' ).toggle();
        resolve( true )
    } );
}

function svgConvertText( array1, array2, group1, group2, camera ) {
    return new Promise( ( resolve ) => {
        let endPoints = [];
        for ( let i = 0; i < array1.length; i++ ) {
            let tempArr = group1.getObjectByName( array1[ i ] ).userData.endpoints;
            if ( endPoints.indexOf( tempArr[ 0 ] ) < 0 ) {
                endPoints.push( tempArr[ 0 ] );
                if( array2.includes(tempArr[0]) ) {
                    array2.splice( array2.indexOf( tempArr[0] ), 1 );
                }
            }
            if ( endPoints.indexOf( tempArr[ 1 ] ) < 0 ) {
                endPoints.push( tempArr[ 1 ] );
                if( array2.includes(tempArr[1]) ) {
                    array2.splice( array2.indexOf( tempArr[1] ), 1 );
                }
            }
        }
        let svgArray = [];
        for ( let i = 0; i < endPoints.length; i++ ) {
            svgGroup.add( group2.getObjectByName( endPoints[ i ] ).clone() );
            svgRenderer.render( svgScene, camera );
            svgArray.push( `<g id="${endPoints[i]}" name="${endPoints[i]}" data-geom-type="text-mesh" class="converted-svg-elements draggable-element">${document.getElementById( 'svg-renderer' ).innerHTML}</g>` );
            svgGroup.remove( svgGroup.children[ 0 ] );
            svgRenderer.render( svgScene, camera );
        }
        for ( let i = 0; i < array2.length; i++ ) {
            svgGroup.add( group2.getObjectByName( array2[ i ] ).clone() );
            svgRenderer.render( svgScene, camera );
            svgArray.push( `<g id="${array2[i]}" name="${array2[i]}" data-geom-type="text-mesh" class="converted-svg-elements draggable-element">${document.getElementById( 'svg-renderer' ).innerHTML}</g>` );
            svgGroup.remove( svgGroup.children[ 0 ] );
            svgRenderer.render( svgScene, camera );
        }
        resolve( svgArray );
    } );
}

function getSvgViewBox() {
    return new Promise( ( resolve ) => {
        let viewBoxValues = [];
        let vB = document.getElementById( 'svg-renderer' ).getAttribute( 'viewBox' ).split( ' ' );
        resolve( document.getElementById( 'svg-renderer' ).getAttribute( 'viewBox' ) );
    } );
}

/*Dev - output vars*/
function devOutput( output = '**Development Output**' ) {
    console.log( output )
}

function getByteLen( normal_val ) {
    // Force string type
    normal_val = String( normal_val );

    var byteLen = 0;
    for ( var i = 0; i < normal_val.length; i++ ) {
        var c = normal_val.charCodeAt( i );
        byteLen += c < ( 1 << 7 ) ? 1 :
            c < ( 1 << 11 ) ? 2 :
            c < ( 1 << 16 ) ? 3 :
            c < ( 1 << 21 ) ? 4 :
            c < ( 1 << 26 ) ? 5 :
            c < ( 1 << 31 ) ? 6 : Number.NaN;
    }
    return byteLen;
}