var google;
if (document.readyState === "loading") {
  // Loading hasn't finished yet
  document.addEventListener("DOMContentLoaded", initGoogleBar<PERSON>hartApi);
} else {
  // `DOMContentLoaded` has already fired
  initGoogleBar<PERSON>hartA<PERSON>();
}
function initGoogleBarChartApi() {
   console.log('Google InIt Fired!')
   // Load the Visualization API and the corechart package.
   google.charts.load('current', {'packages':['corechart']});
   // Set a callback to run when the Google Visualization API is loaded.
   google.charts.setOnLoadCallback(function () {
      console.log('Google On Load Callback Fired!');
      $("#dialog-ent-bargarph").dialog({
         autoOpen: false,
         modal: false,
         width: window.innerWidth / 1.5,
         height: window.innerHeight / 1.5,
         show: {
               effect: "scale"
         },
         hide: {
               effect: "scale"
         },
         buttons: {
            "Download": {
               text: "Download",
               id: "debg-dl-btn",
               click: function() {
                  event.preventDefault();
                  downloadSVG();
               }
            },
            "Close": {
               text: "Close",
               id: "debg-close-btn",
               click: function() {
                  event.preventDefault();
                  $(this).dialog('close');
                  document.getElementById('ent-bargraph-content').innerHTML = '';
               }
            }
         }
      });
   });
}
function downloadSVG() {
   const svg = document.querySelector('#ent-bargraph-content SVG').outerHTML;
   const blob = new Blob([svg.toString()]);
   const element = document.createElement("a");
   element.download = "EntScoreGraph.svg";
   element.href = window.URL.createObjectURL(blob);
   element.click();
   element.remove();
}
function graphEntScore(ents,scores) {
   console.log(ents);
   console.log(scores);
   $('#dialog-ent-bargarph').dialog('open');
   setTimeout(() => {
      let gTable = new google.visualization.DataTable();
      gTable.addColumn('string','Entity Name');
      gTable.addColumn('number','Enrichment Score');
      // gTable.addColumn({role:'annotation'});
      for(let i=0;i<ents.length;i++) {
         gTable.addRow([
            ents[i].replace(/^\(e\)/,''),
            Math.log2(parseFloat(scores[i])),
            // parseFloat(scores[i]).toFixed(2),
         ]);
      }
      console.log(gTable);
      let barGraph = new google.visualization.BarChart(document.getElementById('ent-bargraph-content'));
      let options = {
         'title': spriteTextGroup.getObjectByName('spriteText='+selectedCluster.name).element.innerText,
         'titleTextStyle': {
            'bold': true,
            'fontSize': 24
         },
         'legend': { 
            position: 'none' 
         },
         'hAxis': {
            'title': 'Enrichment Score(log2)',
            'titleTextStyle': {
               'italic': false,
               'bold': true,
               'fontSize': 24
            },
            'minValue': 1,
            'viewWindow': {
               'min': 1
            } 
         },
         'vAxis': {
            'title': 'Entity Name',
            'titleTextStyle': {
               'italic': false,
               'bold': true,
               'fontSize': 24
            }
         },
         selectionMode: 'none',
         'tooltip' : {
            trigger: 'none'
         },
         'bar': {
            'class':'h-axis-bar'
         },
         'colors': [`#${prevColor[prevColor.length-1] ?? '000015'}`],
         height: '100%',
         width: '100%'
      };
      barGraph.draw(gTable,options);
      console.log(barGraph);
      setTimeout(() => {
         document.getElementsByTagName
         document.querySelector('#ent-bargraph-content SVG').setAttribute('xmlns', 'http://www.w3.org/2000/svg');
         document.querySelector('[clip-path]').setAttribute('clip-path','url(#' + document.getElementsByTagName("clipPath")[0].id + ')');
      }, 1000);
   }, 1000);
}
// Window create resizeEnd event
$(window).resize(function() {
    if(this.resizeTO) clearTimeout(this.resizeTO);
    this.resizeTO = setTimeout(function() {
        $(this).trigger('resizeEnd');
    }, 500);
});
// Redraw when window resize is completed  
$(window).on('resizeEnd', function() {
   if($('#dialog-ent-bargarph').dialog('isOpen')) {
      $("#dialog-ent-bargarph").dialog( "option", "width", window.innerWidth / 1.5 );
      $("#dialog-ent-bargarph").dialog( "option", "height", window.innerHeight / 1.5 );
      graphEntScore(selectedCluster.userData.Genes.slice(0,10),selectedCluster.userData.Gene_Scores.slice(0,10));
   }
});
// $("#dialog-ent-bargarph").on('dialogresizestop',function() {
//    if($('#dialog-ent-bargarph').dialog('isOpen')) {
//       graphEntScore(selectedCluster.userData.Genes.slice(0,10),selectedCluster.userData.Gene_Scores.slice(0,10));
//    }
// });