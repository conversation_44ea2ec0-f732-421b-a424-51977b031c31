// ref https://github.com/PierfrancescoSoffritti/light-event-bus.js

var eventBus = (function() {
  function EventBus() {
    const subscriptions = {};

    this.subscribe = function subscribeCallbackToEvent(eventType, callback) {
      const id = Symbol("id");
      if (!subscriptions[eventType]) subscriptions[eventType] = {};
      subscriptions[eventType][id] = callback;
      return {
        unsubscribe: function unsubscribe() {
          delete subscriptions[eventType][id];
          if (
            Object.getOwnPropertySymbols(subscriptions[eventType]).length === 0
          ) {
            delete subscriptions[eventType];
          }
        }
      };
    };

    this.publish = function publishEventWithArgs(eventType, arg) {
      if (!subscriptions[eventType]) return;

      Object.getOwnPropertySymbols(subscriptions[eventType]).forEach(key =>
        subscriptions[eventType][key](arg)
      );
    };
  }

  return new EventBus();
})();

if (typeof onRefChange === 'function') {
	onRefChange((context, property, newValue, oldValue) => {
		eventBus.publish('stateChange', context, property, newValue, oldValue);
	});
}

eventBus.subscribe('stateChange', function ([_, property, newValue]) {
	if (window.location.hostname === 'localhost') {
		if( property !== 'mouseOverDiv' ) {
      console.log('stateChange', property, newValue);
    }
  }
});