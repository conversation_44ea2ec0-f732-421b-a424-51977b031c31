var autoAnnotationActive = false;
var autoAnnotationProfileRestore = false;
sessionStorage.setItem( 'autoAnnotationActive',false );
async function initAutoAnnotation() {
   annotations = null;
   autoAnnotationActive = true;
   showLoader();
   if(autoAnnotationDataPreLoad !== null && autoAnnotationDataPreLoad.length > 0) {
      if(document.getElementById('auto_annotate') !== null) {
         document.getElementById('auto_annotate').remove();
      }
      console.log(autoAnnotationDataPreLoadHeader);
      if(autoAnnotationDataPreLoadHeader !== null) {
         // let hArray = autoAnnotationDataPreLoadHeader.annotations.split("\r\n").map(a => a.replace(/^\t+/, ''));
         let hArray = autoAnnotationDataPreLoadHeader.annotations.replaceAll("\r",'').split("\n\t");
         hArray.shift();
         console.log(hArray);
         setAnnotationHeader(hArray);
      }
      console.log(autoAnnotationDataPreLoad);
      await insertFullAnnotations(autoAnnotationDataPreLoad);
      await initialAnnotationLoadScoreState();
      initialLabelFirstLineState();
      sessionStorage.setItem( 'autoAnnotationActive', true );
   }
   hideLoader();
}
var dbHeader;
function setAnnotationHeader(headerContent) {
   console.log('FIRING HEADER!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!');
   if( Array.isArray( headerContent )) {
      console.log(headerContent);
      headerContent = headerContent.filter( v => v );
      if(headerContent.length > 0) {
         let headerFinal = '';
         headerContent.forEach((l) => {
            headerFinal += '<p>';
            headerFinal += l.split('\t')[0];
            headerFinal += '</p>';
         });
         // console.log(headerFinal);
         $('#auto-annote-header-content').html(headerFinal);
         $('#auto-annote-header').offset({ left: $('#hud').width() }).slideDown().draggable().resizable();
         $('#idea-header-visibility').slideDown();
         $('#idea-header-visibility').click(function() {
            $('#auto-annote-header').slideToggle();
         });
      }
   } else {
      console.log('NOT AN ARRAY!');
   }
}

function insertFullAnnotations(parsedAnnotes) {
   return new Promise( resolve => {
      annotations = [];
      console.log(parsedAnnotes);
      parsedAnnotes.forEach((annote,index) => {
         // console.log(annote);
         if(annote.annotations.length > 0) {
            deselectAll();
            let label = annote.annotations.join(')<br>').replaceAll('\t', '(').replaceAll('  ', ' ') + ')';
            label = label.replace(/\d+\.\d+/g, n => parseFloat(n).toFixed(1));
            annotations.push({"name": annote.name,"annotations": label});
            // annotations[index].annotations = label;
            parameters.a = label;
            selectedCluster = clusterGroup.getObjectByName( annote.name );
            selected.push( selectedCluster.name );
            console.log(selectedCluster.name);
            if(autoAnnotationProfileRestore === false) {
               console.log('Not a restore, Buisness as usual/Normal operations...');
               guiSaveClusterName(true);
            } else if(autoAnnotationProfileRestore && ThemeNameRegistry[selectedCluster.userData.indexKey].Alias === label ) {
               console.log('Alias and label ===');
               guiSaveClusterName(true);
            } else {
               console.log('Alias and label !==');
            }
            deselectAll();
            selectedLabel = spriteTextGroup.getObjectByName(`spriteText=${annote.name}`);
            if(autoAnnotationProfileRestore && ThemeNameRegistry[selectedLabel.userData.indexKey].Alias === label 
               || autoAnnotationProfileRestore === false && autoAnnotationActive === true) {
               selectedLabel.userData.fullAutoAnnotation = label;
               selectedLabel.userData.isCustomName = false;
            }
            if(autoAnnotationProfileRestore && ThemeNameRegistry[selectedLabel.userData.indexKey].hasOwnProperty('fullAnnotation') === false) {
               console.log('ThemeNameRegistry does not contain fullAnnotation!!! Copying annotation...');
               ThemeNameRegistry[selectedLabel.userData.indexKey].fullAutoAnnotation = label;
            }
            selectedLabel.element.style.display = (selectedLabel.element.style.display === 'initial') ? 'flex' : selectedLabel.element.style.display;
            selectedLabel.element.style.flexDirection = 'column';
            selectedLabel.element.style.alignItems = 'center';
            // ThemeNameRegistry[selectedLabel.userData.indexKey].isCustomName = false;
         }
      });
      autoAnnotationProfileRestore = false;
      resolve(true);
   });
   // gui.__folders["Visual Setting"].__listening[0].setValue(3);
}

function disableAutoAnnotationBtn() {
   // let autoAnnotateBtn = document.getElementById('auto_annotate');
   // autoAnnotateBtn.disabled = true;
   // autoAnnotateBtn.style.cursor = 'not-allowed';
   // autoAnnotateBtn.removeEventListener('click', function(event) {
   //    this.removeEventListener('click');
   // });
   sessionStorage.setItem( 'autoAnnotationActive', true );
}

function initialLabelFirstLineState() {
   if(parameters.f6 === true) {
         spriteTextGroup.children.forEach((label) => {
            if(label.userData.isCustomName === false) {
               let labelEl = label.element;
               let labelSpans = Array.from(labelEl.children);
               labelSpans.forEach((span,index) => {
                  if(index > 0) {
                     span.classList.add('flo-hidden');
                  }
               });
            } else {
               console.log(label.name);
               console.log(label.userData)
               console.log('This is a custom name and will will not have multiple line labels');
            }
         });
   } else {
      let hiddenSpans = Array.from( document.getElementsByClassName('flo-hidden'));
      hiddenSpans.forEach(span => span.classList.remove('flo-hidden'));
   }
   for(let i=0;i<boxViewGroup.children.length;i++) {
         boxViewGroup.children[i].element.firstElementChild.innerHTML = spriteTextGroup.children[i].element.innerHTML;
   }
}

function initialAnnotationLoadScoreState() {
   return new Promise( resolve => {
      let checkBoxState = parameters.f4;
      // console.log( checkBoxState );
      if( checkBoxState === true ) {
         console.log( 'removing spans' );
         spriteTextGroup.children.forEach(( labelObject ) => {
            let els = Array.from( labelObject.element.children );
            els.forEach(( span ) => {
               let text = $( span ).text();
               let score = $( span ).data( 'score' ) || '';
               let modText = text + score;
               $( span ).text( modText );
               $( span ).removeAttr( 'data-score' );
            });
         });
      } else {
         console.log( 'adding spans' );
         spriteTextGroup.children.forEach(( labelObject ) => {
            let els = Array.from( labelObject.element.children );
            els.forEach(( span ) => {
               let text = $( span ).text();
               let sIndex = text.lastIndexOf( '(' );
               let modText = text.slice( 0,sIndex );
               let score = text.slice( sIndex,text.length );
               $( span ).attr( 'data-score',score );
               $( span ).text( modText );
            });
         });                
      }
      for(let i=0;i<boxViewGroup.children.length;i++) {
         boxViewGroup.children[i].element.firstElementChild.innerHTML = spriteTextGroup.children[i].element.innerHTML;
      }
      resolve(true);
   });
}
