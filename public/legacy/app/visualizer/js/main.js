'use strict';
/* global mainCfg, get, set */
// const log = console.log.bind(console);
const DEFAULT_PROFILE_NAME = mainCfg.useAutoProfile ? 'Auto Profile' : 'Default';
if( typeof( log ) === 'undefined' ) {
    const log = console.log.bind(console);
}
if (data === undefined || data2 === undefined) {
    var data, data2;
}
// if (mainCfg.useStats) {
//     // setupUsersnap();
//     setupStats();
// }
const HUDNode = function() {
    return mainCfg.showVisDataPanel ? mainCfg.components.visDataPanel._uiPanel._DOM : document.getElementById('hud');
}
const maxSceneDepth = 5000;
var viewFunctions = {};
var epmViewFunctions = {};
var ctrlFunctions = {};
const Key = sessionStorage.getItem('ProjectKey');
const ProjectAcct = sessionStorage.getItem('ProjectAcct');
const Project = sessionStorage.getItem('Project');
const Optimized = (sessionStorage.hasOwnProperty('Optimized')) ? sessionStorage.getItem('Optimized') : false;
const AcctId = sessionStorage.getItem('AcctId');
const ProjectId = sessionStorage.getItem('ProjectId');
/**
* Setup bool value to check against for Regular or Bio-Explorer type project 
* This will allow tubes to display with Bio-Explorer projects 
**/
window.bioExplorerProjectType = (sessionStorage.getItem('BioExplorerProject') === "true") ? true : false;
const bioExplorerProjectType = window.bioExplorerProjectType;
var inactivityTime;
var focusSessionCheck;
var c = [];
var currentLoadedProfile;
var profileParameters;
var parameters;
var maxScore = 0;
var savedProfileData = {};
var initialLoad = true;
var restoreProfile = false;
//HUD Gene View List Object
var geneViewOpen = false;
var geneListObj = {};
var selectedGene = null;
var pvalObj = {}, nesObj = {};
var boxViewActive = false;
var oldPM = null;
var showChat = true;
var showStats = false;

//Gui and GuiSaving
var gui = new dat.GUI();
const guiElement = gui.domElement;
guiElement.id = 'dat-gui';
const guiContainer = gui.domElement.parentElement;
guiContainer.id = 'dat-gui-container';

guiElement.style.marginTop = '25px';
const infoBar = document.createElement('div');
infoBar.style.position = 'fixed';
infoBar.style.height = '25px';
infoBar.style.lineHeight = '25px';
infoBar.style.padding = '0 8px';
infoBar.style.backgroundColor = '#444';
infoBar.style.color = '#fff';
infoBar.style.fontSize = '14px';
infoBar.style.boxSizing = 'border-box';
infoBar.style.zIndex = '9999';
infoBar.style.textWrap = 'nowrap';

// Position just above the dat-gui box
function updateInfoBarPosition() {
    const rect = guiElement.getBoundingClientRect();
    infoBar.style.left = `${rect.left}px`;
    infoBar.style.top = `${rect.top - 25}px`;
    infoBar.style.width = `${rect.width}px`;
}

// Initial position
updateInfoBarPosition();

// Append to body (not guiContainer)
document.body.appendChild(infoBar);

// Update position on resize or layout changes
window.addEventListener('resize', updateInfoBarPosition);

const guiResizeObserver = new ResizeObserver(() => {
    updateInfoBarPosition();
});
guiResizeObserver.observe(guiElement);

function visThemeUpdateStat() {
    let visibleThemes = 0;
    for (let i = 0; i < optiLength; i++) {
        if (boxViewActive === true) {
            if (boxViewGroup.children[i].element.checkVisibility() === true) {
                visibleThemes++;
            }
        } else {
            if (clusterGroup.children[i].visible === true) {
                visibleThemes++;
            }
        }
    }
    infoBar.innerText = `${visibleThemes} of ${optiLength} Themes Displayed`;
}

var conceptsSorted = [];
var conceptLinesSorted = [];
var distanceParam;
/* Temp Visibility Lock Holder - When Filters Are Applied */
var tempVisLockFilterHold = [];

///////////////////////////////////////////////////
//Keep track of all cluster names, all the time!!!
var ThemeNameRegistry = [];
//////////////////////////////////////////////////
//Session Login Attempts
var logCount = 0;


if(bioExplorerProjectType === true) { 
    document.getElementById('gene-view-button').innerText = 'Term';
    document.getElementById('visible-gene-wrap').innerHTML = document.getElementById('visible-gene-wrap').innerHTML.replace('Entity', 'Term').replace('Entities', 'Terms');
}

//Set optiLength
var optiLength = (function(){
    const DEFAULT_LENGTH = mainCfg.optiLengthSize;
    if (typeof data === 'undefined') {
        return DEFAULT_LENGTH;
    }
    return (Optimized === "true" && data.length > DEFAULT_LENGTH) ? DEFAULT_LENGTH : data.length;
})();

function optimalItemsSize() {
    if (mainCfg.showVisDataPanel) {
        if (typeof data === 'undefined') {
            return mainCfg.optiLengthSize;
        }
        return data.length;
    } else {
        return optiLength;
    }
}

var filterPvalAndNescore = function () {
    console.log('original');
    return true;
};

eventBus.subscribe('remove:theme-relation', ({id}) => { removeVisLock.call({id}) });
eventBus.subscribe('deselect:selected-themes:theme', theme => {
  hudDataBindClick(
    clusterGroup.getObjectByName(theme)
  );
  if (mainCfg.showVisDataPanel) {
    mainCfg.components.visDataPanel.toggleThemeByName(theme);
  };
});

eventBus.subscribe('deselect:selected-themes:all', () => {
    deselectAll();
});
//Testing WebGL
if (mainCfg.initialDataCheck(data)) {

    var renderer = new THREE.WebGLRenderer({
        // preserveDrawingBuffer: true, //Added for screenshot function
        preserveDrawingBuffer: true,
        logarithmicDepthBuffer: true,
        // depth: true,
        powerPreference: "high-performance",
        antialias: true,
        alpha: true,
    });

    renderer.domElement.id = 'canvas1';
    var scene = new THREE.Scene();
    var camera1 = new THREE.PerspectiveCamera(30, window.innerWidth / window.innerHeight, 0.1, maxSceneDepth);
    var raycaster = new THREE.Raycaster();
    raycaster.near = camera1.near;
    raycaster.far = camera1.far;
    var mouse = new THREE.Vector2();
    var intersects, INTERSECTED;
    var labelIntersects = [];
    var themeBoxIntersects = [];
    var clusterGroup = new THREE.Group();
    clusterGroup.name = 'clusterGroup';
    var cubeGroup = new THREE.Group();
    cubeGroup.name = 'cubeGroup';
    //CSS3D Renderer
    var labelCssRenderer = new THREE.CSS3DRenderer();
    labelCssRenderer.setSize(window.innerWidth, window.innerHeight);
    labelCssRenderer.domElement.style.position = 'absolute';
    labelCssRenderer.domElement.style.top = 0;
    labelCssRenderer.domElement.style.zIndex = -3;
    labelCssRenderer.domElement.id = 'label-renderer';
    document.body.appendChild(labelCssRenderer.domElement);
    var spriteTextGroup = new THREE.Group();
    spriteTextGroup.name = 'spriteTextGroup';
    //CSS3D Renderer
    //Theme Glowing Halos - for gene/entity view
    var cssrenderer = new THREE.CSS3DRenderer();
    cssrenderer.setSize(window.innerWidth, window.innerHeight);
    cssrenderer.domElement.style.position = 'absolute';
    cssrenderer.domElement.style.top = 0;
    cssrenderer.domElement.style.zIndex = -4;
    cssrenderer.domElement.id = 'glow-renderer';
    document.body.appendChild(cssrenderer.domElement);
    //CSS3DRenderer BoxView
    // var boxViewRenderer = new THREE.CSS3DRenderer();
    // boxViewRenderer.setSize(window.innerWidth, window.innerHeight);
    // boxViewRenderer.domElement.style.position = 'absolute';
    // boxViewRenderer.domElement.style.top = 0;
    // boxViewRenderer.domElement.style.zIndex = -5;
    // boxViewRenderer.domElement.id = 'boxview-renderer';
    // document.body.appendChild(boxViewRenderer.domElement);
    //BoxView Scene/Group
    var boxViewGroup = new THREE.Group();
    boxViewGroup.name = "boxViewGroup";
    // var boxViewScene = new THREE.Scene();
    // boxViewScene.name = "boxViewScene";

    var glowGroup = new THREE.Group();
    var filterHaloGroup = new THREE.Group();
    var cssScene = new THREE.Scene();
    var labelScene = new THREE.Scene();
    var controls = new THREE.TrackballControls(camera1, labelCssRenderer.domElement);
    controls.maxDistance = maxSceneDepth;
    $(function() {
        $("#dialog-wheel").dialog({
            resizable: true,
            autoOpen: false,
            // width: 300,
            close: function() {
                document.getElementById('dialog-wheel').innerHTML = '';
            },
            position: {
                at: 'right center+175'
            },
            show: {
                effect: 'clip'
            },
            hide: {
                effect: 'clip'
            }
        });
    });
    $(function() {
        $('#concept-map').dialog({
            resizable: false,
            draggable: false,
            modal: true,
            height: (window.innerHeight-15),
            width: (window.innerWidth-15),
            autoOpen: false,
            close: function() {
                log('concept map closing');
                let meshConceptGroup = GLOBAL_STATE.meshConceptGroup;
                while( meshConceptGroup.children.length > 0 ) {
                    meshConceptGroup.children[0].children[0].geometry.dispose();
                    meshConceptGroup.children[0].children[0].material.dispose();
                    meshConceptGroup.children[0].children[1].geometry.dispose();
                    meshConceptGroup.children[0].children[1].material.dispose();
                    meshConceptGroup.remove(meshConceptGroup.children[0]);
                }
                while( lineConceptGroup.children.length > 0 ) {
                    lineConceptGroup.children[0].geometry.dispose();
                    lineConceptGroup.children[0].material.dispose();
                    lineConceptGroup.remove(lineConceptGroup.children[0]);
                }
                //Clear to global variable sorted arrays
                GLOBAL_STATE.conceptsSorted = [];
                GLOBAL_STATE.conceptLinesSorted = [];
                GLOBAL_STATE.distanceParam = undefined;
                document.getElementById('hud2-inner-wrap').innerHTML = '';
                if (showStats) {
                    document.getElementById('stats').style.visibility = 'initial';
                }
            },
            open: function() {
                log('concept map opening')
                //Resize canvas3
                camera3.aspect = ( document.getElementById('concept-map').clientWidth / document.getElementById('concept-map').clientHeight );
                camera3.updateProjectionMatrix();
                renderer3.setSize( document.getElementById('concept-map').clientWidth, document.getElementById('concept-map').clientHeight );
                document.getElementById('hud2-inner-wrap').style.height = Math.floor(( document.getElementById('concept-map').clientHeight - 15 ) - document.getElementById('hud2-header').clientHeight ) + 'px';
                document.getElementById('stats').style.visibility = 'hidden';
            },
            show: {
                effect: 'fold'
            },
            hide: {
                effect: 'fold'
            }
        });
    });
    $(function() {
        $("#dialog-notes").dialog({
            height: window.innerHeight/2,
            width: window.innerWidth/2,
            resizable: true,
            draggable: true,
            autoOpen: false,
            close: function() {


            },
            open: function() {

            },
            show: {
                effect: 'clip'
            },
            hide: {
                effect: 'clip'
            }
        });
    });

    // //Gui and GuiSaving
    // var gui = new dat.GUI();
    //Declare Global for dom Element for toggling for canvas3 gui
    var datGui1; //sets element at the end of init()
    var savedProfiles = [];
    if (!mainCfg.useAutoProfile) {
        GLOBAL_STATE.savedProfiles = ['Default'];
    }
    //gui.remember(parameters);

    //Overlap Filters Global Declaration
    var filterArray = [];
    var FILTER_MODES = {
        INTERSECTION: 'Intersection',
        DIFFERENCE: 'Difference'
    };
    var filterModes = Object.values(FILTER_MODES);
    var overlapFilters = ['None'];
    if(sessionStorage.getItem('FilterList') !==  null) {
        let filterNames = JSON.parse(sessionStorage.getItem('FilterList'));
        filterNames.forEach(function(item) {
            overlapFilters.push(item);
        });
    }
    var appliedFilter = overlapFilters[0];

    var PM = 4; //position multiplier(scaling)
    var selectedCluster;
    var selectedTube = 0;
    var currentInfo = 0;
    var centerVec = new THREE.Vector3();
    var prevColor = [];
    var selected = [];
    var lineGeometry = new THREE.Geometry();
    var sharedGenes = 2;
    var combinedConcepts = [];
    var popUp;
    var popUpShown = false;
    var hudClicked = false;
    var clusterInfoShown = false;
    var mouseOverDiv = false;
    var customGroups = {};
    var guiColor = '#000015';
    var guiGroupColor = '#000015';
    var groupSelected = false;
    var selectedGroup;
    var selectedGroupName;
    var prevGroupColor = [];
    var sortedByScore = [];
    var clusterScoreAmount = 0;
    var hudSelected;
    var prevTubeColor = 0;
    var clusterNameChange = false;
    var customClusterNames = [];
    var customClusterColors = [];
    var customGroupColors = [];

    //Tube line Lengths Array
    var tubeLengthsSorted = []; //Currently Unnecessary

    //Create array for reference to always shown tubes (VisLock)
    var visLockedTubes = [];
    //Create array for tubes connected to groupHiddenThemes
    var hiddenGroups = [];
    var groupHiddenTubes = [];

    //Canvas3
    var canvas3;
    // var canvas3CloseBtn;
    var scene3;
    var gui3 = new dat.GUI();
    document.getElementById('concept-map').appendChild(gui3.domElement);
    gui3.domElement.id = 'gui3';
    var conceptMapParameters = {};
    //Declare global for dom element for showing and hiding
    var datGui3; //targets and sets dom element at the end of init()

    var meshConceptGroup;
    var lineConceptGroup;

    var camera3;
    var renderer3;

    var controls3;
    var lights3 = [];

    var raycaster3 = new THREE.Raycaster();
    var mouse3 = new THREE.Vector2();
    var intersects3;
    var INTERSECTED3 = 0;
    var selectedMesh3 = 0;
    var prevColor3 = [];

    var scene3Objs;
    var returnedScore = 0;
    // var conceptsSorted = []; Declared in Temp Global
    // var conceptLinesSorted = []; Declared in Temp Global

    var mouseOverC3 = false;
    //Canvas3 End

    var guiProfileName;

} //Test End
//Init Start
function init() {
    let data2 = GLOBAL_STATE.data2;
    let data = GLOBAL_STATE.data;
    let c = GLOBAL_STATE.c;
    let ThemeNameRegistry = GLOBAL_STATE.ThemeNameRegistry;
    let t = new Date();
    for (let i = 0; i < data2.length; i++) {
        let indexedKeys = Object.keys( data2[i] );
        if( indexedKeys.includes('Gene_ID') ) {
            indexedKeys.splice( indexedKeys.indexOf('Gene_ID'), 1 );
        }
        if( indexedKeys.includes('Num_Concepts') ) {
            indexedKeys.splice( indexedKeys.indexOf('Num_Concepts'), 1 );
        }
        for (let z = 1; z < indexedKeys.length + 1; z++) {
            data2[i][z] = data2[i][z].split(',');
        }
    }
    for (let i = 0; i < data.length; i++) {
        c[i] = {};
        c[i].Name = data[i].Name;



        //Initially set all names and aliases
        ThemeNameRegistry.push({ Name: c[i].Name, Alias: c[i].Name });


        c[i].X = parseFloat(data[i].X);
        if (isNaN(c[i].X)) {
            log('c[' + i + '].X -> NaN');
        }
        c[i].Y = parseFloat(data[i].Y);
        if (isNaN(c[i].Y)) {
            log('c[' + i + '].Y -> NaN');
        }
        c[i].Z = parseFloat(data[i].Z);
        if (isNaN(c[i].Z)) {
            log('c[' + i + '].Z -> NaN');
        }
        c[i].Score = parseFloat(data[i].Score);
        if (isNaN(c[i].Score)) {
            log('c[' + i + '].Score -> NaN');
        }

        //Calculate maxScore
        if (c[i].Score > GLOBAL_STATE.maxScore) {
            GLOBAL_STATE.maxScore = c[i].Score;
        }

        c[i].Concept_Total = Object.keys(data[i].Concepts).length;
        if (isNaN(c[i].Concept_Total)) {
            log('c[' + i + '].Concepts_Total -> NaN');
        }
        c[i].Gene_Total = Object.keys(data[i].Genes).length;
        if (isNaN(c[i].Gene_Total)) {
            log('c[' + i + '].Gene_Total -> NaN');
        }
        // c[i].Concepts = Object.keys(data[i].Concepts);
        /*
        !FIX!
        Arrange Concept Arrays in Proper Order Based on Score
        */
        c[i].Concepts = [];
        (function() {
            let themeObj = Object.assign({}, data[i].Concepts);
            let tempKeys = [];
            let tempSorted = [];
            tempKeys = Object.keys(themeObj);
            tempKeys.forEach(function(key) {
                let arr = [];
                arr = themeObj[key].split(',');
                themeObj[key] = arr[0];
                tempSorted.push([key, themeObj[key]]);
            });
            tempSorted.sort(function(a,b) {
                return a[1] - b[1];
            });
            tempSorted.forEach(function(item) {
                c[i].Concepts.push(item[0]);
            });
        })();

        //End fix
        c[i].Genes = Object.keys(data[i].Genes);
        //Create array of Gene Scores
        c[i].Gene_Scores = [];
        for (let z = 0; z < c[i].Genes.length; z++) {
            c[i].Gene_Scores.push(data[i].Genes[c[i].Genes[z]].Score);
        }
        // Added for PValue and NEScore
        if('P-Value' in data[i] || 'PValue' in data[i] && 'NESScore' in data[i] || 'NEScore' in data[i]) {
            c[i]["PValue"] = ('P-Value' in data[i]) ? data[i]["P-Value"] : data[i]["PValue"];
            c[i]["NEScore"] = ('NESScore' in data[i]) ? data[i]["NESScore"] : data[i]["NEScore"];
        }
        // Added for FisherPValue
        if ("FisherPValue" in data[i]) {
            c[i]["FisherPValue"] = data[i]["FisherPValue"];
        } else {
            c[i]["FisherPValue"] = null;
        }
    }

    var logCount = 0;
    renderer.setPixelRatio(window.devicePixelRatio);
    renderer.setSize(window.innerWidth, window.innerHeight);

    if (!mainCfg.showVisDataPanel) {
        document.getElementById('scale-file').innerHTML = '<span>Data File:</span>' + mainCfg.getDataSrc() + '<br /><span>Scale Factor:</span>' + PM;
    }
    document.body.appendChild(renderer.domElement);
    let ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
    let topDirectionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    let bottomDirectionLight = new THREE.DirectionalLight(0xffffff, 0.8);
    // let pointLight = new THREE.PointLight(0xffffff, 0.7);
    controls.staticMoving = false;
    controls.dynamicDampingFactor = 1;
    controls.zoomSpeed = 1;
    controls.rotateSpeed = 7;
    controls.panSpeed = 1;
    topDirectionalLight.position.set(0, 200, 200);
    bottomDirectionLight.position.set(0, -200, -200);
    // pointLight.position.set(0, 0, 0);
    camera1.position.set(0, 0, -(150 * PM));
    // scene.add(ambientLight, topDirectionalLight, bottomDirectionLight, pointLight, camera1);
    scene.add(ambientLight, topDirectionalLight, bottomDirectionLight, camera1);
    conceptMapParameters = {
        distance: 20,
        score: 70,
        conceptCapture: function() {
            initConceptCapture();
        }
    };
    let tempVal3 = conceptMapParameters.score;
    let gui3FilteringFolder = gui3.addFolder('Filters');
    let gui3ScoreVisibility = gui3FilteringFolder.add(conceptMapParameters, 'score').min(1).max(100).step(1).name('Score').listen();
    gui3ScoreVisibility.onChange(function(tempVal3) {
        tempVal3 = c3ParamsToPercent(tempVal3, 'score');
        for (let i = 0; i < meshConceptGroup.children.length; i++) {
            if (parseFloat(meshConceptGroup.children[i].userData.Score) <= tempVal3) {
                meshConceptGroup.children[i].visible = true;
            } else if (parseFloat(meshConceptGroup.children[i].userData.Score) > tempVal3) {
                meshConceptGroup.children[i].visible = false;
            }
        }
        distanceParam = 100 - conceptMapParameters.distance;
        distanceParam = c3ParamsToPercent(distanceParam, 'distance');
        conceptEPVisChk(distanceParam);
    });
    let gui3EdgeVisibility = gui3FilteringFolder.add(conceptMapParameters, 'distance').min(1).max(100).step(1).name('Distance').listen();
    gui3EdgeVisibility.onChange(function(tempVal3) {
        tempVal3 = 100 - tempVal3;
        tempVal3 = c3ParamsToPercent(tempVal3, 'distance');
        conceptEPVisChk(tempVal3);
    });
    gui3FilteringFolder.open();
    gui3.add( conceptMapParameters, 'conceptCapture' ).name( 'Capture Map' );
    GLOBAL_STATE.renderer3 = new THREE.WebGLRenderer({
        antialias: true
    });
    document.getElementById('concept-map').appendChild(renderer3.domElement);
    GLOBAL_STATE.scene3 = new THREE.Scene();
    GLOBAL_STATE.scene3.background = new THREE.Color(0x0b121c)
    GLOBAL_STATE.camera3 = new THREE.PerspectiveCamera( 60, document.getElementById('concept-map').clientWidth / document.getElementById('concept-map').clientHeight, 0.1, 5000);
    GLOBAL_STATE.camera3.position.z = 50;
    GLOBAL_STATE.renderer3.domElement.id = 'canvas3';
    GLOBAL_STATE.renderer3.setSize( document.getElementById('concept-map').clientWidth, document.getElementById('concept-map').clientHeight );
    GLOBAL_STATE.canvas3 = document.getElementById('canvas3');
    GLOBAL_STATE.controls3 = new THREE.OrbitControls(camera3, renderer3.domElement);
    lights3[0] = new THREE.PointLight(0xffffff, 1, 0);
    lights3[1] = new THREE.PointLight(0xffffff, 1, 0);
    lights3[2] = new THREE.PointLight(0xffffff, 1, 0);

    lights3[0].position.set(0, 0, 50);
    lights3[1].position.set(50, 50, 50);
    lights3[2].position.set(-50, 50, 50);

    lights3[0].name = 'lights4';
    lights3[1].name = 'lights5';
    lights3[2].name = 'lights6';

    scene3.add(lights3[0]);
    scene3.add(lights3[1]);
    scene3.add(lights3[2]);

    meshConceptGroup = new THREE.Group();
    meshConceptGroup.name = 'meshConceptGroup';
    lineConceptGroup = new THREE.Group();
    lineConceptGroup.name = 'lineConceptGroup';

    //Canvas3 EventListeners
    //Raycaster Event listeners (onMouseMove & onMouseDown)
    document.addEventListener('mousedown', onDocumentMouseDown3, false);
    document.addEventListener('touchstart', onDocumentTouchStart3, false);
    canvas3.addEventListener('mousemove', onDocumentMouseMove3, false);

    //MouseMove Start
    function onDocumentMouseMove3(event) {
        mouseOverC3 = true;
        let canvas3 = document.getElementById('canvas3');
        let canvasBounds3 = canvas3.getBoundingClientRect();
        //event.preventDefault();
        mouse3.x = ((event.clientX - canvasBounds3.left) / (canvasBounds3.right - canvasBounds3.left)) * 2 - 1;
        mouse3.y = -((event.clientY - canvasBounds3.top) / (canvasBounds3.bottom - canvasBounds3.top)) * 2 + 1;
        raycaster3.setFromCamera(mouse3, camera3);
        intersects3 = raycaster3.intersectObjects(lineConceptGroup.children);
        if (intersects3.length > 0) {
            if (INTERSECTED3 != intersects3[0].object) {
                if (INTERSECTED3) INTERSECTED3.material.color.setHex(INTERSECTED3.currentHex);
                INTERSECTED3 = intersects3[0].object;
                INTERSECTED3.currentHex = INTERSECTED3.material.color.getHex();
                INTERSECTED3.material.color.setHex(0xff0000);
            }
        } else {
            if (INTERSECTED3) INTERSECTED3.material.color.setHex(INTERSECTED3.currentHex);
            INTERSECTED3 = 0;
        }
    } //MouseMove End
    //Touch Start
    function onDocumentTouchStart3(event) {
        //event.preventDefault();//Cannot use datgui dropdowns
        event.clientX = event.touches[0].clientX;
        event.clientY = event.touches[0].clientY;
        onDocumentMouseDown3(event);
    } //Touch End
    //MouseClick Start
    function onDocumentMouseDown3(event) {
        let canvas3 = document.getElementById('canvas3');
        let canvasBounds3 = canvas3.getBoundingClientRect();
        //event.preventDefault();//Cannot use datgui dropdowns
        mouse3.x = ((event.clientX - canvasBounds3.left) / (canvasBounds3.right - canvasBounds3.left)) * 2 - 1;
        mouse3.y = -((event.clientY - canvasBounds3.top) / (canvasBounds3.bottom - canvasBounds3.top)) * 2 + 1;
        raycaster3.setFromCamera(mouse3, camera3);
        intersects3 = raycaster3.intersectObjects(lineConceptGroup.children);
        if (mouseOverC3 === true) {
            clickSelect3();
        }
    } //MouseClick End
    //Canvas3 End

    let tempVal = 0; //temp value holder
    profileParameters = {
        profileName: mainCfg.useAutoProfile ? '' : savedProfiles[0],
        loadProfile: function() {
            if( document.getElementById('drag_lock').classList.contains('active-draglock-btn')) {
                document.getElementById('drag_lock').click();
            }            
            mainCfg.onLoadProfileClick();
        },
        saveProgress: function() {
            if (profileParameters.profileName !== DEFAULT_PROFILE_NAME) {
                if( document.getElementById('drag_lock').classList.contains('active-draglock-btn')) {
                    document.getElementById('drag_lock').click();
                }
                mainCfg.onSaveProgressClick();
            } else {
                alertInformation(`The ${DEFAULT_PROFILE_NAME} profile is locked.  To save data you must create a profile first`);
            }
        },
        createProfile: function() {
            guiProfileCeationToolsToggle();
        },
        newProfileName: 'Enter Profile Name',
        saveNewProfile: function() {
            if (savedProfiles.includes(profileParameters.newProfileName)) {
                alertInformation('There is already a profile utilizing this name, chose another...'); //modal warning
                profileParameters.newProfileName = 'Enter Profile Name';
            } else {
                if(parameters.a.includes("&") !== true){
                    profileCreation(profileParameters.newProfileName);
                } else {
                    alertInformation('Ampersands are forbidden...');
                }
            }
        },
        cancel: function() {
            guiProfileCeationToolsToggle();
            GLOBAL_STATE.profileParameters.newProfileName = 'Enter Profile Name';
        },
        delete: function() {
            let dialog = document.getElementById('dialog-confirm');
            dialog.title = 'Event Confirmation!';
            dialog.innerHTML = '<p><i class="fa fa-exclamation-circle fa-2x" aria-hidden="true"></i><br>' +
                'You are about to delete the profile<br><span id="dialog-name">' +
                GLOBAL_STATE.profileParameters.profileName
                + '</span><br>Please confirm that you want to continue with this action...</p>';
            $(function() {
                GLOBAL_STATE.raycaster.near = camera1.near;
                $("#dialog-confirm").dialog({
                    resizable: false,
                    height: "auto",
                    width: 400,
                    buttons: {
                        "Confirm": function() {
                            $(this).dialog("close");
                            GLOBAL_STATE.raycaster.near = 0;
                            profileDeletion(GLOBAL_STATE.profileParameters.profileName);
                        },
                        Cancel: function() {
                            $(this).dialog("close");
                            GLOBAL_STATE.raycaster.near = 0;
                        }
                    }
                });
            });
        }
    }
    parameters = {
        a: ((selectedCluster != undefined) ? selectedCluster.userData.Name : "Select A Theme"),
        a1: function() {
            if(parameters.a.includes("&") !== true){
                guiSaveClusterName();
            } else {
                alertInformation('Ampersands are forbidden...');
            }
        },
        b: false,
        // b1: true,
        c: false,
        c1: false,
        c2: 70,
        c3: (optiLength > mainCfg.optiLengthSize) ? mainCfg.minDefaultScore : mainCfg.maxDefaultScore,
        c4: PM.toString(),
        c5: function() {
            if (!isNaN(parseInt(parameters.c4))) {
                let num = parseInt(parameters.c4);
                if (num > 0 && num < 10) {
                    if (num != PM) {
                        let dialog = document.getElementById('dialog-confirm');
                        dialog.title = 'Event Confirmation!';
                        dialog.innerHTML = '<p><i class="fa fa-exclamation-circle fa-2x" aria-hidden="true"></i><br>' +
                            'You are about to rescale the model.  Please confirm that you want to continue with this action...</p>';
                        $(function() {
                            GLOBAL_STATE.raycaster.near = camera1.near;
                            $("#dialog-confirm").dialog({
                                resizable: false,
                                height: "auto",
                                width: 400,
                                modal: true,
                                buttons: {
                                    "Confirm": function() {
                                        $(this).dialog("close");
                                        GLOBAL_STATE.raycaster.near = 0;
                                        applyScale(parameters.c4);
                                    },
                                    Cancel: function() {
                                        $(this).dialog("close");
                                        GLOBAL_STATE.raycaster.near = 0;
                                        parameters.c4 = PM;
                                    }
                                }
                            });
                        });
                    } else {
                        log('same as PM')
                    }
                } else {
                    alertInformation('Enter a number between 1-9');
                    parameters.c4 = PM;
                }
            } else {
                alertInformation('The character(s) entered are not a valid number');
                parameters.c4 = PM;
            }
        },
        c6: 5000,
        c7: true,
        d: guiColor,
        d1: function() {
            guiSaveColor();
        },
        d2: 6,
        // d3: '#ffff00',
        d3: '#ffffff',
        d4: function() {
            visLock();
        },
        d5: '#6f7b91',
        e: mainCfg.newGroupName,
        e1: function() {
            if (parameters.a.includes("&") !== true){
                guiSaveGroup();
            } else {
                alertInformation('Ampersands are forbidden...');
            }
        },
        e2: function() {
            log('Hide Group Btn Clicked');
            hideGroup();
        },
        e3: function() {
            log('Unhide Group Btn Clicked');
            unhideGroup();
        },
        e4: guiGroupColor,
        e5: function() {
            guiGroupColorSave();
        },
        f: 'None',
        f1: '#ffffff',
        f2: 1,
        f3: true,
        f4: false,
        f5: false,
        f6: true,
        f7: false,
        g: function() {
            if (selected.length > 0 && selectedCluster) {
                let tempVec = selectedCluster.position;
                controls.target.set(tempVec.x, tempVec.y, tempVec.z);
            } else {
                alertInformation('Select a theme to set the center of rotation pivot point');
            }
        },
        g1: function() {
            if (controls.target.x != centerVec.x && controls.target.y != centerVec.y && controls.target.z != centerVec.z) {
                controls.target.set(centerVec.x, centerVec.y, centerVec.z);
            } else {
                alertInformation('The rotation pivot is already set to the model center');
            }

        },
        h: function() {
            log('selected items export');
            if (selected.length > 0) {
                let tempDataArr = [];
                let File_Name = parameters.h2 ? parameters.h2 : 'export_data';
                if (File_Name.includes(' ')) {
                    File_Name = File_Name.replace(/ /g, '_');
                }
                if (File_Name.includes('&')) {
                    File_Name = File_Name.replace(/&/g, '-');
                }
                File_Name = File_Name.replace(/[^a-z0-9_-]/gi,'');
                tempDataArr.push(File_Name);
                for (let i = 0; i < optimalItemsSize(); i++) {
                    if (selected.includes(data[i].Name)) {
                        let tempDataObj = {};
                        /*If is needed epam-side, let me know or add conditional*/
                        /*Breaks our export selected feature*/
                        // tempDataObj.idx = i;
                        tempDataObj.Name = 'Name=' + clusterGroup.getObjectByName(data[i].Name).userData.Name.replaceAll('<br>', ' ');
                        tempDataObj.Score = 'Score=' + data[i].Score;
                        tempDataObj.Coordinates = ['Coordinates=X:' + data[i].X, 'Y:' + data[i].Y, 'Z:' + data[i].Z];
                        tempDataObj.Coordinates = tempDataObj.Coordinates.toString();
                        tempDataObj.Genes = Object.keys(data[i].Genes);
                        tempDataObj.Genes_Total = 'Entity_Total=' + Object.keys(data[i].Genes).length.toString();
                        for (let z = 0; z < Object.keys(data[i].Genes).length; z++) {
                            tempDataObj.Genes[z] = 'Entity=' + tempDataObj.Genes[z] + '(' + data[i].Genes[tempDataObj.Genes[z]].Score + ')';
                        }
                        // tempDataObj.Genes = tempDataObj.Genes.toString();
                        tempDataObj.Concepts = Object.keys(data[i].Concepts);
                        tempDataObj.Concepts_Total = 'Concept_Total=' + Object.keys(data[i].Concepts).length.toString();
                        for (let z = 0; z < Object.keys(data[i].Concepts).length; z++) {
                            tempDataObj.Concepts[z] = 'Concept=' + tempDataObj.Concepts[z] + '(' + data[i].Concepts[tempDataObj.Concepts[z]].split(',')[0] + ')';
                        }
                        // PValue and NEScore Addition 4/19/21
                        tempDataObj.PValue = `PValue=${('PValue' in clusterGroup.getObjectByName(data[i].Name).userData) ? clusterGroup.getObjectByName(data[i].Name).userData.PValue.toString() : null}`;
                        tempDataObj.NEScore = `NEScore=${('NEScore' in clusterGroup.getObjectByName(data[i].Name).userData) ? clusterGroup.getObjectByName(data[i].Name).userData.NEScore.toString() : null}`;
                        tempDataArr.push(tempDataObj);
                    }
                }
                tempDataArr = JSON.parse(JSON.stringify(tempDataArr).replace(/&/g, ''));
                let httpData = { 'function': 'exportPHP', 'parameters': { 'Account': ProjectAcct, 'Project': Project, 'Data': tempDataArr } };
                // log(httpData);
                mainCfg.exportVisData(httpData);
            } else {
                alertInformation('One or more Themes must be selected to do this.');
            }
        },
        h1: function() {
            log('export all items');
            let tempDataArr = [];
            let File_Name = parameters.h2 ? parameters.h2 : 'export_data';
            if (File_Name.includes(' ')) {
                File_Name = File_Name.replace(/ /g, '_');
            }
            if (File_Name.includes('&')) {
                File_Name = File_Name.replace(/&/g, '-');
            }
            tempDataArr.push(File_Name);
            for (let i = 0; i < optimalItemsSize(); i++) {
                let tempDataObj = {};
                tempDataObj.Name = 'Name=' + clusterGroup.getObjectByName(data[i].Name).userData.Name.replaceAll('<br>', ' ');
                tempDataObj.Score = 'Score=' + data[i].Score;
                tempDataObj.Coordinates = ['Coordinates=X:' + data[i].X, 'Y:' + data[i].Y, 'Z:' + data[i].Z];
                tempDataObj.Coordinates = tempDataObj.Coordinates.toString();
                tempDataObj.Genes = Object.keys(data[i].Genes);
                tempDataObj.Genes_Total = 'Entity_Total=' + Object.keys(data[i].Genes).length.toString();
                for (let z = 0; z < Object.keys(data[i].Genes).length; z++) {
                    tempDataObj.Genes[z] = 'Entity=' + tempDataObj.Genes[z] + '(' + data[i].Genes[tempDataObj.Genes[z]].Score + ')';
                }
                // tempDataObj.Genes = tempDataObj.Genes.toString();
                tempDataObj.Concepts = Object.keys(data[i].Concepts);
                tempDataObj.Concepts_Total = 'Concept_Total=' + Object.keys(data[i].Concepts).length.toString();
                for (let z = 0; z < Object.keys(data[i].Concepts).length; z++) {
                    tempDataObj.Concepts[z] = 'Concept=' + tempDataObj.Concepts[z] + '(' + data[i].Concepts[tempDataObj.Concepts[z]].split(',')[0] + ')';
                }
                // PValue and NEScore Addition 4/19/21
                tempDataObj.PValue = `PValue=${('PValue' in clusterGroup.getObjectByName(data[i].Name).userData) ? clusterGroup.getObjectByName(data[i].Name).userData.PValue.toString() : null}`;
                tempDataObj.NEScore = `NEScore=${('NEScore' in clusterGroup.getObjectByName(data[i].Name).userData) ? clusterGroup.getObjectByName(data[i].Name).userData.NEScore.toString() : null}`;
                tempDataArr.push(tempDataObj);
            }
            let httpData = { 'function': 'exportPHP', 'parameters': { 'Account': ProjectAcct, 'Project': Project, 'Data': JSON.stringify(tempDataArr).replace(/&/g, '') } };
            mainCfg.exportVisData(httpData);
            // exportDataToCSV(JSON.stringify(tempDataArr));
        },
        h2: 'export_data',
        h3: function() {
            if (selected.length > 0) {
                exportPublicationTables();
            } else {
                alertInformation('One or more themes must be selected to export...');
            }
        },
        h4: function() {
            // if (selected.length > 0) {
                $( "#dialog-graph-options" ).dialog('open');
                // gatherGraphData();
            // } else {
            //     alertInformation('One or more themes must be selected to create a graph...');
            // }
        },
        h5: function() {
            getProjectInputList(true);
        },
        i: function() {
            navigateToHelp();
        },
        j: overlapFilters[0],
        k: function() {
            initCapture();
        },
        j0: filterModes[0],
        j1: handleApplyFilterClick,
        j2: true
    };
    guiProfileName = gui.add(profileParameters, 'profileName', savedProfiles).name('Profile').listen();
    guiProfileName.onChange(function() {
        guiLoadSaveToggle();
    });
    let guiLoadProfile = gui.add(profileParameters, 'loadProfile').name('Load Profile');
    let guiSaveProgress = gui.add(profileParameters, 'saveProgress').name('Save Progress');

    let guiProfileToolsFolder = gui.addFolder('Profile Tools');
    let guiCreateProfile = guiProfileToolsFolder.add(profileParameters, 'createProfile').name('Create New');
    let guiNewProfileName = guiProfileToolsFolder.add(profileParameters, 'newProfileName').name('New Name').listen();
    let guiSaveNewProfile = guiProfileToolsFolder.add(profileParameters, 'saveNewProfile').name('Set New Name');
    let guiCancelNewProfile = guiProfileToolsFolder.add(profileParameters, 'cancel').name('Cancel');
    let guiDeleteProfile = guiProfileToolsFolder.add(profileParameters, 'delete').name('Delete Profile');

    let guiScaleFolder = gui.addFolder('Scale Factor');
    let guiScaleVisibility = guiScaleFolder.add(parameters, 'c4').name('Scale Factor').listen();
    let guiScaleVisibilitySave = guiScaleFolder.add(parameters, 'c5').name('Initiate Scaling');

    let guiVisibilityFolder = gui.addFolder('Filters');
    /* Added for PValue and NEScore Slider Filters */
    if(data[0].hasOwnProperty('PValue') && data[0].PValue !== 'N/A' && data[0].hasOwnProperty('NEScore') && data[0].NEScore !== 'N/A') {
        pvalObj.min = Math.min(...data.map(i => i.PValue).map(i => i.replace(/\<|N\/A/,'')).filter(Number).map(i => Number(i)));
        pvalObj.min = (pvalObj.min > 0.1) ? 0.1 : pvalObj.min;
        pvalObj.max = Math.max(...data.map(i => i.PValue).map(i => i.replace(/\<|N\/A/,'')).filter(Number).map(i => Number(i)));
        pvalObj.max = (pvalObj.max < 0.1) ? 0.1 : pvalObj.max;
        if(pvalObj.min != pvalObj.max) {
            parameters.c8 = 0.1;
            let guiPvalFilter = guiVisibilityFolder.add(parameters, 'c8').min(pvalObj.min).max(pvalObj.max).step(0.001).name('PValue').listen();
            if(optimalItemsSize() > 50) {
                guiPvalFilter.onFinishChange(function(tempVal) {
                    parameterToPercentage();
                });                
            } else {
                guiPvalFilter.onChange(function(tempVal) {
                    parameterToPercentage();
                });                
            }

        }
        filterPvalAndNescore = function() {
            for(let i = 0; i < optimalItemsSize(); i++) {
                if(!clusterGroup.children[i].userData.PValue.includes('N/A') 
                    && Number(clusterGroup.children[i].userData.PValue.replace(/\</,'')) > parameters.c8
                    || !clusterGroup.children[i].userData.NEScore.includes('N/A')
                    && Number(clusterGroup.children[i].userData.NEScore.replace(/\</,'')) < parameters.c9) {
                        if(boxViewActive) {
                            boxViewGroup.children[i].element.style.display = 'none';
                            spriteTextGroup.children[i].element.style.display = 'none';
                            if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                                dragLines.children[i].visible = false;
                            }
                        } else {
                            clusterGroup.children[i].visible = false;
                            cubeGroup.children[i].visible = false;
                            spriteTextGroup.children[i].element.style.display = 'none';
                            if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                                dragLines.children[i].visible = false;
                            }
                        }
                }
            }
            checkVisibility();
        }
    }    
    let guiScoreVisibility = guiVisibilityFolder.add(parameters, 'c3').min(0).max(100).step(1).name('Score(%)').listen();
    guiScoreVisibility.onFinishChange(function(tempVal) {
        parameterToPercentage(100 - tempVal, 'score');
        //Unhide VisLocked Items
        if(visLockedTubes.length > 0 || groupHiddenTubes.length > 0) {
            showVisLockedItems();
        }
        reportVisisbleGenesTotal();
        if( filterHaloGroup.length > 0 && parameters.j2 === true ) {
            haloVisiblityCheck();
        }
    });
    /* Added for PValue and NEScore Slider Filters */
    if(data[0].hasOwnProperty('PValue') && data[0].PValue !== 'N/A' && data[0].hasOwnProperty('NEScore') && data[0].NEScore !== 'N/A') {
        nesObj.min = Math.min(...data.slice(0,optimalItemsSize()).map(i => i.NEScore).map(i => i.replace(/\<|N\/A/,'')).filter(Number).map(i => Number(i))),
        nesObj.max = Math.max(...data.slice(0,optimalItemsSize()).map(i => i.NEScore).map(i => i.replace(/\<|N\/A/,'')).filter(Number).map(i => Number(i)))
        if(nesObj.min != nesObj.max) {
            parameters.c9 = nesObj.min;
            let guiNesFilter = guiVisibilityFolder.add(parameters, 'c9').min(nesObj.min).max(nesObj.max).step(0.001).name('NEScore').listen();
            if(optimalItemsSize() > 50) {
                guiNesFilter.onFinishChange(function(tempVal) {
                    parameterToPercentage();
                    if(visLockedTubes.length>0 || groupHiddenTubes.length > 0) {
                        showVisLockedItems();
                    }
                    if( filterHaloGroup.length > 0 && parameters.j2 === true ) {
                        haloVisiblityCheck();
                    }
                });                
            } else {
                guiNesFilter.onChange(function(tempVal) {
                    parameterToPercentage();
                    if(visLockedTubes.length>0 || groupHiddenTubes.length > 0) {
                        showVisLockedItems();
                    }
                    if( filterHaloGroup.length > 0 && parameters.j2 === true ) {
                        haloVisiblityCheck();
                    }
                });                
            }
        }
    }
    let guiLineVisibility = guiVisibilityFolder.add(parameters, 'c2').min(0).max(100).step(1).name('Distance(%)').listen();
    guiLineVisibility.onChange(function(tempVal) {
        distanceCheck();
        //Unhide VisLocked Items
        if(visLockedTubes.length>0 || groupHiddenTubes.length > 0) {
            showVisLockedItems();
        }
        if( filterHaloGroup.length > 0 && parameters.j2 === true ) {
            haloVisiblityCheck();
        }
    });
    let guiDepthFilter = guiVisibilityFolder.add(parameters, 'c6').min(1).max(5000).step(1).name('Depth Filter(Z)').listen();
    guiDepthFilter.onChange(function(tempVal) {
        if(parameters.c7 === true) {
            camera1.far = ( Math.round(( parameters.c6 * maxSceneDepth ) / 5000 ) > 0 ) ? Math.round(( parameters.c6 * maxSceneDepth ) / 5000 ) : 1;
            raycaster.far = camera1.far;
            camera1.updateProjectionMatrix();
        }
    });
    let guiDepthFilterSwitch = guiVisibilityFolder.add(parameters, 'c7').name('Depth On/Off').listen();
    guiDepthFilterSwitch.onChange(function(tempVal) {
        if(tempVal === true) {
            camera1.far = ( Math.round(( parameters.c6 * maxSceneDepth ) / 5000 ) > 0 ) ? Math.round(( parameters.c6 * maxSceneDepth ) / 5000 ) : 1;
            raycaster.far = camera1.far;
            camera1.updateProjectionMatrix();
        } else if(tempVal === false) {
            camera1.far = maxSceneDepth;
            raycaster.far = camera1.far;
            camera1.updateProjectionMatrix();
        }
    });
    if (mainCfg.hasOverlapFilterMode) {
        let guiOverlapFilter = guiVisibilityFolder.add(parameters, 'j', overlapFilters).name('Overlap Filter').listen();
        guiOverlapFilter.onChange(function(tempVal) {
            log(tempVal);
            guiOverlapFilterMode.domElement.querySelector('select').disabled = tempVal === 'None';
        });
        let guiOverlapFilterMode = guiVisibilityFolder.add(parameters, 'j0', filterModes).name('Filter Mode');
        guiOverlapFilterMode.domElement.querySelector('select').disabled = true;
        let guiFilterHaloSwitch = guiVisibilityFolder.add( parameters, 'j2' ).name('Filter Halo\'s').listen();
        guiFilterHaloSwitch.onChange( tempVal => {
            if( appliedFilter !== 'None' ) {
                applyFilterHalos( tempVal );
            }
        });
        let guiOverlapInit = guiVisibilityFolder.add(parameters, 'j1').name('Apply Filter').listen();
    }
    let guiMaterialFolder = gui.addFolder('Theme Tools');
    let guiClusterName = guiMaterialFolder.add(parameters, 'a').name('Theme Name').listen();
    let guiClusterNameSave = guiMaterialFolder.add(parameters, 'a1').name('Set Name');
    let guiClusterNames = guiMaterialFolder.add(parameters, 'c').name('Hide Names').listen();
    guiClusterNames.onChange(function(tempVal) {
        if (tempVal === true) {
            let show = false;
            spriteTextGroup.visible = show;
            dragLines.visible = show;
            for( let i=0; i<spriteTextGroup.children.length; i++ ) {
                spriteTextGroup.children[i].element.style.display = 'none';
            }
        } else {
            let show = true;
            spriteTextGroup.visible = show;
            dragLines.visible = show;
            for( let i=0; i<spriteTextGroup.children.length; i++ ) {
                if(autoAnnotationActive == true) {
                    spriteTextGroup.children[i].element.style.display = 'flex';
                    spriteTextGroup.children[i].element.style.flexDirection = 'column';
                    spriteTextGroup.children[i].element.style.alignItems = 'center';
                } else {
                    spriteTextGroup.children[i].element.style.display = 'initial';
                }
            }
        }
    });
    let guiClusterConcepts = guiMaterialFolder.add(parameters, 'c1').name('Hide Concepts').listen();
    guiClusterConcepts.onChange(function(tempVal) {
        if (tempVal === true) {
            let show = false;
            cubeGroup.visible = show;
        } else {
            let show = true;
            cubeGroup.visible = show;
        }
    });
    let guiColors = guiMaterialFolder.addColor(parameters, 'd').name('Color').listen();
    guiColors.onChange(function(tempVal) {
        if (selected.length > 0) {
            selectedCluster.material.color.setHex(tempVal.replace("#", "0x"));
        }
    });
    let guiColorSave = guiMaterialFolder.add(parameters, 'd1').name('Set Color');
    let guiVisLock = guiMaterialFolder.add(parameters, 'd4').name('Always Show');

    let guiGroupFolder = gui.addFolder('Group Tools');
    let guiGroup = guiGroupFolder.add(parameters, 'e').name('Group Name').listen();
    let guiGroupSave = guiGroupFolder.add(parameters, 'e1').name('Set Group');
    let guiGroupHide = guiGroupFolder.add(parameters, 'e2').name('Hide Group');

    let guiGroupShowOnly = guiGroupFolder.add(parameters, 'e3').name('Unhide Group');

    let guiGroupColors = guiGroupFolder.addColor(parameters, 'e4').name('Group Color').listen();
    guiGroupColors.onChange(function(tempVal) {
        if (selectedGroup) {
            if (selectedGroup.length > 0) {
                for (let i = 0; i < selectedGroup.length; i++) {
                    if (clusterGroup.getObjectByName(selectedGroup[i])) {
                        clusterGroup.getObjectByName(selectedGroup[i]).material.color.setHex(tempVal.replace("#", "0x"));
                    } else {
                        log('error --> ' + selectedGroup[i] + ' does not seem to exist');
                    }
                }
            }
        }
    });
    let guiGroupColorsSave = guiGroupFolder.add(parameters, 'e5').name('Set Group Color').listen();

    let guiEnviromentFolder = gui.addFolder('Visual Setting');
    let guiClusterNameSize = guiEnviromentFolder.add(parameters, 'd2').min(1).max(15).step(1).name('Theme Text').listen();
    guiClusterNameSize.onChange(function(tempVal) {
        for (let i = 0; i < spriteTextGroup.children.length; i++) {
            spriteTextGroup.children[i].element.style.fontSize = `${tempVal * 4}px`;
        }
        let modVal = ((tempVal - 3) / 10) + 1;
        Array.from(document.getElementsByClassName('themebox-name')).forEach(function(l) {
            l.style.fontSize = `${modVal}em`;
        });
    });
    let guiConceptSize = guiEnviromentFolder.add(parameters, 'f2').min(1).max(2).step(0.1).name('Concept Text').listen();
    guiConceptSize.onChange(function(tempVal) {
        for (let i = 0; i < cubeGroup.children.length; i++) {
            cubeGroup.children[i].scale.set(0, 0, 0);
        }
        for (let i = 0; i < cubeGroup.children.length; i++) {
            cubeGroup.children[i].scale.x += tempVal;
            cubeGroup.children[i].scale.y += tempVal;
            cubeGroup.children[i].scale.z += tempVal;
        }
        Array.from(document.getElementsByClassName('themebox-table')).forEach(function(t) {
            t.style.fontSize = `${tempVal}em`;
        });
    });
    //Theme(cluster sphere) switch between quaternion and lookAt methods in animate function
    // let guiQuaternionSwitch = guiEnviromentFolder.add(parameters, 'f3').name('Text Center').listen();

    let guiClusterNameColor = guiEnviromentFolder.addColor(parameters, 'd3').name('Label Text').listen();
    guiClusterNameColor.onChange(function(tempVal) {
        for (let i = 0; i < spriteTextGroup.children.length; i++) {
            spriteTextGroup.children[i].element.style.color = tempVal;
        }
        Array.from(document.getElementsByClassName('themebox-name')).forEach(function(l) {
            l.style.color = tempVal;
        });
    });
    let guiLabelBackgroundColor = guiEnviromentFolder.addColor(parameters, 'd5').name('Label Background').listen();
    guiLabelBackgroundColor.onChange(function(tempVal) {
        for (let i = 0; i < spriteTextGroup.children.length; i++) {
            spriteTextGroup.children[i].element.style.background = tempVal;
        }
        Array.from(document.getElementsByClassName('themebox-name')).forEach(function(l) {
            l.style.background = tempVal;
        });
    });    
    let guiAnnotationScoresVis = guiEnviromentFolder.add(parameters, 'f4').name('Label Scores').listen();
    guiAnnotationScoresVis.onChange(function( tempVal ) {
        console.log('label score change');
        if( autoAnnotationActive === true ) {
            if( tempVal === true ) {
                console.log( 'removing spans' );
                spriteTextGroup.children.forEach(( labelObject ) => {
                    if(labelObject.userData.isCustomName === false) {
                        let els = Array.from( labelObject.element.children );
                        els.forEach(( span ) => {
                            let text = $( span ).text();
                            let score = $( span ).data( 'score' ) || '';
                            let modText = text + score;
                            $( span ).text( modText );
                            $( span ).removeAttr( 'data-score' );
                        });
                        labelObject.userData.isScoreShown = true;
                    } else {
                        console.log(labelObject.name);
                        console.log(labelObject.userData);
                        console.log('This is a custom name, and the score has been removed');
                    }
                });
            } else {
                console.log( 'adding spans' );
                spriteTextGroup.children.forEach(( labelObject ) => {
                    if(labelObject.userData.isCustomName === false) {
                        let els = Array.from( labelObject.element.children );
                        els.forEach(( span ) => {
                            let text = $( span ).text();
                            let sIndex = text.lastIndexOf( '(' );
                            let modText = text.slice( 0,sIndex );
                            let score = text.slice( sIndex,text.length );
                            $( span ).attr( 'data-score',score );
                            $( span ).text( modText );
                        });
                        labelObject.userData.isScoreShown = false;
                    } else {
                        console.log(labelObject.name);
                        console.log(labelObject.userData);
                        console.log('This is a custom name, and the score has been removed');
                    }
                });                
            }
            for(let i=0;i<boxViewGroup.children.length;i++) {
                boxViewGroup.children[i].element.firstElementChild.innerHTML = spriteTextGroup.children[i].element.innerHTML;
            }
        } else {
            console.log('AutoAnnotations not active, no score to change');
            parameters.f4 = false;
        }
    });
    let guiAnnotationBoldLabelVis = guiEnviromentFolder.add(parameters, 'f5').name('Bold Labels').listen();
    guiAnnotationBoldLabelVis.onChange(function(tempVal) {
        if(tempVal == true) {
            spriteTextGroup.children.forEach((label) => {
                label.element.classList.add('theme-label-bold');
            });
            Array.from(document.getElementsByClassName('themebox-name')).forEach(function(l) {
                l.classList.add('theme-label-bold');
            });
        } else {
            spriteTextGroup.children.forEach((label) => {
                label.element.classList.remove('theme-label-bold');
            });
            Array.from(document.getElementsByClassName('themebox-name')).forEach(function(l) {
                l.classList.remove('theme-label-bold');
            });
        }
    });
    let guiAnnotationFirstLineOnlyLabelVis = guiEnviromentFolder.add(parameters, 'f6').name('First Line Only').listen();
    guiAnnotationFirstLineOnlyLabelVis.onChange(function(tempVal) {
        if(autoAnnotationActive == true) {
            if(tempVal == true) {
                spriteTextGroup.children.forEach((label) => {
                    if(label.userData.isCustomName === false) {
                        let labelEl = label.element;
                        let labelSpans = Array.from(labelEl.children);
                        labelSpans.forEach((span,index) => {
                            if(index > 0) {
                                span.classList.add('flo-hidden');
                            }
                        });
                    } else {
                        console.log(label.name);
                        console.log(label.userData)
                        console.log('This is a custom name and will will not have multiple line labels');
                    }
                });
            } else {
                let hiddenSpans = Array.from( document.getElementsByClassName('flo-hidden'));
                hiddenSpans.forEach(span => span.classList.remove('flo-hidden'));
            }
            for(let i=0;i<boxViewGroup.children.length;i++) {
                boxViewGroup.children[i].element.firstElementChild.innerHTML = spriteTextGroup.children[i].element.innerHTML;
            }
        } else {
            console.log('AutoAnnotations not active, no multi-line labels to change');
            parameters.f6 = false;
        }
    });
    let guiBoxThemeTablesVisibility = guiEnviromentFolder.add(parameters, 'f7').name('Theme-Box Table Hide').listen();
    guiBoxThemeTablesVisibility.onChange(function(tempVal) {
        if(tempVal === true) {
            Array.from(document.getElementsByClassName('themebox-table-cont')).forEach(function(t) {
                t.classList.add('themebox-table-cont-hidden');
            });
        } else {
            Array.from(document.getElementsByClassName('themebox-table-cont-hidden')).forEach(function(t) {
                t.classList.remove('themebox-table-cont-hidden');
            });
        }
    });
    let guiWireframe = guiEnviromentFolder.add(parameters, 'b').name('Wireframe').listen();
    guiWireframe.onChange(function(tempVal) {
        for (let i = 0; i < clusterGroup.children.length; i++) {
            clusterGroup.children[i].material.wireframe = tempVal;
        }
    });
    let guiBackground = guiEnviromentFolder.add(parameters, 'f', [mainCfg.defaultBGName, 1, 2, 3, 4, 5, 6, 7, "None"]).name('Background').listen();
    guiBackground.onChange(function(tempVal) {
        if (tempVal == 'None') {
            console.log("!!!!!!!!!!!!!BG LOAD 1")
            document.body.style.backgroundImage = 'none';
            document.body.style.backgroundColor = parameters.f1;
        } else if(tempVal == mainCfg.defaultBGName) {
            console.log("!!!!!!!!!!!!!BG LOAD 2")
            document.body.style.backgroundImage = 'none';
            document.body.style.background = 'radial-gradient(farthest-side at 80% 45%,#420808 40%,#131313)';
        } else {
            console.log("!!!!!!!!!!!!!BG LOAD 3")
            document.body.style.backgroundImage = `url("../${mainCfg.assetsPath}/images/back${tempVal}-min.jpg")`;
            document.body.style.backgroundRepeat = 'no-repeat';
            document.body.style.backgroundSize = 'cover';
        }
    });
    let guiBackgroundColor = guiEnviromentFolder.addColor(parameters, 'f1').name('Background Color').listen();
    guiBackgroundColor.onChange(function(tempVal) {
        document.activeElement.blur();
        document.body.style.backgroundImage = 'none';
        parameters.f = 'None';
        document.body.style.backgroundColor = tempVal;
    });

    let guiCreateFigureFolder = gui.addFolder('Create Figure');
    let guiBarGraph = guiCreateFigureFolder.add(parameters,'h4').name('Bar Graph');
    let guiCapture = guiCreateFigureFolder.add(parameters, 'k').name('Capture Scene');    

    let guiExportFolder = gui.addFolder('Export Data');
    if (mainCfg.canExportPublicationTable) {
        let guiExportPublication = guiExportFolder.add(parameters, 'h3').name('Export Selected (Publication Table)');
    }
    let guiExportSelected = guiExportFolder.add(parameters, 'h').name(mainCfg.exportSelectedName);
    let guiExportAll = guiExportFolder.add(parameters, 'h1').name(mainCfg.exportAllName);
    let guiExportName = guiExportFolder.add(parameters, 'h2').name('Data File Name').listen();
    let guiExportMapData = guiExportFolder.add(parameters,'h5').name('Entity Mapping Data');

    //Set new center to selected cluster
    let guiPivotSet = gui.add(parameters, 'g').name('Set Center Point');
    //Recenter Pivot Point
    let guiPivotRecenter = gui.add(parameters, 'g1').name('Reset Center Point');

    let guiHelp = gui.add(parameters, 'i').name('<i class="fa fa-question-circle" aria-hidden="true"></i> Help')

    gui.open();
    //set current loaded profile
    currentLoadedProfile = profileParameters.profileName;

    if (!mainCfg.showVisDataPanel) {
        document.getElementById('hudClose').addEventListener('click', function() {
            $('#hud').slideToggle(function() {
                $('#hudOpen').slideToggle();
            });
        });

        document.getElementById('hudOpen').addEventListener('click', function() {
            $('#hudOpen').slideToggle(function() {
                $('#hud').slideToggle();
            });
        });
        //Minimize button end
    }


    sphereConstruct(); //Construct Spheres and Populate HUD Cluster Info

    checkVisibility();
    sortedByScore.sort(function(a, b) {
        return a.score - b.score;
    });
    //Sets the scene to the default score visibility
    parameterToPercentage();

    //Generate HUD GeneViewList
    generateGeneViewList();

    //Detects if mouseover a popup div
    document.getElementById('clusterInfo').onmouseover = function() {
        GLOBAL_STATE.mouseOverDiv = true;
    }
    document.getElementById('clusterInfo').onmouseout = function() {
        GLOBAL_STATE.mouseOverDiv = false;
    }
    document.getElementById('genesPopupDisplay').onmouseover = function() {
        GLOBAL_STATE.mouseOverDiv = true;
    }
    document.getElementById('genesPopupDisplay').onmouseout = function() {
        GLOBAL_STATE.mouseOverDiv = false;
    }
    if (!mainCfg.showVisDataPanel) {
        document.getElementById('hud').onmouseover = function() {
            GLOBAL_STATE.mouseOverDiv = true;
        }
        document.getElementById('hud').onmouseout = function() {
            GLOBAL_STATE.mouseOverDiv = false;
        }
    }
    document.getElementById('dialog-notes').onmouseover = function() {
        GLOBAL_STATE.mouseOverDiv = true;
    }
    document.getElementById('dialog-notes').onmouseout = function() {
        GLOBAL_STATE.mouseOverDiv = false;
    }
    document.getElementById('chat-box').onmouseover = function() {
        GLOBAL_STATE.mouseOverDiv = true;
    }
    document.getElementById('chat-box').onmouseout = function() {
        GLOBAL_STATE.mouseOverDiv = false;
    }
    document.getElementById('dialog-wheel').onmouseover = function() {
        GLOBAL_STATE.mouseOverDiv = true;
    }
    document.getElementById('dialog-wheel').onmouseout = function() {
        GLOBAL_STATE.mouseOverDiv = false;
    }
    document.getElementById('concept-map').onmouseover = function() {
        GLOBAL_STATE.mouseOverDiv = true;
    }
    document.getElementById('concept-map').onmouseout = function() {
        GLOBAL_STATE.mouseOverDiv = false;
    }

    if (mainCfg.useNewThemePopup) {
        document.getElementById('new-theme-popup').onmouseover = function() {
            GLOBAL_STATE.mouseOverDiv = true;
        }
        document.getElementById('new-theme-popup').onmouseout = function() {
            GLOBAL_STATE.mouseOverDiv = false;
        }

        document.getElementById('new-entity-popup').onmouseover = function() {
            GLOBAL_STATE.mouseOverDiv = true;
        }
        document.getElementById('new-entity-popup').onmouseout = function() {
            GLOBAL_STATE.mouseOverDiv = false;
        }
    }

    if (mainCfg.useNewSharedEntitiesPopup) {
        document.getElementById('new-shared-entities-popup').onmouseover = function() {
            GLOBAL_STATE.mouseOverDiv = true;
        }
        document.getElementById('new-shared-entities-popup').onmouseout = function() {
            GLOBAL_STATE.mouseOverDiv = false;
        }
    }



    window.addEventListener('resize', onWindowResize, false);
    // document.addEventListener('mousedown', onDocumentMouseDown, false);
    //Cdev- bind raycaster click event to canvas1 
    document.getElementById('label-renderer').addEventListener('mousedown', onDocumentMouseDown, false);
    document.addEventListener('touchstart', onDocumentTouchStart, false);

    //hide gui panels
    guiLoadSaveToggle();
    guiProfileCeationToolsToggle();

    // hide stats initially
    document.getElementById('stats').style.visibility = 'hidden';
    showStats = false;
    console.log('making stats hidden.');
    
    let hud2InnerWrap = document.getElementById('hud2-inner-wrap');
    hud2InnerWrap.style.height = Math.floor((window.innerHeight - 105) - (window.innerHeight * 0.03)) + 'px';

    document.getElementById('customTermCheckBox').addEventListener('click', function(event) {
        let addOnTermNode = document.getElementById('add-on-term');
        if (event.target.checked) {
            addOnTermNode.style.visibility = 'inherit';
            addOnTermNode.style.display = 'block';
        } else {
            addOnTermNode.style.visibility = 'hidden';
            addOnTermNode.style.display = 'none';
            document.getElementById('customTermTextBox').value = '';
        }
    });
    //CustomSearchEngine input enter button pressed event listener
    document.getElementById('customTermTextBox').addEventListener('keyup', function(event) {
        event.preventDefault();
        if (event.keyCode === 13) {
            document.getElementById("custom-search-btn").click();
        }
    });

    //Hide datGui3 for canvas3
    // let guiMenus = document.querySelectorAll('.dg.main');
    // datGui1 = guiMenus[0];
    // datGui3 = guiMenus[1];
    // datGui3.style.zIndex = 1;
    // $(datGui3).toggle();


    //Add Color/Groups/VisibilityLock to ThemeRegistry
    for (let i = 0; i < optimalItemsSize(); i++) {
        ThemeNameRegistry[i].Color = clusterGroup.children[i].material.color.getHexString();
        ThemeNameRegistry[i].Group = '';
        ThemeNameRegistry[i].GroupColor = '';
        ThemeNameRegistry[i].GroupHidden = false;
        ThemeNameRegistry[i].VisibilityLock = false;
    }

    $('#clusterInfo').draggable({ handle: '.cluster-info-header', drag });

    $('#genesPopupDisplay').draggable({ handle: '.gene-popup-drag-handle', drag });

    if (mainCfg.useNewThemePopup) {
        $('#new-theme-popup').draggable({ handle: '.popup-header', drag });
        $('#new-entity-popup').draggable({ handle: '.popup-header', drag });
    }

    if (mainCfg.useNewSharedEntitiesPopup) {
        $('#new-shared-entities-popup').draggable({ handle: '.popup-header', drag });
    }

    function drag(e, ui) {
        if (ui.position.top < 0 || ui.position.top > document.documentElement.clientHeight ||
            ui.position.left < 0 || ui.position.left >  document.documentElement.clientWidth) {
            e.preventDefault();
            e.stopPropagation();
        }
    }

    if (!mainCfg.showNewRelationDisplay) {
        //Add hidden property to visLockPanel element
        document.getElementById('visLockPanel').style.visibility = 'hidden';
    }
  if (!mainCfg.showNewSelectedThemesPanel) {
    document.getElementById('deselectAll').style.visibility = 'hidden';
    document.getElementById('selectedClusters').style.visibility = 'hidden';
  }

    //Add GlowGroup
    cssScene.add( glowGroup, filterHaloGroup );

    //Disable browser back button
    history.pushState(null, null, location.href);
    window.onpopstate = function() {
        history.go(1);
        alertInformation('The browser back button is disabled');
    };
    if (!mainCfg.showVisDataPanel) {
        document.getElementById('scale-file').innerHTML = 'Data File: ' + mainCfg.getDataSrc() + '<br />Scale Factor: ' + PM;
        if (ExpPVal != null) {
            document.getElementById('exp-pval').innerHTML = '<span>Experiment PValue:</span><span id="exp-pval-val">' + ExpPVal + '</span>';
        } else {
            // document.getElementById('exp-pval').remove();
            console.log('No EXP-VAL');
        }
    }

    // checkVisibility();
    // distanceCheck();
    // Resizable hud container init
    $('#hud').resizable({
        handles: 'e'
    });
    // Resize init end
    reportVisisbleGenesTotal();

    for( let i = 0;i < optimalItemsSize();i++ ) {
        let clusterName = clusterGroup.children[i].name;        
        let clusterPosition = new THREE.Vector3().copy( clusterGroup.children[i].position );
        let labelPosition = new THREE.Vector3().copy( spriteTextGroup.getObjectByName(`spriteText=${clusterName}`).position );
        let visible = clusterGroup.children[i].visible;

        initSingleLabelLine( clusterName,clusterPosition,labelPosition,visible );
    }
    setTimeout(() => {
        initAutoAnnotation();
        visThemeUpdateStat();
    }, 1000);
    onInitDone();
    console.log('INIT TOOK', new Date() - t);
} //Init End

function helpRouting(id) {
    id = id.replace('route-', '');
    switch (id) {
        case 'Profile-Tools':
            navigateToHelp(HELP_URLS.PROFILE_TOOLS);
            break;

        case 'Scale-Factor':
            navigateToHelp(HELP_URLS.SCALE_FACTOR);
            break;

        case 'Filters1':
            navigateToHelp(HELP_URLS.FILTERS1);
            break;

        case 'Theme-Tools':
            navigateToHelp(HELP_URLS.THEME_TOOLS);
            break;

        case 'Group-Tools':
            navigateToHelp(HELP_URLS.GROUP_TOOLS);
            break;

        case 'Visual-Setting':
            navigateToHelp(HELP_URLS.VISUAL_SETTING);
            break;

        case 'Export-Data':
            navigateToHelp(HELP_URLS.EXPORT_DATA);
            break;

        case 'Filters2':
            navigateToHelp(HELP_URLS.FILTERS2);
            break;

        default:
            log('unrecognized id');
            break;
    }
}

//WindowResize Start
function onWindowResize() {
    controls.handleResize();
    camera1.aspect = window.innerWidth / window.innerHeight;
    camera1.updateProjectionMatrix();
    renderer.setSize( window.innerWidth, window.innerHeight );
    cssrenderer.setSize( window.innerWidth, window.innerHeight );
    labelCssRenderer.setSize( window.innerWidth, window.innerHeight );
    //Resize "concept-map" element
    $("#concept-map").dialog("option", "width", window.innerWidth - 15);
    $("#concept-map").dialog("option", "height", window.innerHeight - 15);
    //Resize canvas3
    camera3.aspect = ( document.getElementById('concept-map').clientWidth / document.getElementById('concept-map').clientHeight );
    camera3.updateProjectionMatrix();
    renderer3.setSize( document.getElementById('concept-map').clientWidth, document.getElementById('concept-map').clientHeight );
    document.getElementById('hud2-inner-wrap').style.height = Math.floor( document.getElementById('concept-map').clientHeight - document.getElementById('hud2-header').clientHeight ) + 'px';
    // document.getElementById('hud2-inner-wrap').style.height = Math.floor((window.innerHeight - 105) - (window.innerHeight * 0.03)) + 'px';

} //WindowResize End
labelCssRenderer.domElement.addEventListener('mousemove', onDocumentMouseMove, false);
//MouseMove Start
function onDocumentMouseMove(event) {
    mouseOverC3 = false;
    mouse.x = event.clientX / window.innerWidth * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
    raycaster.setFromCamera(mouse, camera1);
    intersects = raycaster.intersectObjects(clusterGroup.children);
    if( GLOBAL_STATE.mouseOverDiv === false && labelIntersects.length < 1 && themeBoxIntersects.length < 1 ) {
        if (intersects.length > 0) {
            if (INTERSECTED != intersects[0].object) {
                if (INTERSECTED) INTERSECTED.material.emissive ? INTERSECTED.material.emissive.setHex(INTERSECTED.currentHex) : INTERSECTED.material.color.setHex(INTERSECTED.currentHex);
                INTERSECTED = intersects[0].object;
                INTERSECTED.currentHex = INTERSECTED.material.emissive ? INTERSECTED.material.emissive.getHex() : INTERSECTED.material.color.getHex();
                INTERSECTED.material.emissive ? INTERSECTED.material.emissive.setHex(0xbf4b0d) : INTERSECTED.material.color.setHex(0xbf4b0d);
            }
        } else {
            if (INTERSECTED) INTERSECTED.material.emissive ? INTERSECTED.material.emissive.setHex(INTERSECTED.currentHex) : INTERSECTED.material.color.setHex(INTERSECTED.currentHex);
            INTERSECTED = 0;
        }
    }
} //MouseMove End
//Touch Start
function onDocumentTouchStart(event) {
    //event.preventDefault();//Cannot use datgui dropdowns
    event.clientX = event.touches[0].clientX;
    event.clientY = event.touches[0].clientY;
    onDocumentMouseDown(event);
} //Touch End
//MouseClick Start
function onDocumentMouseDown(event) {
    event.preventDefault();//Cannot use datgui dropdowns
    mouse.x = event.clientX / renderer.domElement.clientWidth * 2 - 1;
    mouse.y = -(event.clientY / renderer.domElement.clientHeight) * 2 + 1;
    raycaster.setFromCamera(mouse, camera1);
    intersects = raycaster.intersectObjects(clusterGroup.children);
    if( dragKeyDown === false && labelIntersects.length > 0 && annotations !== null ) {
        labelSelect( event );
    } else if( dragKeyDown === false && themeBoxIntersects.length > 0 ) {
        themeBoxClicked = true;
        themeBoxClickSelect( event );
    } else if( dragKeyDown === false ) {
        clickSelect();
    }
} //MouseClick End

function clickSelect() {
    if (GLOBAL_STATE.mouseOverDiv === false && mouseOverC3 == false) {
        if (intersects.length > 0) {
            if (popUpShown === false || intersects[0].object.userData.type === 'TubeGeometry') {
                selectedCluster = intersects[0].object;
            }
            if (selectedCluster.userData.type === "SphereGeometry") {
                if (popUpShown === false) {
                    if (selected.indexOf(selectedCluster.name) < 0) {
                        prevColor.push(selectedCluster.material.color.getHexString());
                        selectedCluster.material.color.setHex(0xbf4b0d);
                        GLOBAL_STATE.selected = [...GLOBAL_STATE.selected, selectedCluster.name];
                        showSelected();
                        hudDataBind(selectedCluster.name);
                        popupClusterInfo();
                        let bEl = document.getElementById(`themeBoxEl=${selectedCluster.name}`);
                        console.log(bEl);
                        if(bEl !== null) {
                            // bEl.classList.toggle('boxViewSelected');
                            bEl.querySelector('.themebox-table-cont').classList.toggle('boxViewSelected');
                        } else {
                            console.log('Box Element NOT Found!');
                        }
                        if (window.event.ctrlKey) {
                            console.log('CTRL was being held!');
                            connectedMultiSelect();
                        }
                    } else if (selected.indexOf(selectedCluster.name) >= 0) {
                        if (window.event.ctrlKey) {
                            console.log('CTRL was being held for DeSelect!');
                            connectedMultiDeSelect();
                        }
                        let index = selected.indexOf(selectedCluster.name);
                        hudDataBind(selectedCluster.name);
                        selectedCluster.material.color.setHex('0x' + prevColor[index]);
                        prevColor.splice(index, 1);
                        removeSelectedThemeByName(selectedCluster.name);
                        if (!mainCfg.showNewSelectedThemesPanel) {
                          let element = document.getElementById('selectedClusters');
                          element.removeChild(element.childNodes[index]);
                          if (element.childNodes.length < 1) {
                            element.style.visibility = 'hidden';
                            document.getElementById('deselectAll').style.visibility = 'hidden';
                            parameters.a = 'Select A Cluster';
                          }
                        }
                        let bEl = document.getElementById(`themeBoxEl=${selectedCluster.name}`);
                        console.log(bEl);
                        if(bEl !== null) {
                            // bEl.classList.toggle('boxViewSelected');
                            bEl.querySelector('.themebox-table-cont').classList.toggle('boxViewSelected');
                        } else {
                            console.log('Box Element NOT Found!');
                        }
                        if (clusterInfoShown) {
                            popupClusterInfoHide();
                        }
                        if (selected.length > 0) {
                            selectedCluster = clusterGroup.getObjectByName(selected[selected.length - 1]);
                            parameters.a = selectedCluster.userData.Name;
                        } else {
                            selectedCluster = undefined;
                        }
                    }
                }
            } else if (selectedCluster.userData.type === "TubeGeometry") {
                if (selectedTube != selectedCluster) {
                    if (selectedTube != 0) {
                        selectedTube.material.color.setHex(0x4286f4);
                    }
                    selectedTube = selectedCluster;
                    selectedTube.material.color.setHex(0xcc99ff);
                    popupDisplay();
                } else if (selectedTube === selectedCluster) {
                    closeBtn();
                }
            }
        }
    }
} //Mouseclick end
/* 
 * CTRL + CLICK  
 * Multi-Connected Theme Select 
 */
function connectedMultiSelect() {
    let connectedArray = buildSelectionArray();
    connectedArray
    .forEach((c) => {
        if(!selected.includes(c)) {
            let connectedTheme = clusterGroup.getObjectByName(c);
            prevColor.push(connectedTheme.material.color.getHexString());
            connectedTheme.material.color.setHex(0xbf4b0d);
            GLOBAL_STATE.selected = [...GLOBAL_STATE.selected, connectedTheme.name];
            hudDataBind(connectedTheme.name);
            let bEl = document.getElementById(`themeBoxEl=${c}`);
            console.log(bEl);
            if(bEl !== null) {
                // bEl.classList.toggle('boxViewSelected');
                bEl.querySelector('.themebox-table-cont').classList.toggle('boxViewSelected');
            } else {
                console.log('Box Element NOT Found!');
            }
        } else {
            console.log(`${c} already selected! Moving on...`);
        }
    });
    showSelected();
    document.getElementById(selectedCluster.name).scrollIntoView();
    console.log('Connected Multi-Select Finished');
}
function buildSelectionArray() {
    let firstLayerArray = selectedCluster.userData.connectedTubes
    .filter(t=>clusterGroup.getObjectByName(t).visible)
    .map(t=>t.replace('Tube=','')
    .split(','))
    .flat()
    .filter(c=>c!=selectedCluster.name);
    let selectionArray = [...new Set(firstLayerArray)];
    console.log(selectionArray);
    for(let i=0;i<selectionArray.length;i++) {
        console.log(`INDEX: ${i}`);
        console.log(selectionArray.length);
        let tmpArray = clusterGroup.getObjectByName(selectionArray[i])
        .userData.connectedTubes
        .filter(t=>clusterGroup.getObjectByName(t).visible)
        .map(t=>t.replace('Tube=','')
        .split(','))
        .flat()
        .filter(c=>c!=selectedCluster.name);      
        selectionArray = [...new Set([...selectionArray,...tmpArray])];
    };
    return selectionArray;
}
function connectedMultiDeSelect() {
    selectedCluster.userData.connectedTubes
    .filter(t=>clusterGroup.getObjectByName(t).visible)
    .map(t=>t.replace('Tube=','')
    .split(','))
    .flat()
    .filter(c=>c!=selectedCluster.name)
    .forEach((c) => {
        if(selected.includes(c)) {
            document.getElementById(c).click();
        } else {
            console.log(`${c} Is NOT in selected! Moving on...`);
        }
    });
    showSelected();
    document.getElementById(selectedCluster.name).scrollIntoView();
    console.log('Connected Multi-Select Finished');
}
var themeBoxClicked = false;
var themeBoxMouseEvent = null;
function themeBoxClickSelect(event) {
    console.log(event)
    themeBoxMouseEvent = event;
    let selectedBox = event.target.closest('div.theme-boxes');
    console.log(selectedBox);
    if(selectedBox !== null) {
        let selectedId = selectedBox.id.replace('themeBoxEl=','');
        document.getElementById(selectedId).click();
        if(document.getElementById(selectedId).classList.contains('hudSelectedCluster')) {
            document.getElementById(selectedId).scrollIntoView();
        }
    } else {
        console.log("ERROR: BoxView select returned NULL.  Notify support if this happens often, degrades functionality and/or hinders usage...");
    }
}
ctrlFunctions.resetThemeSelectionByName = function(themeName) {
    const selected = GLOBAL_STATE.selected;
    const index = selected.indexOf(themeName);
    const clusterGroup = GLOBAL_STATE.clusterGroup;
    if(GLOBAL_STATE.prevColor.length > 0) {
        const prevColor = GLOBAL_STATE.prevColor;
    }
    removeSelectedThemeByName(themeName);
    const originalColor = prevColor.splice(index, 1);
    const maybeCluster = clusterGroup.getObjectByName(themeName);
    if (maybeCluster && originalColor.length > 0) {
        maybeCluster.material.color.setHex('0x' + originalColor);
    }
    if (selected.length > 0) {
        GLOBAL_STATE.selectedCluster = clusterGroup.getObjectByName(selected[selected.length - 1]);
    }
}
viewFunctions.maybeHideSelectedClustersPanel = function() {
    if (selected.length < 1) {
        const selectedClusters = document.getElementById('selectedClusters');
        const deselectAllNode = document.getElementById('deselectAll');
        selectedClusters.style.visibility = 'hidden';
        deselectAllNode.style.visibility = 'hidden';
    }
}
viewFunctions.onSelectedThemeItemClick = function() {
    const themeName = this.id; // this is DOM Node binding;
    ctrlFunctions.resetThemeSelectionByName(themeName);
    hudDataBind([themeName]);
    this.parentNode.removeChild(this);
    popupClusterInfoHide();
    viewFunctions.maybeHideSelectedClustersPanel();
    if (mainCfg.showVisDataPanel) {
        mainCfg.components.visDataPanel.toggleThemeByName(themeName);
    }
}
viewFunctions.showSelectedThemes = function(selected) {

    selectedClusters.innerHTML = "";
    selectedClusters.style.visibility = 'visible';
    const deselectAllNode = document.getElementById('deselectAll');
    deselectAllNode.style.visibility = 'visible';
    for (let i = 0; i < selected.length; i++) {
        let container = document.createElement("BUTTON");
        //Check Theme Name Registry
        let theName = (themeNameSearch(selected[i]) == selected[i]) ? selected[i] : themeNameSearch(selected[i]);
        container.innerHTML = '&emsp;' + theName + '&emsp;X&emsp;';
        container.id = selected[i];
        selectedClusters.appendChild(container);
        container.onclick = viewFunctions.onSelectedThemeItemClick;
    }
};

//Onscreen Selected Start
function showSelected() {
    if (GLOBAL_STATE.selected.length === 0) {
        return;
    }
    if (!mainCfg.showNewSelectedThemesPanel) {
    viewFunctions.showSelectedThemes(GLOBAL_STATE.selected);
  }


} //Onscreen end
function hudDataBind(ThemeName) {
    if (mainCfg.showVisDataPanel) {
        mainCfg.components.visDataPanel.toggleTheme(data.findIndex(t => t.Name === ThemeName));
    } else {
        let hudDiv = document.getElementById(ThemeName);
        hudDiv.scrollIntoView();
        hudDiv.classList.toggle('hudSelectedCluster');
    }
}

ctrlFunctions.onHudDataBinClickSelected = function(c) {
    let index = selected.indexOf(c.name);
    c.material.color.setHex('0x' + prevColor[index]);
    prevColor.splice(index, 1);
    removeSelectedThemeByName(c.name);
}
ctrlFunctions.onHudDataBindClickUnselected = function(c) {
    if (!selected.includes(c.name)) {
    GLOBAL_STATE.selected = [...GLOBAL_STATE.selected, c.name];
    }
    prevColor.push(c.material.color.getHexString());
    c.material.color.setHex(0xbf4b0d);
    selectedCluster = c;
}

function removeSelectedClusterFromUIByName(clusterName) {
    const selectedClusters = document.getElementById('selectedClusters');
    const maybeItem = selectedClusters.querySelector(`[id="${clusterName}"]`);
    if (maybeItem) {
        selectedClusters.removeChild(maybeItem);
    }
    if (GLOBAL_STATE.selected.length < 1) {
        document.getElementById('deselectAll').style.visibility = 'hidden';
    }
}

viewFunctions.onHudDataBindClick = function(c) {
    let element = document.getElementById(c.name);
    let boxEl = document.getElementById(`themeBoxEl=${c.name}`);
    if (element.classList.contains('hudSelectedCluster')) {
        element.classList.toggle('hudSelectedCluster');
        // boxEl.classList.toggle('boxViewSelected');
        boxEl.querySelector('.themebox-table-cont').classList.toggle('boxViewSelected');
        ctrlFunctions.onHudDataBinClickSelected(c);
        removeSelectedClusterFromUIByName(c.name);
        popupClusterInfoHide();
        //Added to reset parameters.a when using the HUD for deselecting
        if( selected.length > 0 ) {
            parameters.a = clusterGroup.getObjectByName( selected[selected.length - 1] ).userData.Name;
        } else if( selected.length === 0 ) {
            parameters.a = 'Select A Cluster';
        }
    } else {
        element.classList.toggle('hudSelectedCluster');
        // boxEl.classList.toggle('boxViewSelected');
        boxEl.querySelector('.themebox-table-cont').classList.toggle('boxViewSelected');
        ctrlFunctions.onHudDataBindClickUnselected(c);
        showSelected();
        GLOBAL_STATE.hudClicked = true;
        popupClusterInfo();
        GLOBAL_STATE.hudClicked = false;
    }
}

epmViewFunctions.onHudDataBindClick = function(c) {
    const isDeselection = GLOBAL_STATE.selected.includes(c.name);
    if (isDeselection && mainCfg.components.themePopup.visible) {
        popupClusterInfoHide();
    }

    if (isDeselection) {
        ctrlFunctions.onHudDataBinClickSelected(c);
        removeSelectedClusterFromUIByName(c.name);
    } else {
        ctrlFunctions.onHudDataBindClickUnselected(c);
        showSelected();
        GLOBAL_STATE.hudClicked = true;
        popupClusterInfo();
        GLOBAL_STATE.hudClicked = false;
    }
}

function hudDataBindClick(c) {
    if (mainCfg.showVisDataPanel) {
        console.log('LOG 1')
        epmViewFunctions.onHudDataBindClick(c);
    } else {
        console.log('LOG 2')
        viewFunctions.onHudDataBindClick(c);
    }
}

//Onscreen Removal Start
animate();
//Animate Start
function animate() {
    if (mainCfg.limitFrameRateUsingSetTimeout) {
        clearTimeout(animate.timer);
        cancelAnimationFrame(animate.frame);
        animate.timer = setTimeout(()=>{
            cancelAnimationFrame(animate.frame);
            animate.frame = requestAnimationFrame(animate)
        });
    } else {
        requestAnimationFrame(animate)
    }
    controls.update();
    let cL = cubeGroup.children.length;
    for( let i = 0; i < cL; i++ ) {
        cubeGroup.children[i].lookAt( camera1.position );
        clusterGroup.children[i].lookAt( camera1.position );
        spriteTextGroup.children[i].lookAt( camera1.position );
        // boxViewGroup.children[i].lookAt( camera1.position );
        cubeGroup.children[i].up = camera1.up;
        clusterGroup.children[i].up = camera1.up;
        spriteTextGroup.children[i].up = camera1.up;
        boxViewGroup.children[i].up = camera1.up;
    }
    // for( let i=0; i<cL; i++ ) {
    //     if( cameraViewCheck( spriteTextGroup.children[i], camera1 ) === true && clusterGroup.children[i].visible === true && spriteTextGroup.visible === true ) {
    //         if(autoAnnotationActive == true) {
    //             spriteTextGroup.children[i].element.style.display = 'flex';
    //             spriteTextGroup.children[i].element.style.flexDirection = 'column';
    //             spriteTextGroup.children[i].element.style.alignItems = 'center';
    //         } else {
    //             spriteTextGroup.children[i].element.style.display = 'initial';
    //         }
    //     } else {
    //         spriteTextGroup.children[i].element.style.display = 'none';

    //     }
    // }
    if( glowGroup.children.length > 0 ) {
        glowGroup.children.forEach( item => {
            item.lookAt( camera1.position );
        });
    }
    if( filterHaloGroup.children.length > 0 ) {
        filterHaloGroup.children.forEach( item => {
            item.lookAt( camera1.position );
            if( cameraViewCheck( item, camera1 ) === true && item.visible === true && filterHaloGroup.visible === true ) {
                item.element.style.display = 'initial';
            } else {
                item.element.style.display = 'none';
            }
        });
    }
    if( typeof dragObjects !== 'undefined' ) {
        if( dragObjects.children.length > 0 ) {
            dragObjects.children.forEach( (item ) => {
                item.lookAt( camera1.position );
                item.up = camera1.up;
            });
        }
    }
    renderer.render(scene, camera1);
    labelCssRenderer.render( labelScene, camera1 );
    cssrenderer.render(cssScene, camera1);
    if( typeof controls3 !== 'undefined' ) {
        controls3.update();
        for (let i = 0; i < meshConceptGroup.children.length; i++) {
            meshConceptGroup.children[i].quaternion.copy(camera3.quaternion);
        }
        renderer3.render(scene3, camera3);
    }
    if( rotateScene === true ) {
        checkRotation();
    }
    if( camRotateX === true ){
        // camRotate();
        rotateCameraY();
    }
}
//Animate End
// function render() {
//     renderer.render( scene, camera1 );
//     labelCssRenderer.render( labelScene, camera1 );
//     renderer3.render( scene3, camera3 );
// }
//Name Registry Search
function themeNameSearch(name, filter) {
    let index = -1;
    if (filter) {
        for (let i = 0; i < optimalItemsSize(); i++) {
            if (name == ThemeNameRegistry[i].Alias) {
                index = i;
                break;
            }
        }
        if (i != -1) {
            return ThemeNameRegistry[index].Name;
        } else {
            log('No matches in the ThemeNameRegistry --> Returning ' + name);
            return name;
        }
    } else {
        for (let i = 0; i < optimalItemsSize(); i++) {
            if (name == ThemeNameRegistry[i].Name) {
                index = i;
                break;
            }
        }
        if (index != -1) {
            return ThemeNameRegistry[index].Alias;
        } else {
            log('No matches in the ThemeNameRegistry --> Returning ' + name);
            return name;
        }
    }
}

/*FunctionJS*/

//Spheres Start
function sphereConstruct() {
    //Radius max: 18 min:5
    let radius = 18;
    let segment = 32;
    let cluster;
    for (let z = 0; z < optimalItemsSize(); z++) {
        //Create Cluster
        let bufferGeometry = new THREE.SphereBufferGeometry(radius, segment, segment);
        let material = new THREE.MeshPhongMaterial({
            color: 0x000015,
            wireframe: false,
            // flatShading: true,
            // side: THREE.Backside,
            shininess: 50
        });
        // let material = new THREE.MeshLambertMaterial({
        //     color: 0x000015,
        //     // wireframe: false,
        //     // // flatShading: true,
        //     // // side: THREE.Backside,
        //     // shininess: 50
        //     reflectivity: 1
        // });
        cluster = new THREE.Mesh(bufferGeometry, material);
        sortedByScore.push({ name: c[z].Name, score: c[z].Score });
        c[z].key = z;
        cluster.name = c[z].Name;
        cluster.userData = Object.assign({},c[z]);
        cluster.userData.indexKey = z;
        cluster.userData.customGroup = '';
        cluster.userData.connectedTubes = [];
        //added when switched to BufferGeometry for raycast clickselect type
        cluster.userData.type = 'SphereGeometry';
        cluster.userData.radius = radius;
        cluster.position.set(c[z].X * PM, c[z].Y * PM, c[z].Z * PM);
        console.log(c[z]);
        console.log(cluster.userData);
        // if(typeof autoAnnotationDataPreLoad !== 'undefined' && autoAnnotationDataPreLoad !== null) {
        //     cluster.userData.Name = getInitialFullAnnotationName(cluster.name) ?? cluster.userData.Name;
        // }
        // console.log(c[z]);
        // console.log(cluster.userData);
        clusterGroup.add(cluster);

        hudPopulation(z);
        inSphereText(z, radius);
        aboveSphereText(z, cluster.name, radius);
        themeBoxCreate(z);

        //Decrement Radius Value Until min: 5
        if (radius > 5) {
            radius = radius - 0.5;
        }
    }

    scene.add(clusterGroup, cubeGroup);
    labelScene.add( spriteTextGroup );
    labelScene.add(boxViewGroup);

    clusterGroup.children.forEach(function(sphere, index) {

        while(collisionCheck(sphere) == true) {

            let sphPos = adjustPosition(sphere.position);

            log('adjusting relational objects...');

            cubeGroup.children[index].position.x = sphPos.x;
            cubeGroup.children[index].position.y = sphPos.y;
            cubeGroup.children[index].position.z = sphPos.z;

            spriteTextGroup.children[index].position.x = sphPos.x;
            spriteTextGroup.children[index].position.y = sphPos.y + (sphere.userData.radius * 1.5) + 4;
            spriteTextGroup.children[index].position.z = sphPos.z;

            if( typeof boxViewGroup.children[index] !== 'undefined' ) {
                boxViewGroup.children[index].position.set(sphPos.x, sphPos.y, sphPos.z);
            }

            log('sphere reposition complete...');

        }

    });

    log('all objects repositioned successfully!');

    drawLines();

} //Spheres End
/* Get Full Annotation First Line for Initial Theme Name */
function getInitialFullAnnotationName(name) {
    let initalAnnotationThemeName = null;
    for(let i=0;i<autoAnnotationDataPreLoad.length;i++) {
        if(autoAnnotationDataPreLoad[i].name === name && autoAnnotationDataPreLoad[i].annotations.length > 0) {
            initalAnnotationThemeName = autoAnnotationDataPreLoad[i].annotations[0].split("\t")[0];
            break;
        }
    }
    return initalAnnotationThemeName;
}

function themeBoxCreate(themeIndex) {
    // let text = clusterGroup.children[themeIndex].name;
    // let textConcepts = c[themeIndex].Concepts.slice(0,4).join(", ");
    // textConcepts += (c[themeIndex].Concepts.length > 4) ? '...' : '';
    // let textEntities = c[themeIndex].Genes.slice(0,4).join(", ");
    // textEntities += (c[themeIndex].Genes.length > 4) ? '...' : '';
    // let themeBoxElement = document.createElement('DIV');
    // let innrHtml = `<div class="themebox-name">${text}</div>
    //     <div class="themebox-concepts">Concepts: ${textConcepts}</div>
    //     <div class="themebox-entities">Entities: ${textEntities}</div>`;
    // Modifying to grab spritext label innercontent for autoannotations in box theme
    let text = (autoAnnotationActive === true) ? spriteTextGroup.children[themeIndex].element.innerHTML : clusterGroup.children[themeIndex].name;
    console.log('Themebox Create: ' + themeIndex);
    // let text = spriteTextGroup.children[themeIndex].element.innerHTML;
    let textConcepts = c[themeIndex].Concepts.slice(0,5);
    let textEntities = c[themeIndex].Genes.slice(0,5);
    let themeBoxElement = document.createElement('DIV');
    let innrHtml = `<div class="themebox-name">${text}</div>`;
    innrHtml += '<div class="themebox-table-cont" style="background-color: #243248;"><table class="themebox-table"><thead><tr><th class="themebox-con">Concepts</th><th class="themebox-ent">Entities</th></tr></thead><tbody>';
    for(let i=0;i<5;i++) {
        innrHtml += `<tr><td class="themebox-con">${textConcepts[i] ?? ''}</td><td class="themebox-ent">${(/^\(e\)/.test(textEntities[i])) ? '<span class="hidden">(e)</span>' + textEntities[i].replace('(e)','') : textEntities[i] ?? ''}</td></tr>`;
    }
    innrHtml += '</tbody></table></div>';
    themeBoxElement.innerHTML = innrHtml;
    // themeBoxElement.innerText = text;
    themeBoxElement.style.color = '#e3ff15';
    themeBoxElement.style.fontSize = '1em';
    themeBoxElement.style.fontWeight = '';
    // themeBoxElement.style.fontFamily = '"Roboto", sans-serif';
    themeBoxElement.style.fontFamily = 'sans-serif';
    themeBoxElement.id = `themeBoxEl=${text}`;
    themeBoxElement.classList.add('theme-boxes')
    themeBoxElement.style.zIndex = '-3';
    // themeBoxElement.style.background = parameters.d5;
    themeBoxElement.style.display = "none";
    // themeBoxElement.addEventListener('click', themeBoxClickSelect);
    themeBoxElement.addEventListener( 'mouseenter', function( event ) {
        themeBoxIntersects.push( event.target );

    });
    themeBoxElement.addEventListener( 'mouseleave', function( event ) {
        themeBoxIntersects = [];
    });
    // let themeBoxObject = new THREE.CSS3DObject( themeBoxElement );
    let themeBoxObject = new THREE.CSS3DSprite( themeBoxElement );
    themeBoxObject.name = `themeBoxEl=${text}`;
    themeBoxObject.userData.indexKey =themeIndex;
    let tempX = clusterGroup.children[themeIndex].position.x;
    let tempY = clusterGroup.children[themeIndex].position.y;
    let tempZ = clusterGroup.children[themeIndex].position.z;
    themeBoxObject.position.set( tempX, tempY, tempZ );
    themeBoxObject.scale.set(0.175,0.175,0.175);
    boxViewGroup.add( themeBoxObject );
}

function collisionCheck(object) {

    for(let i=0;i<optimalItemsSize();i++) {

        if(object !== clusterGroup.children[i]) {

            if(detectSphereCollision(object, clusterGroup.children[i]) == true) {

                return true;

            }

        }

    }

}

function detectSphereCollision(object1, object2){
  object1.geometry.computeBoundingSphere();
  object2.geometry.computeBoundingSphere();
  object1.updateMatrixWorld();
  object2.updateMatrixWorld();

  var sphere1 = object1.geometry.boundingSphere.clone();
  sphere1.radius += sphere1.radius;
  sphere1.applyMatrix4(object1.matrixWorld);

  var sphere2 = object2.geometry.boundingSphere.clone();
  sphere2.radius += sphere2.radius;
  sphere2.applyMatrix4(object2.matrixWorld);

    if(sphere1.intersectsSphere(sphere2) == true) {

        log('collision detected...');
        log(object1.name + ' ~ ' + object2.name);

        return true;

    }

}

function adjustPosition(coordinates) {

    log('recalculating coordinates...');

    coordinates.x = (coordinates.x * 0.1) + coordinates.x;
    coordinates.y = (coordinates.y * 0.1) + coordinates.y;
    coordinates.z = (coordinates.z * 0.1) + coordinates.z;

    return coordinates;

}

//Heads Up Display Cluster Info List15in
function hudPopulation(i) {
    if (mainCfg.showVisDataPanel) {
        return;
    }
    let container = document.createElement("DIV");
    let attribute = document.createAttribute("id");
    const cluster = c[i];
    attribute.value = cluster.Name;
    let attribute2 = document.createAttribute("onclick");
    attribute2.value = "hudDataBindClick(clusterGroup.getObjectByName(this.id))";
    let attribute3 = document.createAttribute('class');
    attribute3.value = 'hudCluster';
    let a = (bioExplorerProjectType === true) ? `<p class="clusterName">${clusterGroup.children[i].userData.Name}</p>
        <hr>
        <p class="theme-index">Index:<span class="indent-value theme-index-value">${i + 1}</span></p>
        <p class="score-conceptTotal">
            <span class="score">Enrichment Score:<span class="indent-value">${cluster.Score}</span><br><span class="geneTotal">Term Total:<span class="indent-value">${cluster.Gene_Total}</span></span>
        </p>` : `<p class="clusterName">${clusterGroup.children[i].userData.Name}</p>
        <hr>
        <p class="theme-index">Index:<span class="indent-value theme-index-value">${i + 1}</span></p>
        <p class="score-conceptTotal">
            <span class="score">Enrichment Score:<span class="indent-value">${cluster.Score}</span><br><span class="geneTotal">Entity Total:<span class="indent-value">${cluster.Gene_Total}</span></span>
        </p>`;
    if('NEScore' in cluster && 'PValue' in cluster) {
        a += `<p class="score-pVal-nes">
            <span class="score">PValue:<span class="indent-value p-val">${cluster.PValue}</span><br><span class="nes-score">NEScore:<span class="indent-value">${cluster.NEScore}</span></span>
        </p>`;
    } else {
        console.log('NEScore AND PValue NOT FOUND');
    }
    if (cluster['FisherPValue'] != null) {
        a += `<p class="fisherpvalue" style="margin:0;">
            FisherPValue:<span class="indent-value fisherpvalue-val">${cluster.FisherPValue}</span>
        </p>`;
    } else {
        console.log('No FisherPValue');
    }
    let b = '';
    for (let y = 0; y < cluster.Concepts.length; y++) {
        y === cluster.Concepts.length - 1 ? b += cluster.Concepts[y] : b += cluster.Concepts[y] + ', ';
    }
    container.setAttributeNode(attribute);
    container.setAttributeNode(attribute2);
    container.setAttributeNode(attribute3);
    container.innerHTML += `${a}<p class="conceptTotal">Concept Total:<span class="indent-value theme-concept-total">${cluster.Concept_Total}</span></p><div class="concepts">${b}</div>`;
    document.getElementById('theme-view').appendChild(container);
} //Hud End
//Hud GeneView Start
function generateGeneViewList() {

    let tempArr = [];
    let tempScore = 0;
    let sortedGeneList = [];

    for (let i = 0; i < data2.length; i++) {
        geneListObj[data2[i].Gene_ID] = {};
        geneListObj[data2[i].Gene_ID].Score = {};
        geneListObj[data2[i].Gene_ID].Concepts = [];
        geneListObj[data2[i].Gene_ID].Themes = [];
        tempScore = 0;
        for (let z = 0; z < optimalItemsSize(); z++) {
            if (Object.keys(data[z].Genes).includes(data2[i].Gene_ID)) {
                geneListObj[data2[i].Gene_ID].Themes.push(data[z].Name);
                tempScore = (Number(data[z].Genes[data2[i].Gene_ID].Score) > tempScore) ? Number(data[z].Genes[data2[i].Gene_ID].Score) : tempScore;
                tempArr = Object.keys(data[z].Genes[data2[i].Gene_ID].Concepts);
                for (let y = 0; y < tempArr.length; y++) {
                    if (!(geneListObj[data2[i].Gene_ID].Concepts.includes(tempArr[y]))) {
                        geneListObj[data2[i].Gene_ID].Concepts.push(tempArr[y]);
                    }
                }
            }
        }
        geneListObj[data2[i].Gene_ID].Score = tempScore;
    }
    tempArr = Object.keys(geneListObj);

    for (let i = 0; i < tempArr.length; i++) {
        sortedGeneList.push({ 'name': tempArr[i], 'theme_total': geneListObj[tempArr[i]].Themes.length });
    }
    sortedGeneList.sort(function(a, b) {
        return b.theme_total - a.theme_total;
    });

    for (let i = 0; i < sortedGeneList.length; i++) {
        if (geneListObj[sortedGeneList[i].name].Score == 0) {
            break;
        } else if (geneListObj[sortedGeneList[i].name].Concepts.length < 3) {
            continue;
        } else {
            if (!mainCfg.showVisDataPanel) {
                let container = document.createElement('DIV');
                let attribute = document.createAttribute('id');
                attribute.value = sortedGeneList[i].name;
                let attribute2 = document.createAttribute('onclick');
                attribute2.value = 'geneViewThemeHighlight(this.id)';
                let attribute3 = document.createAttribute('class');
                attribute3.value = 'gene-container';
                container.setAttributeNode(attribute);
                container.setAttributeNode(attribute2);
                container.setAttributeNode(attribute3);
                let a = `<p class="clusterName">${attribute.value}</p><hr>`;
                let b = `<p class="score-conceptTotal"><span class="geneTotal">Theme Total: ${geneListObj[attribute.value].Themes.length}</span><br>
                    <span class="score">Highest Score: ${geneListObj[attribute.value].Score.toFixed(2)}</span></p>
                    <p class="conceptTotal">Concept Total: ${geneListObj[attribute.value].Concepts.length}</p>`;
                let c = `<p id="${attribute.value}-gene-list" class="gene-theme-list">${geneListObj[attribute.value].Concepts.join(', ')}</p>`;
                container.innerHTML = a + b + c;
                document.getElementById('gene-view').appendChild(container);
            }
        }
    }
}

ctrlFunctions.cleanGlowGroup = function(gene) {
    if (glowGroup.children.length) {
        for (let i = 0; i < glowGroup.children.length; i) {
            glowGroup.remove(glowGroup.children[i]);
        }
    }
    selectedGene = gene;
}
ctrlFunctions.createGlowGroups = function(gene) {
    if( appliedFilter === 'None' || parameters.j2 === false ) { 
        let tempThemeList = geneListObj[gene].Themes;
        for (let i = 0; i < tempThemeList.length; i++) {
            if (clusterGroup.getObjectByName(tempThemeList[i]).visible == true 
            || boxViewGroup.getObjectByName(`themeBoxEl=${tempThemeList[i]}`).element.style.display !== 'none') {
                let tempIndex = clusterGroup.getObjectByName(tempThemeList[i]).userData.indexKey;
                let tempScore = data[tempIndex].Genes[gene].Score;
                let tempColor = '#ffffff';
                if(tempScore<10) {
                    tempColor = '#0034f1';
                } else if(tempScore>=10 && tempScore<100) {
                    tempColor = '#00ff00';
                } else if(tempScore>=100 && tempScore<1000) {
                    tempColor = '#ffa500';
                } else if(tempScore>=1000) {
                    tempColor = '#ff0000';
                }
                let tempPosition = new THREE.Vector3();
                tempPosition = clusterGroup.getObjectByName(tempThemeList[i]).position;
                let rad = clusterGroup.getObjectByName(tempThemeList[i]).userData.radius;

                let element = document.createElement('div');
                element.className = 'glow-element';
                element.id = 'Glow' + clusterGroup.getObjectByName(tempThemeList[i]).name;
                element.style.height = rad * 2 + 15 + 'px';
                element.style.width = rad * 2 + 15 + 'px';
                element.style.backgroundImage = 'radial-gradient(circle, rgba(0,0,0,0.1) 50%, ' + tempColor + ')';

                let div = new THREE.CSS3DObject(element);
                div.name = 'Glow' + tempThemeList[i];
                div.position.set(tempPosition.x,tempPosition.y,tempPosition.z);
                glowGroup.add(div);
            }
        }
    } else {
        console.log('Glow didnt pass checks')
    }
}
viewFunctions.geneViewThemeHighlight = function(gene) {
    let tempElements = document.getElementById('gene-view');
    if (document.getElementById(gene).classList.contains('hudSelectedGene')) {
        document.getElementById(gene).classList.remove('hudSelectedGene');
        document.getElementById(gene + '-gene-list').classList.remove('gene-theme-list-selected');
        document.getElementById(gene + '-gene-list').scrollTop = 0;
        ctrlFunctions.cleanGlowGroup(null);
    } else {
        if (glowGroup.children.length > 0) {
            for (let i = 0; i < tempElements.children.length; i++) {
                tempElements.children[i].classList.remove('hudSelectedGene');
                tempElements.children[i].children[3].classList.remove('gene-theme-list-selected');
                tempElements.children[i].children[3].scrollTop = 0;
            }
        }
        ctrlFunctions.cleanGlowGroup(gene);
        document.getElementById(gene).classList.add('hudSelectedGene');
        document.getElementById(gene + '-gene-list').classList.add('gene-theme-list-selected');
        ctrlFunctions.createGlowGroups(gene);
    }
}
epmViewFunctions.geneViewThemeHighlight = function(gene) {
    const selectedGenesNames = mainCfg.components.visDataPanel._model.entitiesData.entitiesList.filter(({selected})=>selected).map(({name})=>name);
    if (!selectedGenesNames.includes(gene)) {
        ctrlFunctions.cleanGlowGroup(null);
    } else {
        ctrlFunctions.cleanGlowGroup(gene);
        ctrlFunctions.createGlowGroups(gene);
    }
}
function geneViewThemeHighlight(gene) {
    if (mainCfg.showVisDataPanel) {
        epmViewFunctions.geneViewThemeHighlight(gene);
    } else {
        viewFunctions.geneViewThemeHighlight(gene);
    }
}

function hudListViewToggle(event) {
    if (mainCfg.showVisDataPanel) {
        return;
    }
    if (event.target.classList.contains('active-view-btn') == false) {
        let selectedView = event.target.id.replace(/-button/g, '');
        if(selectedView == 'gene-view') {
            document.getElementById('gene-view-key').style.display = 'inline-block';
        } else {
            document.getElementById('gene-view-key').style.display = 'none';
        }
        let viewBtnArray = [].slice.call(document.getElementsByClassName('view-btn'));
        let hudViewsArray = [].slice.call(document.getElementsByClassName('hud-views'));
        viewBtnArray.forEach(function(item) {
            if(item==event.target) {
                item.classList.add('active-view-btn');
            } else {
                item.classList.remove('active-view-btn');
            }
        });
        if(selectedView === "box-view" && boxViewActive === false) {
            initBoxView();
        } else if(selectedView === "theme-view" && boxViewActive === true) {
            boxViewActive = false;
            deactivateBoxView();
        }
        selectedView = (selectedView === "box-view") ? "theme-view" : selectedView;
        hudViewsArray.forEach(function(item) {
            if(item.id==selectedView) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });

    } else {
        log('already active');
    }
}
function deactivateBoxView() {
    boxViewGroup.children.forEach((box) => { box.element.style.display = "none"; });
    checkVisibility();
    parameterToPercentage();
}
function initBoxView() {
    boxViewActive = true;
    clusterGroup.children.forEach((cluster, index) => {
        if(cluster.geometry.type === "SphereBufferGeometry" && cluster.visible === true) {
            boxViewGroup.children[index].element.style.display = "initial";
            cluster.visible = false;
        }
    });
    cubeGroup.children.forEach((cube) => { cube.visible = false; });
}
//Hud GeneView End
function createHiPPICanvas(width, height) {
    const ratio = window.devicePixelRatio;
    const canvas = document.createElement("canvas");
    canvas.width = width * ratio;
    canvas.height = height * ratio;
    canvas.style.width = width + "px";
    canvas.style.height = height + "px";
    canvas.getContext("2d").scale(ratio, ratio);
    return canvas;
}
function inSphereText(i, radius) {
    let t = 0;
    c[i].Concepts.length > 8 ? t = sphereTextSort(c[i].Concepts.slice(0, 8)) : t = sphereTextSort(c[i].Concepts.slice(0, c[i].Concepts.length));
    let canvas = document.createElement("CANVAS");
    //canvas.fillStyle = 'white';
    canvas.width = 512;
    canvas.height = 512;

    let ctx = canvas.getContext("2d");
    let textColor = '#ffffff';
    let weight = '900';
    let fontFamily = 'sans-serif';
    let fontSize = 60;
    let fontVariant = 'normal';
    ctx.fillStyle = textColor;
    ctx.font = `${fontVariant} ${weight} ${fontSize}px ${fontFamily}`;
    let txtMeasurements = [];
    for (let x = 0; x < t.length; x++) {
        txtMeasurements.push(ctx.measureText(t[x]).width);
    }
    let max = txtMeasurements.reduce(function(a, b) {
        return Math.max(a, b);
    });
    let multiplier = 1;
    for (let w = 0; w < t.length; w++) {
        let txtWidth = ctx.measureText(t[w]).width;
        ctx.textAlign = 'center';
        ctx.fillText(t[w], 256, ((canvas.height / 2) / t.length + fontSize / 4) * multiplier + fontSize * 1.5, canvas.width - 1);
        multiplier++;
    }
    let texture = new THREE.Texture(canvas);
    texture.needsUpdate = true;
    texture.anisotropy = renderer.capabilities.getMaxAnisotropy();

    let geometry = new THREE.BoxBufferGeometry(radius * 2, radius * 2, radius * 2);

    let cubeMaterials = [
        new THREE.MeshBasicMaterial({ transparent: true, opacity: 0 }),
        new THREE.MeshBasicMaterial({ transparent: true, opacity: 0 }),
        new THREE.MeshBasicMaterial({ transparent: true, opacity: 0 }),
        new THREE.MeshBasicMaterial({ transparent: true, opacity: 0 }),
        new THREE.MeshBasicMaterial({ transparent: true, map: texture }),
        new THREE.MeshBasicMaterial({ transparent: true, opacity: 0 })
    ];
    let cube = new THREE.Mesh(geometry, cubeMaterials);
    cube.position.set(clusterGroup.children[i].position.x,
                        clusterGroup.children[i].position.y,
                        clusterGroup.children[i].position.z);
    cube.userData.clusterRef = c[i].Name;
    cube.name = c[i].Name;
    cube.userData.concepts = t;
    cubeGroup.add(cube);
} //Text Sphere End

/*Sorting the list that will be displayed on the spheres*/
/*Sorting strings by character length (longest toward the middle)*/
function sphereTextSort(arr) {
  let sortedList = [];
  arr.sort(function(a, b) {
    return a.length - b.length;
  });
  let listLength = arr.length;
  for (let i = listLength - 1; i >= 0; i--) {
    if (i % 2 == 0) {
      sortedList.push(arr[i]);
    } else {
      sortedList.unshift(arr[i]);
    }
  }
  return sortedList;
}
//SpriteText Above Spheres
function aboveSphereText(i, text, radius) {
    let labelElement = document.createElement('DIV');
    labelElement.innerText = clusterGroup.children[i].userData.Name;
    labelElement.style.color = '#ffffff';
    // labelElement.style.color = '#ffff00';
    labelElement.style.fontSize = '24px';
    labelElement.style.fontWeight = '';
    // labelElement.style.fontFamily = 'Arial, Helvetica, sans-serif';
    // labelElement.style.fontFamily = '"Roboto", sans-serif';
    labelElement.style.fontFamily = 'sans-serif';
    labelElement.id = `spriteText=${text}`;
    labelElement.classList.add('theme-labels')
    labelElement.style.zIndex = '-3';
    labelElement.style.background = parameters.d5;
    labelElement.addEventListener( 'mouseenter', function( event ) {
        labelIntersects.push( event.target );
        // dragObjects.push
    });
    labelElement.addEventListener( 'mouseleave', function( event ) {
        labelIntersects = [];
    });
    let labelObject = new THREE.CSS3DObject( labelElement );
    labelObject.name = `spriteText=${text}`;
    labelObject.userData.indexKey = i;
    let tempX = clusterGroup.children[i].position.x;
    let tempY = clusterGroup.children[i].position.y + (radius * 1.5) + 8;
    let tempZ = clusterGroup.children[i].position.z;
    labelObject.position.set( tempX, tempY, tempZ );
    labelObject.scale.set(0.175,0.175,0.175);
    spriteTextGroup.add( labelObject );
    if( annotations !== null && annotations.hasOwnProperty( text ) && annotations[ text ].hasOwnProperty('Annotations') ) {
        console.log('CREATING ANNOTATION SELECTION --> ' + text);
        createAnnotationSelection( labelObject );
        appendFlagToText( labelObject );
    } else {
        console.log('FAILED CREATING ANNOTATION SELECTION --> ' + text);
    }
} //SpriteText End

function drawLines() {
    console.log('Called: drawLines')
    let z, i, y;
    let curve;
    let bufferGeometry;
    let tubeMaterial;
    let tube;
    let tubeRadius;
    let index, score1, score2;
    let len = optimalItemsSize();
    for (z = 0; z < len; z++) {
        for (i = z + 1; i < len; i++) {
            sharedGenes = arrayIntersect(c[z].Genes, clusterGroup.children[i].userData.Genes);
            tubeRadius = (bioExplorerProjectType) ? 1.5 : sharedGenes.length / 10 * 2;
            combinedConcepts = c[z].Concepts.concat(clusterGroup.children[i].userData.Concepts);
            if (tubeRadius < 0.5) {
                tubeRadius = 0.5;
            } else if (tubeRadius > 3) {
                tubeRadius = 3;
            }
            curve = new THREE.CatmullRomCurve3([
                new THREE.Vector3(
                    clusterGroup.children[z].position.x,
                    clusterGroup.children[z].position.y,
                    clusterGroup.children[z].position.z),
                new THREE.Vector3(
                    clusterGroup.children[i].position.x,
                    clusterGroup.children[i].position.y,
                    clusterGroup.children[i].position.z)
            ]);
            bufferGeometry = new THREE.TubeBufferGeometry(
                curve, //path
                1, //segments
                tubeRadius, //radius - Min 0.2 Max 2.0 size depends on shared genes
                4, //radiusSegments
                false //open end
            );
            tubeMaterial = new THREE.MeshLambertMaterial({
                color: 0x4286f4,
                wireframe: false
            });
            // tubeMaterial = new THREE.MeshBasicMaterial({
            //     color: 0x4286f4,
            //     wireframe: false
            // });       
            tube = new THREE.Mesh(bufferGeometry, tubeMaterial);
            tube.userData.lineLength = Math.sqrt(
                Math.pow(clusterGroup.children[z].position.x - clusterGroup.children[i].position.x, 2) +
                Math.pow(clusterGroup.children[z].position.y - clusterGroup.children[i].position.y, 2) +
                Math.pow(clusterGroup.children[z].position.z - clusterGroup.children[i].position.z, 2)
            );
            tube.userData.endpoints = [data[z].Name, clusterGroup.children[i].name];
            tube.userData.sharedGenes = sharedGenes;
            tube.userData.combinedConcepts = combinedConcepts;
            //added when switched to BufferGeometry for raycast clickselect type
            tube.userData.type = 'TubeGeometry';
            tube.name = 'Tube=' + tube.userData.endpoints[0] + ',' + tube.userData.endpoints[1];
            //add tube reference to Theme connectedTubes array
            clusterGroup.children[z].userData.connectedTubes.push(tube.name);
            clusterGroup.children[i].userData.connectedTubes.push(tube.name);

            //Set userData gene scores for shared Genes
            tube.userData.sharedGeneScores = [];
            for (y = 0; y < sharedGenes.length; y++) {
                index = c[z].Genes.indexOf(sharedGenes[y]);
                score1 = c[z].Gene_Scores[index];
                index = clusterGroup.children[i].userData.Genes.indexOf(sharedGenes[y]);
                score2 = clusterGroup.children[i].userData.Gene_Scores[index];
                tube.userData.sharedGeneScores.push({ score1, score2 });
            }
            clusterGroup.add(tube);
        }
    }
}

function distanceCheck() {
    let len = clusterGroup.children.length;
    for (let i = optimalItemsSize(); i < len; i++) {
        if(boxViewActive) {
            if (boxViewGroup.getObjectByName('themeBoxEl=' + clusterGroup.children[i].userData.endpoints[0]).element.style.display === 'initial' &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).userData.filterHidden === undefined &&
            boxViewGroup.getObjectByName('themeBoxEl=' + clusterGroup.children[i].userData.endpoints[1]).element.style.display === 'initial' &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).userData.filterHidden === undefined) {
                if (clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && clusterGroup.children[i].userData.sharedGenes.length > 2 ||
                clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && bioExplorerProjectType) {
                    // Chad requested opening the filter threshold
                // if (clusterGroup.children[i].userData.lineLength < parameters.c2 * PM/2 && clusterGroup.children[i].userData.sharedGenes.length > 2 ||
                // clusterGroup.children[i].userData.lineLength < parameters.c2 * PM/2 && bioExplorerProjectType) {
                    clusterGroup.children[i].visible = true;
                    
                } else {
                    clusterGroup.children[i].visible = false;
                }
            } else {
                clusterGroup.children[i].visible = false;
            }
        } else {
            if (clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).visible === true &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).userData.filterHidden === undefined &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).visible === true &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).userData.filterHidden === undefined) {
                if (clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && clusterGroup.children[i].userData.sharedGenes.length > 2 ||
                clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && bioExplorerProjectType) {
                    // Chad requested opening the filter threshold
                // if (clusterGroup.children[i].userData.lineLength < parameters.c2 * PM/2 && clusterGroup.children[i].userData.sharedGenes.length > 2 ||
                // clusterGroup.children[i].userData.lineLength < parameters.c2 * PM/2 && bioExplorerProjectType) {
                    clusterGroup.children[i].visible = true;
                    
                } else {
                    clusterGroup.children[i].visible = false;
                }
            } else {
                clusterGroup.children[i].visible = false;
            }
        }
    }
    for(let i=0;i<visLockedTubes.length;i++) {
        clusterGroup.getObjectByName(visLockedTubes[i]).visible = true;
    }
    for(let i=0;i<groupHiddenTubes.length;i++) {
        clusterGroup.getObjectByName(groupHiddenTubes[i]).visible = false;
    }
}

function checkVisibility() {
    for (let i = optimalItemsSize(); i < clusterGroup.children.length; i++) {
        if(boxViewActive) {
            if (boxViewGroup.getObjectByName('themeBoxEl=' + clusterGroup.children[i].userData.endpoints[0]).element.style.display === 'initial' &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).userData.filterHidden === undefined &&
            boxViewGroup.getObjectByName('themeBoxEl=' + clusterGroup.children[i].userData.endpoints[1]).element.style.display === 'initial' &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).userData.filterHidden === undefined) {
                if (clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && clusterGroup.children[i].userData.sharedGenes.length > 2 ||
                clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && bioExplorerProjectType) {
                    if((clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).userData.filterHidden === undefined) 
                        && (clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).userData.filterHidden === undefined)) {
                        clusterGroup.children[i].visible = true;
                    } else {
                        clusterGroup.children[i].visible = false;
                    }
                } else {
                    clusterGroup.children[i].visible = false;
                }
            } else {
                clusterGroup.children[i].visible = false;
            }
        } else {
            if (clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).visible === true &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).userData.filterHidden === undefined &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).visible === true &&
            clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).userData.filterHidden === undefined) {
                if (clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && clusterGroup.children[i].userData.sharedGenes.length > 2 ||
                clusterGroup.children[i].userData.lineLength < parameters.c2 * (PM/4) && bioExplorerProjectType) {
                    if((clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[0]).userData.filterHidden === undefined) && (clusterGroup.getObjectByName(clusterGroup.children[i].userData.endpoints[1]).userData.filterHidden === undefined)) {
                        clusterGroup.children[i].visible = true;
                    } else {
                        clusterGroup.children[i].visible = false;
                    }
                } else {
                    clusterGroup.children[i].visible = false;
                }
            } else {
                clusterGroup.children[i].visible = false;
            }
        }
    }
    //Check for groupSelected show/hide group
    if (groupSelected) {
        if (parameters.e2 === false) {
            for (let i = 0; i < selectedGroup.length; i++) {
                if(clusterGroup.getObjectByName(selectedGroup[i]) !== undefined) {
                    if( boxViewActive ) {
                        boxViewGroup.children[i].element.style.display = 'none';
                        spriteTextGroup.children[index].visible = false;
                        spriteTextGroup.children[index].element.style.display = 'none';
                        if( typeof dragLines != 'undefined' && typeof dragLines.children[index] != 'undefined' ) {
                            dragLines.children[index].visible = false;
                        }
                    } else {
                        let index = clusterGroup.getObjectByName(selectedGroup[i]).userData.indexKey;
                        clusterGroup.children[index].visible = false;
                        spriteTextGroup.children[index].visible = false;
                        spriteTextGroup.children[index].element.style.display = 'none';
                        cubeGroup.children[index].visible = false;
                        if( typeof dragLines != 'undefined' && typeof dragLines.children[index] != 'undefined' ) {
                            dragLines.children[index].visible = false;
                        }
                    }
                }
            }
            distanceCheck();
        } else if (parameters.e3 === false) {
            let groupKeys = [];
            for (let i = 0; i < selectedGroup.length; i++) {
                if(clusterGroup.getObjectByName(selectedGroup[i]) !== undefined){
                    groupKeys.push(clusterGroup.getObjectByName(selectedGroup[i]).userData.indexKey);
                }
            }
            for (let i = 0; i < optimalItemsSize(); i++) {
                if (groupKeys.indexOf(i) < 0) {
                    if(boxViewActive) {
                        boxViewGroup.children[i].element.style.display = 'none';
                        spriteTextGroup.children[i].visible = false;
                        spriteTextGroup.children[i].element.style.display = 'none';
                        if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                            dragLines.children[i].visible = false;
                        }
                    } else { 
                        clusterGroup.children[i].visible = false;
                        spriteTextGroup.children[i].visible = false;
                        spriteTextGroup.children[i].element.style.display = 'none';
                        cubeGroup.children[i].visible = false;
                        if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                            dragLines.children[i].visible = false;
                        }
                    }
                } else {
                    continue;
                }
            }
            distanceCheck();
        }
    }
    visThemeUpdateStat();
} //checkVisibility End

function parameterToPercentage(num, filter) {
    if (filter == undefined) {
        //if argument is not provided set to score(the original default)
        filter = 'score';
    }
    if (num == undefined) {
        //if argument is not provided set to parameters.c3 (the original default)
        num = 100 - parameters.c3;
    }
    if (filter == 'distance') {
        let percentageAmount = Math.round((num / 100) * tubeLengthsSorted.length);
        if (percentageAmount != 0) {
            percentageAmount -= 1;
        }
        return tubeLengthsSorted[percentageAmount];
    } else if (filter == 'score') {
        let percentageAmount = Math.round((num / 100) * sortedByScore.length);
        if (percentageAmount != 0) {
            percentageAmount -= 1;
        }
        clusterScoreAmount = sortedByScore[percentageAmount].score;
        checkScoreVisibility();
    }
    filterPvalAndNescore();
    visThemeUpdateStat()
}

function checkScoreVisibility() {
    let i, z;
    for (i = 0; i < optimalItemsSize(); i++) {
        if (clusterGroup.children[i].userData.Score < clusterScoreAmount) {
            if( boxViewActive ) {
                boxViewGroup.children[i].element.style.display = "none";
                spriteTextGroup.children[i].visible = false;
                spriteTextGroup.children[i].element.style.display = 'none';
                if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                    dragLines.children[i].visible = false;
                }
            } else {
                clusterGroup.children[i].visible = false;
                cubeGroup.children[i].visible = false;
                spriteTextGroup.children[i].visible = false;
                spriteTextGroup.children[i].element.style.display = 'none';
                if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                    dragLines.children[i].visible = false;
                }
            }
            for (z = optimalItemsSize(); z < clusterGroup.children.length; z++) {
                if (clusterGroup.children[z].userData.endpoints[0] === clusterGroup.children[i].name ||
                    clusterGroup.children[z].userData.endpoints[1] === clusterGroup.children[i].name) {
                        clusterGroup.children[z].visible = false;
                }
            }
        } else if (clusterGroup.children[i].userData.Score >= clusterScoreAmount) {
            if(clusterGroup.children[i].userData.filterHidden === undefined) {
                if( boxViewActive ) {
                    boxViewGroup.children[i].element.style.display = "initial";
                    if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                        dragLines.children[i].visible = true;
                    }
                    spriteTextGroup.children[i].visible = true;
                    if(autoAnnotationActive == true) {
                        spriteTextGroup.children[i].element.style.display = 'flex';
                        spriteTextGroup.children[i].element.style.flexDirection = 'column';
                        spriteTextGroup.children[i].element.style.alignItems = 'center';
                    } else {
                        spriteTextGroup.children[i].element.style.display = 'initial';
                    }
                } else {
                    clusterGroup.children[i].visible = true;
                    cubeGroup.children[i].visible = true;
                    spriteTextGroup.children[i].visible = true;
                    if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                        dragLines.children[i].visible = true;
                    }
                    if(autoAnnotationActive == true) {
                        spriteTextGroup.children[i].element.style.display = 'flex';
                        spriteTextGroup.children[i].element.style.flexDirection = 'column';
                        spriteTextGroup.children[i].element.style.alignItems = 'center';
                    } else {
                        spriteTextGroup.children[i].element.style.display = 'initial';
                    }
                }
                for (z = optimalItemsSize(); z < clusterGroup.children.length; z++) {
                    if (clusterGroup.children[z].userData.endpoints[0] === clusterGroup.children[i].name
                        || clusterGroup.children[z].userData.endpoints[1] === clusterGroup.children[i].name) {
                        // clusterGroup.children[i].name;
                        // console.log(clusterGroup.children[i].name);
                        clusterGroup.children[z].visible = true;
                        // console.log(clusterGroup.children[z].name);
                    }
                }
                checkVisibility();
            }
        }
    }
    if(visLockedTubes.length>0 || groupHiddenTubes.length > 0) {
        showVisLockedItems();
    }


    if(selectedGene!=null) {
        // we need to redraw selected gene area, because some balls don't exists after filtering
        if (mainCfg.showVisDataPanel) {
            epmViewFunctions.geneViewThemeHighlight(selectedGene);
            epmViewFunctions.geneViewThemeHighlight(selectedGene);
        } else {
            let tempGene = selectedGene;
            document.getElementById(tempGene).click();
            document.getElementById(tempGene).click();
        }
    }

}

function arrayIntersect(a, b) {
    var setA = new Set(a);
    var setB = new Set(b);
    var intersection = new Set([...setA].filter(x => setB.has(x)));
    return Array.from(intersection);
}

function popupDisplay() {
    if (mainCfg.useNewSharedEntitiesPopup) {
      if (clusterInfoShown === true) {
        mainCfg.components.themePopup.hide(true);
      }
      mainCfg.components.sharedEntitiesPopup.show({
        selectedCluster,
        clusterGroup,
        eventFilterController,
        hideCb: closeBtn,
        position: {x: event.clientX, y: event.clientY}
      });
      if(appliedFilter !== 'None') {
        filterTubePopup(filterArray);
      }
      return;
    }
    if (clusterInfoShown === true) {
        popupClusterInfoHide();
    }
    let x = event.clientX;
    let y = event.clientY;
    let d = document.getElementById('genesPopupDisplay');
    let listLen = selectedCluster.userData.sharedGenes.length;
    popUpShown = true;
    d.style.visibility = 'visible';
    let list = listLen;
    list += (bioExplorerProjectType === true) ? ' Shared Terms</div></div><div id="popUpGeneBody" class="allow-user-select"><table>' : ' Shared Entities</div></div><div id="popUpGeneBody" class="allow-user-select"><table>';
    if (listLen <= 0) {
        list += (bioExplorerProjectType === true) ? 'No Shared Terms' : 'No Shared Entities';
    } else {
        let sharedGenesTemp = [];
        for(let i=0;i<selectedCluster.userData.sharedGenes.length;i++) {
            let tempObj = {
                'Gene': selectedCluster.userData.sharedGenes[i],
                'Score1': selectedCluster.userData.sharedGeneScores[i].score1,
                'Score2': selectedCluster.userData.sharedGeneScores[i].score2,
                'LowScore': (Number(selectedCluster.userData.sharedGeneScores[i].score1)<Number(selectedCluster.userData.sharedGeneScores[i].score2))?selectedCluster.userData.sharedGeneScores[i].score1:selectedCluster.userData.sharedGeneScores[i].score2
            }
            sharedGenesTemp.push(tempObj);
        }
        log(sharedGenesTemp);
        sharedGenesTemp.sort(function(a,b) {
            return b.LowScore - a.LowScore;
        });
        list += '<tr class="shared-thead"><th>Entity</th><th colspan="2">Enrichment Scores</th></tr>';
        for (let i = 0; i < listLen; i++) {
            list += "<tr class='popup-table-row'><th onclick='eventFilterController(this.textContent)'>";
            list += (/^\(e\)/.test(sharedGenesTemp[i].Gene)) ? '<span class="hidden">(e)</span>' + sharedGenesTemp[i].Gene.replace('(e)','') : sharedGenesTemp[i].Gene;
            list += "</th><td class='geneScore'>" +
                parseFloat(sharedGenesTemp[i].Score1).toFixed(1) +
                "</td><td class='geneScore exp-val'>" +
                parseFloat(sharedGenesTemp[i].Score2).toFixed(1) +
                "</td></tr>";
        }
    }
    list += '</table></div>'
    if (x + 250 > window.innerWidth) {
        x = x - 300;
    }
    if (y + 200 > window.innerHeight) {
        y = y - 100;
    }
    d.style.left = x + 'px';
    d.style.top = y + 'px';
    let tubeEndPoints = clusterGroup.getObjectByName(selectedCluster.userData.endpoints[0]).userData.Name.split('<br>')[0] + '</p><hr><p class="info-panel-txt info-panel-disp-name">' + clusterGroup.getObjectByName(selectedCluster.userData.endpoints[1]).userData.Name.split('<br>')[0];
    let header = '<div class="gene-info-header gene-popup-drag-handle">' +
        '<div id="popUpGeneHeader" class="allow-user-select"><p class="info-panel-txt info-panel-disp-name">' + tubeEndPoints + '</p></div>' +
        '<input type="button" value="X" id="closeBtn" class="info-panel-clsbtn" onclick="closeBtn()" />';
    d.innerHTML = header + '<div class="popUpGeneSubHeader">' + list;

    if(appliedFilter !== 'None') {

        filterTubePopup(filterArray);

    }
}
function popupClusterInfo() {
    if (mainCfg.useNewThemePopup) {
      if (selectedCluster === currentInfo) {
        mainCfg.components.themePopup.hide();
      } else {
        if (popUpShown === true) {
          closeBtn();
        }
        currentInfo = selectedCluster;
        clusterInfoShown = true;
        parameters.a = selectedCluster.userData.Name;
        let x;
        let y;
        if (hudClicked === false) {
          x = event.clientX;
          y = event.clientY;
        } else {
          x = HUDNode().scrollWidth + 100;
          y = window.innerHeight / 2 - 115;
        }
        if (x + 300 > window.innerWidth) {
          x = window.innerWidth - 300;
        }
        if (y + 700 > window.innerHeight) {
          y = window.innerHeight - 700;
        }
        mainCfg.components.themePopup.show({
          selectedCluster,
          imageSearch,
          openConceptMap,
          popupClusterInfoHide,
          position: {x, y}
        });
        if(appliedFilter !== 'None') {

          filterPopup(filterArray);

        }
      }
      return;
    }
    if (selectedCluster === currentInfo) {
        popupClusterInfoHide();
        if(themeBoxClicked === true) {
            themeBoxClicked = false;
            themeBoxMouseEvent = null;
        }
    } else {
        if (popUpShown === true) {
            closeBtn();
        }
        let geneList = '';
        if(sessionStorage.Expression_List!="undefined" && sessionStorage.Entity_List!="undefined") {
            geneList += (bioExplorerProjectType === true) ? `<thead><tr class="t-head"><th>Terms ( <span id="entity-table-head-count">${selectedCluster.userData.Genes.length}</span> )</th><th>Enrichment</th><th>Exp</th></tr></thead><tbody>` : `<thead><tr class="t-head"><th>Entities ( <span id="entity-table-head-count">${selectedCluster.userData.Genes.length}</span> )</th><th>Enrichment</th><th>Exp</th></tr></thead><tbody>`;
            let expList = ( sessionStorage.Expression_List.includes( '\n' )) ? sessionStorage.Expression_List.trim().replaceAll( '\r','' ).split('\n') : sessionStorage.Expression_List.trim().replaceAll( '\n','' ).split('\r');
            let entList = ( sessionStorage.Entity_List.includes( '\n' )) ? sessionStorage.Entity_List.trim().replaceAll( '\r','' ).split('\n') : sessionStorage.Entity_List.trim().replaceAll( '\n','' ).split('\r');
            console.log(expList);
            for(let i=0;i<entList.length;i++) {
                entList[i] = entList[i].toLowerCase();
            }
            let themeGeneExp = [];
            for(let i=0;i<selectedCluster.userData.Genes.length;i++) {
                let searchString = (selectedCluster.userData.Genes[i].includes('(')) ? selectedCluster.userData.Genes[i].substring( 3 ) : selectedCluster.userData.Genes[i];
                let expIndex = entList.indexOf(searchString);
                themeGeneExp.push(expList[expIndex]);
            }
            for (let i = 0; i < selectedCluster.userData.Genes.length; i++) {
                    let tempExpValue = (typeof themeGeneExp[i] === 'undefined') ? '<small>undefined</small>' : themeGeneExp[i].substring(0,8);
                    geneList += '<tr class="entity-table-row popup-table-row"><th onclick="eventFilterController(this.textContent)">';
                    geneList += (/^\(e\)/.test(selectedCluster.userData.Genes[i])) ? '<span class="hidden">(e)</span>' + selectedCluster.userData.Genes[i].replace('(e)','') : selectedCluster.userData.Genes[i];
                    geneList += '</th><td class="geneScore">' +
                        parseFloat(selectedCluster.userData.Gene_Scores[i]).toFixed(2) + '</td><td class="geneScore exp-val">' +
                        tempExpValue + '</td></tr>';
            }
        } else {
            geneList += (bioExplorerProjectType === true) ? `<thead><tr class="t-head"><th>Terms ( <span id="entity-table-head-count">${selectedCluster.userData.Genes.length}</span> )</th><th>Enrichment</th></tr></thead><tbody>` : `<thead><tr class="t-head"><th>Entities ( <span id="entity-table-head-count">${selectedCluster.userData.Genes.length}</span> )</th><th>Enrichment</th></tr></thead><tbody>`;
            for (let i = 0; i < selectedCluster.userData.Genes.length; i++) {
                geneList += '<tr class="entity-table-row popup-table-row"><th onclick="eventFilterController(this.textContent)">';
                geneList += (/^\(e\)/.test(selectedCluster.userData.Genes[i])) ? '<span class="hidden">(e)</span>' + selectedCluster.userData.Genes[i].replace('(e)','') : selectedCluster.userData.Genes[i];
                geneList += '</th><td class="geneScore">' +
                    parseFloat(selectedCluster.userData.Gene_Scores[i]).toFixed(2) + '</td></tr>';
            }
        }

        currentInfo = selectedCluster;
        let x;
        let y;
        if (hudClicked === false || themeBoxClicked === true) {
            x = (themeBoxMouseEvent === null) ? event.clientX : themeBoxMouseEvent.clientX;
            y = (themeBoxMouseEvent === null) ? event.clientY : themeBoxMouseEvent.clientY;
            themeBoxClicked = false;
            themeBoxMouseEvent = null;
        } else {
            if (mainCfg.showVisDataPanel) {
                let bcr = mainCfg.components.visDataPanel.getBoundingClientRect();
                x = bcr.width + 100;
            } else {
                x = HUDNode().scrollWidth + 100;
            }
            y = window.innerHeight / 2 - 115;
        }
        const conceptTable = buildConceptTable();
        let d = document.getElementById('clusterInfo');
        clusterInfoShown = true;
        d.style.visibility = 'visible';
        d.innerHTML = '<div class="cluster-info-header popup-drag-handle">' +
            '<div id="popUpHeader" class="allow-user-select"><p class="info-panel-txt info-panel-disp-name">' + selectedCluster.userData.Name + '</p></div>' +
            '<input type="button" value="X" id="clusterInfoHideBtn" class="info-panel-clsbtn" onclick="popupClusterInfoHide()" />' +
            '</div>' + 
            '<div class="popup-btns">' +
            '<input type="button" value="Image Search" class="imageSearchBtn clusterInfoBtns" onclick="imageSearch(selectedCluster.userData.Concepts)" />' +
            '</div>' +
            '<div class="popup-btns">' +
            '<input type="button" value="Concept Map" class="conceptMapOpenBtn clusterInfoBtns" onclick="openConceptMap(selectedCluster.name)" />' +
            '<input type="button" value="Entity Score Bar Graph" class="entityScoreGraphBtn clusterInfoBtns" onclick="graphEntScore(selectedCluster.userData.Genes.slice(0,10),selectedCluster.userData.Gene_Scores.slice(0,10))" />' +
            '</div>' +
            '<div id="popUpBody" class="allow-user-select"><table id="concept-table" class="popup-table">' + conceptTable + '</table><table id="entity-table" class="popup-table">' + geneList + '</tbody></table></div>';
        parameters.a = selectedCluster.userData.Name;
        if (x + 250 > window.innerWidth) {
            x = x - 300;
        }
        if (y + 230 > window.innerHeight) {
            y = y - 150;
        }
        d.style.left = x + 25 + 'px';
        d.style.top = y - 25 + 'px';

        if(appliedFilter !== 'None') {

            filterPopup(filterArray);

        }
        sortTableByEnrichment(document.getElementById('concept-table'));
        hideZeroScoreRows(document.getElementById('concept-table'));
    }
}
function hideZeroScoreRows(table) {
    let scoreEls = table.querySelectorAll('.concept-score');
    scoreEls.forEach((el) => {
        if(el.innerText === '0') {
            el.parentElement.classList.add('hidden');
        }
    })
}
function calculatePopupConceptEnrichmentScore(index,conceptName) {
    let themeIndexObject = data[index];
    let retVal = Object.values(themeIndexObject.Genes).filter(g=>g.Concepts[conceptName])
    if(retVal.length > 0) {
        return Number(Object.values(themeIndexObject.Genes).filter(g=>g.Concepts[conceptName]).map(o=>o.Concepts[conceptName]).reduce((r,s)=> Number(r) + Number(s))).toFixed(2).replace(/[.,]00$/, "");
    } else {
        return 0;
    }
}
function sortTableByEnrichment(table) {
	let i,x,y,swap,swaping = true;
	while(swaping) {
		swaping = false;
		let rows = table.querySelector('TBODY').rows;
		for(i=0;i<(rows.length-1);i++) {
			swap = false;
			x = Number(rows[i].getElementsByTagName('TD')[0].innerHTML);
			y = Number(rows[i+1].getElementsByTagName('TD')[0].innerHTML);
			if(x<y) {
				swap = true;
				break;
			}
		}
		if(swap) {
			rows[i].parentNode.insertBefore(rows[i+1],rows[i]);
			swaping = true;
		}
	}
}
function buildConceptTable() {
    const index = selectedCluster.userData.indexKey
    const themeConcepts = data[ index ].Concepts;
    const conceptsAndScores = Object.entries( themeConcepts ).map( array => {
        let score = calculatePopupConceptEnrichmentScore( selectedCluster.userData.indexKey,array[0]);
        return `<tr class="concept-table-row popup-table-row"><th onclick="clusterConceptsToGenes('${array[ 0 ]}')">${array[ 0 ]}</th><td class="concept-score">${score}</td></tr>`;
    } );
    const tableHead = `<thead><tr class="t-head"><th>Concepts ( <span id="concept-table-head-count">${conceptsAndScores.length}</span> )</th><th>Enrichment</th></tr></thead>`;
    return `${tableHead}<tbody>${conceptsAndScores.join('')}</tbody>`;
}
viewFunctions.onPupupClusterInfoHide = function() {
    if (document.getElementById('geneViewSelection')) {
        closeGeneViewSelection();
    }
    let d = document.getElementById('clusterInfo');
    d.style.visibility = 'hidden';
    d.style.height = '100%';
}
epmViewFunctions.onPupupClusterInfoHide = function() {
    mainCfg.components.themePopup.hide();
}
ctrlFunctions.onHideClusterInfoPupup = function() {
    GLOBAL_STATE.clusterInfoShown = false;
    GLOBAL_STATE.currentInfo = 0;
    GLOBAL_STATE.hudClicked = false;
}
function popupClusterInfoHide() {
    if (mainCfg.useNewThemePopup) {
        epmViewFunctions.onPupupClusterInfoHide();
    } else {
        viewFunctions.onPupupClusterInfoHide();
    }
    ctrlFunctions.onHideClusterInfoPupup();
}
viewFunctions.onCloseGenePopup = function() {
    if (document.getElementById('geneViewSelection')) {
        closeGeneViewSelection();
    }
    let d = document.getElementById('genesPopupDisplay');
    d.style.visibility = 'hidden';
    d.style.height = '100%';
}
epmViewFunctions.onCloseGenePopup = function() {
    mainCfg.components.sharedEntitiesPopup.hide();
}
ctrlFunctions.onCloseGenePopup = function() {
    GLOBAL_STATE.selectedTube.material.color.setHex(0x4286f4);
    GLOBAL_STATE.selectedTube = 0;
    GLOBAL_STATE.popUpShown = false;
}
function closeBtn() {
    if (mainCfg.useNewSharedEntitiesPopup) {
      epmViewFunctions.onCloseGenePopup();
    } else {
      viewFunctions.onCloseGenePopup();
    }
    ctrlFunctions.onCloseGenePopup();
}
ctrlFunctions.onThemeRename = function (oldName, newName, massChange = false) {
  let matchFound = false;
  //Cdev- rename fix
  //Why are we resetting this when the user just inputted the newName?
  parameters.a = newName;
  //Cdev- rename fix
  //userData.Alias was unnecessary eg. AlwaysOriginalName-> selectedCluster.name & DisplayNameThatChanges-> selectedCluster.userData.Name
  selectedCluster.userData.Alias = newName;
  //Cdev- rename fix
  //Added this back so WU clusterInfo panel updates correct name
  selectedCluster.userData.Name = newName;
  let index = selected.indexOf(oldName);
  //Cdev- rename fix
  //selected[index] = newName;
  for (let i = 0; i < optimalItemsSize(); i++) {
    if (ThemeNameRegistry[i].Alias === oldName || (!ThemeNameRegistry[i].Alias && ThemeNameRegistry[i].Name === oldName)) {
      matchFound = true;
      ThemeNameRegistry[i].Alias = newName;
      //spriteTextGroup.children[i].material.map.text = newName;
      if( appliedFilter === 'None' ){
          if(typeof autoAnnotationActive != 'undefined' && autoAnnotationActive == true) {
              spriteTextGroup.children[i].element.innerHTML = '<span>' + newName.replaceAll('<br>', '</span>\n<span>') + '</span>';
              spriteTextGroup.children[i].element.style.cursor = 'default';
          } else {
              spriteTextGroup.children[i].element.innerText = newName.replaceAll('<br>', '\n');
            }
      } else {
        let spanCountElement = spriteTextGroup.getObjectByName(`spriteText=${selectedCluster.name}`).element.getElementsByClassName('label-concept-count')[0];
        if(typeof autoAnnotationActive != 'undefined' && autoAnnotationActive == true) {
            spriteTextGroup.children[i].element.innerHTML = '<span>' + newName.replaceAll('<br>', '</span>\n<span>') + '</span>';
            spriteTextGroup.children[i].element.style.cursor = 'default';
        } else {
            spriteTextGroup.children[i].element.innerText = newName.replaceAll('<br>', '\n');
        }
        spriteTextGroup.getObjectByName(`spriteText=${selectedCluster.name}`).element.appendChild( spanCountElement );

      }
      if(massChange == false) {
          alertInformation('New name set');
          if( autoAnnotationActive ) {
            ThemeNameRegistry[i].isCustomName = true;
            spriteTextGroup.children[i].userData.isCustomName = true;
          }
      }
      /* Update New Box-View Element */
      let spriteContent = spriteTextGroup.getObjectByName(`spriteText=${selectedCluster.name}`).element.innerHTML;
    //   console.log(spriteContent);
        // spriteContent = spriteContent.replaceAll(/(\()\s([0-9])/gm, '$1$2').replaceAll("</span>\n<span>", '</span><hr style="margin:0;"><span>');
        
        /* ThemeName Scores Space Fix */
        spriteContent = spriteContent.replaceAll(/(\()\s([0-9])/gm, '$1$2');
        
        let themeBoxNameEl = boxViewGroup.getObjectByName(`themeBoxEl=${selectedCluster.name}`).element.firstElementChild;
        // if(spriteContent.includes('</span><hr style="margin:0;"><span>')) {
        //     themeBoxNameEl.style.fontSize = '70%';
        // } else {
        //     themeBoxNameEl.style.fontSize = '';
        // }
        themeBoxNameEl.innerHTML = spriteContent;
        // let spArr = Array.from(themeBoxNameEl.querySelectorAll('SPAN'));
        // if(spArr.length > 1) {
        //     console.log(spArr);
        //     spArr.forEach(function(sp) {
        //         sp.style.fontSize = '';
        //         let w1 = themeBoxNameEl.offsetWidth;
        //         let w2 = sp.offsetWidth + 15;
        //         if(w2 >= w1) {
        //             let fs = 100;
        //             while((sp.offsetWidth + 15) >= w1) {
        //                 fs--; 
        //                 sp.style.fontSize = `${fs}%`;
        //             }
        //         }
        //     });
        // }

      break;
    } else if (i >= ThemeNameRegistry.length) {
      log('NAME REGISTRY ERROR!!!');
      log('error occurred at index --> ' + i);
      log('parameters.a --> ' + newName + ' selectedCluster.name --> ' + oldName);
      log(ThemeNameRegistry);
      alertInformation('An error in the program prevented the new name from setting.  Please try again.  If the problem continues contact a CompBio Team Member.');
    }
  }
  return matchFound;
};

viewFunctions.updateThemeNameInSelectedThemesBottomPanel = function (oldName, newName) {
    //Cdev- rename fix
    oldName = ( oldName === selectedCluster ) ? oldName : selectedCluster.name;
    let hudNode = document.getElementById(oldName);
    let hudNodeChild = document.getElementById(oldName).children[0];
    if (!hudNodeChild) {
        hudNode.innerHTML = newName;
        //Cdev- rename fix
        //hudNode.id = newName;
    } else {
        hudNodeChild.innerHTML = newName;
        //Cdev-rename fix
        //hudNodeChild.id = newName;
    }
    const node = document.getElementById('selectedClusters');
    const children = [].slice.call( node.children );
    const childrenInnerText = children.map( child => {
        return child.innerText.replace( /&emsp;X&emsp;/g, '' ).replace( /&emsp;/g, '' );
    });
    const index = childrenInnerText.indexOf( oldName );
    if( index >= 0 ) {
       const index = selected.indexOf(oldName);
       node.childNodes[index].innerHTML = newName + "&emsp;X&emsp;";        
    } else {
        return;
    }
};

viewFunctions.updateThemeNameInHUD = function () {
  if (clusterInfoShown === true) {
    let element = document.getElementById('clusterInfo');
    let positionX = element.style.left;
    let positionY = element.style.top;
    popupClusterInfoHide();
    popupClusterInfo();
    element.style.left = positionX;
    element.style.top = positionY;
  }
};

epmViewFunctions.updateThemeNameInHUD = function (oldName, newName) {
  eventBus.publish("update:theme:name", [oldName, newName]);
};

function guiSaveClusterName(massChange = false) {
    if (parameters.a.length > 0 && parameters.a != 'Select a Theme' && selected.length > 0) {
        // if (parameters.a === selectedCluster.userData.Alias || !selectedCluster.userData.Alias && parameters.a === selectedCluster.userData.Name) {
        if(parameters.a === selectedCluster.userData.Alias && massChange === false || parameters.a === selectedCluster.userData.Name && massChange === false) {
            alertInformation('To set a new name, a new name must be entered first');
        } else {
            const oldName = selectedCluster.userData.Alias || selectedCluster.userData.Name;
            const newName = parameters.a;
            // Fix 01092022 Broken Multi-line labels
            // const newName = parameters.a.replace(/[^a-z0-9_-]/gi,'');
            console.log(oldName + ' , ' + newName)
            ctrlFunctions.onThemeRename(oldName, newName, massChange);
            if (!mainCfg.showNewRelationDisplay) {
                viewFunctions.updateThemeNameInSelectedThemesBottomPanel(oldName, newName);
            }
            if (mainCfg.showVisDataPanel) {
                epmViewFunctions.updateThemeNameInHUD(oldName, newName);
            } else {
                viewFunctions.updateThemeNameInHUD(oldName, newName);
            }
        }
    } else {
        alertInformation('Select a Theme');
    }
}

function guiSaveColor() {
    if (selected.length > 0) {
        let index = selected.indexOf(selectedCluster.name);
        if (index >= 0) {
            prevColor[index] = parameters.d.replace('#', '');
            for (let i = 0; i < ThemeNameRegistry.length; i++) {
                if (ThemeNameRegistry[i].Name == selectedCluster.name) {
                    ThemeNameRegistry[i].Color = prevColor[index];
                    let bEl = document.getElementById(`themeBoxEl=${selectedCluster.name}`);
                    if(bEl !== null) {
                        console.log('Saving Box Color...');
                        console.log(bEl);
                        console.log(ThemeNameRegistry[i].Color);
                        // bEl.classList.toggle('boxViewSelected');
                        bEl.style.border = `10px solid #${ThemeNameRegistry[i].Color}`;
                        console.log(bEl);
                    } else {
                        console.log('Box Element NOT Found!');
                    }
                    break;
                }
            }
            alertInformation('Color applied');
        }
    } else {
        alertInformation('You must select something first...');
    }
}

function guiSaveGroup() {
    // if(groupSelected === true) {
    //     if(document.getElementById('groupList').querySelector('.listItemSelected') !== null) {
    //         document.getElementById('groupList').querySelector('.listItemSelected').click();
    //     }
    // }
    if ( selected.length > 0 ) {
        if ( groupSelectionCheck() === true ) {
            if ( parameters.e.length > 0 && parameters.e !== 'New Group' ) {
                //Check saved groups to prevent duplicate groups
                //Trim white space avoid error on groupSelect
                const groupName = parameters.e.trim();
                if ( !customGroups[ groupName ] ) {
                    customGroups[ groupName ] = [];
                    for ( let i = 0; i < selected.length; i++ ) {
                        const originalColor = prevColor[ i ];
                        const cluster = clusterGroup.getObjectByName( selected[ i ] );
                        cluster.userData.customGroup = groupName;
                        cluster.material.color.setHex( originalColor );
                        customGroups[ groupName ].push( selected[ i ] );
                        let bEl = document.getElementById(`themeBoxEl=${selected[i]}`);
                        if(bEl !== null) {
                            console.log('Adding Custom Group Data Attribute...');
                            bEl.setAttribute('data-custgroup', groupName);
                        }
                    }
                    //Need to pass 'prevColor' values to 'prevGroupColor' before clearing the array
                    prevGroupColor = prevColor;
                    GLOBAL_STATE.prevColor = [];
                    for ( let i = 0; i < ThemeNameRegistry.length; i++ ) {
                        for ( let z = 0; z < selected.length; z++ ) {
                            if ( selected[ z ] == ThemeNameRegistry[ i ].Name ) {
                                ThemeNameRegistry[ i ].Group = groupName;
                                log( ThemeNameRegistry[ i ].Name + ' set to group ' + groupName );
                                break;
                            }
                        }
                    }
                    //We do not want to save to profileData yet - Error when saving groups on the 'Default' profile
                    //Just save to global variable customGroups until profile has been saved
                    //If this step is necessary in your system then add conditional logic checking for profile === 'Default'
                    // savedProfileData[ profileParameters.profileName ].customGroups = JSON.parse( JSON.stringify( customGroups ) );
                    alertInformation( 'New group ' + groupName + ' set' );
                    deselectAll();
                    groupDisplay();
                    groupSelect( groupName );
                    parameters.e = 'New Group';
                    parameters.a = 'Select a Theme';
                    document.getElementById( 'deselectAll' ).style.visibility = 'hidden';
                    const groupElementNodeList = document.getElementById('groupList').children;
                    groupElementNodeList[groupElementNodeList.length - 1].click();
                    $("#group-view-button").click();
                } else {
                    alertInformation( 'There is already a group named ' + groupName );
                }
            } else {
                alertInformation( 'You must enter a group name to continue' );
            }
        } else {
            alertInformation( 'You may only add theme spheres to groups' );
        }
    } else {
        alertInformation( 'You must select something to add to the group' );
    }
}

function groupSelectionCheck() {
    let checkStatus = true;
    for (let i = 0; i < selected.length; i++) {
        if (clusterGroup.getObjectByName(selected[i]).userData.type === 'SphereGeometry') {
            continue;
        } else {
            checkStatus = false;
            break;
        }
    }
    return checkStatus;
}
viewFunctions.showGroupsList = function() {
    // let groupListWrap = document.getElementById('groupListWrap');
    let groupList = document.getElementById('groupList');
    if (groupList.childNodes.length > 0) {
        while (groupList.hasChildNodes()) {
            groupList.removeChild(groupList.firstChild);
        }
    }
    let list = Object.keys(customGroups);
    // groupListWrap.style.visibility = 'visible';
    for (let i = 0; i < list.length; i++) {
        // let tempNameArray = customGroups[list[i]];
        // tempNameArray.forEach(function(item) {
        //     item = themeNameSearch(item);
        // });
        // groupList.innerHTML += '<button id="' + list[i] + '" class="listItem" onclick="groupSelect(this.id)">' + list[i] + '</button>';
        groupList.innerHTML += '<div id="' + list[i] + '" class="group-container" onclick="groupSelect(this.id)">'
        + '<p class="group-name">' + list[i] + '</p>'
        // + '<p class="group-num"> ' + customGroups[list[i]].length + ' </p>'
        // + '<p class="group-themes"> ' + tempNameArray.toString() + ' </p>'
        + '</div>';
    }
    groupSelect(groupList.childNodes[groupList.childNodes.length-1].getAttribute('id'));
    hudSelected = document.getElementsByClassName('hudSelectedCluster');
    let len = hudSelected.length;
    for (let i = 0; i < len; i++) {
        hudSelected[0].classList.remove('hudSelectedCluster');
    }
}
epmViewFunctions.showGroupsList = function() {
    mainCfg.components.visDataPanel.deselectAllThemeItems();
    mainCfg.components.visDataPanel.setGroups(composeGroupData());
}

ctrlFunctions.showGroupsList = function() {
    GLOBAL_STATE.selected = [];
    GLOBAL_STATE.prevColor = [];
}
function groupDisplay() {
    if (mainCfg.showVisDataPanel) {
        epmViewFunctions.showGroupsList();
    } else {
        viewFunctions.showGroupsList();
    }
    ctrlFunctions.showGroupsList();
}
ctrlFunctions.onMaybeResetGroupSelection = function() {
    if (prevGroupColor.length > 0) {
        for (let i = 0; i < selectedGroup.length; i++) {
            if(clusterGroup.getObjectByName(selectedGroup[i]) !== undefined) {
                let currentItem = clusterGroup.getObjectByName(selectedGroup[i]);
                let gColor = 0;
                ThemeNameRegistry.forEach((item) => {
                    if (item.Name == selectedGroup[i]) {
                        if(item.hasOwnProperty('GroupColor') == true && item.GroupColor !== '') {
                            gColor = item.GroupColor;
                        }
                     }
                });
                if(gColor !== 0) {
                    currentItem.material.color.setHex(`0x${gColor}`);
                } else {
                    currentItem.material.color.setHex('0x' + prevGroupColor[i]);
                }
                let bEl = document.getElementById(`themeBoxEl=${currentItem.name}`);
                console.log(bEl);
                if(bEl !== null) {
                    // bEl.classList.toggle('boxViewSelected');
                    bEl.querySelector('.themebox-table-cont').classList.remove('boxViewGroupSelected');
                } else {
                    console.log('Box Element NOT Found!');
                }
            }
        }
        prevGroupColor = [];
    }
}
ctrlFunctions.onMaybeRollbakGroupSelectionColor = function() {
    const selectedThemes = (GLOBAL_STATE.selectedGroup || []).slice(0);
    if (prevGroupColor.length > 0) {
        for (let i = 0; i < selectedThemes.length; i++) {
             if(clusterGroup.getObjectByName(selectedThemes[i]) !== undefined) {
                let currentItem = clusterGroup.getObjectByName(selectedThemes[i]);
                // Correct the custom group color on sphere
                let tmpGroupColorVal = (typeof ThemeNameRegistry[currentItem.userData.indexKey].GroupColor !== 'undefined' 
                    && ThemeNameRegistry[currentItem.userData.indexKey].GroupColor !== '') 
                    ? ThemeNameRegistry[currentItem.userData.indexKey].GroupColor : prevGroupColor[i];
                currentItem.material.color.setHex('0x' + tmpGroupColorVal);
                let bEl = document.getElementById(`themeBoxEl=${currentItem.name}`);
                console.log(bEl);
                if(bEl !== null) {
                    // bEl.classList.toggle('boxViewSelected');
                    bEl.querySelector('.themebox-table-cont').classList.remove('boxViewGroupSelected');
                } else {
                    console.log('Box Element NOT Found!');
                }
            }
        }
        GLOBAL_STATE.prevGroupColor = [];
    }
}
ctrlFunctions.applyGroupSelection = function() {
    const selectedThemes = (GLOBAL_STATE.selectedGroup || []).slice(0);
    for (let i = 0; i < selectedThemes.length; i++) {
        const currentItem = clusterGroup.getObjectByName(selectedThemes[i]);
        if (currentItem === undefined) {
            continue;
        }
        const currentColor = currentItem.material.color.getHexString();
        prevGroupColor.push(currentColor);
        currentItem.material.color.setHex(0xa29b23);
        let bEl = document.getElementById(`themeBoxEl=${currentItem.name}`);
        console.log(bEl);
        if(bEl !== null) {
            // bEl.classList.toggle('boxViewSelected');
            bEl.querySelector('.themebox-table-cont').classList.add('boxViewGroupSelected');
        } else {
            console.log('Box Element NOT Found!');
        }
    }
}
function groupSelect(groupName) {
    let element = null;
    let gItems = null;
    let isAlreadySelected = false;
    let hasRestoreProfileFlag = restoreProfile !== false;
    Array.from(document.querySelectorAll('.boxViewGroupSelected')).forEach((el)=>{el.classList.remove('boxViewGroupSelected')});
    if (mainCfg.showVisDataPanel) {
        console.log('GS FLAG:1')
        const selectedGroup = mainCfg.components.visDataPanel.selectedGroup;
        isAlreadySelected = selectedGroup && selectedGroup.name === groupName;
        mainCfg.components.visDataPanel.toggleGroupByName(groupName);
    } else {
        console.log('GS FLAG:2')
        element = document.getElementById(groupName);
        gItems = document.getElementById('groupListItems');
        gItems.innerHTML = '';
        isAlreadySelected = element && ('classList' in element) && element.classList.contains('listItemSelected') === true;
    }

    if (isAlreadySelected) {
        console.log('GS FLAG:3')
        groupSelected = false;
        if (!mainCfg.showVisDataPanel) {
            console.log('GS FLAG:4')
            element.classList.remove('listItemSelected');
            gItems.style.visibility = 'hidden';
        }
        if (!hasRestoreProfileFlag) {
            console.log('GS FLAG:5')
            ctrlFunctions.onMaybeResetGroupSelection();
        }
        parameters.e = 'New Group';
        selectedGroup = [];
        selectedGroupName = void(0);
    } else {
        console.log('GS FLAG:6')
        if (groupSelected === true) {
            console.log('GS FLAG:7')
            if (!mainCfg.showVisDataPanel) {
                console.log('GS FLAG:8')
                let groupList = document.getElementById('groupList');
                for (let i = 0; i < groupList.children.length; i++) {
                    if (groupList.children[i].classList.contains('listItemSelected')) {
                        console.log('GS FLAG:9')
                        groupList.children[i].classList.remove('listItemSelected');
                    }
                }
            }
            if (!hasRestoreProfileFlag) {
                console.log('GS FLAG:10')
                ctrlFunctions.onMaybeRollbakGroupSelectionColor();
                selectedGroup = [];
                selectedGroupName = void(0);
                parameters.e = 'New Group';
            }
        }
        groupSelected = true;
        if (!mainCfg.showVisDataPanel) {
            console.log('GS FLAG:11')
            element.classList.add('listItemSelected');
        }
        selectedGroup = customGroups[groupName];
        selectedGroupName = groupName;
        parameters.e = groupName;

        if (!hasRestoreProfileFlag) {
            console.log('GS FLAG:12')
            ctrlFunctions.applyGroupSelection();
        }

        if (!mainCfg.showVisDataPanel) {
            //GroupEdit
            console.log('GS FLAG:13')
            gItems.style.visibility = 'visible';
            for (let i = 0; i < selectedGroup.length; i++) {
                if (i === selectedGroup.length - 1) {
                    console.log('GS FLAG:14')
                    gItems.innerHTML += '<span id="' + selectedGroup[i] + '" onclick="deleteGroupItem(this.id)">' + themeNameSearch(selectedGroup[i]) +
                        ' <i class="fa fa-times-circle" aria-hidden="true"></i></span';
                } else {
                    console.log('GS FLAG:15')
                    gItems.innerHTML += '<span id="' + selectedGroup[i] + '" onclick="deleteGroupItem(this.id)">' + themeNameSearch(selectedGroup[i]) +
                        ' <i class="fa fa-times-circle" aria-hidden="true"></i>, </span>';
                }
            }
        }

    }

    if (!mainCfg.showVisDataPanel) {
        console.log('GS FLAG:16')
        //GroupEdit
        //edit group btn
        let groupEditBtn = document.getElementById('group-tools');
        if (groupSelected === true) {
            console.log('GS FLAG:17')
            groupEditBtn.style.visibility = 'visible';
        } else {
            console.log('GS FLAG:18')
            groupEditBtn.style.visibility = 'hidden';
        }
        //End

    }
}

function guiGroupColorSave() {
    if (selectedGroup !== undefined) {
        if (selectedGroup.length > 0) {
            let matchFound = false;
            let color;
            for(let i = 0;i<selectedGroup.length;i++) {
                if(clusterGroup.getObjectByName(selectedGroup[i]) !== undefined) {
                    color = clusterGroup.getObjectByName(selectedGroup[i]).material.color.getHexString();
                    break;
                }
            }
            prevGroupColor = [];
            if(color === undefined) {
                color = parameters.e4.substr(1);
            }
            for (let i = 0; i < customGroupColors.length; i++) {
                if (customGroupColors[i].Group == selectedGroupName) {
                    matchFound = true;
                    customGroupColors[i].Color = color;
                    break;
                }
            }
            if (matchFound == false) {
                customGroupColors.push({ Group: selectedGroupName, Color: color });
            }
            //Adjust the name registry
            for (let i = 0; i < ThemeNameRegistry.length; i++) {
                if (selectedGroupName == ThemeNameRegistry[i].Group) {
                    ThemeNameRegistry[i].GroupColor = color;
                    let bEl = document.getElementById(`themeBoxEl=${ThemeNameRegistry[i].Name}`);
                    console.log(bEl);
                    if(bEl !== null) {
                        console.log('Saving Box Color...');
                        console.log(ThemeNameRegistry[i].Color);
                        bEl.style.border = `10px solid #${color}`;
                        console.log(bEl);
                    }
                }
            }
            alertInformation('Color applied');
        }
    }
}
ctrlFunctions.onHideGroup = function() {
    if (typeof(selectedGroup) === 'undefined' || typeof(selectedGroupName) === 'undefined') {
        return;
    }
    selectedGroup.forEach(function(item) {
        if(clusterGroup.getObjectByName(item) !== undefined) {
            let index = clusterGroup.getObjectByName(item).userData.indexKey;
            ThemeNameRegistry[index].GroupHidden = true;
            clusterGroup.getObjectByName(item).visible = false;
            spriteTextGroup.children[index].visible = false;
            spriteTextGroup.children[index].element.style.display = 'none';
            cubeGroup.children[index].visible = false;
            boxViewGroup.children[index].element.style.diplay = 'none';
            if( typeof dragLines != 'undefined' && typeof dragLines.children[index] != 'undefined' ) {
                dragLines.children[index].visible = false;
            }            
            if (glowGroup.children.length > 0) {
                if (typeof(glowGroup.getObjectByName('Glow' + item)) !== 'undefined') {
                    log(item);
                    document.getElementById('Glow' + item).style.visibility = 'hidden';
                }
            }
            //add tubes connected to hidden Themes to array groupHiddenTubes
            groupHiddenTubes = groupHiddenTubes.concat(clusterGroup.getObjectByName(item).userData.connectedTubes);
        }
    });

    if(hiddenGroups.includes(selectedGroupName) === false) {
        hiddenGroups.push(selectedGroupName);
    }
}
function hideGroup() {
    if(typeof(selectedGroup) !== 'undefined' && typeof(selectedGroupName) !== 'undefined') {
        if (!mainCfg.showVisDataPanel) {
            if(selectedGroup.length > 0 && document.getElementById(selectedGroupName).classList.contains('group-hidden') == false) {
                ctrlFunctions.onHideGroup();
                document.getElementById(selectedGroupName).classList.add('group-hidden');
                distanceCheck();
                alertInformation('Group ' + selectedGroupName + ' hidden');
            }
        } else {
            if(selectedGroup.length > 0) {
                ctrlFunctions.onHideGroup();
                distanceCheck();
                alertInformation('Group ' + selectedGroupName + ' hidden');
            }
        }

    } else {
        alertInformation('Select a group first.');
    }

    reportVisisbleGenesTotal();
    if (mainCfg.showVisDataPanel) {
      mainCfg.components.visDataPanel.groupsVisibilityChanged();
    }
}


ctrlFunctions.onUnHideGroup = function() {
    if (typeof(selectedGroup) === 'undefined' || typeof(selectedGroupName) === 'undefined') {
        return;
    }
    let tempTubeArray = [];
    selectedGroup.forEach(function(item) {
        if(clusterGroup.getObjectByName(item) !== undefined) {
            let index = clusterGroup.getObjectByName(item).userData.indexKey;
            ThemeNameRegistry[index].GroupHidden = false;
            if (glowGroup.children.length > 0) {
                if (typeof(glowGroup.getObjectByName('Glow' + item)) !== 'undefined') {
                    document.getElementById('Glow' + item).style.visibility = 'visible';
                }
            }
            tempTubeArray = tempTubeArray.concat(clusterGroup.getObjectByName(item).userData.connectedTubes);
        }
    });
    tempTubeArray.forEach(function(item) {
        let index = groupHiddenTubes.indexOf(item);
        if(index>-1) {
            groupHiddenTubes.splice(index, 1);
        }
    });
    let groupIndex = hiddenGroups.indexOf(selectedGroupName);
    if( groupIndex >=0 ) {
        hiddenGroups.splice(groupIndex, 1);
    } else {
        log('ERROR! Could not find group name in array');
    }
}

function unhideGroup() {
    if(typeof(selectedGroup) != 'undefined' && typeof(selectedGroupName) !== 'undefined') {
        if (!mainCfg.showVisDataPanel) {
            if(selectedGroup.length > 0 && document.getElementById(selectedGroupName).classList.contains('group-hidden') == true) {
                document.getElementById(selectedGroupName).classList.remove('group-hidden');
                ctrlFunctions.onUnHideGroup();
                parameterToPercentage();
                alertInformation('Group ' + selectedGroupName + ' unhidden');
            }
        } else {
            if(selectedGroup.length > 0 && hiddenGroups.includes(selectedGroupName)) {
                ctrlFunctions.onUnHideGroup();
                parameterToPercentage();
                alertInformation('Group ' + selectedGroupName + ' unhidden');
            }
        }

    } else {
        alertInformation('You must select a group first.');
    }

    reportVisisbleGenesTotal();
    if (mainCfg.showVisDataPanel) {
      mainCfg.components.visDataPanel.groupsVisibilityChanged();
    }
}

function guiInitScaling(tempVal) {
    tempVal = parseInt(tempVal);
    for (let i = 0; i < optimalItemsSize(); i++) {
        let radius = clusterGroup.children[i].userData.radius;
        clusterGroup.children[i].position.set(c[i].X * tempVal, c[i].Y * tempVal, c[i].Z * tempVal);
        cubeGroup.children[i].position.set(c[i].X * tempVal, c[i].Y * tempVal, c[i].Z * tempVal);
        spriteTextGroup.children[i].position.set(((c[i].X) * tempVal), ((c[i].Y) * tempVal) + (radius * 1.5) + 8, ((c[i].Z) * tempVal));
        if( typeof boxViewGroup.children[i] !== 'undefined' ) {
            boxViewGroup.children[i].position.set(c[i].X * tempVal, c[i].Y * tempVal, c[i].Z * tempVal);
        }
    }
    PM = tempVal;
    parameters.c4 = PM.toString();
    let len = clusterGroup.children.length;
    let len2 = optimalItemsSize();
    let myIt = 0;
    for (let i = len2; i < len; i++) {
        clusterGroup.remove(clusterGroup.children[len2]);
    }
    clusterGroup.children.forEach(function(sphere, index) {
        while(collisionCheck(sphere) == true) {
            let sphPos = adjustPosition(sphere.position);
            log('adjusting relational objects...');
            cubeGroup.children[index].position.x = sphPos.x;
            cubeGroup.children[index].position.y = sphPos.y;
            cubeGroup.children[index].position.z = sphPos.z;

            spriteTextGroup.children[index].position.x = sphPos.x;
            spriteTextGroup.children[index].position.y = sphPos.y + (sphere.userData.radius * 1.5) + 4;
            spriteTextGroup.children[index].position.z = sphPos.z;

            if( typeof boxViewGroup.children[index] !== 'undefined' ) {
                boxViewGroup.children[index].position.set(sphPos.x, sphPos.y, sphPos.z);
            }
            log('sphere repoisition complete...');
        }
    });
    log('all objects repositioned successfully!');
    drawLines();
    checkVisibility();
    // len = optimalItemsSize();
    for (let i = len2; i < len; i++) {
        tubeLengthsSorted.push({ name: clusterGroup.children[i].name, lineLength: clusterGroup.children[i].userData.lineLength });
    }
    tubeLengthsSorted.sort(function(a, b) {
        return a.lineLength - b.lineLength;
    });
    checkScoreVisibility();

    if (!mainCfg.showVisDataPanel) {
        log('checking gene view');
        let el = document.getElementById('gene-view');
        let geneTarget = 0;
        for(let i=0;i<el.children.length;i++) {
            if(el.children[i].classList.contains('hudSelectedGene')) {
                geneTarget = el.children[i].id;
                log('selected gene detected --> ' + geneTarget);
            }
        }

        if(geneTarget!=0) {
            log('geneTarget not == 0');
            document.getElementById(geneTarget).click();
            document.getElementById(geneTarget).click();
        }
    }
    cleanDragLines();
    for( let i = 0;i < optimalItemsSize();i++ ) {
        let clusterName = clusterGroup.children[i].name;        
        let clusterPosition = new THREE.Vector3().copy( clusterGroup.children[i].position );
        let labelPosition = new THREE.Vector3().copy( spriteTextGroup.getObjectByName(`spriteText=${clusterName}`).position );
        let visible = clusterGroup.children[i].visible;
        initSingleLabelLine( clusterName,clusterPosition,labelPosition,visible );
    }
}

function applyScale (scale) {
    if (mainCfg.useLegacyScale) {
        log('using legacy scale');
        return initLoading(savedProfileData[name].PM);
    }
    scale = Math.max(1, scale);
    showLoader();
    setTimeout(function() {
        // in case if loader was somehow hidden within those 500ms
        showLoader();
        guiInitScaling(scale);
        hideLoader();
    }, 500);
}

/*

function initLoading(tempVal) {
    if (tempVal < 1) {
        tempVal = 1;
    }
    let loading = document.createElement('DIV');
    loading.setAttribute('id', 'loadingScreen');
    loading.innerHTML = '<image src="../assets/images/ajax-loader.gif" id="loadingImage">';
    document.body.prepend(loading);
    setTimeout(function() {
        guiInitScaling(tempVal);
    }, 500);
}

*/


function delectItemsInSelectedGroup() {
    if (!Array.isArray(selectedGroup)) {
        return;
    }
    for (let i = 0; i < selectedGroup.length; i++) {
        clusterGroup.getObjectByName(selectedGroup[i]).material.color.setHex('0x' + prevColor[i]);
    }
    selectedGroup = [];
}
function deselectAll() {
    parameters.a = 'Select A Theme';
    if (selected.length === 0) {
        return;
    }
    selected.slice(0).forEach((themeName)=>{
        ctrlFunctions.resetThemeSelectionByName(themeName);
    });
    Array.from( document.getElementsByClassName('boxViewSelected')).forEach((boxEl) => boxEl.classList.toggle("boxViewSelected"));
    if (clusterInfoShown === true) {
        popupClusterInfoHide();
    }
    if (mainCfg.showVisDataPanel) {
        mainCfg.components.visDataPanel.deselectAllThemeItems();
    } else {
        hudSelected = document.getElementsByClassName('hudSelectedCluster');
        let len = hudSelected.length;
        for (let i = 0; i < len; i++) {
            hudSelected[0].classList.remove('hudSelectedCluster');
        }
    }
    if (!mainCfg.showNewSelectedThemesPanel) {
    viewFunctions.maybeHideSelectedClustersPanel();
  }
}

viewFunctions.removeGroupItemFromHudPanel = function(item) {
    //remove item from Group Items element
    let gItems = document.getElementById('groupListItems');
    let index = selectedGroup.indexOf(item);
    if (gItems.children.length >= 1) {
        gItems.removeChild(gItems.childNodes[index]);
        ctrlFunctions.removeGroupItem(item);
        //added to remove deleted item from HUD
        // document.getElementById(selectedGroupName).innerText = document.getElementById(selectedGroupName).innerText.split(',').splice(index,1).toString();
    }
    if (gItems.children.length < 1) {
        gItems.innerHTML = 'There are no items in this group...';
    }
}
ctrlFunctions.removeGroupItem = function(item) {
    let index = selectedGroup.indexOf(item);
    if (index !== -1) {
        selectedGroup.splice(index, 1);
    }

    const maybeCluster = clusterGroup.getObjectByName(item);
    if (maybeCluster !== undefined) {
        let colorVal = (ThemeNameRegistry[maybeCluster.userData.indexKey].Color !== '') ? ThemeNameRegistry[maybeCluster.userData.indexKey].Color : '000015';
        maybeCluster.material.color.setHex(`0x${colorVal}`);
        maybeCluster.userData.customGroup = '';
        if(typeof maybeCluster.userData.customGroupColor !== 'undefined') {
            delete maybeCluster.userData.customGroupColor;
        }
        prevGroupColor.splice(index, 1);
    }
    let matchFound = false;
    for (let i = 0; i < ThemeNameRegistry.length; i++) {
        if (item === ThemeNameRegistry[i].Name) {
            ThemeNameRegistry[i].Group = '';
            ThemeNameRegistry[i].GroupColor = '';
            matchFound = true;
            let bEl = document.getElementById(`themeBoxEl=${ThemeNameRegistry[i].Name}`);
            if(bEl !== null) {
                console.log('Removing Box Color...');
                if (ThemeNameRegistry[i].Color === '000015' || ThemeNameRegistry[i].Color === '') {   
                    bEl.style.border = '';
                } else {
                    bEl.style.border = `10px solid #${ThemeNameRegistry[i].Color}`;
                }
                bEl.removeAttribute('data-custgroup');
                console.log(bEl);              
                bEl.querySelector('.themebox-table-cont').classList.remove('boxViewGroupSelected');
            }
            break;
        }
    }
    if (matchFound == false) {
        alertInformation('There has been an error --> ErrMes = GID1')
        log('ERROR --> GID1');
        log('item --> ' + item);
    }
}
//GroupEdit
function deleteGroupItem(item) {
    if (mainCfg.showVisDataPanel) {
        ctrlFunctions.removeGroupItem(item);
        return;
    } else {
        viewFunctions.removeGroupItemFromHudPanel(item);
    }
}
function findBy(arr, key, value) {
    let results = (arr || []).filter((el)=>el[key] === value);
    if (results) {
        return results[0];
    } else {
        return undefined;
    }
}
ctrlFunctions.onAddThemesToGroup = function(groupName) {
    let color = 0;
    let selectedThemes = selected.slice();
    deselectAll();

    const customGroupColor = findBy(customGroupColors, 'Group', groupName);
    if (customGroupColor) {
        color = customGroupColor.Color;
    }

    for (let i = 0; i < selectedThemes.length; i++) {
        let same = false;
        //checking if any selection are already in group
        for (let z = 0; z < selectedGroup.length; z++) {
            if (selectedThemes[i] === selectedGroup[z]) {
                same = true;
                log('Already in group --> ' + selectedThemes[i]);
            }
        }
        if (same == true) {
            continue;
        } else if (same == false) {
            //Change name registry
            let matchFound = false;
            for (let z = 0; z < ThemeNameRegistry.length; z++) {
                if (selectedThemes[i] == ThemeNameRegistry[z].Name) {
                    matchFound = true;
                    ThemeNameRegistry[z].Group = groupName;
                    let bEl = document.getElementById(`themeBoxEl=${ThemeNameRegistry[z].Name}`);
                    if(bEl !== null) {
                        console.log(bEl);
                        console.log('BoxView adding group selected class');
                        bEl.setAttribute('data-custgroup', groupName);
                        bEl.querySelector('.themebox-table-cont').classList.add('boxViewGroupSelected');
                    }
                    if (color != 0) {
                        ThemeNameRegistry[z].GroupColor = color;
                        console.log(bEl);
                        if(bEl !== null) {
                            console.log('Saving Box Color...');
                            console.log(ThemeNameRegistry[z].Color);
                            bEl.style.border = `10px solid #${color}`;
                            console.log(bEl);
                        }
                    }
                    break;
                }
            }
            if (matchFound == false) {
                log('ERROR --> Could not find name in the registry! Group --> ' + groupName + ' name --> ' + selectedThemes[i]);
            }
            clusterGroup.getObjectByName(selectedThemes[i]).userData.customGroup = groupName; //set to group
            selectedGroup.push(selectedThemes[i]);
            if (color != 0) {
                prevGroupColor.push(color);
            } else {
                prevGroupColor.push(prevColor[i]); //get current color
            }
        }
    }

    for (let i = 0; i < selectedGroup.length; i++) {
        clusterGroup.getObjectByName(selectedGroup[i]).material.color.setHex(0xa29b23); //set to group selected color
    }
}
function groupAdd() {
    if (GLOBAL_STATE.selected.length > 0 && groupSelectionCheck() === true) {
        let group = null;
        let gItems = null;
        if (!mainCfg.showVisDataPanel) {
            group = document.getElementById('groupList').getElementsByClassName('listItemSelected')[0].children[0].innerText;
            gItems = document.getElementById('groupListItems');
        } else {
            group =  mainCfg.components.visDataPanel.selectedGroup.name;
        }
        ctrlFunctions.onAddThemesToGroup(group);
        if (!mainCfg.showVisDataPanel) {
            //add to gItems
            gItems.innerHTML = '';
            for (let i = 0; i < selectedGroup.length; i++) {
                if (i === selectedGroup.length - 1) {
                    gItems.innerHTML += '<span id="' + selectedGroup[i] + '" onclick="deleteGroupItem(this.id)">' + themeNameSearch(selectedGroup[i]) +
                        ' <i class="fa fa-times-circle" aria-hidden="true"></i></span';
                } else {
                    gItems.innerHTML += '<span id="' + selectedGroup[i] + '" onclick="deleteGroupItem(this.id)">' + themeNameSearch(selectedGroup[i]) +
                        ' <i class="fa fa-times-circle" aria-hidden="true"></i>, </span>';
                }
            }
        }
    } else {
        alertInformation('Select one or more theme(s) to add');
    }
}

function deleteGroup(groupName) {
    let group = groupName;
    if (groupSelected === true) {
        if (!mainCfg.showVisDataPanel) {
            let gItems = document.getElementById('groupListItems');
            group = document.getElementById('groupList').getElementsByClassName('listItemSelected')[0].children[0].innerText;
            gItems.innerHTML = '';
            gItems.style.visibility = 'hidden';
        }
        for (let i = 0; i < selectedGroup.length; i++) {
            if(clusterGroup.getObjectByName(selectedGroup[i]) !== undefined) {
                let tmpCustGroupItem = clusterGroup.getObjectByName(selectedGroup[i]);
                tmpCustGroupItem.userData.customGroup = '';
                if(typeof tmpCustGroupItem.userData.customGroupColor !== 'undefined') {
                    delete tmpCustGroupItem.userData.customGroupColor;
                }
                let colorVal = (ThemeNameRegistry[tmpCustGroupItem.userData.indexKey].Color !== '') ? ThemeNameRegistry[tmpCustGroupItem.userData.indexKey].Color : '000015';
                tmpCustGroupItem.material.color.setHex(`0x${colorVal}`);
                let bEl = document.getElementById(`themeBoxEl=${tmpCustGroupItem.name}`);
                console.log(bEl);
                console.log('Saving Box Color...');
                console.log(colorVal);
                if(bEl !== null) {
                    if (colorVal === '000015') {   
                        bEl.style.border = '';
                    } else {
                        bEl.style.border = `10px solid #${colorVal}`;
                    }
                    bEl.removeAttribute('data-custgroup');
                    bEl.querySelector('.themebox-table-cont').classList.remove('boxViewGroupSelected');
                    console.log(bEl);
                }
            }
        }
        if (customGroupColors.length > 0) {
            for (let i = 0; i < customGroupColors.length; i++) {
                if (selectedGroupName == customGroupColors[i].Group) {
                    customGroupColors.splice(i, 1);
                    break;
                }
            }
        }
        //Change the name registry
        for (let i = 0; i < ThemeNameRegistry.length; i++) {
            if (group == ThemeNameRegistry[i].Group) {
                ThemeNameRegistry[i].Group = '';
                ThemeNameRegistry[i].GroupColor = '';
            }
        }
        delete customGroups[group];
        selectedGroup = [];
        prevGroupColor = [];
        groupSelected = false;
        if (!mainCfg.showVisDataPanel) {
            // group = document.getElementById('groupList').getElementsByClassName('listItemSelected')[0];
            document.getElementById(group.trim()).parentNode.removeChild(document.getElementById(group.trim()));
               // document.getElementById('groupEdit').style.visibility = 'hidden';
            document.getElementById('group-tools').style.visibility = 'hidden';
               // document.getElementById('groupList').style.visibility = 'hidden';
            if (Object.keys(customGroups).length == 0) {
                // group.style.visibility = 'hidden';
                document.getElementById('groupList').style.visibility = 'hidden';
            }
        }

    }
} //End

function httpRequest(data) {
    if (typeof data === 'object') {
        // Laravel add needed info to every request parameters
        if (data.hasOwnProperty('parameters') && typeof data.parameters === 'object') {
            if (!data.parameters.hasOwnProperty('AcctId')) {
                data.parameters.AcctId = AcctId;
            }
            if (!data.parameters.hasOwnProperty('ProjectId')) {
                data.parameters.ProjectId = ProjectId;
            }
            if (!data.parameters.hasOwnProperty('BioExplorerType')) {
                data.parameters.BioExplorerType = bioExplorerProjectType;
            }
        }
        data = JSON.stringify(data);
    }
    log(data);
    let header = 'PostRequest=';
    let request = header + data;
    let url = '../assets/php/cbv_functions.php';
    let xhr = new XMLHttpRequest();
    // xhr.timeout = 100000;
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    // xhr.setRequestHeader("Content-type", "application/json");
    // xhr.ontimeout = function() {
    //     log('request-error -> ' + xhr.statusText);
    // }
    xhr.onreadystatechange = function() {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            log(response);
            if (response != 'request complete') {
                httpResponse(response);
            } else {
                log('hello from js');
            }
        }
    }
    xhr.send(request);
}

function httpResponse(response) {
    console.log(response);
    let data = 0;
    try {
        data = JSON.parse(response);
    } catch (e) {
        console.log(e);
    }
    if (data == 0) {
        data = response;
    }
    if (data.hasOwnProperty('switch_cmd')) {
        let cmd = data['switch_cmd'];
        switch (cmd) {
            case 'ERROR!':
                log('!!!Error!!!');
                break;

            case 'SessionStatus':
                log('session-status');
                if(data['Prompt'] == true) {
                    log('Reauthenticate!');
                    $("#dialog-session-confirm").dialog('open');
                    // console.log('SESSION_EXPIRED: Redirecting to Login!');
                    // window.location.href = Webroot + 'login';
                } else {
                    log('data-prompt is false');
                    if($('#dialog-session-confirm').hasClass('ui-dialog-content')) {
                        let isOpen = $("#dialog-session-confirm").dialog('isOpen');
                        if(data['Prompt'] != true && isOpen == true) {
                            log('prompt is false and dialog is open');
                            $("#dialog-session-confirm").dialog('close');
                        }
                    } else {
                        log('session-confirm has not initialized yet -> 2910');
                    }
                }
                break;

            case 'Reauth':
                log(data);
                if(data['Status']==true) {
                    log('Reauth Success');
                    logCount = 0;
                    $('#dialog-session-confirm').dialog('close');
                    document.getElementById('inactive-acct-name').value = '';
                    document.getElementById('inactive-password').value = '',
                    document.getElementById('auth-msg').style.visibility = 'hidden';
                    let data = {'function':'reauthSuccess'};
                    httpRequest(data);
                } else {
                    log('Reauth Failed');
                    logCount++;
                    log(logCount);
                    document.getElementById('auth-msg').style.visibility = 'visible';
                    if(logCount>=4) {
                        let data = {'function':'logout'};
                        httpRequest(data);
                    }
                }
                break;

            case 'InputList':
                log(data);
                if( data['List'] !== null ) {
                    sessionStorage.setItem( 'Entity_List',data['List'] );
                }
                if( data['ExpressionList'] !== null ) {
                    sessionStorage.setItem( 'Expression_List',data['ExpressoinList'] );
                }
                break;
            
            case 'EntMapListReturn':
                log(data);
                if( data['List'] !== null ) {
                    sessionStorage.setItem( 'Entity_List',data['List'] );
                    getUnmappedEntitiesExported( data['List'] );
                } else {
                    console.log('ERROR: System is Returning Invalid or Empty Entity List Data!');
                    alertInformation('ERROR: System is Returning Invalid or Empty Entity List Data! Cannot Complete Request...');
                }
                if( data['ExpressionList'] !== null ) {
                    sessionStorage.setItem( 'Expression_List',data['ExpressoinList'] );
                }
                break;            

            case 'ProfileDeleted':
                log('Profile Deleted & FileWritten');
                location.reload();
                break;

            case 'FileWritten':
                log('FileWritten');
                restoreProfileData(data['ProfileData']);
                alertInformation('Progress Saved');
                break;

            case 'SavedProfiles':
                log('SavedProfiles');
                restoreProfileData(data['ProfileData']);
                alertInformation('Profile Data Loaded');
                break;

            case 'NoFile':
                log('file does not exist');
                break;

            case 'ExportLink':
                log('Received Link');
                let newWindow = window.open(Webroot + data['Link']);
                newWindow.onunload = function() {
                    let httpData = { 'function': 'removecsvPHP', 'parameters': { 'FilePath': data['Link'] } };
                    httpRequest(httpData);
                }
                break;

            case 'RemoveExport':
                log(data['Response']);
                break;

            case 'ReturnFilterList':
                log('returnFilter');
                if(data['List'] !== ' NoList') {
                    document.getElementById('theme-view-button').click();
                    removeCubeFiltering();
                    removeHudFilters();
                    filterArray = [];
                    if( typeof data['List'] === 'string' ) {
                        filterArray = JSON.parse(data['List']);
                    } else {
                        filterArray = [...data['List']];
                    }
                    applyFilterMasking(filterArray);
                    filterConceptsInHud(filterArray);
                    filterInSphereText(filterArray);
                    if( parameters.j2 === true ) {
                        applyFilterHalos( true );
                    }
                } else {
                    log('NoList');
                }
                document.body.classList.remove('wait');
                break;

            case 'LoggedOut':
                sessionStorage.removeItem('Laboratory');
                // window.location.replace(`${AppWebroot}logout`);
                $.post(`${AppWebroot}logout`, null, (data, status) => {
                    console.log(data);
                });
                window.location.replace(`${AppWebroot}`);
                break;                
                // sessionStorage.removeItem('Laboratory');
                // window.location.replace(Webroot);
                // break;

            case 0:
                log('do nothing');
                break;

            default:
                log('Default Case');
                document.body.classList.remove('wait');
                break;
        }
    } else {
        log(response);
    }
}

function profileCreation(newName) {
    //add name to gui menu
    savedProfiles.push(newName);
    let newOption = document.createElement('OPTION');
    newOption.value = savedProfiles[savedProfiles.length - 1];
    newOption.innerHTML = newOption.value;

    guiProfileName.domElement.children[0].options.add(newOption);
    profileParameters.profileName = savedProfiles[savedProfiles.length - 1];
    currentLoadedProfile = savedProfiles[savedProfiles.length - 1];
    gui.domElement.children[1].children[2].style.display = 'inherit';

    //create new user obj
    savedProfileData[newName] = {};
    if (initialLoad == false) {
        saveProgressFunc(newName);
    }

    //reset gui state
    gui.__folders["Profile Tools"].close();
    profileParameters.newProfileName = 'Enter Profile Name';
    guiProfileCeationToolsToggle();
}

function profileDeletion(name) {
    let index = savedProfiles.indexOf(name);
    savedProfiles.splice(index, 1);
    delete savedProfileData[name];
    let savedProfileDataString = JSON.stringify(savedProfileData);
    let httpData = { 'function': 'processPHP', 'parameters': { 'Account': ProjectAcct, 'Project': Project, 'ProfileData': savedProfileDataString, 'ProfileDelete':  true, 'Profile': name } };
    mainCfg.deleteProfile(httpData);
}

function guiProfileCeationToolsToggle() {
    if (gui.__folders["Profile Tools"].domElement.children[0].children[2].style.display === 'none') {
        gui.__folders["Profile Tools"].domElement.children[0].children[2].style.display = 'inherit';
        gui.__folders["Profile Tools"].domElement.children[0].children[3].style.display = 'inherit';
        gui.__folders["Profile Tools"].domElement.children[0].children[4].style.display = 'inherit';
    } else {
        gui.__folders["Profile Tools"].domElement.children[0].children[2].style.display = 'none';
        gui.__folders["Profile Tools"].domElement.children[0].children[3].style.display = 'none';
        gui.__folders["Profile Tools"].domElement.children[0].children[4].style.display = 'none';
    }
    guiDeleteToggle();
}

function guiLoadSaveToggle() {
    if (currentLoadedProfile !== profileParameters.profileName) {
        gui.domElement.children[1].children[1].style.display = 'inherit';
        gui.domElement.children[1].children[2].style.display = 'none';
        // document.getElementById('quickSaveBtn').style.visibility = 'hidden';
    } else if (currentLoadedProfile == profileParameters.profileName && currentLoadedProfile != 'Default') {
        gui.domElement.children[1].children[2].style.display = 'inherit';
        gui.domElement.children[1].children[1].style.display = 'none';
        // document.getElementById('quickSaveBtn').style.visibility = 'visible';
    } else if (currentLoadedProfile == profileParameters.profileName && currentLoadedProfile == 'Default') {
        gui.domElement.children[1].children[2].style.display = 'none';
        gui.domElement.children[1].children[1].style.display = 'none';
        // document.getElementById('quickSaveBtn').style.visibility = 'hidden';
    }
    guiDeleteToggle();
}

function guiDeleteToggle() {
    if (profileParameters.profileName == 'Default' || profileParameters.profileName !== currentLoadedProfile) {
        gui.__folders["Profile Tools"].domElement.children[0].children[5].style.display = 'none';
    } else if (profileParameters.profileName !== 'Default' && profileParameters.profileName == currentLoadedProfile) {
        gui.__folders["Profile Tools"].domElement.children[0].children[5].style.display = 'inherit';
    }
}

function saveProgressFunc(name) {
    if (savedProfileData[name] !== undefined) {
        Object.assign(savedProfileData[name], composeProfileObj());
    } else {
        savedProfileData[name] = composeProfileObj();
    }
    savedProfileData = fixPlusOut(savedProfileData);
    console.log(savedProfileData);
    let savedProfileDataString = JSON.stringify(savedProfileData);
    let httpData = { 'function': 'processPHP', 'parameters': { 'AcctId': AcctId, 'Account': ProjectAcct, 'ProjectId': ProjectId, 'Project': Project, 'BioExplorerType': bioExplorerProjectType, 'Profile': name, 'ProfileData': savedProfileDataString}};
    mainCfg.saveProfileReq(httpData);
}
function fixPlusOut(object) {
    if (object !== null) {
        switch (typeof object) {
            case 'string' :
                object = object.replaceAll('+', '%2B');
                break;
            case 'object':
                if (object instanceof Array) {
                    const length = object.length;
                    for (let i = 0; i < length; i++) {
                        object[i] = fixPlusOut(object[i]);
                    }
                } else {
                    for (let i in object) {
                        object[i] = fixPlusOut(object[i]);
                    }
                }
                break;
        }
    }
    return object;
};
function restoreProfileData(ajaxData) {
    console.log(ajaxData);
    if (ajaxData != 'file does not exist') {
        savedProfileData = JSON.parse(ajaxData.replaceAll('\\ ', '+'));
        if (Object.keys(savedProfileData).length >= 1) {
            let arrLen = savedProfiles.length;
            savedProfiles = Object.keys(savedProfileData);
            if (!mainCfg.useAutoProfile) {
                savedProfiles = ['Default'].concat(savedProfiles);
            }
            //Next If and For loop figure and add only new options to profile dropdowns
            if (savedProfiles.length > arrLen) {
                for (let i = arrLen; i < savedProfiles.length; i++) {
                    let newOption = document.createElement('OPTION');
                    newOption.value = savedProfiles[i];
                    newOption.innerHTML = newOption.value;
                    guiProfileName.domElement.children[0].options.add(newOption);
                }
            }
            if (mainCfg.useAutoProfile && initialLoad) {
                if (savedProfileData[DEFAULT_PROFILE_NAME]) {
                    profileParameters.profileName = DEFAULT_PROFILE_NAME;
                    guiLoadSaveToggle();
                    switchProfile(DEFAULT_PROFILE_NAME);
                } else {
                    createDefaultProfileAuto();
                }
            }
            log('restore complete');
        }
    } else {
        if (mainCfg.useAutoProfile) {
            createDefaultProfileAuto();
        } else {
            log('no data to load');
        }
    }
    initialLoad = false;
    hideLoader();
}

function switchProfile(name) {
    deselectAll();
    if (name == 'Default') {
        location.reload();
    } else {
        if (clusterInfoShown) {
            popupClusterInfoHide();
        }
        if(groupSelected === true){
            if(selectedGroupName !== undefined) {
                log('deselecting group');
                groupSelect(selectedGroupName);
            } else {
                log('Error Group Deselect Failure');
            }
        }
        // if(typeof(savedProfileData[name].autoAnnotationActive) !== 'undefined') {
        //     sessionStorage.setItem( 'autoAnnotationActive', savedProfileData[name].autoAnnotationActive );
        //     autoAnnotationActive = sessionStorage.getItem( 'autoAnnotationActive' );
        // } else {
        //     sessionStorage.setItem( 'autoAnnotationActive',false );
        //     autoAnnotationActive = sessionStorage.getItem( 'autoAnnotationActive' );
        // }
        /* Need To Double Check Alias Names Match Still
            (In cases where the user has changed a theme to a custom name ) */
        autoAnnotationActive = savedProfileData[name].autoAnnotationActive || false;
        //Restore cluster names/color/group/GroupColor
        ThemeNameRegistry = savedProfileData[name].ThemeNameRegistry;
        ThemeNameRegistry.forEach(function(item) {
            if(item.hasOwnProperty('GroupHidden') != true) {
                item.GroupHidden = false;
            }
        });
        // document.getElementById('auto_annotate').disabled = false;
        // document.getElementById('auto_annotate').style.cursor = 'pointer';
        if(autoAnnotationActive == true) {
            console.log('Restore Profile Clicking AutoAnnotation Button...');
            autoAnnotationProfileRestore = true;
            initAutoAnnotation();
            // document.getElementById('auto_annotate').click();
            // disableAutoAnnotationBtn();
        } else {
            initAutoAnnotation();
        }
        log('loading scene');
        applyScale(savedProfileData[name].PM);
        if(!autoAnnotationActive) {
            $('#idea-header-visibility').slideUp();
            $('#auto-annote-header').slideUp();
        }
        for (let i = 0; i < optimalItemsSize(); i++) {
            // clusterGroup.children[i].name = ThemeNameRegistry[i].Name;
            clusterGroup.children[i].userData.Name = ThemeNameRegistry[i].Alias;
            clusterGroup.children[i].userData.Alias = ThemeNameRegistry[i].Alias;
            //Set Theme text label
            // spriteTextGroup.children[i].material.map.text = ThemeNameRegistry[i].Alias;
            spriteTextGroup.children[i].element.innerText = ThemeNameRegistry[i].Alias;
            clusterGroup.children[i].userData.Group = ThemeNameRegistry[i].Group;
            if (ThemeNameRegistry[i].hasOwnProperty('GroupColor') && ThemeNameRegistry[i].GroupColor != '') {
                clusterGroup.children[i].material.color.setHex('0x' + ThemeNameRegistry[i].GroupColor);
            } else if(ThemeNameRegistry[i].hasOwnProperty('Color') && ThemeNameRegistry[i].Color != '') {
                clusterGroup.children[i].material.color.setHex('0x' + ThemeNameRegistry[i].Color);
            } else {
                ThemeNameRegistry.Color = '000015';
                clusterGroup.children[i].material.color.setHex('0x000015');
            }
        }
        //Update HUD with proper names
        if (!mainCfg.showVisDataPanel) {
            for (let i = 0; i < optimalItemsSize(); i++) {
                document.getElementById(ThemeNameRegistry[i].Name).children[0].innerHTML = ThemeNameRegistry[i].Alias;
            }
        }
        log('log restore from registry complete');
        if (savedProfileData[name].customGroups !== undefined && customGroups !== undefined) {
            if (Array.isArray(customGroups)) {
                log(customGroups);
                log('customGroups array detected-> reassigning...');
                customGroups = Object.assign({}, customGroups);
                log(customGroups);
            }
            if (Array.isArray(savedProfileData[name].customGroups)) {
                log(savedProfileData[name].customGroups);
                log('serverCustomGroups array detected-> reassigning...');
                savedProfileData[name].customGroups = Object.assign({}, savedProfileData[name].customGroups);
                log(savedProfileData[name].customGroups);
            }
            customGroups = savedProfileData[name].customGroups;
            log('restored customGroups');
        }
        if (savedProfileData[name].customClusterNames !== undefined && customClusterNames !== undefined) {
            customClusterNames = savedProfileData[name].customClusterNames;
            log('restore customClusterNames');
        }
        if (savedProfileData[name].customClusterColors !== undefined && customClusterColors !== undefined) {
            customClusterColors = savedProfileData[name].customClusterColors;
            log('restored customClusterColors');
        }
        if (savedProfileData[name].customGroupColors !== undefined && customGroupColors !== undefined) {
            customGroupColors = savedProfileData[name].customGroupColors;
            log('restored customGroupColors');
        }
        setTimeout(() => {
            for (let i = 0; i < optimalItemsSize(); i++) {
                if( autoAnnotationActive === true ) {
                    // Correcting AutoAnnotations that have been changed to a custom name
                    // Checking if this property exist and adding it only if it does not.
                    if(ThemeNameRegistry[i].hasOwnProperty('isCustomName') === false) {
                        if(ThemeNameRegistry[i].hasOwnProperty('annotations') === false) {
                            console.log('Annotations are not assigned yet!!! Searching...');
                            let themeAnnotation = false;
                            for(let b = 0; b<annotations.length; b++) {
                                if(annotations[b].name === ThemeNameRegistry[i].Name) {
                                    themeAnnotation = annotations[b].annotations;
                                    break;
                                }
                            }
                            if(themeAnnotation === false) {
                                console.log(ThemeNameRegistry[i].Name + ': No annotation found...');
                                ThemeNameRegistry[i].isCustomName = true;
                            } else {
                                if(ThemeNameRegistry[i].Alias === themeAnnotation) {
                                    ThemeNameRegistry[i].isCustomName = false;
                                } else {
                                    ThemeNameRegistry[i].isCustomName = true;
                                }
                            }
                        }
                    }
                    if(ThemeNameRegistry[i].isCustomName) {
                        spriteTextGroup.children[i].element.innerText = ThemeNameRegistry[i].Alias.replaceAll('<br>', '\n');
                        spriteTextGroup.children[i].userData.isCustomName = true;
                    } else {
                        spriteTextGroup.children[i].element.innerHTML = '<span>' + ThemeNameRegistry[i].Alias.replaceAll('<br>', '</span>\n<span>') + '</span>';
                        spriteTextGroup.children[i].element.style.cursor = 'default';
                        spriteTextGroup.children[i].element.style.display = 'flex';
                        spriteTextGroup.children[i].element.style.flexDirection = 'column';
                        spriteTextGroup.children[i].element.style.alignItems = 'center';
                        spriteTextGroup.children[i].userData.isCustomName = false;
                    }
                } else {
                    console.log('Trying to restore old annotation selection...');
                    if( annotations !== null && annotations.hasOwnProperty( ThemeNameRegistry[i].Name ) && annotations[ ThemeNameRegistry[i].Name ].hasOwnProperty('Annotations') ) {
                        console.log('Creating old annotation menu selection');
                        // createAnnotationSelection( spriteTextGroup.children[i] );
                        appendFlagToText( spriteTextGroup.children[i] );
                    } else {
                        console.log('Restore appendFlagToText failed conditions...');
                        console.log(ThemeNameRegistry[i].Name);
                    }
                    spriteTextGroup.children[i].userData.isCustomName = false;
                }
            }
        }, 2000);
        // send event to clear all previously showed relations
        eventBus.publish('clear:theme-relation');
        if (savedProfileData[name].visLockedTubes !== undefined && visLockedTubes !== undefined) {
            visLockedTubes = savedProfileData[name].visLockedTubes;
            if (visLockedTubes.length > 0) {
                restoreVisLockedItems(visLockedTubes);
            }
            log('restored visLockedTubes');
        }
        //Restore necessary parameters
        restoreProfile = false;
        prevGroupColor = [];
        prevColor = [];
        currentLoadedProfile = name;
        guiLoadSaveToggle();
        if (savedProfileData[name].parameters.c6 != parameters.c6) {
            parameters.c6 = savedProfileData[name].parameters.c6;
        }
        if (savedProfileData[name].parameters.c8 != parameters.c8) {
            parameters.c8 = savedProfileData[name].parameters.c8;
        }
        if (savedProfileData[name].parameters.c9 != parameters.c9) {
            parameters.c9 = savedProfileData[name].parameters.c9;
        }
        if (savedProfileData[name].parameters.c2 != parameters.c2) {
            parameters.c2 = savedProfileData[name].parameters.c2;
            distanceCheck();
            log('c2 restored');
        } else {
            log('c2 same');
        }
        if (savedProfileData[name].parameters.c3 != parameters.c3) {
            parameters.c3 = savedProfileData[name].parameters.c3;
            parameterToPercentage();
            log('c3 restored');
        } else {
            log('c3 same');
        }
        if (savedProfileData[name].parameters.f) {
            if (savedProfileData[name].parameters.f != parameters.f) {
                parameters.f = savedProfileData[name].parameters.f;
                if (parameters.f == 'None') {
                    document.body.style.backgroundImage = 'none';
                    document.body.style.backgroundColor = parameters.f1;
                } else if(parameters.f == mainCfg.defaultBGName) {
                    document.body.style.backgroundImage = 'none';
                    document.body.style.background = 'radial-gradient(farthest-side at 80% 45%,#420808 40%,#131313)';
                } else {
                    document.body.style.backgroundImage = `url("../${mainCfg.assetsPath}/images/back${parameters.f}-min.jpg")`;
                    document.body.style.backgroundRepeat = 'no-repeat';
                    document.body.style.backgroundSize = 'cover';
                }
                log('f restored');
            } else {
                log('f same');
            }
        } else {
            log('f does not exist');
        }
        if (savedProfileData[name].parameters.f1) {
            if (savedProfileData[name].parameters.f1 != parameters.f1) {
                parameters.f1 = savedProfileData[name].parameters.f1;
                document.body.style.backgroundColor = parameters.f1;
                log('f1 restored');
            } else {
                log('f1 same');
            }
        } else {
            log('f1 does not exist');
        }
        if (savedProfileData[name].parameters.d2) {
            if (savedProfileData[name].parameters.d2 != parameters.d2) {
                parameters.d2 = savedProfileData[name].parameters.d2;
                for (let i = 0; i < spriteTextGroup.children.length; i++) {
                    spriteTextGroup.children[i].element.style.fontSize = `${parameters.d2 * 4}px`;
                }
                log('d2 restored');
            } else {
                log('d2 same');
            }
        } else {
            log('d2 does not exist');
        }
        if (savedProfileData[name].parameters.d3) {
            if (savedProfileData[name].parameters.d3 != parameters.d3) {
                parameters.d3 = savedProfileData[name].parameters.d3;
                for (let i = 0; i < spriteTextGroup.children.length; i++) {
                    spriteTextGroup.children[i].element.style.color = parameters.d3;
                }
                log('d3 restored');
            } else {
                log('d3 same');
            }
        } else {
            log('d3 does not exist');
        }
        if (savedProfileData[name].parameters.f3) {
            if (savedProfileData[name].parameters.f3 != parameters.f3) {
                parameters.f3 = savedProfileData[name].parameters.f3;
                log('parameters.f3 restored');
            } else {
                log('parameters.f3 same');
            }
        } else {
            log('parameters.f3 does not exist in savedProfileData');
        }
        if (customGroups) {
            if (Object.keys(customGroups).length > 0) {
                groupDisplay();
                if (!mainCfg.showVisDataPanel) {
                    let groupList = [].slice.call(document.getElementById('groupList').children);
                    groupList.forEach(function(item){
                        if (item.classList.contains('listItemSelected')) {
                            item.click();
                        }
                    });
                }
            } else {
                if (!mainCfg.showVisDataPanel) {
                    document.getElementById('groupList').innerHTML = '';
                } else {
                    groupDisplay();
                }
            }
        }
        if(typeof(savedProfileData[name].hiddenGroups) !== "undefined") {
            hiddenGroups = savedProfileData[name].hiddenGroups;
            if (savedProfileData[name].hiddenGroups.length > 0) {
                hiddenGroups.forEach(function(item) {
                    if(customGroups.hasOwnProperty(item)) {
                        if (mainCfg.showVisDataPanel) {
                            groupSelect(item);
                            hideGroup();
                            groupSelect(item);
                        } else {
                            document.getElementById(item).click();
                            hideGroup();
                            document.getElementById(item).click();
                        }
                    } else {
                        hiddenGroups = hiddenGroups.filter(function(group) {
                            return group != item;
                        });
                    }
                });
            }
        } else {
            log('Information -> Correcting ThemeNameRegistry');
            //Added to correct theme name registry before hidden groups could be saved
            ThemeNameRegistry.forEach(function(item) {
                item.GroupHidden = false;
            });
        }
        if(typeof(savedProfileData[name].notes) !== 'undefined') {
            if(savedProfileData[name].notes != '') {
                document.getElementById('notes-text-area').value = savedProfileData[name].notes;
                log('Information --> Notes Restored');
            }
        }
        //Camera and Controls Restore
        if(typeof(savedProfileData[name].cameraControlPositions) !== 'undefined') {
            if(typeof(savedProfileData[name].cameraControlPositions.cameraPosition) !== 'undefined'
                && typeof(savedProfileData[name].cameraControlPositions.cameraPosition) === 'object') {
                camera1.position.set(
                    savedProfileData[name].cameraControlPositions.cameraPosition.x,
                    savedProfileData[name].cameraControlPositions.cameraPosition.y,
                    savedProfileData[name].cameraControlPositions.cameraPosition.z
                );
            }
            if(typeof(savedProfileData[name].cameraControlPositions.cameraTarget) !== 'undefined' 
                && typeof(savedProfileData[name].cameraControlPositions.cameraTarget) === 'object') {
                    controls.target.set(
                        savedProfileData[name].cameraControlPositions.cameraTarget.x,
                        savedProfileData[name].cameraControlPositions.cameraTarget.y,
                        savedProfileData[name].cameraControlPositions.cameraTarget.z                    
                    );
            }
            if(typeof(savedProfileData[name].cameraControlPositions.cameraUp) !== 'undefined' 
                && typeof(savedProfileData[name].cameraControlPositions.cameraUp) === 'object') {
                    camera1.up.set(
                        savedProfileData[name].cameraControlPositions.cameraUp.x,
                        savedProfileData[name].cameraControlPositions.cameraUp.y,
                        savedProfileData[name].cameraControlPositions.cameraUp.z                    
                    );
            }
            controls.update();
        }
        showVisLockedItems();
        reportVisisbleGenesTotal();
        cleanDragLines();
        cleanDragObjects();
        setTimeout( dragObjsRestore, 1000);
        if (savedProfileData[name].parameters.f4 !== $(gui.__folders["Visual Setting"].__controllers[4].__checkbox).is(":checked") ) {
            setTimeout(() => {
                console.log('clicking params f4');
                gui.__folders["Visual Setting"].__controllers[4].__checkbox.click();
            }, 4500);
        } else {
            setTimeout(() => {
                console.log('clicking params f4');
                gui.__folders["Visual Setting"].__controllers[4].__checkbox.click();
                console.log("DoubleClick params f4 in progress");
                gui.__folders["Visual Setting"].__controllers[4].__checkbox.click();
                console.log("DoubleClick params f4 in Finished");
            }, 4500);
        }
        if (savedProfileData[name].parameters.f6 !== $(gui.__folders["Visual Setting"].__controllers[6].__checkbox).is(":checked")) {
            setTimeout(() => {
                console.log('clicking params f6');
                gui.__folders["Visual Setting"].__controllers[6].__checkbox.click();
            }, 4500);
        } else {
            setTimeout(() => {
                console.log('clicking params f6');
                gui.__folders["Visual Setting"].__controllers[6].__checkbox.click();
                console.log("DoubleClick params f6 in progress");
                gui.__folders["Visual Setting"].__controllers[6].__checkbox.click();
                console.log("DoubleClick params f6 in Finished");
            }, 4500);
        }
        setTimeout(() => {
            parameterToPercentage(100 - parameters.c3, 'score');
        }, 4000);
        alertInformation(name + " Restored");
        log('Restore Complete');
    }
}

function restoreWireFrame(name) {
    if(typeof(savedProfileData[name].wireFrame) != 'undefined') {
        if(savedProfileData[name].wireFrame == true) {
            gui.__folders["Visual Setting"].__controllers[4].setValue(true);
            log('parameters.b/wireframe data restored');
        } else {
            gui.__folders["Visual Setting"].__controllers[4].setValue(false);
            log('savedProfileData contained wireframe data but parameters.b == false');
        }
    } else {
        gui.__folders["Visual Setting"].__controllers[4].setValue(false);
        log('savedProfileData does not contain wireframe data');
    }
};

//Modal functions
function alertInformation(message) {
    let pop = document.getElementById('info-popout');
    let content = document.getElementById('inner-popout');
    content.innerHTML = message;
    $(pop).slideDown('slow', 'swing').fadeOut(2000, "swing");
}

viewFunctions.addThemeRelationDomNode = function(tempSelected) {
    let visLockItem = document.createElement('DIV');
    visLockItem.id = tempSelected[0] + ',' + tempSelected[1];
    visLockItem.addEventListener('click', removeVisLock, false);
    visLockItem.innerHTML = tempSelected[0] + ' ~ ' + tempSelected[1] + ' <i class="fa fa-times-circle" aria-hidden="true"></i>';
    document.getElementById('visLockPanel').appendChild(visLockItem);
}

//VisLock (manual always show theme pair and tube between)
function visLock() {
    if (selected.length < 2 || selected.length > 2) {
        alertInformation('Select two Themes only');
    } else {
        const [ left, right ] = selected;

        let tubeArr1 = 'Tube=' + left + ',' + right;
        let tubeArr2 = 'Tube=' + right + ',' + left;

        if (visLockedTubes.includes(tubeArr1) || visLockedTubes.includes(tubeArr2)) {
            alertInformation('Connection between ' +left + ' and ' + right + ' already exists');
            return;
        }

        let matchIt = 0;
        //change visiblity lock to true in name registry and set Theme visibilty to true
        for (let i = 0; i < optimalItemsSize(); i++) {
            if (matchIt < 2) {
                if (ThemeNameRegistry[i].Name == left || ThemeNameRegistry[i].Name == right) {
                    ThemeNameRegistry[i].VisibilityLock = true;
                    matchIt++;
                    clusterGroup.children[i].visible = true;
                }
            } else {
                break;
            }
        }
        //find correct tube and turn visibility to true

        //Changed this for loop during merge
        //I believe 'i' should be set to 0 but I may be incorrect about this
        for (let i = 0; i < clusterGroup.children.length; i++) {
        // for (let i = ThemeNameRegistry.length; i < clusterGroup.children.length; i++) {
            if (clusterGroup.children[i].name == tubeArr1 || clusterGroup.children[i].name == tubeArr2) {
                clusterGroup.children[i].visible = true;
                visLockedTubes.push(clusterGroup.children[i].name);
            }
        }

        if (!mainCfg.showNewRelationDisplay) {
            viewFunctions.addThemeRelationDomNode(selected);
            const visLocPanelNode = document.getElementById('visLockPanel');

            if (visLocPanelNode.style.visibility === 'hidden') {
                visLocPanelNode.style.visibility = 'visible';
            }
        } else {
            epmViewFunctions.addThemeRelation(selected);
        }

        const [ theme1, theme2 ] = [ findBy(ThemeNameRegistry, 'Name', left), findBy(ThemeNameRegistry, 'Name', right) ];
        alertInformation('Visibilty Lock enabled on ' + (theme1.Alias || left) + ' and ' + (theme2.Alias || right));
    }
    reportVisisbleGenesTotal();
    showVisLockedItems();
}

epmViewFunctions.addThemeRelation = function([left, right]) {
    const [ theme1, theme2 ] = [ findBy(ThemeNameRegistry, 'Name', left), findBy(ThemeNameRegistry, 'Name', right) ];
    eventBus.publish('create:theme-relation', { id: [left, right].join(','), left: { id: left, name: theme1.Alias || left }, right: { id: right, name: theme2.Alias || right } });
}
//Restore profile visLocked pairs
function restoreVisLockedItems(items) {
    for (let i = 0; i < items.length; i++) {
        clusterGroup.getObjectByName(items[i]).visible = true;
        log(clusterGroup.getObjectByName(items[i]));
        log(clusterGroup.getObjectByName(items[i]).visible);
        let tempSelected = items[i].replace('Tube=', '').split(',');
        if (!mainCfg.showNewRelationDisplay) {
            viewFunctions.addThemeRelationDomNode(tempSelected);
        } else {
            epmViewFunctions.addThemeRelation(tempSelected);
        }
    }
    if (!mainCfg.showNewRelationDisplay) {
        document.getElementById('visLockPanel').style.visibility = 'visible';
    }
}
//Remove VisLocked Pair from list
function removeVisLock() {
    let str = this.id;
    let visLockedTubeItem = 'Tube=' + str;
    let itemIndex = visLockedTubes.indexOf(visLockedTubeItem);
    visLockedTubes.splice(itemIndex, 1);
    let arr = str.split(",");
    visLockedTubes.forEach(function(item) {
        if(item.includes(arr[0])) {
            arr.shift();
        }
        if(item.includes(arr[arr.length-1])) {
            arr.splice(arr.length-1, 1);
        }
    });
    if(arr.length>0) {
        arr.forEach( function(item) {
            for (let i = 0; i < ThemeNameRegistry.length; i++) {
                if(ThemeNameRegistry[i].Name==item.trim()) {
                    ThemeNameRegistry[i].VisibilityLock = false;
                }
            }
        });
    }

    if (!mainCfg.showNewRelationDisplay) {
        const visLockPanel = document.getElementById('visLockPanel');
        visLockPanel.removeChild(document.getElementById(str));
        if (visLockPanel.children.length < 1) {
            visLockPanel.style.visibility = 'hidden';
        }
    } else {
        eventBus.publish('delete:theme-relation',{ id: str });
    }
    alertInformation('Visibilty Lock disabled on ' + arr[0] + ' and ' + arr[1]);
    distanceCheck();
    checkScoreVisibility();
    reportVisisbleGenesTotal();

}

function showVisLockedItems() {
    for (let i = 0; i < optimalItemsSize(); i++) {
        if (ThemeNameRegistry[i].VisibilityLock == true) {
            if(boxViewActive) {
                boxViewGroup.children[i].element.style.display = 'initial';
                spriteTextGroup.children[i].visible = true;
                clusterGroup.children[i].visible = false;
                if(autoAnnotationActive == true) {
                    spriteTextGroup.children[i].element.style.display = 'flex';
                    spriteTextGroup.children[i].element.style.flexDirection = 'column';
                    spriteTextGroup.children[i].element.style.alignItems = 'center';
                } else {
                    spriteTextGroup.children[i].element.style.display = 'initial';
                }
                if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                    dragLines.children[i].visible = true;
                }
            } else {
                clusterGroup.children[i].visible = true;
                spriteTextGroup.children[i].visible = true;
                boxViewGroup.children[i].element.style.display = 'none';
                if(autoAnnotationActive == true) {
                    spriteTextGroup.children[i].element.style.display = 'flex';
                    spriteTextGroup.children[i].element.style.flexDirection = 'column';
                    spriteTextGroup.children[i].element.style.alignItems = 'center';
                } else {
                    spriteTextGroup.children[i].element.style.display = 'initial';
                }
                cubeGroup.children[i].visible = true;
                if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                    dragLines.children[i].visible = true;
                }
            }
        }
        if(ThemeNameRegistry[i].GroupHidden == true) {
            log('GroupHidden -> true');
            log(ThemeNameRegistry[i].Name);
            if( boxViewActive ) {
                clusterGroup.children[i].visible = false;
                boxViewGroup.children[i].element.style.display = 'none';
                spriteTextGroup.children[i].visible = false;
                spriteTextGroup.children[i].element.style.display = 'none';
                if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                    dragLines.children[i].visible = false;
                }
            } else {
                clusterGroup.children[i].visible = false;
                boxViewGroup.children[i].element.style.display = 'none';
                spriteTextGroup.children[i].visible = false;
                spriteTextGroup.children[i].element.style.display = 'none';
                cubeGroup.children[i].visible = false;
                if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                    dragLines.children[i].visible = false;
                }
            }
        }
    }
    visLockedTubes.forEach(function(item) {
        clusterGroup.getObjectByName(item).visible = true;
    });
    groupHiddenTubes.forEach(function(item) {
        clusterGroup.getObjectByName(item).visible = false;
    });
}

function customNameSearch(needle) {
    let nameGroup = customClusterNames;
    for (let i = 0; i < nameGroup.length; i++) {
        if (needle == nameGroup[i].temp1) {
            return nameGroup[i].temp2;
        } else if (needle == nameGroup[i].temp2) {
            return nameGroup[i].temp1;
        } else {
            return 0;
        }
    }
}

//Image Search Engine Start
function imageSearch( query ) {
    log('Query Search Initiated');
    log(query);
    let queryString = 'pathway AND ';
    let num = 1;
    if (query.length > 3 && query.length < 6) {
        num = 2;
    } else if (query.length > 6) {
        num = 5;
    }
    for (let i = 0; i < query.length; i++) {
        if (i == 0) {
            queryString += query[i];
        } else {
            if (i > num) {
                queryString += ' OR ' + query[i];
            } else {
                queryString += ' AND ' + query[i];
            }
        }
        if (i == 12) {
            break;
        }
    }
    log(queryString);
    log(query);
    window.open(mainCfg.composeImgSearchLink(queryString));
}
/*FunctionJs End*/

/*MappingJS*/

var conObj = [];
var conScores = [];

function openConceptMap(cluster) {
    log('open concept mapping');
    // datguiToggle();
    if (mainCfg.useStats) {
        document.getElementById('stats').style.visibility = 'hidden';
        console.log('stats hidden')
    }
    if($('#dialog-wheel').hasClass('ui-dialog-content')) {
        if($('#dialog-wheel').dialog('isOpen')) {
            $('#dialog-wheel').dialog('close');
        }
    } else {
        log('wheel has not initialized yet -> 3467');
    }
    if($('#dialog-wheel').dialog('isOpen')) {

        $('#dialog-wheel').dialog('close')

    }
    if (document.getElementById('geneViewSelection')) {
        closeGeneViewSelection();
    }
    let conCoords = [];
    var PM3 = 8;
    if (clusterGroup.getObjectByName(cluster)) {
        // what was the intent?
        // cluster = cluster;

        //Answer:  ¯\_(ツ)_/¯  I have no idea.
        //The only thing i can think of is if there was a variable
        //being reset at one point and i couldnt find where...but this was an original
        //feature created a long time ago....
    } else {
        log('Error -> could not find cluster reference within the data file');
        log('Cluster Name Parameter -> ' + cluster);
        log('Returned Results of Custom Name Search -> ' + customNameSearch(cluster));
        // closeCanvas3();
        return false;
    }
    for (let i = 0; i < optimalItemsSize(); i++) {
        if (data[i].Name == cluster) {
            conObj = Object.entries(data[i].Concepts);
            break;
        }
    }
    if (conObj.length > 3) {
        for (let i = 0; i < conObj.length; i++) {
            conCoords = conObj[i].pop().split(',');
            conObj[i] = conObj[i].concat(conCoords);
            conScores.push(conObj[i][1]);
        }
        $('#concept-map').dialog('open');
        //Get textsize based on scores
        let secDivider = Math.round(conScores.length / 2);
        conScores.sort(function(a, b) { return a - b });
        log(renderer3.info.memory);
        for (let i = 0; i < conObj.length; i++) {
            let geoSize = 1.2;
            let matColor = 0x0030ff;
            if (i < secDivider) {
                geoSize = 1.5;
                matColor = 0x00ff00;
            }
            let mesh = new THREE.Object3D();
            mesh.add(new THREE.LineSegments(
                new THREE.Geometry(),
                new THREE.LineBasicMaterial()
            ));
            mesh.add(new THREE.Mesh(
                new THREE.Geometry(),
                new THREE.MeshBasicMaterial({
                    color: matColor
                })
            ));
            let loader = new THREE.FontLoader();
            loader.load(`../${mainCfg.assetsPath}/font/optimer_bold.typeface.json`, function(font) {
                let geometry = new THREE.TextGeometry(conObj[i][0], {
                    font: font,
                    size: geoSize,
                    height: 0.1,
                    curveSegments: 5
                });
                geometry.center();

                updateGroupGeometry(mesh, geometry);
            });
            //Set userData
            mesh.name = conObj[i][0];
            mesh.userData.Score = conObj[i][1];
            mesh.userData.IndexKey = i;
            conceptsSorted.push({ name: mesh.name, score: mesh.userData.Score });
            //Hide the wireframe
            mesh.children[0].visible = false;
            //set position
            mesh.position.set(conObj[i][2] * PM3, conObj[i][3] * PM3, conObj[i][4] * PM3);
            meshConceptGroup.add(mesh);
        }
        conceptsSorted.sort(function(a, b) {
            return a.score - b.score;
        });
        drawGeneLines();
        scene3.add(meshConceptGroup);
        scene3.add(lineConceptGroup);
        //Reset vars
        secDivider = 0;
        conScores = [];
        // var render = function() {
        //     requestAnimationFrame(render);
        //     controls3.update()
        //     for (let i = 0; i < meshConceptGroup.children.length; i++) {
        //         meshConceptGroup.children[i].quaternion.copy(camera3.quaternion);
        //     }
        //     renderer3.render(scene3, camera3);
        // };
        // render();


        function drawGeneLines() {
            let len = meshConceptGroup.children.length;
            for (let z = 0; z < len; z++) {
                for (let i = 0; i < len; i++) {
                    if (i <= z) { continue; }

                    let material = new THREE.LineBasicMaterial({
                        color: 0x00ff00,
                        transparent: true,
                        opacity: 0.1,
                    });
                    let geometry = new THREE.Geometry();
                    geometry.vertices.push(
                        meshConceptGroup.children[z].position,
                        meshConceptGroup.children[i].position
                    );
                    let tempLine = new THREE.LineSegments(geometry, material);
                    tempLine.userData.lineLength = geometry.vertices[0].distanceTo(geometry.vertices[1]);
                    let curve = new THREE.CatmullRomCurve3([
                        meshConceptGroup.children[z].position,
                        meshConceptGroup.children[i].position
                    ]);
                    let tubeGeometry = new THREE.TubeGeometry(
                        curve, //path
                        1, //segments
                        0.2, //radius - Min 0.2 Max 2.0 size depends on shared genes
                        3 //radiusSegments
                        //true //open end
                    );
                    let tubeMaterial = new THREE.MeshBasicMaterial({
                        transparent: true,
                        color: 0xffffff,
                        opacity: 0.4
                    });
                    let line = new THREE.Mesh(tubeGeometry, tubeMaterial);
                    line.userData.lineLength = tempLine.userData.lineLength;
                    line.userData.endpoints = [meshConceptGroup.children[z].name, meshConceptGroup.children[i].name];
                    lineConceptGroup.add(line);
                }
            }
            for (let i = 0; i < lineConceptGroup.children.length; i++) {
                lineConceptGroup.children[i].name = 'line' + i;
                conceptLinesSorted.push({ name: lineConceptGroup.children[i].name, lineLength: lineConceptGroup.children[i].userData.lineLength });
            }
            conceptLinesSorted.sort(function(a, b) {
                return b.lineLength - a.lineLength;
            });
        }

        let tempVal3 = conceptMapParameters.score;
        tempVal3 = c3ParamsToPercent(tempVal3, 'score');
        for (let i = 0; i < meshConceptGroup.children.length; i++) {
            if (parseFloat(meshConceptGroup.children[i].userData.Score) <= tempVal3) {
                meshConceptGroup.children[i].visible = true;
            } else if (parseFloat(meshConceptGroup.children[i].userData.Score) > tempVal3) {
                meshConceptGroup.children[i].visible = false;
            }
        }
        distanceParam = 100 - conceptMapParameters.distance;
        distanceParam = c3ParamsToPercent(distanceParam, 'distance');
        conceptEPVisChk(distanceParam);

        //Populate HUD2
        hud2Population(selectedCluster.userData.Concepts);
    } else {
        alertInformation(cluster + ' does not have enough concepts to map.');
        // closeCanvas3();
    }
}
//Close canvas3...Obviously...
function closeCanvas3() {
    // for (let i = 0; i < meshConceptGroup.children.length; i) {
    while( meshConceptGroup.children.length > 0 ) {
        meshConceptGroup.children[0].children[0].geometry.dispose();
        meshConceptGroup.children[0].children[0].material.dispose();
        meshConceptGroup.children[0].children[1].geometry.dispose();
        meshConceptGroup.children[0].children[1].material.dispose();
        meshConceptGroup.remove(meshConceptGroup.children[0]);
    }
    // for (let i = 0; i < lineConceptGroup.children.length; i) {
    while( lineConceptGroup.children.length > 0 ) {
        lineConceptGroup.children[0].geometry.dispose();
        lineConceptGroup.children[0].material.dispose();
        lineConceptGroup.remove(lineConceptGroup.children[0]);
    }
    //Clear to global variable sorted arrays
    conceptsSorted = [];
    conceptLinesSorted = [];
    distanceParam = undefined;
    // hud2.style.visibility = 'hidden';
    // document.getElementById('concept-map').style.visibility = 'hidden';
    document.getElementById('hud2-inner-wrap').innerHTML = '';
    // canvas3.style.visibility = 'hidden';
    // canvas3CloseBtn.style.visibility = 'hidden';
    // if (mainCfg.useStats) {
    //     document.getElementById('stats').style.visibility = 'initial';
    // }
    // datguiToggle();
}
//toggling datgui menus when showing canvas3
function datguiToggle() {
    if (datGui3.style.display == 'none') {
        $(datGui1).slideToggle();
        $(datGui3).slideToggle();
    } else {
        $(datGui3).slideToggle();
        $(datGui1).slideToggle();
    }
} //toggle end

function clickSelect3() {
    if (mainCfg.useSessionExpirationCheck) {
        if (!$('#dialog-session-confirm').hasClass('ui-dialog-content')) {
            log('session-confirm has not initialized yet -> 3712');
            return;
        } else if ($('#dialog-session-confirm').dialog('isOpen')) {
            log('you must resubmit your login credetials before proceeding...');
            return;
        }
    }
    if (INTERSECTED3 && INTERSECTED3 !== 0) {
        selectedMesh3 = INTERSECTED3;
        log(selectedMesh3.userData.endpoints);
        let abstractData = {'Key':Key, 'a':selectedMesh3.userData.endpoints[0], 'b':selectedMesh3.userData.endpoints[1], 'c':'Concept', 'ThemeConcepts':selectedCluster.userData.Concepts, 'Version': ( typeof( mainCfg.wustlAbstractVersion ) !== 'undefined' ) ? true : false };
        localStorage.setItem('abstractData', JSON.stringify(abstractData));
        controls3.saveState();
        window.open(mainCfg.composeAbstractLink({
            conceptId: selectedMesh3.userData.endpoints[0],
            conceptName: selectedMesh3.userData.endpoints[1]
        }));
        controls3.reset();
    }
}

//Convert sliders to evenly distributed percentage based sliders
function c3ParamsToPercent(num, filter) {
    if (filter == 'distance') {
        let percentageAmount = Math.round((num / 100) * lineConceptGroup.children.length);
        if (percentageAmount != 0) {
            percentageAmount -= 1;
        }
        returnedScore = conceptLinesSorted[percentageAmount].lineLength;
    } else if (filter == 'score') {
        let percentageAmount = Math.round((num / 100) * meshConceptGroup.children.length);
        if (percentageAmount != 0) {
            percentageAmount -= 1;
        }
        console.log(conceptLinesSorted)
        returnedScore = parseFloat(conceptsSorted[percentageAmount].score);
    } else {
        log('c3ParamsToPercent ERROR!!!');
        log('filter != distance || score');
    }
    return returnedScore;
}
//Show/Hide via score and distance
function conceptEPVisChk(distance) {
    for (let i = 0; i < lineConceptGroup.children.length; i++) {
        let temp1 = lineConceptGroup.children[i].userData.endpoints[0];
        let temp2 = lineConceptGroup.children[i].userData.endpoints[1];
        if (meshConceptGroup.getObjectByName(temp1) && meshConceptGroup.getObjectByName(temp2)) {
            if (meshConceptGroup.getObjectByName(temp1).visible == false ||
                meshConceptGroup.getObjectByName(temp2).visible == false ||
                lineConceptGroup.children[i].userData.lineLength > distance) {
                lineConceptGroup.children[i].visible = false;
            } else if (meshConceptGroup.getObjectByName(temp1).visible == true && meshConceptGroup.getObjectByName(temp2).visible == true) {
                if (lineConceptGroup.children[i].userData.lineLength <= distance) {
                    lineConceptGroup.children[i].visible = true;
                }
            }
        } else {
            log('conceptEPVisChk error!!!');
            log('temp1 -> ' + temp1);
            log('temp2 -> ' + temp2);
            log('i -> ' + i);
        }
    }
}

function hud2Population(arr) {
    let hud2InnerWrap = document.getElementById('hud2-inner-wrap');
    for (let i = 0; i < arr.length; i++) {
        let conDiv = document.createElement('DIV');
        conDiv.classList.add('concept-item');
        conDiv.id = arr[i];
        conDiv.innerHTML = arr[i];
        conDiv.addEventListener('click', hud2Select);
        hud2InnerWrap.appendChild(conDiv);
    }
}

//HUD2 Concept Select for Custom Search
function hud2Select(elmnt) {
    let conElmnt = elmnt.target;
    if (conElmnt.classList.contains('selected-concept')) {
        conElmnt.classList.remove('selected-concept');
    } else {
        conElmnt.classList.add('selected-concept');
    }
}

function customSearchConstruct() {
    let customQueryObj = [];
    if ( document.getElementById('customTermCheckBox').checked ) {
        const userEnteredTerms = document.getElementById('customTermTextBox').value.split(',').join(' AND ');
        customQueryObj.push( userEnteredTerms );
    }
    let hud2Arr = document.getElementById('hud2-inner-wrap').children;
    for (let i = 0; i < hud2Arr.length; i++) {
        if (hud2Arr[i].classList.contains('selected-concept')) {
            customQueryObj.push(hud2Arr[i].id);
        }
    }
    if (customQueryObj.length < 2 ) {
        alertInformation('Query terms need to be selected before the search can begin');
    } else {
        imageSearch( customQueryObj );
    }
}
/*MappingJS End*/
/* GeneJS */
var coords = {};

function eventFilterController(id, parent) {
    if (mainCfg.useNewThemePopup) {
        if (geneViewOpen) {
            mainCfg.components.entityPopup.hide(true);
        }
        mainCfg.components.entityPopup.show({
            hideCb: closeGeneViewSelection,
            entity: id,
            position: parent.position,
            themes: data,
            entities: data2,
            selectedThemes: parent.themes,
            abstractQuery,
            selectedCluster,
            minHeight: parent.height
        });

        geneViewOpen = true;
        return;
    }
    if (geneViewOpen) {
        log('eventFilterController inside if');
        closeGeneViewSelection();
    }
    log('eventFilterController');
    geneViewOpen = true;
    let geneMenu = document.createElement('DIV');
    geneMenu.id = 'geneViewSelection';
    geneMenu.style.position = 'absolute';
    let x = event.clientX;
    let y = event.clientY;

    geneMenu.style.left = x + 10 + 'px';
    geneMenu.style.top = y + 15 + 'px';

    let clusterDisplay = document.getElementById('clusterInfo');
    let edgeDisplay = document.getElementById('genesPopupDisplay');

    geneMenu.onmouseover = function() {
        GLOBAL_STATE.mouseOverDiv = true;
    }
    geneMenu.onmouseout = function() {
        GLOBAL_STATE.mouseOverDiv = false;
    }
    //check which display is being utilized
    if (clusterDisplay.style.visibility == 'visible' || mainCfg.useNewThemePopup) {
        geneMenu.innerHTML = (bioExplorerProjectType === true) ? '<div class="geneViewSelectionHeader">' +
            '<span onclick="closeGeneViewSelection()">Close <i class="fa fa-times-circle" aria-hidden="true"></i></span></div>' +
            '<span onclick="geneToClusterConcepts(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Term-To-Theme</span>' +
            '<span onclick="geneToConcepts(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Term-To-All</span>' : '<div class="geneViewSelectionHeader">' +
            '<span onclick="closeGeneViewSelection()">Close <i class="fa fa-times-circle" aria-hidden="true"></i></span></div>' +
            '<span onclick="geneToClusterConcepts(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Entity-To-Theme</span>' +
            '<span onclick="geneToConcepts(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Entity-To-All</span>';
    } else if (edgeDisplay.style.visibility == 'visible') {
        geneMenu.innerHTML = (bioExplorerProjectType === true) ? '<div class="geneViewSelectionHeader">' +
            '<span onclick="closeGeneViewSelection()">Close <i class="fa fa-times-circle" aria-hidden="true"></i></span></div>' +
            '<span onclick="geneToTube(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Term-To-Themes</span>' +
            '<span onclick="geneToConcepts(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Term-To-All</span>' : '<div class="geneViewSelectionHeader">' +
            '<span onclick="closeGeneViewSelection()">Close <i class="fa fa-times-circle" aria-hidden="true"></i></span></div>' +
            '<span onclick="geneToTube(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Entity-To-Themes</span>' +
            '<span onclick="geneToConcepts(&apos;' +
            id + '&apos;)" class="geneViewSelectionItem">Entity-To-All</span>';
    }

    document.body.appendChild(geneMenu);
}

function closeGeneViewSelection() {
    if (mainCfg.useNewThemePopup) {
        mainCfg.components.entityPopup.hide();
    } else {
        let geneMenu = document.getElementById('geneViewSelection');
        document.body.removeChild(geneMenu);
    }
    geneViewOpen = false;
}

//Sort Object and get top scoring concepts
function sortGenes(obj) {
    return Object.keys(obj).sort(function(a, b) {
        return obj[b] - obj[a];
    });
}

function reportVisisbleGenesTotal() {
    let geneTotal = 0;
    let genes = [];

    let allGenes = [];
    let allGeneTotal = (ValidGeneCount == null) ? [...new Set(data.map(item => Object.keys(item.Genes)).flat())].length : ValidGeneCount;
    
    for(let i=0;i<optimalItemsSize();i++) {

        if(clusterGroup.children[i].visible == true) {

            genes = genes.concat(clusterGroup.children[i].userData.Genes);

        }

    }

    allGenes = allGenes.concat(genes);

    genes = removeDuplicates(genes);
    allGenes = removeDuplicates(allGenes);
    geneTotal = genes.length;

    if (mainCfg.showBottomPanel) {
        mainCfg.components.bottomPanel.entitiesInfo = {
            visibleEntitiesNum: geneTotal,
            totalEntitiesNum: allGeneTotal
        };
    }
    if (!mainCfg.showVisDataPanel) {
        document.getElementById('visible-gene-total').innerText = geneTotal.toString();
        document.getElementById('all-gene-total').innerText = allGeneTotal.toString();
    }


}

function removeDuplicates(array) {

  let unique = {};
  array.forEach(function(i) {

    if(!unique[i]) {

      unique[i] = true;

    }

  });

  return Object.keys(unique);

}

function applyFilterMasking( filter ) {
    const depthToggle = parameters.c7;
    parameters.c7 = false;
    removeConceptTotalFromLabels();
    restoreDefaultHudConceptTotals();
    let filterMode = parameters.j0;
    for ( let i = 0; i < optimalItemsSize(); i++ ) {
        delete clusterGroup.children[ i ].userData.filterHidden;
        delete cubeGroup.children[ i ].userData.filterHidden;
        delete spriteTextGroup.children[ i ].userData.filterHidden;
    }
    appliedFilter = parameters.j;
    for ( let i = 0; i < optimalItemsSize(); i++ ) {
        let found = false;
        let foundCount = 0;
        for ( let z = 0; z < filter.length; z++ ) {
            if ( clusterGroup.children[ i ].userData.Concepts.includes( filter[ z ] ) === true ) {
                foundCount++;
                found = true;
                if ( filterMode !== FILTER_MODES.DIFFERENCE ) {
                    break;
                }
            }
        }
        const isIntersectionFiltering = filterMode === FILTER_MODES.INTERSECTION && found === false;
        const isDifferenceFiltering = filterMode === FILTER_MODES.DIFFERENCE && found === true && filter.length === foundCount;
        const isCummonFiltering = filterMode !== FILTER_MODES.DIFFERENCE && found === false;
        if ( isDifferenceFiltering || isIntersectionFiltering || isCummonFiltering ) {
            clusterGroup.children[ i ].visible = false;
            clusterGroup.children[ i ].userData.filterHidden = true;
            cubeGroup.children[ i ].visible = false;
            cubeGroup.children[ i ].userData.filterHidden = true;
            spriteTextGroup.children[ i ].visible = false;
            spriteTextGroup.children[i].element.style.display = 'none';
            spriteTextGroup.children[ i ].userData.filterHidden = true;
            if( typeof dragLines != 'undefined' && typeof dragLines.children[i] != 'undefined' ) {
                dragLines.children[i].visible = false;
                dragLines.children[i].userData.filterHidden = true;
            }
            if(typeof boxViewGroup.children[i] !== 'undefined') {
                boxViewGroup.children[i].element.style.display = 'none';
            }
        }
    }
    if ( mainCfg.showBottomPanel ) {
        let commonConcepts = {},
            uniqueProjectConcepts = {},
            uniqueFilterConceptsNum;
        data.forEach( theme => {
            for ( let concept in theme.Concepts ) {
                if ( filter.indexOf( concept ) === -1 ) {
                    uniqueProjectConcepts[ concept ] = true;
                } else {
                    commonConcepts[ concept ] = true;
                }
            }
        } );
        const filterConceptsNumber = filter.filter( f => f ).length;
        const commonConceptsNum = Object.keys( commonConcepts ).length;
        const uniqueProjectConceptsNum = Object.keys( uniqueProjectConcepts ).length;
        uniqueFilterConceptsNum = filterConceptsNumber - commonConceptsNum;
        mainCfg.components.bottomPanel.filterInfo = {
            filterName: appliedFilter,
            commonConceptsNum,
            uniqueProjectConceptsNum,
            uniqueFilterConceptsNum
        }
    }
    if ( mainCfg.useNewThemePopup ) {
        mainCfg.components.entityPopup.filter = { concepts: filter, mode: filterMode };
    }
    parameterToPercentage( 100 - parameters.c3, 'score' );
    distanceCheck();
    reportVisisbleGenesTotal();
    if( clusterInfoShown === true ) {
        popupClusterInfoHide();
    }
    parameters.c7 = depthToggle;
}

function applyFilterHalos( bool = false ) {
    if( bool === false ) {
        removeFilterHalos();
    } else {
        if( filterHaloGroup.children.length > 0 ) {
            removeFilterHalos();
        }
        const visibleSpheres = [];
        const themes = [].slice.call( clusterGroup.children );
        themes.forEach(( theme, index ) => {
            if( theme.visible === true && index < optiLength ) {
                visibleSpheres.push( theme.name );
            }
        });
        addfilterHalos( visibleSpheres );
    }
}

function addfilterHalos( array ) {
    array.forEach( name => {
        const position = clusterGroup.getObjectByName( name ).position;
        const radius = clusterGroup.getObjectByName( name ).userData.radius;
        const element = document.createElement('DIV');
        element.classList.add('filter-halo-element');
        element.id = `filter-halo-${name}`;
        element.style.height = `${radius * 2 + 15}px`;
        element.style.width = `${radius * 2 + 15}px`;
        const haloColor = getHaloColor( name );
        element.style.backgroundImage = `radial-gradient(circle, rgba(0,0,0,0.1) 50%, ${haloColor})`;
        const haloObject = new THREE.CSS3DObject( element );
        haloObject.name = `filterHalo=${name}`;
        haloObject.userData.themeReference = name;
        haloObject.position.set( position.x, position.y, position.z );
        filterHaloGroup.add( haloObject );
    });
}

function removeFilterHalos() {
    const halos = [].slice.call( filterHaloGroup.children );
    halos.forEach( halo => {
        filterHaloGroup.remove( halo );
    });
}

function getHaloColor( name ) {
    /* ColorReference: blue, green, orange, red */
    const colors = ['#0034f1', '#00ff00', '#ffa500', '#ff0000'];
    const ratioValues = document.getElementById( name ).getElementsByClassName('hud-concept-count')[0].innerText.split('/');
    const percentage = ratioValues[0] / ratioValues[1];
    if( percentage < 0.25 ) {
        return colors[0];
    } else if( percentage < 0.5 ) {
        return colors[1];
    } else if( percentage < 0.75 ) {
        return colors[2];
    } else {
        return colors[3];
    }
}

function haloVisiblityCheck() {
    filterHaloGroup.children.forEach( halo => {
        halo.visible = clusterGroup.getObjectByName( halo.userData.themeReference ).visible;
    });
}

function filterConceptsInHud(filter) {
    if (mainCfg.showVisDataPanel) {
        // @todo - figure out how filtered concepts will look like in new panel
        // KC-2226
        return;
    }
    let tempConElement = [].slice.call(document.getElementsByClassName('concepts'));
    tempConElement.forEach( element => {
        let tempTextArray = element.innerText.split(',');
        let hiddenText = 0;
        let filteredInnerText = [];
        tempTextArray.forEach(( item ) => {
            if( filter.indexOf( item ) < 0 && parameters.j0 === 'Intersection' ) {
                filteredInnerText.push(`<span class="filter-text">${item}</span>`);
                hiddenText++;
            } else if( filter.indexOf( item ) >= 0 && parameters.j0 === 'Difference' ) {
                filteredInnerText.push(`<span class="filter-text">${item}</span>`);
                hiddenText++;
            } else {
                filteredInnerText.push( item );
            }
        });
        element.innerHTML = filteredInnerText.join(', ');
        if( hiddenText === filteredInnerText.length ) {
            element.parentElement.classList.add('filter-parent-hide');
        } else {
            let remainingConceptsCount = filteredInnerText.length - hiddenText;
            appendConceptTotalToLabels( remainingConceptsCount, filteredInnerText.length, element.parentElement.id );
            appendConceptTotalsToHud( remainingConceptsCount, filteredInnerText.length, element.parentElement.id );
        }
    });
}

function appendConceptTotalToLabels( overCount, underCount, name ) {
    spriteTextGroup.getObjectByName(`spriteText=${name}`).element.innerHTML += `<span class="label-concept-count"> (${overCount}/${underCount})</span>`;
}

function appendConceptTotalsToHud( overCount, underCount, name ) {
    let hudElement = document.getElementById( name ).getElementsByClassName('theme-concept-total')[0];
    hudElement.classList.add('hide-default-count');
    let parent = hudElement.parentElement;
    parent.innerHTML += `<span class="label-concept-count hud-concept-count"> ${overCount}/${underCount}</span>`;    
}

function removeConceptTotalFromLabels() {
    const conceptTotals = [].slice.call( document.getElementsByClassName('label-concept-count'));
    conceptTotals.forEach( element => {
        element.parentElement.removeChild( element );
    });
}

function restoreDefaultHudConceptTotals() {
    const hudDefaultConceptTotals = [].slice.call( document.getElementsByClassName('hide-default-count'));
    hudDefaultConceptTotals.forEach( element => {
        element.classList.remove('hide-default-count');
    });
}

function filterInSphereText(filter) {
    let filterMode = parameters.j0;
    if (mainCfg.showVisDataPanel) {
        const themes = mainCfg.components.visDataPanel._model.themes.map(({Name})=>Name);
        const useIntersection = filterMode === FILTER_MODES.INTERSECTION;
        themes.forEach((themeName)=>{
            const cube = cubeGroup.getObjectByName(themeName);
            if (cube === undefined) {
                log(`Unable to find Group, named ${themeName}`);
                return;
            }
            let cubeConcepts = cube.userData.concepts;
            let filteredConcepts = [];
            cubeConcepts.forEach((concept) => {
                if (useIntersection) {
                    filteredConcepts.push(filter.includes(concept) ? concept : '');
                } else {
                    filteredConcepts.push(!filter.includes(concept) ? concept : '');
                }
            });
            alterCubeText(filteredConcepts, themeName);
        });
        return;
    }
    let hudThemeView = document.getElementById('theme-view');
    let hudThemeViewChildren = [].slice.call(hudThemeView.children);
    let visibleChildren = [];
    for(let i=0;i<hudThemeViewChildren.length;i++) {
        if(hudThemeViewChildren[i].classList.contains('filter-parent-hide') === false) {
            visibleChildren.push(hudThemeViewChildren[i].id);
        }
    }
    visibleChildren.forEach(function(item) {
        let cubeConcepts = cubeGroup.getObjectByName(item).userData.concepts;
        let themeConceptsTotal = clusterGroup.getObjectByName(item).userData.concepts
        let filteredConcepts = [];
        cubeConcepts.forEach(function(concept) {
            if( filter.indexOf(concept) < 0 && filterMode === 'Intersection' ) {
                filteredConcepts.push('');
            } else if( filter.indexOf(concept) < 0 && filterMode === 'Difference' ) {
                filteredConcepts.push(concept);
            } else if( filter.indexOf(concept) >= 0 && filterMode === 'Difference' ) {
                filteredConcepts.push('');
            } else {
                filteredConcepts.push(concept);
            }
        });  
        alterCubeText(filteredConcepts, item);
    });
}

function alterCubeText(concepts, cube) {
    let canvas = document.createElement("CANVAS");
    canvas.width = 512;
    canvas.height = 512;
    let ctx = canvas.getContext("2d");
    let textColor = '#ffffff';
    let weight = '900';
    let fontFamily = '"Arial Black", Gadget, sans-serif';
    let fontSize = 60;
    let fontVariant = 'normal';
    ctx.fillStyle = textColor;
    ctx.font = `${fontVariant} ${weight} ${fontSize}px ${fontFamily}`;
    let txtMeasurements = [];
    for (let x = 0; x < concepts.length; x++) {
        txtMeasurements.push(ctx.measureText(concepts[x]).width);
    }
    let max = txtMeasurements.reduce(function(a, b) {
        return Math.max(a, b);
    });
    let multiplier = 1;
    for (let w = 0; w < concepts.length; w++) {
        let txtWidth = ctx.measureText(concepts[w]).width;
        ctx.textAlign = 'center';
        ctx.fillText(concepts[w], 256, ((canvas.height / 2) / concepts.length + fontSize / 4) * multiplier + fontSize * 1.5, canvas.width - 1);
        multiplier++;
    }
    let texture = new THREE.Texture(canvas);
    texture.needsUpdate = true;
    texture.anisotropy = renderer.capabilities.getMaxAnisotropy();

    cubeGroup.getObjectByName(cube).material[4].map = texture;
    cubeGroup.getObjectByName(cube).userData.visibleConcepts = concepts;
}

function removeFilterMasking() {
    appliedFilter = 'None';
    if (mainCfg.useNewThemePopup) {
        mainCfg.components.entityPopup.filter = [];
    }
    if (mainCfg.showBottomPanel) {
        mainCfg.components.bottomPanel.filterInfo = {filterName: ''}
    }
    for (let i=0; i<data.length; i++) {
        if (clusterGroup.children[i] && 'userData' in clusterGroup.children[i]) {
            delete clusterGroup.children[i].userData.filterHidden;
        }
        if (cubeGroup.children[i] && 'userData' in cubeGroup.children[i]) {
            delete cubeGroup.children[i].userData.filterHidden;
        }
        if (spriteTextGroup.children[i] && 'userData' in spriteTextGroup.children[i]) {
            delete spriteTextGroup.children[i].userData.filterHidden;
        }
    }
    removeConceptTotalFromLabels();
    restoreDefaultHudConceptTotals();
    parameterToPercentage(100 - parameters.c3, 'score');
    distanceCheck();
    reportVisisbleGenesTotal();
    applyFilterHalos();
    if( clusterInfoShown === true ) {
        popupClusterInfoHide();
    }
}

function removeHudFilters() {
    log('Removing filters from Hud');
    if (mainCfg.showVisDataPanel) {
        // @todo - figure out how to remove filters from concepts in new panel
        // KC-2226
        return;
    }
    // let tempConElement = [].slice.call(document.getElementsByClassName('concepts'));
    let tempConElement = Array.from(document.getElementsByClassName('concepts'));
    tempConElement.forEach(function(element, index) {
        element.parentElement.classList.remove('filter-parent-hide');
        element.innerText = '';
        for (let y = 0; y < c[index].Concepts.length; y++) {
            (y === c[index].Concepts.length - 1) ? element.innerText += c[index].Concepts[y] : element.innerText += c[index].Concepts[y] + ', ';
        }
    });
}

function removeCubeFiltering() {
    if (mainCfg.showVisDataPanel) {
        const themes = mainCfg.components.visDataPanel._model.themes.map(({Name})=>Name);
        themes.forEach((themeName)=>{
            const cube = cubeGroup.getObjectByName(themeName);
            if (cube === undefined) {
                log(`Unable to find Group, named ${themeName}`);
                return;
            }
            if( typeof( cube.userData.visibleConcepts ) !== 'undefined' ) {
                delete cube.userData.visibleConcepts;
            }
            let cubeConcepts = cube.userData.concepts;
            alterCubeText(cubeConcepts.slice(0), themeName);
        });
        return;
    }
    let hudThemeView = document.getElementById('theme-view');
    let hudThemeViewChildren = [].slice.call(hudThemeView.children);
    let visibleChildren = [];
    for(let i=0;i<hudThemeViewChildren.length;i++) {
        if(hudThemeViewChildren[i].classList.contains('filter-parent-hide') === false) {
            visibleChildren.push(hudThemeViewChildren[i].id);
        }
    }
    visibleChildren.forEach(function(item) {
        let cubeConcepts = cubeGroup.getObjectByName(item).userData.concepts;
        alterCubeText(cubeConcepts, item);
    });
}

function filterPopup(filter) {
    log('filtering popup table');
    console.log(filter)
    let filterMode = parameters.j0;
    let tempObj = {};
    for(let i=0;i<optimalItemsSize();i++) {
        if(selectedCluster.name == data[i].Name) {
            tempObj = Object.assign({}, data[i]);
            console.log(tempObj);
            break;
        } else {
            console.log('filterPopup: data/selectedCluster name mismatch');
        }
    }
    let tempGenes = Object.keys(tempObj.Genes);
    let visibleGenes = [];
    tempGenes.forEach( gene => {        
        let fCount = 0;
        if( filterMode === 'Intersection' ) {
            for( let i=0; i<filter.length; i++ ) {
                if( typeof( tempObj.Genes[gene].Concepts[filter[i]] ) != "undefined" ) {
                    visibleGenes.push(gene);
                    break;                    
                }
            }
        } else if( filterMode === 'Difference' ) {            
            let innerConcepts = Object.keys( tempObj.Genes[gene].Concepts );
            for( let i=0; i<innerConcepts.length; i++ ) {                
                if( filter.indexOf( innerConcepts[i] ) >= 0 ) {
                    fCount++;
                } else {                                        
                    break;
                }
            }
            if( fCount < innerConcepts.length ) {
                visibleGenes.push( gene );
            }
        }
    });
    let popupRows = [].slice.call(document.getElementsByClassName('entity-table-row'));
    popupRows.forEach(function(row) {
        if( visibleGenes.indexOf( row.children[0].textContent ) < 0 ) {
            row.classList.add('v-none');
        }
    });
    filterPopupConcepts();
    modifyPopupCounts();
}

function filterPopupConcepts() {
    let visibleConcepts = document.getElementById( selectedCluster.name ).getElementsByClassName('concepts')[0].innerText.split(',').map( item => {
        if( item.trim() !== '' ) {
            const trimmedItem = item.trim();
            return trimmedItem;
        }
    });
    console.log(visibleConcepts);
    let rows = [].slice.call( document.getElementsByClassName('concept-table-row'));
    rows.forEach( row => {
        if( visibleConcepts.indexOf( row.children[0].innerText ) < 0 ) {
            row.classList.add( 'v-none' );
        }
    });
}

function modifyPopupCounts() {
    const conceptTable = document.getElementById('concept-table');
    const entityTable = document.getElementById('entity-table');
    const conceptRows = [].slice.call( document.getElementsByClassName('concept-table-row'));
    let conceptHiddenCount = 0;
    conceptRows.forEach( row => {
        if( row.classList.contains('v-none')) {
            conceptHiddenCount++;
        }
    });
    document.getElementById('concept-table-head-count').innerText = ( parameters.j0 === 'None' ) ? conceptRows.length : `${conceptRows.length - conceptHiddenCount} / ${conceptRows.length}`;
    const entityRows = [].slice.call( document.getElementsByClassName('entity-table-row'));
    let entityHiddenCount = 0;
    entityRows.forEach( row => {
        if( row.classList.contains('v-none')) {
            entityHiddenCount++;
        }
    });
    document.getElementById('entity-table-head-count').innerText = ( parameters.j0 === 'None' ) ? entityRows.length : `${entityRows.length - entityHiddenCount} / ${entityRows.length}`;
}

function filterTubePopup(filter) {
    log('filtering tube popup table');
    let filterMode = parameters.j0;
    for(let z=0;z<2;z++) {
        let tempObj = {};
        for(let i=0;i<optimalItemsSize();i++) {
            if(selectedCluster.userData.endpoints[z] == data[i].Name) {
                tempObj = Object.assign({}, data[i]);
                break;
            }
        }
        let tempGenes = Object.keys(tempObj.Genes);
        let visibleGenes = [];
        tempGenes.forEach(function(gene) {
        let fCount = 0;
        for(let i=0;i<filter.length;i++) {
            if( typeof( tempObj.Genes[gene].Concepts[filter[i]] ) != "undefined" && filterMode === 'Intersection' ) {
                visibleGenes.push(gene);
                break;
            } else if( typeof( tempObj.Genes[gene].Concepts[filter[i]] ) == "undefined" && filterMode === 'Difference' ) {
                fCount++;
                if( fCount === filter.length ) {
                    visibleGenes.push(gene);
                    break;
                }
            }
        }
        });
        let popupRows = [].slice.call(document.getElementsByClassName('popup-table-row'));
        popupRows.forEach(function(row) {
            if(visibleGenes.indexOf(row.children[0].textContent) < 0) {
                log(row.children[0].innerText);
                row.classList.add('v-none');
            }
        });
    }
}

function createDefaultProfileAuto () {
    guiProfileCeationToolsToggle();
    profileCreation(DEFAULT_PROFILE_NAME);
    saveProgressFunc(DEFAULT_PROFILE_NAME);
}

function onInitDone () {
    // log('creating help icons');
    // let helpIconArray = document.getElementsByClassName('title');
    // let doubleLabelIndex = 0;
    // let labelText = '';
    // for (let i = 0; i < helpIconArray.length; i++) {
    //     log(helpIconArray[i].innerText);
    //     labelText = helpIconArray[i].innerText;
    //     labelText = labelText.replace(/ /g, '-');
    //     if (labelText == 'Filters') {
    //         doubleLabelIndex++;
    //         labelText = labelText + doubleLabelIndex.toString();
    //     }
    //     helpIconArray[i].innerHTML += '<i class="fa fa-question-circle fa-pull-right" aria-hidden="true" id="route-' + labelText + '"></i>';
    // }
    // helpIconArray = document.getElementsByClassName('fa fa-question-circle fa-pull-right');
    // for (let i = 0; i < helpIconArray.length; i++) {
    //     helpIconArray[i].addEventListener('click', function(event) {
    //         event.stopPropagation();
    //         helpRouting(event.target.id);
    //     });
    // }
    hideLoader();
    document.getElementById('help-tab').style.height = '34px';
    document.getElementById('notes-tab').style.height = '32px';
    document.getElementById('ent-map-tab').style.height = '30px';
    document.getElementById('chat-tab').style.height = '28px';
    controls.handleResize();
    const hud = document.getElementById("hud");
    const chatBox = document.getElementById("chat-box");
    const hudWidth = hud.offsetWidth;
    chatBox.style.width = `calc(100% - ${hudWidth}px)`;
    // Observe the hud div for resizing
    const resizeObserver = new ResizeObserver(() => {
        const hudWidth = hud.offsetWidth;
        chatBox.style.width = `calc(100% - ${hudWidth}px)`;
    });
    resizeObserver.observe(hud);
}

function showProjectLoadFailure () {
  document.querySelector('.global-message-screen').classList.remove('hidden');
}

function hideProjectLoadFailure () {
  document.querySelector('.global-message-screen').classList.add('hidden');
}

function showLoader () {
  document.querySelector('#loadingScreen').classList.remove('hidden');
}

function hideLoader() {
  document.querySelector('#loadingScreen').classList.add('hidden');
}

function navigateToHelp (path = HELP_URLS.DEFAULT) {
  window.open(path, '_blank');
}

function setupUsersnap () {
    var s = document.createElement('script');
    s.type = 'text/javascript';
    s.async = true;
    s.src = '//api.usersnap.com/load/a18feae2-4712-4f5b-b4c3-8287a03c1546.js';
    var x = document.getElementsByTagName('script')[0];
    x.parentNode.insertBefore(s, x);
}

function setupStats () {
    var script = document.createElement('script');
    script.onload = function() {
        var stats = new Stats();
        document.body.appendChild(stats.dom).setAttribute('id', 'stats');
        requestAnimationFrame(function loop() {
            stats.update();
            requestAnimationFrame(loop);
        });
    };
    script.src = 'https://rawgit.com/mrdoob/stats.js/master/build/stats.min.js';
    document.head.appendChild(script);
}

function defineInactivityTime () {
    inactivityTime = (function () {
        $(function() {
            $("#dialog-session-confirm").dialog({
                dialogClass: "no-close",
                draggable: false,
                autoOpen: false,
                modal: true,
                width: 400,
                show: {
                    effect: "scale"
                },
                hide: {
                    effect: "scale"
                },
                buttons: {
                    "Login": {
                        id: "session-login-btn",
                        text: "Login",
                        click: function() {
                            const newTab = window.open(`${Webroot}login`, '_blank');
                            if (newTab) {
                                newTab.focus();
                            }
                            // console.log('Submitting Login Information');
                            // let form = document.getElementById('reauth-form');
                            // submitReauthForm(form);
                            // let acctCredentials = {
                            //     account: document.getElementById('inactive-acct-name').value.trim(),
                            //     password: document.getElementById('inactive-password').value.trim(),
                            //     session: sessionStorage.getItem('Laboratory')
                            // };
                            // if(acctCredentials.account!='' && acctCredentials.password!='') {
                            //     if(logCount<4) {
                            //         if(acctCredentials.account == acctCredentials.session.trim()) {
                            //             let data = { 'function': 'reauthSession', 'parameters': {
                            //                 'Laboratory': acctCredentials.account,
                            //                 'Password': acctCredentials.password,
                            //                 'CBV': true,
                            //                 'ProjectKey': sessionStorage.getItem('ProjectKey'),
                            //                 'ProjectAcct': sessionStorage.getItem('ProjectAcct'),
                            //                 'Project': sessionStorage.getItem('Project')
                            //                 }
                            //             };
                            //             httpRequest(data);
                            //         } else {
                            //             logCount++;
                            //             document.getElementById('auth-msg').style.visibility = 'visible';
                            //         }
                            //     } else {
                            //         let data = {'function':'logout'};
                            //         httpRequest(data);
                            //     }
                            // }
                        }
                    },
                    "Logout": function() {
                        let data = {'function':'logout'};
                        console.log(data);
                        httpRequest(data);
                        window.location.replace(`${AppWebroot}`);
                    }

                },
                open: function() {
                    raycaster.near = camera1.near;
                },
                close: function() {
                    raycaster.near = 0;
                }
            });
            // document.getElementById('_token').value = csrfToken;
            function eventSubmitReauthForm(e) {
                e.preventDefault();
                const form = e.target;
                submitReauthForm(form);
            }
            function submitReauthForm(form) {
                const formData = new FormData(form);

                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-XSRF-TOKEN': csrfToken
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Success:', data);
                
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            };
        // document.getElementById('reauth-form').addEventListener('submit', eventSubmitReauthForm);
        });
        var t;
        window.addEventListener('beforeunload', function() {
            console.log('ln 6503, beforeUnload event');
            if($('#dialog-session-confirm').hasClass('ui-dialog-content')) {
                let isOpen = $('#dialog-session-confirm').dialog('isOpen');
                if(isOpen==true) {
                    let data = {'function':'logout'};
                    httpRequest(data);
                }
            } else {
                log('session-confirm has not initialized yet -> 169');
            }

        });
        window.onload = resetTimer;
        document.onmousemove = resetTimer;
        document.onkeypress = resetTimer;
        document.onfocus = resetTimer;
        document.onmousewheel = resetTimer;
        document.onwheel = resetTimer;

        function sessionPrompt() {
            log('sessionPrompt');
            if($('#dialog-session-confirm').hasClass('ui-dialog-content')) {
                let isOpen = $('#dialog-session-confirm').dialog('isOpen');
                if(isOpen!=true) {
                    log('dialog closed')
                    let data = {'function':'checkSessionStatus'};
                    httpRequest(data);
                    resetTimer();
                }
            } else {
                log('session-confirm has not initialized yet -> 193')
            }
        }
        function resetTimer() {
            clearTimeout(t);
            t = setTimeout(sessionPrompt, 1000 * 60 * 15);
                // t = setTimeout(sessionPrompt, 1000);
        }
    })();
    focusSessionCheck = (function () {
        window.addEventListener('focus', function() {
            log('Refocus');
            let data = {'function':'checkSessionStatus'};
            httpRequest(data);
        });
    })();
    // setAuthEnterEventListener();
}
function setAuthEnterEventListener() {
    console.log('starting listener function');
    document.getElementById('inactive-acct-name').addEventListener('keyup', (event) => {
        if($("#dialog-session-confirm").dialog("isOpen")) {
            if(event.keyCode === 13) {
                event.preventDefault();
                document.getElementById('session-login-btn').click();
            }
        }
    });
    document.getElementById('inactive-password').addEventListener('keyup', (event) => {
        if($("#dialog-session-confirm").dialog("isOpen")) {
            if(event.keyCode === 13) {
                event.preventDefault();
                document.getElementById('session-login-btn').click();
            }
        }
    });
}

function composeProfileObj () {
    let profile = {
        parameters,
        PM,
        customClusterNames,
        customClusterColors,
        customGroupColors,
        ThemeNameRegistry,
        visLockedTubes,
        hiddenGroups,
        notes: document.getElementById('notes-text-area').value,
        wireFrame: parameters.b,
        autoAnnotationActive: autoAnnotationActive || false,
        firstLabelInit: firstLabelInit,
        labelPositions: spriteTextGroup.children.map( item  => item.position ), 
        dragVerts: dragLines.children.map( line => line.userData.originalPositions ),
        autoAnnotationActive: autoAnnotationActive,
        cameraControlPositions: saveControlCameraPositions()
    };
    console.log( profile );
    if (Array.isArray(customGroups)) {
        customGroups =  JSON.parse(JSON.stringify(customGroups));
    }
    profile.customGroups = customGroups;

    //Save ThemeNameRegistry (percayia may dispose of this if not used)
    /*ThemeNameRegistry correction due to optimode*/
    if (optimalItemsSize() != data.length) {
        for (let i = optimalItemsSize(); i < data.length; i++) {
            if(ThemeNameRegistry[i] === undefined) {
                ThemeNameRegistry.push({ Name: data[i].Name, Alias: data[i].Name, Group: '', GroupColor: '' });
            }
        }
        profile.ThemeNameRegistry = ThemeNameRegistry;
    } else {
        profile.ThemeNameRegistry = ThemeNameRegistry;

    }
    //Save End
    // creating actual deep copy
    profile = JSON.parse(JSON.stringify(profile));
    return profile;
}
function saveControlCameraPositions() {
    let positions = {
        cameraPosition: camera1.position,
        cameraTarget: controls.target,
        cameraMatrix: camera1.matrix,
        cameraUp: camera1.up
    }
    for (let i=0; i<positions.cameraMatrix.elements.length;i++) {
        positions.cameraMatrix.elements[i] = +(Math.round(positions.cameraMatrix.elements[i] + "e+5") + "e-5")
    }
    return positions;
}
function handleApplyFilterClick () {
    document.body.classList.add('wait');
    if (parameters.j == 'None') {
        applyConceptFilter();
        if( tempVisLockFilterHold.length > 0 ) {
            tempVisLockRestore();
        }
        document.body.classList.remove('wait');
    } else {
        document.body.classList.add('wait');
        if( visLockedTubes.length > 0 ) {
            tempVisLockDisable();
        }
        let httpData = { 'function': 'getFilterList', 'parameters': { 
            'AcctId': AcctId, 
            'Account': ProjectAcct, 
            'ProjectId': ProjectId, 
            'Project': Project, 
            'FilterName':  parameters.j 
        }};
        mainCfg.getConceptFilter(httpData);
    }
}

function tempVisLockDisable() {
    Array.from( visLockPanel.children ).forEach( element => {
        tempVisLockFilterHold.push( element.id );
        element.click();
    });
}

function tempVisLockRestore() {
    if( typeof( selectedCluster ) !== 'undefined' ) {
        const tempName = selectedCluster.name;
    }
    if( selected.length > 0 ) {
        deselctAll();
    }
    tempVisLockFilterHold.forEach( item => {
        const theme1 = item.split(',')[0];
        const theme2 = item.split(',')[1];
        selected.push( theme1, theme2 );
    });
    visLock();
    if( typeof( tempName ) !== 'undefined' ) {
        document.getElementById( tempName ).click();
    }
    tempVisLockFilterHold = [];
}

function applyConceptFilter (filter = []) {
    filterArray = Array.from(filter);
    removeCubeFiltering();
    removeHudFilters();
    if (filterArray.length === 0) {
        removeFilterMasking();
    } else {
        applyFilterMasking(filterArray);
        filterConceptsInHud(filterArray);
        filterInSphereText(filterArray);        
        if( parameters.j2 === true ) {
           applyFilterHalos( true );
        }
    }
}

function removeSelectedThemeByName(ThemeNameToRemove) {
  GLOBAL_STATE.selected = GLOBAL_STATE.selected.filter((Name)=>Name !== ThemeNameToRemove);
}

window.onload = function () {
    mainCfg.onload();
};

var rotateScene = false;
var rotationSpeed = 0.0025;
function checkRotation() {
  var x = camera1.position.x,
    y = camera1.position.y,
    z = camera1.position.z;
    camera1.position.x = x * Math.cos(rotationSpeed) - z * Math.sin(rotationSpeed);
    camera1.position.z = z * Math.cos(rotationSpeed) + x * Math.sin(rotationSpeed);
  camera1.lookAt(scene.position);
}

/* Get Input Data/List */
function getProjectInputList(mapFlag = false) {
    let httpData = { 'function': 'returnInputList', 'parameters': { 'ExpId': ProjectId, 'Account': ProjectAcct, 'Experiment': Project, 'EntMapFlag': mapFlag } };
    httpRequest(httpData);
}

/* Custom Console Functions */
function getUnmappedEntities() {
    getProjectInputList();
    let allGenes = [];
    for(let i=0;i<optimalItemsSize();i++) {
        allGenes = allGenes.concat(clusterGroup.children[i].userData.Genes);
    }
    let genes = [...new Set(allGenes)];  
    setTimeout(() => {
        let entListString = sessionStorage.getItem('Entity_List');
        let tmpEntList = entListString.split('\n');
        console.log('Project Entities:');
        console.log(genes);
        console.log('Input List Entities:');
        console.log(tmpEntList);
        genes = genes.map( gene => gene.replace( '(e)','' ).toLowerCase().trim());
        tmpEntList = tmpEntList.map( ent => ent.replace( '(e)','' ).toLowerCase().trim());
        // console.log(genes);
        // console.log(entList);
        let diff = tmpEntList.filter( x => !genes.includes( x ));
        let intsct = tmpEntList.filter( x => genes.includes( x ));
        console.log( 'Unmapped Entities ( Pre-Validation ):');
        console.log( diff );
        console.log( 'Maapped Entities ( Pre-Validation ):');
        console.log( intsct );
    }, 1000);
}

async function getUnmappedEntitiesExported(listString = null) {
    let tmpEntList = ( listString === null )
        ? sessionStorage.getItem('Entity_List').split("\n").map( ent => ent.replace( '(e)','' ).toLowerCase().trim())
        : listString.split("\n").map( ent => ent.replace( '(e)','' ).toLowerCase().trim());
    let allGenes = [];
    for(let i=0;i<optimalItemsSize();i++) {
        allGenes = allGenes.concat(clusterGroup.children[i].userData.Genes);
    }
    let projectEntities = [...new Set(allGenes)].map( ent => ent.replace( '(e)','' ).toLowerCase().trim());
    let validResp = await getValidatedEntities( tmpEntList );
    let unmapped = validResp.valid.filter( x => !projectEntities.includes( x ));
    validResp.unmapped = unmapped;
    validResp.inputList = tmpEntList;
    validResp.projectEntities = projectEntities;
    /*
     * Object Mapping:
     * inputList = submitted list
     * diff = invalid submitted ents
     * valid = valid submitted ents
     * projectEntities = mapped project ents
     * unmapped = unmapped valid ents
    */
    console.log('Final Results:');
    console.log( validResp );
    let headers = [
        'Input List (' + validResp.inputList.length + ')',
        'Invalid (' + validResp.diff.length + ')',
        'Valid (' + validResp.valid.length + ')',
        'Mapped (' + validResp.projectEntities.length + ')',
        'Unmapped (' + validResp.unmapped.length + ')'
    ];
    let lengths = [
        headers.length,
        validResp.inputList.length,
        validResp.diff.length,
        validResp.valid.length,
        validResp.projectEntities.length,
        validResp.unmapped.length
    ];
    let largestLength = Math.max( ...lengths );
    let finalCsvArray = [];
    finalCsvArray.push(headers);
    for( let i=0;i<largestLength;i++ ) {
        let csvRow = [
            ( typeof validResp.inputList[i] === 'undefined' ) ? '' : validResp.inputList[i],
            ( typeof validResp.diff[i] === 'undefined' ) ? '' : validResp.diff[i],
            ( typeof validResp.valid[i] === 'undefined' ) ? '' : validResp.valid[i],
            ( typeof validResp.projectEntities[i] === 'undefined' ) ? '' : validResp.projectEntities[i],
            ( typeof validResp.unmapped[i] === 'undefined' ) ? '' : validResp.unmapped[i]
        ];
        finalCsvArray.push( csvRow );
    }
    console.log( finalCsvArray );
    let csv = '';
    finalCsvArray.forEach( row => csv += row.join(',') + "\n" );
    var hiddenElement = document.createElement('a');  
    hiddenElement.href = 'data:text/csv;charset=utf-8,' + encodeURI(csv);
    hiddenElement.target = '_blank';  
    hiddenElement.download = `unmapped_entities_${Date.now()}.csv`;  
    hiddenElement.style.display = 'none';
    document.body.appendChild( hiddenElement );
    hiddenElement.click();
    document.body.removeChild( hiddenElement );
}

function getValidatedEntities(listArray = null) {
    return new Promise(( resolve,reject ) => {
        let tmpEntList = ( listArray === null )
        ? sessionStorage.getItem('Entity_List').split("\n").map( ent => ent.replace( '(e)','' ).toLowerCase().trim())
        : listArray;
        let pcmmEndpoint = getPcmmEndpoint();
        $.ajax({
            url : pcmmEndpoint,
            type : 'POST',
            data : JSON.stringify({
                "jsonrpc" : "2.0",
                "method" : "GetValidIdeas",
                "id" : "111",
                "params" : {
                    "IdeaType": "entity",
                    "InputIdeas": tmpEntList 
                }
            }),
            dataType : "json",
            success : (response) => {
                console.log( response.result );
                // let valid = response.result.map( q => q.Query );
                let valid = response.result.FilteredList;
                let diff = tmpEntList.filter( x => !valid.includes( x ));
                let resp = { 'valid':valid,'diff':diff };
                resolve( resp );
            },
            error: (error) => {
                console.log(error)
                reject( error );
            }
        });
    })
}
var camRotateX = false;
function camRotate() {

        camera1.position.x += 0.5;

}

function rotateCameraY(radiansIncrement = 0.01) {
var x = camera1.position.x; var y = camera1.position.y; var z = camera1.position.z;
var signx = x > 0 ? 1 : -1;

// get current radians from z and x coords.
var radians = x == 0 ? Math.PI/2 : Math.atan(z/x);
if (signx == -1) radians += Math.PI;

radians += radiansIncrement;
if (radians > Math.PI*2) radians = radians%(Math.PI*2);
while (radians < 0) radians += Math.PI*2;

var radius = Math.sqrt(x*x + z*z);
camera1.position.x = radius * Math.cos(radians);
camera1.position.z = radius * Math.sin(radians);
}
// Ruteja Export Overlap
function rutejaExportOverlap() {
	if(parameters.j != "None") {
        deselectAll();
		let rows = [["Index Name","Theme Name","Number Concepts Overlapped","Total Concepts","Concepts","Entities"]];
		let hudVisible = hud.querySelectorAll(".hudCluster:not(.filter-parent-hide)");
		for(let i=0;i<hudVisible.length;i++) {
            hudVisible[i].click();
            let row = [
				hudVisible[i].id,
				hudVisible[i].querySelector(".clusterName").innerHTML.split("<br>")[0].replaceAll("\n","").replaceAll(","," ").replaceAll(" )",")"),
				hudVisible[i].querySelector(".hud-concept-count").innerHTML.split("/")[0].trim(),
				hudVisible[i].querySelector(".hud-concept-count").innerHTML.split("/")[1].trim(),
                getFilteredConcepts(),
                getFilteredEnts()
			];
			rows.push(row);
            deselectAll();
		}
		let csvContent = "data:text/csv;charset=utf-8," + rows.map(e => e.join(",")).join("\n");
		var encodedUri = encodeURI(csvContent);
		window.open(encodedUri);
	} else {
		console.log('No filter applied!');
	}
}
function getFilteredEnts() {
	let ents = Array.from(
			document.querySelectorAll(
				"#entity-table > tbody > tr.entity-table-row.popup-table-row > th:not(#entity-table > tbody > tr.entity-table-row.popup-table-row.v-none > th"
				)
			).map(
				el => el.innerText
			);
	return '"' + ents.join() + '"';
}
function getFilteredConcepts() {
    document.querySelectorAll("#concept-table > tbody > tr.concept-table-row.popup-table-row > th:not(#concept-table > tbody > tr.concept-table-row.popup-table-row.v-none > th")

    let cons = Array.from(
			document.querySelectorAll(
				"#concept-table > tbody > tr.concept-table-row.popup-table-row > th:not(#concept-table > tbody > tr.concept-table-row.popup-table-row.v-none > th"
				)
			).map(
				el => el.innerText
			);
	return '"' + cons.join() + '"';
}
// stats toggle on or off using 'd' key
$(document).on("keypress", function (e) {
    console.log('key pressed: ' + e.key);
    if(e.key === 'd') {
        if (!showStats) {
            console.log('making stats visible');
            document.getElementById('stats').style.visibility = 'visible';
            showStats = true;
        } else {
            document.getElementById('stats').style.visibility = 'hidden';
            showStats = false;
            console.log('making stats hidden.');
        }
    }
});

function larvRequest(data) {
    if (typeof data !== 'string') {
        data = JSON.stringify(data);
    }
    log(data);
    let header = 'PostRequest=';
    let request = header + data;
    let url = '../assets/php/cbv_functions.php';
    let xhr = new XMLHttpRequest();
    // xhr.timeout = 100000;
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
    // xhr.setRequestHeader("Content-type", "application/json");
    // xhr.ontimeout = function() {
    //     log('request-error -> ' + xhr.statusText);
    // }
    xhr.onreadystatechange = function () {
        if (xhr.readyState == 4 && xhr.status == 200) {
            let response = xhr.responseText;
            log(response);
            if (response != 'request complete') {
                httpResponse(response);
            } else {
                log('hello from js');
            }
        }
    }
    xhr.send(request);
}