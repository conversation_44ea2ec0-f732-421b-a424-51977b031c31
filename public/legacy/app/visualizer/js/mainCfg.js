/* globals */
import router from "../../../src/js/router.js";
import backendAPI from "../../../src/js/backendAPI.js";
import userData from "../../../src/js/utils/userData.js";
import utils from "../../../src/js/utils/utils.js";
import VisDataPanel from "../epmdev/js/components/VisDataPanel/VisDataPanel.js";
import errorHandlers from "../../../src/js/errorHandlers.js";
import BottomPanel from "../epmdev/js/components/BottomPanel/BottomPanel";
import ThemePopup from "../epmdev/js/components/ThemePopup/ThemePopup";
import SharedEntitiesPopup from "../epmdev/js/components/SharedEntitiesPopup/SharedEntitiesPopup";
import EntityPopup from "../epmdev/js/components/EntityPopup/EntityPopup";
import ModalRemoveConfirm from "../epmdev/js/components/ModalRemoveConfirm/ModalRemoveConfirm";
import ModalRename from "../epmdev/js/components/ModalRename/ModalRename";
import ThemeRelationDisplay from "../epmdev/js/components/ThemeRelationDisplay/ThemeRelationDisplay";
import SelectedThemesPanel from "../epmdev/js/components/SelectedThemesPanel/SelectedThemesPanel";

// vue app usage example
// import HelloWorldApp from '../epmdev/js/components/HelloWorld/HelloWorld';
// 	var app = new HelloWorldApp({
// 		container: document.querySelector('#app-container'),
// 		data: {
// 			textName: 'HelloWorld',
// 			buttonName: 'hello',
// 			onClick: function() {
// 				alert(1);
// 			}
// 		}
// 	});
//  to remove app, call app.remove();

const refs = {
  savedProfileData() {
    // eslint-disable-next-line no-undef
    return savedProfileData;
  },
  profileParameters() {
    // eslint-disable-next-line no-undef
    return profileParameters;
  },
  data() {
    // eslint-disable-next-line no-undef
    return data;
  },
  Key() {
    // eslint-disable-next-line no-undef
    return Key;
  },
  data2() {
    // eslint-disable-next-line no-undef
    return data2;
  },
  alertInformation(...args) {
    // eslint-disable-next-line no-undef
    alertInformation(...args);
  },
  deselectAll(...args) {
    // eslint-disable-next-line no-undef
    deselectAll(...args);
  },
  delectItemsInSelectedGroup(...args) {
    // eslint-disable-next-line no-undef
    delectItemsInSelectedGroup(...args);
  },
  applyConceptFilter(...args) {
    // eslint-disable-next-line no-undef
    applyConceptFilter(...args);
  },
  hudDataBindClick(...args) {
    // eslint-disable-next-line no-undef
    hudDataBindClick(...args);
  },
  eventFilterController(...args) {
    // eslint-disable-next-line no-undef
    eventFilterController(...args);
  },
  clusterGroup() {
    // eslint-disable-next-line no-undef
    return clusterGroup;
  },
  FILTER_MODES() {
    // eslint-disable-next-line no-undef
    return FILTER_MODES;
  },
  geneViewThemeHighlight(...args) {
    // eslint-disable-next-line no-undef
    geneViewThemeHighlight(...args);
  },
  groupSelect(...args) {
    // eslint-disable-next-line no-undef
    groupSelect(...args);
  },
  groupAdd(...args) {
    // eslint-disable-next-line no-undef
    groupAdd(...args);
  },
  deleteGroup(...args) {
    // eslint-disable-next-line no-undef
    deleteGroup(...args);
  },
  deleteGroupItem(...args) {
    // eslint-disable-next-line no-undef
    deleteGroupItem(...args);
  },
  restoreProfileData(...args) {
    // eslint-disable-next-line no-undef
    restoreProfileData(...args);
  },
  saveProgressFunc(...args) {
    // eslint-disable-next-line no-undef
    saveProgressFunc(...args);
  },
  switchProfile(...args) {
    // eslint-disable-next-line no-undef
    switchProfile(...args);
  },
  visLock(...args) {
    // eslint-disable-next-line no-undef
    visLock(...args);
  },
  hideGroup(...args) {
    // eslint-disable-next-line no-undef
    hideGroup(...args);
  },
  unhideGroup(...args) {
    // eslint-disable-next-line no-undef
    unhideGroup(...args);
  },
  parameters() {
    // eslint-disable-next-line no-undef
    return parameters;
  },
  epmViewFunctions() {
    // eslint-disable-next-line no-undef
    return epmViewFunctions;
  },
  ctrlFunctions() {
    // eslint-disable-next-line no-undef
    return ctrlFunctions;
  },
  viewFunctions() {
    // eslint-disable-next-line no-undef
    return viewFunctions;
  },
  restoreWireFrame(...args) {
    // eslint-disable-next-line no-undef
    restoreWireFrame(...args);
  },
  showProjectLoadFailure(...args) {
    // eslint-disable-next-line no-undef
    showProjectLoadFailure(...args);
  },
  hideLoader(...args) {
    // eslint-disable-next-line no-undef
    hideLoader(...args);
  },
  init(...args) {
    // eslint-disable-next-line no-undef
    init(...args);
  },
  selected() {
    // eslint-disable-next-line no-undef
    return selected;
  },
  ThemeNameRegistry() {
    // eslint-disable-next-line no-undef
    return ThemeNameRegistry;
  },
  overlapFilters() {
    // eslint-disable-next-line no-undef
    return overlapFilters;
  },
  Detector() {
    // eslint-disable-next-line no-undef
    return Detector;
  },
  setOverlapFilters(value) {
    // eslint-disable-next-line no-undef
    overlapFilters = value;
  },
  setData(value) {
    // eslint-disable-next-line no-global-assign
    // eslint-disable-next-line no-undef
    data = value;
  },
  setData2(value) {
    // eslint-disable-next-line no-global-assign
    // eslint-disable-next-line no-undef
    data2 = value;
  },
  eventBus() {
    // eslint-disable-next-line no-undef
    return eventBus;
  },
  selectedCluster() {
    return selectedCluster;
  },
  guiSaveColor(...args) {
    guiSaveColor(...args);
  },
  themeNameSearch(...args) {
    return themeNameSearch(...args);
  }
};

// eslint-disable-next-line no-unused-vars
const HELP_URLS = {
  PROFILE_TOOLS:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65339433/5.+MANAGE+PROJECT+PROFILE",
  SCALE_FACTOR:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65142906/6.1.+CHANGE+SCALE+FACTOR",
  FILTERS1:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65241169/6.2.+SET+UP+FILTERS",
  FILTERS2:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65405002/4.1.4.+VIEW+CONCEPT+MAP",
  THEME_TOOLS:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65142940/6.5.+ANNOTATE+THEMES",
  GROUP_TOOLS:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65405027/6.7.+MANAGE+GROUPS",
  VISUAL_SETTING:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65405023/6.3.+ADJUST+VISUAL+SETTINGS",
  EXPORT_DATA:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65306699/4.2.+EXPORT+RESULTS",
  DEFAULT:
    "https://percayai.atlassian.net/wiki/spaces/CH/pages/65208321/CompBio+User+Guide"
};

var projectDescription = {
  authorEmail: "",
  authorId: 1,
  authorName: "",
  conceptsNum: 25,
  context: "25",
  createdDatetime: "2018-12-13T13:10:13.873602Z",
  entities: "ACD",
  entitiesMap: {},
  entitiesNum: 1,
  id: "2186",
  inputDataVersion: "",
  name: "test",
  pubMedVersion: "",
  sharedTo: [],
  status: "submitted",
  submittedDatetime: ""
};

var conceptFilters = {};

function composeGroupData() {
  let i, j;
  let curProfile = refs.savedProfileData()[
    refs.profileParameters().profileName
  ];
  let groups = [];
  let themeObject;
  for (i in curProfile.customGroups) {
    groups.push({
      name: i,
      themes: [],
      selected: false
    });
    for (j = 0; j < curProfile.customGroups[i].length; j++) {
      themeObject = refs
        .data()
        .find(t => t.Name === curProfile.customGroups[i][j]);
      if (themeObject) {
        groups[groups.length - 1].themes.push(themeObject);
      }
    }
  }
  return groups;
}

// eslint-disable-next-line no-unused-vars
const mainCfg = {
  useAutoProfile: true,
  useLegacyScale: true,
  canExportPublicationTable: false,
  useStats: false,
  maxDefaultScore: 30,
  minDefaultScore: 30,
  optiLengthSize: 50,
  limitFrameRateUsingSetTimeout: true,
  showVisDataPanel: true,
  useNewRemoveConfirmModal: true,
  hasOverlapFilterMode: true,
  exportSelectedName: "Export Selected",
  exportAllName: "Export All",
  newGroupName: "New Group",
  defaultBGName: "Default",
  assetsPath: "visualizer",
  useSessionExpirationCheck: false,
  showNewRelationDisplay: true,
  showNewSelectedThemesPanel: true,
  components: {},
  showBottomPanel: true,
  useNewThemePopup: true,
  useNewSharedEntitiesPopup: true,
  initialDataCheck: function() {
    return refs.Detector().webgl;
  },
  composeGeneSelectLink: function(geneQuery) {
    return `https://www.ncbi.nlm.nih.gov/gene?term=(${encodeURIComponent(
      geneQuery.toUpperCase()
    )}%5BGene%20Name%5D)%20AND%20homo%20sapience%5BOrganism%5D`;
  },
  composeAbstractLink: function(params = {}) {
    if (params.geneId) {
      return `abstract.html?projectId=${projectDescription.id}&geneId=${params.geneId}&conceptName=${params.conceptName}`;
    }
    return `abstract.html?projectId=${projectDescription.id}&conceptId=${params.conceptId}&conceptName=${params.conceptName}`;
  },
  composeImgSearchLink: function(queryString) {
    return `search.html?q=${encodeURI(queryString)}`;
  },
  saveProfileReq: function(params = {}, newProfile) {
    backendAPI
      .updateProjectProfile(
        projectDescription.id,
        params.parameters.ProfileData
      )
      .then(() => {
        let auditData;
        if (newProfile) {
          auditData = {
            operationCode: "createProfile",
            description: `Profile ${params.parameters.Profile} created in Project ${projectDescription.name}`,
            objectName: params.parameters.Profile,
            objectId: projectDescription.id
          };
        } else {
          auditData = {
            operationCode: "updateProfile",
            description: `Profile ${params.parameters.Profile} updated in Project ${projectDescription.name}`,
            objectName: params.parameters.Profile,
            objectId: projectDescription.id
          };
        }
        backendAPI.addAuditRecord(auditData).catch(err => {
          errorHandlers.handleAll(err);
        });
        refs.alertInformation("Progress Saved");
      });
  },
  exportVisData: function(params = {}) {
    console.log("exporting", params);
    refs.alertInformation(
      "Please wait, your export file is being generated..."
    );
    backendAPI
      .exportVisData(projectDescription.id, {
        Data: params.parameters.Data.slice(1, params.parameters.Data.length),
        profileName: refs.profileParameters().profileName
      })
      .then(resp => {
        backendAPI
          .addAuditRecord({
            operationCode: "exportVisData",
            description: `Data exported from Profile ${
              refs.profileParameters().profileName
            } in Project ${projectDescription.name}`,
            objectName: params.parameters.Profile,
            objectId: projectDescription.id
          })
          .catch(err => {
            errorHandlers.handleAll(err);
          });
        utils.downloadBlob(resp, params.parameters.Data[0], "zip");
      });
  },
  getDataSrc: function() {
    return projectDescription.name;
  },
  deleteProfile: function(params = {}) {
    backendAPI
      .updateProjectProfile(
        projectDescription.id,
        params.parameters.ProfileData
      )
      .then(() => {
        backendAPI
          .addAuditRecord({
            operationCode: "deleteProfile",
            description: `Profile ${params.parameters.Profile} deleted from Project ${projectDescription.name}`,
            objectName: params.parameters.Profile,
            objectId: projectDescription.id
          })
          .then(() => {
            refs.alertInformation("Profile Deleted & FileWritten");
            location.reload();
          })
          .catch(err => {
            errorHandlers.handleAll(err);
            refs.alertInformation("Profile Deleted & FileWritten");
            location.reload();
          });
      });
  },
  onSaveProgressClick: function() {
    backendAPI.getProjectProfile(projectDescription.id).then(resp => {
      refs.restoreProfileData(resp);
      refs.saveProgressFunc(refs.profileParameters().profileName);
    });
  },
  onLoadProfileClick: function() {
    backendAPI.getProjectProfile(projectDescription.id).then(resp => {
      const profileName = refs.profileParameters().profileName;
      refs.restoreProfileData(resp);
      refs.switchProfile(profileName);
      if (this.showVisDataPanel) {
        this.components.visDataPanel.setThemes(
          refs.data(),
          refs.ThemeNameRegistry()
        );
      }
      if (profileName !== "Default") {
        //Delay wireframe restore while tubes are processed and drawn
        //2secs to accomodate slower machines
        setTimeout(function() {
          refs.restoreWireFrame(profileName);
        }, 2000);
      }
    });
  },
  composeGeneList: function(selectedCluster) {
    let i;
    let gene,
      expVal,
      geneScore,
      geneList = "";
    geneList +=
      '<thead class="t-head"><th>Entity</th><th>Score</th><th>Exp Value</th></thead><tbody>';
    for (i = 0; i < selectedCluster.userData.Genes.length; i++) {
      gene = selectedCluster.userData.Genes[i];
      expVal = projectDescription.entitiesMap[gene];
      geneScore = (
        parseFloat(selectedCluster.userData.Gene_Scores[i]) || 0
      ).toFixed(2);
      geneList += `<tr class="popup-table-row">
      <td onclick="eventFilterController(this.textContent)">${(/^\(e\)/.test(gene)) ? '<span class="hidden">(e)</span>' + gene.replace('(e)','') : gene}</td>
      <td class="geneScore">${geneScore}</td><td class="geneScore exp-val">${expVal}</td>
      </tr>`;
    }
    geneList += "</tbody>";
    return geneList;
  },
  getConceptFilter: function(params = {}) {
    let filter = conceptFilters[params.parameters.FilterName];
    backendAPI
      .getFilter(filter.id)
      .then(resp => {
        refs.applyConceptFilter(resp.concepts);
        document.body.classList.remove("wait");
      })
      .catch(err => {
        errorHandlers.handleAll(err);
      });
  },
  setupComponents({ visDataPanel }) {
    if (this.showVisDataPanel) {
      this.components.visDataPanel.setHeader(visDataPanel.projectName);
      this.components.visDataPanel.setThemes(
        visDataPanel.themes,
        visDataPanel.themeSettings
      );
      this.components.visDataPanel.setEntities(
        visDataPanel.entities,
        visDataPanel.themes,
        visDataPanel.entitiesMap
      );
      this.components.visDataPanel.setGroups(composeGroupData());

      refs
        .eventBus()
        .subscribe("update:theme:name", function([oldValue, newValue]) {
          mainCfg.components.visDataPanel.onThemeRename(oldValue, newValue);
          mainCfg.components.themePopup.title = newValue;
        });
    }
  },
  initComponents() {
    if (this.showBottomPanel) {
      this.components.bottomPanel = new BottomPanel({
        container: document.body
      });
    }
    if (this.useNewThemePopup) {
      this.components.themePopup = new ThemePopup({
        container: document.querySelector("#new-theme-popup"),
        minimumConceptsNum: 4
      });
      this.components.entityPopup = new EntityPopup({
        container: document.querySelector("#new-entity-popup"),
        composeAbstractLink: this.composeAbstractLink,
        FILTER_MODES: refs.FILTER_MODES(),
        key: refs.Key()
      });
    }
    if (this.useNewSharedEntitiesPopup) {
      this.components.sharedEntitiesPopup = new SharedEntitiesPopup({
        container: document.querySelector("#new-shared-entities-popup"),
        onEntityClick: (id, context) => {
          refs.eventFilterController(id, context);
        }
      });
    }
    if (this.useNewRemoveConfirmModal) {
      this.components.removeConfirmModal = new ModalRemoveConfirm({
        data: { visible: false },
        container: document.body
      });
      this.components.renameModal = new ModalRename({
        data: { visible: false },
        container: document.body
      });
    }

    if (this.showVisDataPanel) {
      this.components.visDataPanel = new VisDataPanel({
        noDragHeader: false,
        container: document.body,
        components: this.components,
        themeItemClick: theme => {
          refs.hudDataBindClick(
            refs.clusterGroup().getObjectByName(theme.Name)
          );
        },
        renameThemeClick: (oldName, newName) => {
          if (oldName === newName) {
            return;
          }
          refs.ctrlFunctions().onThemeRename(oldName, newName);
          if (!this.showNewRelationDisplay) {
            refs
              .viewFunctions()
              .updateThemeNameInSelectedThemesBottomPanel(oldName, newName);
          }
          refs.epmViewFunctions().updateThemeNameInHUD(oldName, newName);
        },
        themeShowRelationButtonClick: () => {
          refs.visLock();
          refs.deselectAll();
        },
        themeExportButtonClick: maybeSelectedThemes => {
          return refs.ctrlFunctions().exportThemes(maybeSelectedThemes);
        },
        setGroupVisibility: (_, isCurrentlyHidden) => {
          if (isCurrentlyHidden) {
            refs.unhideGroup();
          } else {
            refs.hideGroup();
          }
        },
        groupCreateButtonClick: () => {
          Object.assign(this.components.renameModal, {
            visible: true,
            items: this.components.visDataPanel._model.groups,
            itemKey: "name",
            label: "group name",
            title: "Create Group",
            mainActionName: "Create",
            allowInitialName: false,
            name: "",
            validationErrorDuplicate: "A group with this name already exists.",
            validationErrorFresh: "You need to enter a name before setting.",
            onRename: (_, newName) => {
              refs.parameters().e = newName;
              refs.parameters().e1();
            }
          });
        },
        entityItemClick: ({ name }) => {
          refs.geneViewThemeHighlight(name);
        },
        groupItemClick: ({ name }) => {
          refs.groupSelect(name);
          if (this.components.visDataPanel.selectedGroup === undefined) {
            refs.deselectAll();
            refs.delectItemsInSelectedGroup();
          }
        },
        addToGroupClick: groupIdx => {
          // theoretically addition is an external method in theme's UI
          let i;
          let themes = [];
          let selected = refs.selected();
          let data = refs.data();
          for (i = 0; i < selected.length; i++) {
            themes.push(data.find(t => t.Name === selected[i]));
          }
          this.components.visDataPanel.addThemesToGroup(themes, groupIdx);
          // not actually necessary, but for usefull for testing
          refs.groupAdd();
        },
        removeGroupClick: (groupIdx, groupName, next) => {
          if (this.useNewRemoveConfirmModal) {
            Object.assign(this.components.removeConfirmModal, {
              visible: true,
              title: "Delete Group",
              message: `Are you sure you want to delete "${groupName}"?`,
              onConfirm() {
                refs.deleteGroup(groupName);
                next(groupIdx);
              }
            });
          } else {
            refs.deleteGroup(groupIdx);
          }
        },
        removeFromGroupClick: ({ Name }) => {
          refs.deleteGroupItem(Name);
        },
        onColorChange: (value) => {
          refs.selectedCluster().material.color.setHex(value.replace("#", "0x"));
          refs.parameters().d = value;
        },
        saveColor: () => {
          refs.guiSaveColor();
        }
      });
    }

    if (this.showNewRelationDisplay) {
      const bus = refs.eventBus();
      this.components.themeRelationDisplay = new ThemeRelationDisplay({
        data: {
          onClick(relation) {
            bus.publish("remove:theme-relation", relation);
          }
        },
        container: document.body
      });

      bus.subscribe("create:theme-relation", relation => {
        this.components.themeRelationDisplay.addRelation(relation);
      });
      bus.subscribe("clear:theme-relation", () => {
        this.components.themeRelationDisplay.clear();
      });
      bus.subscribe("delete:theme-relation", ({ id }) => {
        this.components.themeRelationDisplay.deleteRelationById(id);
      });
      bus.subscribe("update:theme:name", ([oldName, newName]) => {
        this.components.themeRelationDisplay.renameTheme(oldName, newName);
        this.components.selectedThemesPanel.renameTheme(oldName, newName);
      });
    }
    if (this.showNewSelectedThemesPanel) {
      const bus = refs.eventBus();
      this.components.selectedThemesPanel = new SelectedThemesPanel({
        data: {
          onClick(theme) {
            bus.publish('deselect:selected-themes:theme', theme);
          },
          onDeselectAll() {
            bus.publish('deselect:selected-themes:all');
          }
        },
        container: document.body
      });
      bus.subscribe('stateChange', ([_, property, value]) => {
        if (property === 'selected') {
          const selectedThemes = value.map(themeName => {
            return ({
              id: themeName,
              name: this.components.visDataPanel.resolveThemeName(themeName)
            });
          });
          this.components.selectedThemesPanel.themes = selectedThemes;
        }
      });
    }
  },
  onload: function() {
    document.querySelector("#dialog-session-confirm").style.display = "none";
    userData.init();
    if (!userData.isAuthTokenPresent) {
      router.navigate({
        state: router.STATES.LOGIN,
        changeLocation: true,
        replace: true
      });
      return;
    }
    if (!refs.Detector().webgl) {
      alert("No webgl support");
      return;
    }
    let queryParams = utils.extractQueryParams(location.search);
    Promise.all([
      backendAPI.getProjectThemes(queryParams.projectId),
      backendAPI.getProjectGenes(queryParams.projectId),
      backendAPI.getProject(queryParams.projectId),
      backendAPI.getProjectProfile(queryParams.projectId),
      backendAPI.getFilters({
        page: 1,
        perPage: 500
      })
    ])
      .then(result => {
        refs.setData(result[0]);
        refs.setData2(result[1]);
        projectDescription = result[2];
        document.title = `${projectDescription.name} - COMPBIO Visualizer`;
        let profileData = result[3];
        if (
          (profileData instanceof Object && utils.isEmptyObject(profileData)) ||
          profileData === "{}"
        ) {
          profileData = "file does not exist";
        }
        conceptFilters = result[4].data.reduce((acc, filter) => {
          acc[filter.name] = filter;
          return acc;
        }, {});
        refs.setOverlapFilters(
          refs.overlapFilters().concat(result[4].data.map(f => f.name))
        );

        this.initComponents();
        refs.init();
        refs.restoreProfileData(profileData);

        this.setupComponents({
          visDataPanel: {
            projectName: projectDescription.name,
            themes: refs.data(),
            themeSettings: refs.ThemeNameRegistry(),
            entities: refs.data2(),
            entitiesMap: projectDescription.entitiesMap
          }
        });

        backendAPI.getVersion().then(({ computeEngine }) => {
          document.querySelector("#version-display").textContent =
            computeEngine.deploy;
        });
      })
      .catch(err => {
        errorHandlers.handleAll(err);
        refs.hideLoader();
        refs.showProjectLoadFailure();
      });
  }
};
