const HELP_URLS = {
  PROFILE_TOOLS: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=21',
  SCALE_FACTOR: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=22',
  FILTERS1: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=23',
  FILTERS2: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=15',
  THEME_TOOLS: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=25',
  GROUP_TOOLS: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=26',
  VISUAL_SETTING: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=28',
  EXPORT_DATA: Webroot + 'help/2018_12_19_compbio_help_slides.pdf#page=29',
  DEFAULT: Webroot + 'help/CompBio_Manual.pdf'
};

const mainCfg = {
    useAutoProfile: false,
    useStats: true,
    canExportPublicationTable: true,
    maxDefaultScore: 65,
    minDefaultScore: 30,
    optiLengthSize: 50,
    limitFrameRateUsingSetTimeout: false,
    useLegacyScale: false,
    hasOverlapFilterMode: true,
    useNewRemoveConfirmModal: false,
    newGroupName: 'New Group',
    defaultBGName: 'WUSTL Default',
    assetsPath: './visualizer',
    exportSelectedName: 'Export Selected (Raw Data)',
    exportAllName: 'Export All (Raw Data)',
    useSessionExpirationCheck: true,
    wustlAbstractVersion: true,
    initialDataCheck: function (data) {
        if (!Detector.webgl){
            var warning = Detector.getWebGLErrorMessage();
            document.getElementById('container').appendChild(warning);
            return false;
        }

        if(!(data instanceof Array) || data.length < 1) {
            document.body.innerHTML = `
            <div id="major-error-wrap" style="position:absolute;text-align:center;top:50%;left:50%;transform:translate(-50%,-50%);">
                <h1>There has been an unexpected error...</h1>
                <p>The data files associated with this project are corrupt.  If this problem persists, please contact a memeber of the CompBio Team or report the issue via the feedback button.</p>
            </div>
            `;

            document.body.style.height = '100vh';
            document.body.style.background = '#fff';
            return false;
        }
        return true;
    },
    composeGeneSelectLink: function (geneQuery) {
        return 'https://www.genecards.org/cgi-bin/carddisp.pl?gene=' + encodeURIComponent(geneQuery.toUpperCase()) + '&keywords=' + encodeURIComponent(geneQuery.toLowerCase());
    },
    composeAbstractLink: function () {
        return 'abstract/';
    },
    composeImgSearchLink: function (queryString) {
        return 'search/?q=' + encodeURI(queryString);
    },
    saveProfileReq: function (params = {}) {
        httpRequest(params);
    },
    exportVisData: function (params = {}) {
        httpRequest(params);
    },
    getDataSrc: function () {
        let dataSrc = document.getElementById('dataSrc').attributes.src.nodeValue;
        let displaySrc = dataSrc.replace('data/', '');
        displaySrc = displaySrc.replace('_Theme_Coordinates.js', '');
        displaySrc = displaySrc.split('/');
        displaySrc = displaySrc[displaySrc.length - 1];
        // let displaySrc = sessionStorage.getItem('Path');
        return displaySrc;
    },
    deleteProfile: function (params = {}) {
        httpRequest(params);
    },
    onSaveProgressClick: function () {
        saveProgressFunc(profileParameters.profileName);
    },
    onLoadProfileClick: function () {
        if( parameters.j0 !== 'None' ) {
            parameters.j = overlapFilters[0];
            handleApplyFilterClick();
        }
        let profileName  = profileParameters.profileName;
        switchProfile(profileName);
        if(profileName != 'Default') {
            //Delay wireframe restore while tubes are processed and drawn
            //2secs to accomodate slower machines
            setTimeout(function() {
                restoreWireFrame(profileName);
            }, 2000);

        }
	},
	getConceptFilter: function (httpData = {}) {
		httpRequest(httpData);
	},
    composeGeneList: function (selectedCluster) {
        let geneList = '';
        if(sessionStorage.Expression_List!= 'undefined' && sessionStorage.Entity_List!= 'undefined') {
            geneList += '<tr class="t-head"><th>Entity</th><th>Score</th><th>Exp</th></tr>';
            let expList = sessionStorage.Expression_List.trim().split('\n');
            let entList = sessionStorage.Entity_List.trim().split('\n');
            for(let i=0;i<entList.length;i++) {
                entList[i] = entList[i].toLowerCase();
            }
            let themeGeneExp = [];
            for(let i=0;i<selectedCluster.userData.Genes.length;i++) {
                let expIndex = entList.indexOf(selectedCluster.userData.Genes[i]);
                themeGeneExp.push(expList[expIndex]);
            }
            log(themeGeneExp);
            for (let i = 0; i < selectedCluster.userData.Genes.length; i++) {
                    geneList += '<tr class="popup-table-row"><th onclick="eventFilterController(this.textContent)">';
                    geneList += geneList += (/^\(e\)/.test(selectedCluster.userData.Genes[i])) ? '<span class="hidden">(e)</span>' + selectedCluster.userData.Genes[i].replace('(e)','') : selectedCluster.userData.Genes[i];
                    geneList += '</th><td class="geneScore">' +
                        parseFloat(selectedCluster.userData.Gene_Scores[i]).toFixed(2) + '</td><td class="geneScore exp-val">' +
                        parseFloat(themeGeneExp[i]).toFixed(2) + '</td></tr>';
            }
        }
        return geneList;
    },
    onload: function () {
        defineInactivityTime();
        httpRequest({ 'function': 'returnPHP', 'parameters': { 'Account': ProjectAcct, 'Project': Project } });
        init();
    }
};