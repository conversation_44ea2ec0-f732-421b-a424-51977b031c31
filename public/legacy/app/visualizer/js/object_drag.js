"use strict";
var dragKeyDown = false;
var dragObjects = new THREE.Group();
var dragLines = new THREE.Group();
scene.add( dragObjects,dragLines );
var dragControls = new THREE.DragControls( dragObjects.children,camera1,renderer.domElement );
var initZindex = labelCssRenderer.domElement.style.zIndex;
var firstLabelInit = false;
var originalLabelPositions = [];
var firstLabelInitComplete = false;

/* Init Draw Lines */
// document.onreadystatechange = function() {
//    if( this.readyState === 'complete') {
//       setTimeout( function() {
//          console.log( spriteTextGroup.children.length );
//          console.log( optiLength );
//       }, 0);
//    }
// }

document.getElementById('drag_lock').addEventListener( 'click', lockDragControl );
dragControls.addEventListener( 'dragend', dragEnd );

function lockDragControl( e ) {
   let allLabels = Array.from( $('.theme-labels'));
   let visibleLabels = allLabels.map( function( labelEl ) {
      return ( labelEl.style.display === 'none' ) ? false : true;
   });
   let labelArray = [];
   if( dragKeyDown === false ) {
      e.target.classList.add( 'active-draglock-btn' );
      dragKeyDown = true;
      controls.enabled = false;
      dragControls.activate();
      labelCssRenderer.domElement.style.zIndex = '-99';
      labelArray = Array.from( spriteTextGroup.children );
      let dbMaterial = new THREE.MeshBasicMaterial({ color: 'blue',wireframe: true });
      let iterator = 0
      labelArray.forEach( function( cssObj ) {
         let specs = cssObj.element.getBoundingClientRect();
         const geometry = new THREE.PlaneGeometry( specs.width,specs.height,2,2 );
         // Drag Box ( db )
         const db = new THREE.Mesh( geometry,dbMaterial );
         db.position.copy( cssObj.position );
         db.userData.refObject = cssObj.name;
         db.userData.originalPosition = ( firstLabelInit === false ) ? new THREE.Vector3().copy( cssObj.position ) : originalLabelPositions[ iterator ];
         db.scale.copy( cssObj.scale );
         db.quaternion.copy( camera1.quaternion );
         let tmpName = cssObj.name.split('=');
         let objName = '';
         if( tmpName.length > 1) {
            tmpName.shift();
            db.name = tmpName.join('');
         } else {
            db.name = tmpName.join('');
         }
         db.userData.refTheme = ( typeof cssObj.userData.referenceName === 'undefined' ) ? db.name : cssObj.userData.referenceName;
         db.userData.refThemeCoords = new THREE.Vector3().copy( clusterGroup.getObjectByName( db.userData.refTheme ).position );
         db.lookAt( camera1.position );
         db.visible = visibleLabels[iterator];
         dragObjects.add( db );
         iterator++;
      });
      console.log('drag key activated');
   } else {
      e.target.classList.remove('active-draglock-btn');
      dragKeyDown = false;
      dragControls.deactivate();
      controls.enabled = true;
      labelCssRenderer.domElement.style.zIndex = initZindex;
      console.log('drag key deactivated');
      cleanDragObjects();

   }
}

function cleanDragObjects() {
   console.log( 'Cleaning ' + dragObjects.children.length + ' DragObjects from scene...' );
   // let dbsLength = dragObjects.children.length;
   // for( let i=dbsLength-1; i>=0; i-- ) {
   //    dragObjects.remove( dragObjects.children[i] );
   // }
   while( dragObjects.children.length > 0 ) {
      dragObjects.remove( dragObjects.children[0] );
   }
}

function cleanDragLines() {
   console.log( 'Cleaning ' + dragLines.children.length + ' DragLines from scene...' );
   while( dragLines.children.length > 0 ) {
      dragLines.remove( dragLines.children[0] );
   }
   // let dbsLength = dragLines.children.length;
   // for( let i=dbsLength-1; i>=0; i-- ) {
   //    dragLines.remove( dragLines.children[i] );
   // }
}

function returnLabelPositions() {
   if( typeof savedProfileData[ currentLoadedProfile ].originalLabelPositions === 'object' && savedProfileData[ currentLoadedProfile ].originalLabelPositions.length > 0 
      && typeof savedProfileData[ currentLoadedProfile ].dragLineNames === 'object' && savedProfileData[ currentLoadedProfile ].dragLineNames.length > 0 ) {
      savedProfileData[ currentLoadedProfile ].dragLineNames.forEach( function( lineName,index ) {
         spriteTextGroup.getObjectByName( `spriteText=${lineName}` ).position.copy( savedProfileData[ currentLoadedProfile ].originalLabelPositions[ index ] );
      });
   }
}

function dragEnd( e ) {
   let refLineExists = ( typeof e.object.userData.refLine === 'undefined' ) ? false : true;
   let lineExists = ( typeof dragLines.getObjectByName( e.object.name ) === 'undefined' ) ? false : true;
   if( refLineExists === false && lineExists === false ) {
      let material = new THREE.LineBasicMaterial( { color: 'grey',linewidth: 2 } );
      let lineGeometry = new THREE.Geometry();
      lineGeometry.vertices.push( new THREE.Vector3().copy( e.object.userData.refThemeCoords ), new THREE.Vector3().copy( e.object.position ));
      let line = new THREE.Line( lineGeometry, material );
      line.name = e.object.name;
      e.object.userData.refLine = line.name;
      dragLines.add( line );
   } else {
      let currentLine = dragLines.getObjectByName( e.object.userData.refLine ) ?? dragLines.getObjectByName( e.object.name );
      e.object.userData.refLine = currentLine.name;
      currentLine.geometry.vertices[1].copy( e.object.position );
      currentLine.geometry.verticesNeedUpdate = true;
   }
   spriteTextGroup.getObjectByName( e.object.userData.refObject ).userData.originalPosition = spriteTextGroup.getObjectByName( e.object.userData.refObject ).position;
   spriteTextGroup.getObjectByName( e.object.userData.refObject ).position.copy( e.object.position );
}

function dragObjsRestore() {
   console.log( 'Restoring ' + currentLoadedProfile + ' profile drag data...' );
   if( typeof savedProfileData[ currentLoadedProfile ].labelPositions === 'object' && typeof savedProfileData[ currentLoadedProfile ].dragVerts === 'object' ) {
      cleanDragLines();
      for( let i = 0;i < optimalItemsSize();i++ ) {
         let clusterName = clusterGroup.children[i].name;
         let clusterPosition = new THREE.Vector3().copy( clusterGroup.children[i].position );
         let labelPosition =  new THREE.Vector3().copy( savedProfileData[ currentLoadedProfile ].labelPositions[i] );
         spriteTextGroup.getObjectByName(`spriteText=${clusterName}`).position.copy( labelPosition );
         let visible = clusterGroup.children[i].visible;

         initSingleLabelLine( clusterName,clusterPosition,labelPosition,visible );
      }
      for( let i = 0;i < optimalItemsSize();i++ ) {
         dragLines.children[i].userData.originalPositions = savedProfileData[ currentLoadedProfile ].dragVerts[i];
      }

   }
   console.log( 'Label position and line restore complete...');

}

function initSingleLabelLine( name,clusterPosition,labelPosition,visible = true ) {
   let material = new THREE.LineBasicMaterial( { color: 'grey',linewidth: 4 } );
   let lineGeometry = new THREE.Geometry();
   lineGeometry.vertices.push( clusterPosition,labelPosition );
   let line = new THREE.Line( lineGeometry, material );
   line.userData.themeName = name;
   line.userData.labelName = `spriteText=${name}`;
   line.userData.originalPositions = {
      clusterPosition: clusterPosition,
      labelPosition: labelPosition
   };
   line.name = name;
   line.visible = visible;
   dragLines.add( line );
}