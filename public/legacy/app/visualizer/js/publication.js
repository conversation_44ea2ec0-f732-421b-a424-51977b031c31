"use strict";
class PublicationTable {
    constructor(name, score, entities, scores, expressions) {
        this.name = name;
        this.score = score;
        this.entities = entities;
        this.scores = scores;
        this.expressions = expressions;
    }
    logObj() {
        console.log(this);
    }
}

class PublicationGroup {
    constructor(tables) {
    	this.account = ProjectAcct;
    	this.project = Project;
        this.tables = tables;
        this.AcctId = AcctId;
        this.ProjectId = ProjectId;
    }
    sendTables() {
        const jsonString = 'tables=' + JSON.stringify(this.tables).replace(/&/g, '');
        const xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                const link = this.responseText;
                const downloadWindow = window.open(link);
                downloadWindow.onunload = function() {
                    const xmlhttp2 = new XMLHttpRequest();
                    xmlhttp2.onreadystatechange = function() {
                        if (this.readyState == 4 && this.status == 200) {
                        	console.log(this.responseText)
                        }
                    }
                    xmlhttp2.open("GET", Webroot + "legacy/app/assets/php/publication.php?q=delete&l=" + encodeURI(link) + '&a=' + encodeURIComponent(ProjectAcct) + '&p=' + encodeURIComponent(Project), true);
                    xmlhttp2.send();
                }
            }
        };
        xmlhttp.open("POST", Webroot + "legacy/app/assets/php/publication.php", true);
        xmlhttp.setRequestHeader("Content-type", "application/x-www-form-urlencoded");
        xmlhttp.send(jsonString + '&account=' + encodeURIComponent(this.account) + '&project=' + encodeURIComponent(this.project) + '&filename=' + encodeURIComponent(parameters.h2));

    }
}

let tmpExpList = ( sessionStorage.Expression_List.includes( '\n' )) ? sessionStorage.Expression_List.trim().replaceAll( '\r','' ).split('\n') : sessionStorage.Expression_List.trim().replaceAll( '\n','' ).split('\r');
let tmpEntList = ( sessionStorage.Entity_List.includes( '\n' )) ? sessionStorage.Entity_List.trim().replaceAll( '\r','' ).split('\n') : sessionStorage.Entity_List.trim().replaceAll( '\n','' ).split('\r');
const expList = ( tmpExpList.length === 1 && tmpExpList[0] === 'undefined' ) ? 'undefined' : [...tmpExpList];
const entList = ( tmpEntList.length === 1 && tmpEntList[0] === 'undefined' ) ? 'undefined' : [...tmpEntList];
if( entList !== 'undefined' ) {
    for(let i=0;i<entList.length;i++) {
        entList[i] = entList[i].toLowerCase();
    }
}

function exportPublicationTables() {
	const publicationData = [];
	selected.forEach(function(themeName) {
		const themeUserData = clusterGroup.getObjectByName(themeName).userData;
		const themeExpressions = [];
        if(expList !== "undefined") {
            themeUserData.Genes.forEach(( gene ) => {
                let needle = ( gene[0] === '(' ) ? gene.slice( 3 ) : gene;
                let needleIndex = entList.indexOf( needle );
                if( needleIndex >= 0 ) {
                    themeExpressions.push( expList[ needleIndex ]);
                }
            });
			console.log(themeExpressions);
		}
		const themeData = new PublicationTable(themeUserData.Name.replaceAll('<br>', ' '), themeUserData.Score, themeUserData.Genes, themeUserData.Gene_Scores, themeExpressions);
		publicationData.push(themeData);
	});
	const selectedThemes = new PublicationGroup(publicationData);
	selectedThemes.sendTables();
}