var {
	get, setState, r, globalState, onRefChange
} = (function(){
	
	const onChangeCallbacks = [];
	const contextMap = new WeakMap();

	function defaultContext() {
		return window;
	}

	function resolveItemAndContext(ctx, item) {
		if (item === undefined && typeof ctx === 'string') {
			item = ctx;
			ctx = defaultContext();
		}
		
		return { item, ctx };
	}
	function emptyReference() {
		return {
			value() {
				return undefined;
			},
			update() {
				return;
			}
		}
	}
	function referenceFor(ctx, item) {
		if (typeof ctx !== 'object' || ctx === null) {
			return emptyReference();
		}
		if (!contextMap.has(ctx)) {
			contextMap.set(ctx, {});
		}
		const paths = item.split('.');
		if (paths.length > 1) {
			return referenceFor(referenceFor(ctx, paths.shift()).value(), paths.join('.'));
		} 
		const existingRefs = contextMap.get(ctx);
		if (!(item in existingRefs)) {
			const ref = {
				value() {
					return ctx[item];
				},
				update(value) {
					const oldValue = ctx[item];
					ctx[item] = value;
					onChangeCallbacks.forEach((cb)=>{
						cb([ctx, item, value, oldValue]);
					});
					return value;
				}
			};
			existingRefs[item] = ref;
		}
		return existingRefs[item] ;
	}


	function get(maybeContxt, maybeItem) {
		const { ctx, item } = resolveItemAndContext(maybeContxt, maybeItem);
		return referenceFor(ctx, item).value();
	}
	function set(...args) {
		if (args.length <= 1) {
			return undefined;
		}
		if (args.length === 2) {
			const { ctx, item } = resolveItemAndContext(args[0]);
			return referenceFor(ctx, item).update(args[1]);
		} else {
			return referenceFor(args[0], args[1]).update(args[2]);
		}

	}

	function r(maybeContxt, maybeItem) {
		const { ctx, item } = resolveItemAndContext(maybeContxt, maybeItem);
		return referenceFor(ctx, item);
	}

	// r('name').value()
	// r('name').update(12);
	// get('name');
	// set('name', 'foo');


	var handler = {
		get(target, name){
			return get(name);
		},
		set(target, name, value) {
			set(name, value);
			return true;
		}
	};

	var globalState = new Proxy({}, handler);

	return {
		get,
		set,
		setState: set,
		r,
		globalState,
		onRefChange(cb) {
			onChangeCallbacks.push(cb);
		}
	}
})();

var GLOBAL_STATE = globalState; 