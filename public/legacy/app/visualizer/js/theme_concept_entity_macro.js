"use strict";
function rbarveHelp() {
	/*
		0x1F64B 0x200D 0x2642 0xFE0F
	*/
	const wavesEmoji = String.fromCodePoint(0x1F44B);
	console.log(`%cHi Ruteja! ${wavesEmoji}`,'color: DeepSkyBlue');
	console.log('%cTo map the Concepts to the associated Entities within each Theme use the function call below:','color: DeepSkyBlue');
	console.log('%crelationMappingMacro()','color: Yellow');
	return true;
}

async function relationMappingMacro() {
	await closePanels();
	const sidebarElements = await returnSidebarElements();
	const relationships = sidebarElements.map(clickSidebarElement);
	await closePanels();
	console.log(relationships);
	const tableString = await convertObjectToTable(relationships);
	const element = document.createElement('a');
	element.setAttribute('href', 'data:text/csv;charset=utf-8,' + encodeURIComponent(tableString));
	element.setAttribute('download', 'ThemeConceptEntityMapping.csv');
	element.style.display = 'none';
	document.body.appendChild(element);
	element.click();
	document.body.removeChild(element);

	// await downloadThemeEntityMapTable(tableString);
	// return relationships;
	return true;
}

function closePanels() {
	return new Promise((resolve) => {
		deselectAll();
		$("#dialog-wheel").dialog('close');
		resolve();
	});
}

function returnSidebarElements() {
	return new Promise((resolve) => {
		const sidebar = document.getElementById('theme-view');
		const eleArray = Array.from(sidebar.children);
		resolve(eleArray);
	});
}

function clickSidebarElement(element) {
	const themeObject = {};
	themeObject.id = element.id;
	themeObject.name = element.getElementsByClassName('clusterName')[0].innerText;
	element.click();
	const conceptTable = document.getElementById('concept-table');
	const conceptTableBody = conceptTable.getElementsByTagName('TBODY')[0];
	const conceptTableRows = Array.from(conceptTableBody.getElementsByTagName('TR'));
	themeObject.concepts = conceptTableRows.map(clickConceptTableRow);
	return themeObject;
}

function clickConceptTableRow(element) {
	const conceptObject = {};
	conceptObject.name = element.children[0].innerText;
	conceptObject.score = element.children[1].innerText;
	element.children[0].click();
	const entityTable = document.getElementById('color-wheel-table');
	const entityTableRows = Array.from(entityTable.getElementsByClassName('wheel-table-row'));
	conceptObject.entities = entityTableRows.map(entityTableDataReturn);
	return conceptObject;
}

function entityTableDataReturn(element) {
	const entityObject = {};
	entityObject.name = element.children[0].innerText;
	entityObject.score = element.children[1].innerText;
	return entityObject;
}

function convertObjectToTable(data) {
	return new Promise((resolve) => {
		const br = '\r\n';
		let str = `ID,Theme,Concepts${br}`;
		data.forEach((theme) => {
			let maxEntitiesLength = 0;
			let conceptRow = `${theme.id},${theme.name}`;
			theme.concepts.forEach((concept,index) => {
				conceptRow += (theme.concepts.length === index) ? br : `,${concept.name} (${concept.score})`;
				maxEntitiesLength = (concept.entities.length > maxEntitiesLength) ? concept.entities.length : maxEntitiesLength;
			});
			let entityRowsArray = [];
			for(let i = 0; i < maxEntitiesLength; i++) {
				let entityRow = [','];
				theme.concepts.forEach((concept) => {
					if(typeof concept.entities[i] === "undefined") {
						entityRow.push(',');
					} else {
						entityRow.push(`,${concept.entities[i].name} (${concept.entities[i].score})`);
					}
				});
				entityRow.push(br);
				entityRowsArray.push(entityRow.join(''));
			}
			str += conceptRow + br;
			str += entityRowsArray.join('');
		});
		resolve(str);
	});
}

/*
const br = '\n';
let str = `ID,Theme,Concepts${br}`;
fetch('./result.json')
.then(response => response.json())
.then((data) => {
	data.forEach((theme) => {
		let maxEntitiesLength = 0;
		let conceptRow = `${theme.id},${theme.name}`;
		theme.concepts.forEach((concept,index) => {
			conceptRow += (theme.concepts.length === index + 1) ? br : `,${concept.name} (${concept.score})`;
			maxEntitiesLength = (concept.entities.length > maxEntitiesLength) ? concept.entities.length : maxEntitiesLength;
		});
		let entityRowsArray = [];
		for(let i = 0; i < maxEntitiesLength; i++) {
			let entityRow = [','];
			theme.concepts.forEach((concept) => {
				if(typeof concept.entities[i] === "undefined") {
					entityRow.push(',');
				} else {
					entityRow.push(`,${concept.entities[i].name} (${concept.entities[i].score})`);
				}
			});
			entityRow.push(br);
			entityRowsArray.push(entityRow.join(''));
		}
		str += conceptRow;
		str += entityRowsArray.join('');
	});

});
*/