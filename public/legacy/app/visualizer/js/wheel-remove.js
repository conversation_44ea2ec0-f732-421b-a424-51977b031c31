"use strict";
function geneToConcepts( gene ) {
    log( gene );
    let obj = data2.find( o => o.Gene_ID === gene );
    if ( typeof(obj) === 'undefined' ) {
        log( 'gene cannot be found' );
    } else {
        let results = {};
        obj = Object.values( obj );
        obj.forEach( function( item ) {
            if ( Array.isArray( item ) ) {
                results[ item[ 0 ] ] = {};
                results[ item[ 0 ] ].Score = Number( item[ 1 ] ).toFixed( 2 );
            }
        } );
        let head = (bioExplorerProjectType === true) ? `
            <table id="color-wheel-table">
            <tr id="gene-head" onclick="centerGeneSelect('${gene}')">
                <th colspan="4">Selected Term: <span>${gene}</span></th>
            </tr>
        ` : `
            <table id="color-wheel-table">
            <tr id="gene-head" onclick="centerGeneSelect('${gene}')">
                <th colspan="4">Selected Entity: <span>${gene}</span></th>
            </tr>
        `;
        let table = '<tr class="table-th"><th>Concept</th><th>Score</th><th class="th-line-br">ChatGPT<br>Summary</th><th>Abstracts</th></tr>';
        for ( var ent in results ) {
            table += `<tr class="wheel-table-row">
                        <td>${ent}</td><td>${results[ent].Score}</td>
                        <td><button class="wheel-table-btns" onclick="chatGptSummaryQuery('${gene}', '${ent}')">Show Summary</button></td>
                        <td><button class="wheel-table-btns" onclick="abstractQuery('${gene}', '${ent}')">Show Abstracts</button></td>
                      </tr>`;
        }
        table += `</table>`;
        document.getElementById( 'dialog-wheel' ).innerHTML = head + table;
        $( '#dialog-wheel' ).dialog( 'option', 'width', 'auto' ).dialog( 'open' );
        closeGeneViewSelection();
        if ( appliedFilter !== 'None' ) {
            filterWheelTable( filterArray );
        }
    }
} //geneToConcepts End

function geneToClusterConcepts( gene ) {
    log( gene );
    if ( data[ selectedCluster.userData.indexKey ].Genes[ gene ].Concepts === undefined ) {
        log( 'gene cannot be found' );
    } else {
        let concepts = data[ selectedCluster.userData.indexKey ].Genes[ gene ].Concepts;
        let obj = Object.keys( concepts );
        let results = {};
        obj.forEach( function( item ) {
            results[ item ] = {};
            results[ item ].Score = Number( concepts[ item ] ).toFixed( 2 );
        } );
        let head = (bioExplorerProjectType === true) ? `
            <table id="color-wheel-table">
            <tr id="gene-head" onclick="centerGeneSelect('${gene}')">
                <th colspan="4">Selected Term: <span>${gene}</span></th>
            </tr>
        ` : `
            <table id="color-wheel-table">
            <tr id="gene-head" onclick="centerGeneSelect('${gene}')">
                <th colspan="4">Selected Entity: <span>${gene}</span></th>
            </tr>
        `;
        let table = '<tr class="table-th"><th>Concept</th><th>Score</th><th class="th-line-br">ChatGPT<br>Summary</th><th>Abstracts</th></tr>';
        for ( var ent in results ) {
            table += `<tr class="wheel-table-row">
                        <td>${ent}</td><td>${results[ent].Score}</td>
                        <td><button class="wheel-table-btns" onclick="chatGptSummaryQuery('${gene}', '${ent}')">Show Summary</button></td>
                        <td><button class="wheel-table-btns" onclick="abstractQuery('${gene}', '${ent}')">Show Abstracts</button></td>
                      </tr>`;
        }
        table += `</table>`;
        document.getElementById( 'dialog-wheel' ).innerHTML = head + table;
        $( '#dialog-wheel' ).dialog( 'option', 'width', 'auto' ).dialog( 'open' );
        closeGeneViewSelection();
        log( results );
        if ( appliedFilter !== 'None' ) {
            filterWheelTable( filterArray );
        }
    }
} //geneToClusterConcepts End

function geneToTube( gene ) {
    log( gene );
    let cluster1 = clusterGroup.getObjectByName( selectedTube.userData.endpoints[ 0 ] );
    let cluster2 = clusterGroup.getObjectByName( selectedTube.userData.endpoints[ 1 ] );
    let concepts = data[ cluster1.userData.indexKey ].Genes[ gene ].Concepts;
    let obj = Object.keys( concepts );
    let results = {};
    obj.forEach( function( item ) {
        results[ item ] = {};
        results[ item ].Score = Number( concepts[ item ] ).toFixed( 2 );
        results[ item ].Cluster = cluster1.userData.Name;
    } );
    concepts = data[ cluster2.userData.indexKey ].Genes[ gene ].Concepts;
    obj = Object.keys( concepts );
    obj.forEach( function( item ) {
        results[ item ] = {};
        results[ item ].Score = Number( concepts[ item ] ).toFixed( 2 );
        results[ item ].Cluster = cluster2.userData.Name;
    } );
    let head = (bioExplorerProjectType === true) ? `
        <table id="color-wheel-table">
        <tr id="gene-head" onclick="centerGeneSelect('${gene}')">
            <th colspan="3">Selected Term: <span>${gene}</span></th>
        </tr>
    ` : `
        <table id="color-wheel-table">
        <tr id="gene-head" onclick="centerGeneSelect('${gene}')">
            <th colspan="3">Selected Entity: <span>${gene}</span></th>
        </tr>
    `;
    let table = '<tr class="table-th"><th>Concept</th><th>Score</th><th>Theme</th></tr>';
    let objSorted = sortObjScores( results );
    objSorted.forEach( function( item ) {
        table += `<tr class="wheel-table-row" onclick="abstractQuery('${gene}', '${item}')"><td>${item}</td><td>${results[item].Score}</td><td class="table-theme-name">${results[item].Cluster}</td></tr>`;
    } );
    table += `</table>`;
    document.getElementById( 'dialog-wheel' ).innerHTML = head + table;
    $( '#dialog-wheel' ).dialog( 'option', 'width', 'auto' ).dialog( 'open' );
    closeGeneViewSelection();
    log( results );
    if ( appliedFilter !== 'None' ) {
        filterWheelTable( filterArray );
    }
} //geneToTube End

function centerGeneSelect( geneQuery ) {
    if(geneQuery.includes('(')) {
        geneQuery = geneQuery.substring( 3 );
    }
    window.open( 'https://www.genecards.org/cgi-bin/carddisp.pl?gene=' + encodeURIComponent( geneQuery.toUpperCase() ) + '&keywords=' + encodeURIComponent( geneQuery.toLowerCase() ) );
}

function abstractQuery( gene, concept ) {
    log( 'Gene --> ' + gene );
    log( 'Concept --> ' + concept );
    if ( $( '#dialog-session-confirm' ).hasClass( 'ui-dialog-content' ) ) {
        if ( $( '#dialog-session-confirm' ).dialog( 'isOpen' ) == false ) {
            let selectedObjConcepts = ( selectedCluster.userData.hasOwnProperty( 'combinedConcepts' ) ) ? selectedCluster.userData.combinedConcepts : selectedCluster.userData.Concepts;
            let abstractData = { 'Key': Key, 'a': gene, 'b': concept, 'ThemeConcepts': selectedObjConcepts, 'Version': ( typeof( mainCfg.wustlAbstractVersion ) !== 'undefined' ) ? true : false };
            localStorage.setItem( 'abstractData', JSON.stringify( abstractData ) );
            window.open( 'abstract/' );
        } else {
            log( 'you must resubmit your login credetials before proceeding...' );
        }
    } else {
        log( 'session-confirm has not initialized yet -> 4365' );
    }
}

function sortObjScores( obj ) {
    return Object.keys( obj ).sort( function( a, b ) {
        return obj[ b ].Score - obj[ a ].Score;
    } );
}

function filterWheelTable( filter ) {
    log( 'filtering tube popup table' );
    let filterMode = parameters.j0;
    let tableRows = [].slice.call( document.getElementsByClassName( 'wheel-table-row' ) );
    tableRows.forEach( function( row ) {
        let concept = row.children[ 0 ].innerText;
        let index = filter.indexOf( concept );
        if ( index < 0 && filterMode === 'Intersection' ) {
            row.classList.add( 'v-none' );
        } else if( index >= 0 && filterMode === 'Difference' ) {
            row.classList.add( 'v-none' );
        }
    } );
}

/*New ClusterToGeneSelect*/
function clusterConceptsToGenes( concept ) {
    let head = (bioExplorerProjectType === true) ? `
            <table id="color-wheel-table">
            <tr id="gene-head"><th colspan="4">Selected Concept: <span>${concept}</span></th></tr>
            <tr class="table-th"><th>Term</th><th>Score</th><th>Summaries</th><th>Abstracts</th></tr>` : `
            <table id="color-wheel-table">
            <tr id="gene-head"><th colspan="4">Selected Concept: <span>${concept}</span></th></tr>
            <tr class="table-th"><th>Entity</th><th>Score</th><th class="th-line-br">ChatGPT<br>Summary</th><th>Abstracts</th></tr>`;
    const index = selectedCluster.userData.indexKey;
    const filteredGenes = Object.keys( data[ index ].Genes ).filter( gene => {
        return data[ index ].Genes[ gene ].Concepts.hasOwnProperty( concept ) 
    });
    const genesScored = filteredGenes.sort(( a, b ) => { 
        return data[ index ].Genes[ b ].Concepts[ concept ] - data[ index ].Genes[ a ].Concepts[ concept ] 
    });
    const innerTableContents = genesScored.map( gene => {
        return `<tr class="wheel-table-row">
                    <td>${gene}</td><td>${Number( data[index].Genes[ gene ].Concepts[ concept ] ).toFixed( 2 )}</td>
                    <td><button class="wheel-table-btns" onclick="chatGptSummaryQuery('${gene}', '${concept}')">Show Summary</button></td>
                    <td><button class="wheel-table-btns" onclick="abstractQuery('${gene}', '${concept}')">Show Abstracts</button></td>
                </tr>`;
    });
    document.getElementById( 'dialog-wheel' ).innerHTML = head + innerTableContents.join('') + '</table>';
    $( '#dialog-wheel' ).dialog( 'option', 'width', 'auto' ).dialog( 'open' );
}

// ChatGPT Summaries
function chatGptSummaryQuery( gene, concept ) {
    log( 'Gene --> ' + gene );
    log( 'Concept --> ' + concept );
    alertInformation('ChatGPT Summaries Disabled Currently. This Feature Will Be Fixed Within the Next Few Days.');
    // if ( $( '#dialog-session-confirm' ).hasClass( 'ui-dialog-content' ) ) {
    //     if ( $( '#dialog-session-confirm' ).dialog( 'isOpen' ) == false ) {
    //         let selectedObjConcepts = ( selectedCluster.userData.hasOwnProperty( 'combinedConcepts' ) ) ? selectedCluster.userData.combinedConcepts : selectedCluster.userData.Concepts;
    //         let abstractData = { 'Key': Key, 'a': gene, 'b': concept, 'ThemeConcepts': selectedObjConcepts, 'Version': ( typeof( mainCfg.wustlAbstractVersion ) !== 'undefined' ) ? true : false, 'AiSummary': true };
    //         localStorage.setItem( 'abstractData', JSON.stringify( abstractData ) );
    //         window.open( 'abstract/' );
    //     } else {
    //         log( 'you must resubmit your login credetials before proceeding...' );
    //     }
    // } else {
    //     log( 'session-confirm has not initialized yet -> 4365' );
    // }
}