{"name": "visualizer", "version": "1.0.0", "description": "Visualizer", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+ssh://*****************/percayai/visualizer.git"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-commit": "git update-index --again"}}, "devDependencies": {"eslint": "^6.2.0", "eslint-config-prettier": "^6.0.0", "jsdom": "^14.0.0", "lint-staged": "^9.2.3", "prettier": "1.18.2"}, "author": "", "license": "", "homepage": "https://bitbucket.org/percayai/visualizer#readme", "lint-staged": {"./epmdev/**/*.js": ["prettier --write", "eslint --fix", "git add"], "./js/mainCfg.js": ["prettier --write", "eslint --fix", "git add"]}, "dependencies": {"husky": "^3.0.5"}}