<?php
    include('../../../config/config.php');
    session_start();
    if(isset($_SESSION['LoggedIn'])){
        include(TEMPLATES_ROOT.DIRECTORY_SEPARATOR.'cpv_header.php');
        echo '<title>GTAC-CompBio Theme Image Results-WUSTL</title>';
        if(file_exists('js/search.min.css') && file_exists('js/search.min.js')) {
            echo '<link rel="stylesheet" type="text/css" href="css/search.min.css?ver='.filemtime('css/search.min.css').'">';
            echo '<script src="js/search.min.js?ver='.filemtime('js/search.min.js').'"></script>';
        } else {
            echo '<link rel="stylesheet" type="text/css" href="css/search.css?ver='.filemtime('css/search.css').'">';
            echo '<script src="js/search.js?ver='.filemtime('js/search.js').'"></script>';
        }        
    } else {
        session_destroy();
        header('Location: '.WEBROOT);exit;
    }
?>
</head>

<body onload="getQuery()">
    <div id="content-all-wrap">
        <h1 id='page-header'>Image Results</h1>
        <div id="stats">
            <p id="query_string"></p>
            <p id="total_results"></p>
        </div>
        <div id="results-wrap">
            <div id='results'>
            </div>
        </div>
    </div>
    <div id="load-more-btn">
        <button onclick="getMoreImgs()">Load More Images...</button>
    </div>
    <div id="outer-item-modal">
        <div id="item-modal">
            <div id="panel-close" onclick="panelClose()"><i class="fa fa-times-circle" aria-hidden="true"></i></div>
            <div id="selectedImg-wrapper">
            </div>
            <div id="info-wrapper">
            </div>
        </div>
    </div>
    <?php echo file_get_contents(TEMPLATES_ROOT.DIRECTORY_SEPARATOR.'footer.html'); ?>
