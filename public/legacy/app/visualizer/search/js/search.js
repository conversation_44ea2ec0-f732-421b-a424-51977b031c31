'use strict';
var imageQuery = '';
var KEY = 'AIzaSyCKFodD1VvFr3Oa66O0xyg-fu9_6tX-Dpc';
var CX = '010920598414907349300:jblwmpe81ju';
var data = {};
var itemObj = [];
var theIndex = 1;
var htmlPlaceHolder = 0;

function getQuery() {
    log(window.location.search)
    imageQuery = window.location.search;
    imageQuery = decodeURIComponent(imageQuery);
    imageQuery = imageQuery.substr(1);
    // imageQuery = imageQuery.replace(/,/g, '+');
    log(imageQuery)
    search(encodeURI(imageQuery));
}

//Google API Query Function
function search(query) {
    if (theIndex < 101) {
        var request = new XMLHttpRequest();
        request.open('GET', 'https://www.googleapis.com/customsearch/v1?key=' + KEY + '&cx=' + CX + '&q=' + query + '&searchType=image&alt=json&filter=1&start=' + theIndex, true);
        request.onload = function() {
            // Begin accessing JSON data here
            data = JSON.parse(this.response);
            if (request.status >= 200 && request.status < 400) {
                if (data.items) {
                    log(data)
                    imageObjConstructor(data);
                } else {
                    log('error -> no results returned')
                }
            } else {
                log('error');
            }
        }
        request.send();
    } else {
        log('cannot exceed image result startIndex of 101')
    }

}
//Add items to one obj
function imageObjConstructor(data) {
    for (let i = 0; i < data.items.length; i++) {
        itemObj.push(data.items[i]);
    }
    filterResults();
    displayOutput(itemObj);
}
//Filter out duplicate images
function filterResults() {
    for (let i = 0; i < itemObj.length; i++) {
        let currentSnippet = itemObj[i].snippet;
        let currentContext = itemObj[i].image.contextLink;
        if(currentSnippet == '') {
            currentSnippet = 'Snippet -> no data';
            log(currentSnippet);
        }
        if(currentContext == '') {
            currentContext = 'Context -> no data';
            log(currentSnippet)
        }
        let outer = document.getElementById('results');
        for (let z = outer.children.length; z < itemObj.length; z++) {
            if (i == z) {
                continue;
            } else {
                log('Logging Item-->  ' + itemObj[z]);
                if (currentSnippet == itemObj[z].snippet || currentContext == itemObj[z].image.contextLink ) {
                    log('BF z -> ' + z)
                    log('BF OT -> ' + outer.children.length)
                    log('Current -> ' + currentSnippet + ' Item -> ' + itemObj[z].snippet)
                    log('Current -> ' + currentContext + ' Item -> ' + itemObj[z].image.contextLink)
                    let rmvItem = itemObj.splice(z, 1);
                    log(rmvItem)
                    if(z>0) {
                        z--;
                    }
                    log('AF z -> ' + z)
                    log('AF OT -> ' + outer.children.length)
                } else if (itemObj[z].link.includes('preview') || itemObj[z].link.includes('page') || itemObj[z].image.contextLink.includes('download') || itemObj[z].link.includes('Expires=') || itemObj[z].link.includes('x-raw-image')) {
                    if(itemObj[z].link.includes('preview')) {
                        log('Contains preview') 
                        log(itemObj[z].link)
                        let rmvItem = itemObj.splice(z, 1);
                        log(rmvItem)
                        if(z>0) {
                            z--;
                        }
                    }
                    if(itemObj[z].link.includes('page')) {
                        log('Contains page')
                        log(itemObj[z].link)
                    }
                    if(itemObj[z].image.contextLink.includes('download')) {
                        log('Contains donwload')
                        log(itemObj[z].image.contextLink)
                    }
                    if(itemObj[z].link.includes('Expires=')) {
                        log('Contains Expires=')
                        let rmvItem = itemObj.splice(z, 1);
                        log(rmvItem)
                        if(z>0) {
                            z--;
                        }
                    }
                    if(itemObj[z].link.includes('x-raw-image')) {
                        log('Contains x-raw-image')
                        let rmvItem = itemObj.splice(z, 1);
                        log(rmvItem)
                        if(z>0) {
                            z--;
                        }                 
                    }
                } else if(itemObj[z].link.includes('x-raw-image:')) {
                        log('Not a Valid Image Type')
                        log(itemObj[z].link)
                        let rmvItem = itemObj.splice(z, 1);
                        log(rmvItem)
                        if(z>0) {
                            z--;
                        }              
                    }
            }
        }
    }
}

//decode URI component, return as a string
function decode(obj) {
    obj = decodeURIComponent(obj.replace(/\+/g, " "));
    return obj;
}

//display results 
function displayOutput(obj) {
    document.getElementById('query_string').innerHTML = data.queries.request[0].searchTerms;
    document.getElementById('total_results').innerHTML = data.searchInformation.formattedTotalResults;
    let outer = document.getElementById('results');
    // outer.innerHTML = '';
    for (let i = outer.children.length; i < obj.length; i++) {
            setTimeout(function() {
            let container = document.createElement('DIV');
            container.classList.add('img-container');
            container.addEventListener('click', imgSelect);
            let img = document.createElement('IMG');
            img.classList.add('result-img');
            img.src = obj[i].link;
            img.alt = obj[i].image.contextLink;
            img.setAttribute('data-index', i);
            // img.addEventListener('click', imgSelect);
            container.appendChild(img);
            // container.innerHTML = '<img src="' + obj.items[i].link + '" class="result-img">';
            outer.appendChild(container);
        }, 500);

    }
    log('results displayed')
}
//load more images
function getMoreImgs() {
    if (!data.error && data.queries.nextPage) {
        theIndex = data.queries.nextPage[0].startIndex;
        search(encodeURI(imageQuery));
    } else {
        log('CSE error: ')
        log('no more results to get or no more nextPages')
    }
}

function imgSelect(elmt) {
    let selectedOuterWrapper = document.getElementById('selectedImg-wrapper');
    if (selectedOuterWrapper.children.length > 0) {
        selectedOuterWrapper.innerHTML = '';
    }
    if ($('#outer-item-modal').css('display') == 'none') {
        $('#outer-item-modal').slideToggle();
    }
    let imgSrc = document.createElement('IMG');
    imgSrc.src = elmt.target.src;
    let index = elmt.target.getAttribute('data-index');
    log(imgSrc)
    log(index)
    document.getElementById('selectedImg-wrapper').appendChild(imgSrc);
    document.getElementById('info-wrapper').innerHTML = '<p><span>Display Link:</span><br>' + itemObj[index].displayLink + '</p>' +
        '<p><span>Snippet:</span><br>' + itemObj[index].htmlSnippet + '</p>' +
        '<p><span>Context Link:</span><br>' + itemObj[index].image.contextLink + '</p>' +
        '<a href="' + itemObj[index].image.contextLink + '" target="_blank"><div class="link-btn">Go To Document</div></a>' +  
        '<p><span>Image Link:</span><br>' + itemObj[index].link + '</p>' +
        '<a href="' + itemObj[index].link + '" target="_blank"><div class="link-btn">Open Image In New Tab</div></a>' +
        '<p><span>Height:</span><br>' + itemObj[index].image.height + ' Width: ' + itemObj[index].image.width + '</p>' +
        '<p><span>Type:</span><br>' + itemObj[index].mime + '</p>';
}

function panelClose() {
    $('#outer-item-modal').slideToggle();
}
function goToContext(pageLink) {
    log(pageLink)
}