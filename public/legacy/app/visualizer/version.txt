//CompBio Visualizer Release Notes//

--Version 1.4--
Feature Updates
-svg export tool to replace pdf capture
-seperated abstract and image-search logic/files into visualizer sub-directories

Bug Fix/Patches
-wustl-version abstracts fixed - set mainCfg variable

--Version 1.3-- 
Feature Updates
-screen capture/edit pdf tool
-visualizer Optimization
-publication export
-concept/in sphere text lists sort (sorted longest strings toward the center)
-dynamic score filter value (Optimized mode on default score 65 vs off score default 30)
-depth-filter (z-index slider)
-depth-filter on/off switch
-dynamic score filter default value (themes > 50 score = 30 : themes <= 50 score = 65)

Bug Fix/Patches
-blank concept map
-css hud display cleaned up
-capture tool pixel ratio setting
-ThemeNameRegistry error sets to NULL (resulted from optimization mode)
-userProfileData object sets to NULL when last profile is deleted(due to the way php handles empty JSON objects) 
-group issues due to optimization mode
-increase readability of sphere text (changed font, increased font-weight)
-adjusted the camera FOV from 45 to 30
-adjusted the camera default z position from (100xPM) to (150xPM) 
-collision adjustment incorrectly positioning spriteText objs (above sphere text)
-capture tool list positions adjusted
-new group initial color black
-tubes not being drawn
-ThemeNameRegistry assign error on save 

--Version 1.2--
Feature Updates
-color-wheel removed/replaced with popup table
-added project comparison filter creation
-added project comparison project creation 
-added apply comparison filter on visualization
-filter application filters popup displays/HUD/sphere text
-updated new account creation autofill account name
-visualizer export function retains annotated theme names
-profile save/restore retains wireframe selection 
-visualizer added dynamic visible entity total of total entities
-added Theme name in popup dragbar
-added data file length check onload
-concept map added notice "2 or more" notice in header
-added animations to dialog show/hide boxes
-mobile repositioned "Feedback" and "Help" buttons
-mobile added logout button to menu
-mobile resized content containers (all except visualizer)

Bug Fix/Patches
-synonym alert on project submission
-reanalyze project submission
-sudo copy/paste project
-removed the term "Gene" from client-side
-removed the term "Cluster" from client side
-session prompt initiation error
-tube popup panel displays annotated theme names
-theme concept sorting/displaying in correct order (low to high) on project loading
-concept map blank/black display

------------------------------------------------

--Version 1.1--
Feature Updates
-Responsive Design via Bootstrap 4 (added in all areas except visualizer templates)
-Sphere collision detection and auto-adjustment
-Admin panel project folders (completed project list generated from projects.json now instead of php scandir)
-Abstract page dynamic key ("Selected Gene"&"Selected Concepts" vs "Selected Concepts":when querying abstract via concept map)
-Profile data sync with server before saving profile data (prevent overwriting of other profiles)
-Added theme name to theme popup
-Adjusted default filter values
-Utils scripts for creating files account_info.json, projects.json and info.json
-Added "Reanalyzation Date" to "Project Information" table (reanalyzed projects only) 

Bug Fix/Patches
-scale factor limted to minimum value of 1
-restoring saved groups when profile loaded
-hidden groups visible
-abstract selected gene not highlighting

-------------------------------------------------

--Version 1.0--
Feature Updates
-Version Display
-Login Timeout (Revarification after inactivity)
-CSS3D Gene Haloing and Color Range based on enrichment score
-Gene halo color reference key
-Hide/Show Groups (changed checkbox to button)
-PMID abstracts/gene synonyms api
-PMID keyword clientside highlighting
-PMID keyword color reference key
-Group tab moved to HUD
-Admin page submission processing type
-Submission concept autofill amount adjusted
-Submission context "cells" default removed
-Group deselected on saved profile load
 
Bug Fix/Patches 
-Account email/password change 
-Hide multiple groups 
-Concept map edge selection to PMID abstract
