<!DOCTYPE html>
<html lang="en">
<head>
   <meta charset="UTF-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Abstract Proxy Test</title>
   <script src="../libs/jquery/jquery-3.4.1.min.js"></script>
</head>
<body>
   <h1>Proxy Test</h1>
   <div id="output">

   </div>
   <script>
      let testData = ["16777964","16890524","17352737","17483508","18029449","18460602","18824692","20409480","20624975","26908646"];
      document.body.onload = function() {
         console.log(testData);
         getPublicationsProxy( testData );
      };
      function getPcmmEndpoint() {
         let hostname = ( location.host === 'localhost' ) ? 'at-2.wucon.wustl.edu:8001/pcmmrpc' : location.host + ':8001/pcmmrpc';
         if( hostname.includes('gtac-compbio.wustl.edu')) {
            console.log('MainServerWithDNS: using proxy...');
            hostname = location.host + '/pcmm_proxy_api/';
         } else if( hostname.includes('gtac-compbio-demo.wustl.edu')) {
            console.log('MainExternalServerWithDNS: using proxy...');
            hostname = location.host + '/pcmm_proxy_api/';
         } else if( hostname.includes('gtac-compbio-ex.wustl.edu')) {
            console.log('MainExternalServerWithDNS: using proxy...');
            hostname = location.host + '/pcmm_proxy_api/';
         } else {
            console.log('Using local address...');
         }
         let protocol = location.protocol + '//';
         let connectionURL = `${protocol}${hostname}`;
         console.log(connectionURL);
         return connectionURL;
      }
      function getPublicationsProxy( array ) {
         // let testingServer = 'https://gtac-compbio-demo.wustl.edu/';
         // let testingServer = 'http://at-3.wucon.wustl.edu/';
         let testingServer = getPcmmEndpoint();
         $.ajax({
            url : testingServer,
            type : 'POST',
            data : JSON.stringify( array ),
            success : function(response) {
                  response = JSON.parse( response );
                  console.log(response);
                  if(typeof response !== 'undefined' && response.length > 0) {
                     document.getElementById("output").innerText = response;
                  } else {
                     console.log('ERROR: pcmm query returned no results...')
                  }
            },
            error: (error) => {
                  console.log(error)
            }
         });
      }      
   </script>
</body>
</html>