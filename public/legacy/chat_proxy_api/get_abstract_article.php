<?php

$json = file_get_contents('php://input');
$data = json_decode($json);

$resp = getAbstractArticles($data);
print_r(json_encode($resp));
function getAbstractArticles($arr)
{
   $curl = curl_init();
   curl_setopt_array(
      $curl,
      array(
         CURLOPT_URL => 'localhost:8001/pcmmrpc',
         CURLOPT_RETURNTRANSFER => true,
         CURLOPT_ENCODING => '',
         CURLOPT_MAXREDIRS => 10,
         CURLOPT_TIMEOUT => 0,
         CURLOPT_FOLLOWLOCATION => true,
         CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
         CURLOPT_CUSTOMREQUEST => 'POST',
         CURLOPT_POSTFIELDS => '{"jsonrpc":"2.0","method":"GetCordArticle","id":"202","params":' . json_encode($arr) . '}',
         CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
         ),
      )
   );
   $response = curl_exec($curl);
   curl_close($curl);
   return $response;
}
