<?php

$resp = getBioExplorerFormData();
print_r(json_encode($resp));
function getBioExplorerFormData()
{
    $body = (object)[
        'jsonrpc' => '2.0',
        'method' => 'GetAllIdeaNames',
        'id' => '55',
        'params' => null
    ];

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, 'localhost:8001/pcmmrpc');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body));
    curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');

    $headers = array();
    $headers[] = 'Connection: keep-alive';
    $headers[] = 'Content-Type: text/plain;charset=UTF-8';
    $headers[] = 'Accept: */*';
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);


    $result = curl_exec($ch);
    if (curl_errno($ch))
    {
        return 'Error:' . curl_error($ch);
    }
    curl_close($ch);

    return json_decode($result);
}

?>