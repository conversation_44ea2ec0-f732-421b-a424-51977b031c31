<?php

$data = file_get_contents('php://input');
$output = submitCommand($data);
header('Content-Type: application/json');
echo $output;

function submitCommand($json)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/api/washu-chatgpt-query');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);
    curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');
    $headers = [
        'Connection: keep-alive',
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Basic ' . base64_encode('admin:password')
    ];
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $result = curl_exec($ch);
    if (curl_errno($ch)) {
        return json_encode(['error' => curl_error($ch)]);
    }
    curl_close($ch);
    return $result;
}