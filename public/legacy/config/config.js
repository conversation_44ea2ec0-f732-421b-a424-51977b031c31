'use strict';
const log = console.log.bind(console);
const Webroot = getWebroot();
const PcmmEndpoint = getPcmmEndpoint();
const AppWebroot = getAppWebroot();

function getWebroot() {
   let protocol = location.protocol;
   let host = location.host;
   let origin = location.origin;
   let pathname = location.pathname;
   if( host === 'localhost' || host === '127.0.0.1:8000' ) {
      let localenv = pathname.replace(/^\//g,'').replace(/\/$/g,'').split('/');
      if( localenv.length === 1 && localenv[ 0 ] === '' ) {
         return origin + '/';
      } else {
         return origin + '/' + localenv[0] + '/';
      }
   } else {
      return origin + '/';
   }
}
function getAppWebroot() {
    return origin + '/';
}
function getPcmmEndpoint() {
   let hostname = ( location.host === 'localhost' ) ? 'at-2.wucon.wustl.edu:8001/pcmmrpc' : location.host + ':8001/pcmmrpc';
   if( hostname.includes('gtac-compbio.wustl.edu')) {
      console.log('MainServerWithDNS: using proxy...');
      hostname = location.host + '/legacy/pcmm_proxy_api/';
   } else if( hostname.includes('gtac-compbio-demo.wustl.edu')) {
      console.log('MainExternalServerWithDNS: using proxy...');
      hostname = location.host + '/legacy/pcmm_proxy_api/';
   } else if( hostname.includes('gtac-compbio-ex.wustl.edu')) {
      console.log('MainExternalServerWithDNS: using proxy...');
      hostname = location.host + '/legacy/pcmm_proxy_api/';
   } else {
      console.log('Using local address...');
   }
   let protocol = location.protocol + '//';
   let connectionURL = `${protocol}${hostname}`.replace(':8000','');
   console.log(connectionURL);
   return connectionURL;
}
function pcmmConnectionTest(){
    let postBody = {
        "jsonrpc":"2.0",
        "method":"GetPCMMVesrionInfo",
        "id":"1001",
        "params":""
    };
    let opts = {"body": JSON.stringify( postBody ),"method":"POST","mode":"cors"};
    let hostname = ( location.host === 'localhost' ) ? 'at-2.wucon.wustl.edu:8001/pcmmrpc' : location.host + ':8001/pcmmrpc';
    if( hostname.includes('gtac-compbio.wustl.edu')) {
        console.log('MainServerWithDNS: using proxy...');
        hostname = location.host + '/legacy/pcmm_proxy_api/';
    } else if( hostname.includes('gtac-compbio-demo.wustl.edu')) {
        console.log('MainExternalServerWithDNS: using proxy...');
        hostname = location.host + '/legacy/pcmm_proxy_api/';
    } else if( hostname.includes('gtac-compbio-ex.wustl.edu')) {
        console.log('MainExternalServerWithDNS: using proxy...');
        hostname = location.host + '/legacy/pcmm_proxy_api/';
    } else {
        console.log('Using local address...');
    }
    hostname = hostname.replace(':8000','');
    let protocol = location.protocol + '//';
    fetch(`${protocol}${hostname}`, opts)
    .then(resp => console.log(resp))
    .catch(err => console.log('ERROR!!!: ' + err));
}
pcmmConnectionTest();

function getHostNameRoot() {
   let protocol = location.protocol + '//';
   let hostname = location.hostname;
   return `${protocol}${hostname}`;
}
function getChatApiEndPoint() {
   /* 
   * 08/06/2025 Switched to proxy for all
   * Something changed and the direct connection is no longer working.
   * Failing due to CORS blocking
   */
   console.log('Using Proxy Chat API');
   return `${location.protocol}//${location.host}/legacy/chat_proxy_api/`;
   // if(
   //    location.host.includes('gtac-compbio.wustl.edu') || 
   //    location.host.includes('gtac-compbio-ex.wustl.edu')
   // ) {
   //    console.log('Using Proxy Chat API');
   //    return `https://${location.host}/legacy/chat_proxy_api/`;
   // } else {
   //    console.log('Using Direct API Chat Connection');
   //    return `${location.protocol}//${location.host.split(':')[0]}:8080/api/washu-chatgpt-query`;
   // }
}