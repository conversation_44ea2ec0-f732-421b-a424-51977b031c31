<?php
/*
 * !!! IMPORTANT !!!
 * Variables That MUST Be Filled In:
 * WEBROOT, ABS_ROOT, ACCOUNT_STORAGE_ROOT, ASSERTION_ENGINE_ROOT, RANDOM_RUN_ROOT
 * 
 * If running in a developement enviroment and just need the web interface without the PCMM connection and Assertion Engine
 * the ASSERTION_ENGINE_ROOT and RANDOM_RUN_ROOT can be skipped.
*/
define('HOSTNAME', $_SERVER['HTTP_HOST']);

// Web Root URL
// Example: "https://gtac-compbio.wustl.edu/"
define('WEBROOT', '');
define('ADMIN_WEBROOT', WEBROOT.'app/admin');
define('CBV_WEBROOT', WEBROOT.'app/visualizer');
define('CSS_WEBROOT', WEBROOT.'app/assets/css');
define('JS_WEBROOT', WEBROOT.'app/assets/js');
define('IMAGES_WEBROOT', WEBROOT.'app/assets/images');

// Apache Server Public Root Path of Application 
// ( Unless using a virtual host, the default typically is "/var/www/html" ) 
define('ABS_ROOT', '');
define('LIBS_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'libs');
define('CSS_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'assets'.DIRECTORY_SEPARATOR.'css');
define('JS_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'assets'.DIRECTORY_SEPARATOR.'js');
define('IMAGES_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'assets'.DIRECTORY_SEPARATOR.'images');
define('TEMPLATES_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'templates');
define('CBV_VIZ_WEBROOT', WEBROOT.'app/visualizer');
define('CSS_VIZ_WEBROOT', WEBROOT.'app/visualizer/css');
define('JS_VIZ_WEBROOT', WEBROOT.'app/visualizer/js');
define('IMAGES_VIZ_WEBROOT', WEBROOT.'app/visualizer/images');
define('CSS_VIZ_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'visualizer'.DIRECTORY_SEPARATOR.'css');
define('JS_VIZ_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'visualizer'.DIRECTORY_SEPARATOR.'js');
define('IMAGES_VIZ_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'visualizer'.DIRECTORY_SEPARATOR.'images');
define('TEMPLATES_VIZ_ROOT', ABS_ROOT.DIRECTORY_SEPARATOR.'app'.DIRECTORY_SEPARATOR.'templates');

// Account Storage Directory Path
// Example: "/huge1/COMPBIO"
define('ACCOUNT_STORAGE_ROOT', '');

// Assertion Engine Root Directory Path
// Example: "/huge1/nyee/assertion_engine/"
define('ASSERTION_ENGINE_ROOT', '');

// PCMM Random Run Root Path
// Example: "/huge1/nyee/PCMM/random_genes/Compbio_outputs"
define('RANDOM_RUN_ROOT', '');

// Assertion N Random Genes Allocation
define('N_RANDOM_GENES', 100000);