var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,h,f){e!=Array.prototype&&e!=Object.prototype&&(e[h]=f.value)};$jscomp.getGlobal=function(e){return"undefined"!=typeof window&&window===e?e:"undefined"!=typeof global&&null!=global?global:e};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.polyfill=function(e,h,f,z){if(h){f=$jscomp.global;e=e.split(".");for(z=0;z<e.length-1;z++){var k=e[z];k in f||(f[k]={});f=f[k]}e=e[e.length-1];z=f[e];h=h(z);h!=z&&null!=h&&$jscomp.defineProperty(f,e,{configurable:!0,writable:!0,value:h})}};$jscomp.polyfill("Math.imul",function(e){return e?e:function(e,f){e=Number(e);f=Number(f);var h=e&65535,k=f&65535;return h*k+((e>>>16&65535)*k+h*(f>>>16&65535)<<16>>>0)|0}},"es6","es3");
$jscomp.polyfill("Math.clz32",function(e){return e?e:function(e){e=Number(e)>>>0;if(0===e)return 32;var f=0;0===(e&4294901760)&&(e<<=16,f+=16);0===(e&4278190080)&&(e<<=8,f+=8);0===(e&4026531840)&&(e<<=4,f+=4);0===(e&3221225472)&&(e<<=2,f+=2);0===(e&2147483648)&&f++;return f}},"es6","es3");$jscomp.polyfill("Math.trunc",function(e){return e?e:function(e){e=Number(e);if(isNaN(e)||Infinity===e||-Infinity===e||0===e)return e;var f=Math.floor(Math.abs(e));return 0>e?-f:f}},"es6","es3");
$jscomp.SYMBOL_PREFIX="jscomp_symbol_";$jscomp.initSymbol=function(){$jscomp.initSymbol=function(){};$jscomp.global.Symbol||($jscomp.global.Symbol=$jscomp.Symbol)};$jscomp.symbolCounter_=0;$jscomp.Symbol=function(e){return $jscomp.SYMBOL_PREFIX+(e||"")+$jscomp.symbolCounter_++};
$jscomp.initSymbolIterator=function(){$jscomp.initSymbol();var e=$jscomp.global.Symbol.iterator;e||(e=$jscomp.global.Symbol.iterator=$jscomp.global.Symbol("iterator"));"function"!=typeof Array.prototype[e]&&$jscomp.defineProperty(Array.prototype,e,{configurable:!0,writable:!0,value:function(){return $jscomp.arrayIterator(this)}});$jscomp.initSymbolIterator=function(){}};$jscomp.arrayIterator=function(e){var h=0;return $jscomp.iteratorPrototype(function(){return h<e.length?{done:!1,value:e[h++]}:{done:!0}})};
$jscomp.iteratorPrototype=function(e){$jscomp.initSymbolIterator();e={next:e};e[$jscomp.global.Symbol.iterator]=function(){return this};return e};$jscomp.makeIterator=function(e){$jscomp.initSymbolIterator();var h=e[Symbol.iterator];return h?h.call(e):$jscomp.arrayIterator(e)};$jscomp.FORCE_POLYFILL_PROMISE=!1;
$jscomp.polyfill("Promise",function(e){function h(){this.batch_=null}function f(e){return e instanceof k?e:new k(function(t,f){t(e)})}if(e&&!$jscomp.FORCE_POLYFILL_PROMISE)return e;h.prototype.asyncExecute=function(e){null==this.batch_&&(this.batch_=[],this.asyncExecuteBatch_());this.batch_.push(e);return this};h.prototype.asyncExecuteBatch_=function(){var e=this;this.asyncExecuteFunction(function(){e.executeBatch_()})};var z=$jscomp.global.setTimeout;h.prototype.asyncExecuteFunction=function(e){z(e,
0)};h.prototype.executeBatch_=function(){for(;this.batch_&&this.batch_.length;){var e=this.batch_;this.batch_=[];for(var f=0;f<e.length;++f){var k=e[f];delete e[f];try{k()}catch(pa){this.asyncThrow_(pa)}}}this.batch_=null};h.prototype.asyncThrow_=function(e){this.asyncExecuteFunction(function(){throw e;})};var k=function(e){this.state_=0;this.result_=void 0;this.onSettledCallbacks_=[];var f=this.createResolveAndReject_();try{e(f.resolve,f.reject)}catch(ha){f.reject(ha)}};k.prototype.createResolveAndReject_=
function(){function e(e){return function(t){k||(k=!0,e.call(f,t))}}var f=this,k=!1;return{resolve:e(this.resolveTo_),reject:e(this.reject_)}};k.prototype.resolveTo_=function(e){if(e===this)this.reject_(new TypeError("A Promise cannot resolve to itself"));else if(e instanceof k)this.settleSameAsPromise_(e);else{a:switch(typeof e){case "object":var f=null!=e;break a;case "function":f=!0;break a;default:f=!1}f?this.resolveToNonPromiseObj_(e):this.fulfill_(e)}};k.prototype.resolveToNonPromiseObj_=function(e){var f=
void 0;try{f=e.then}catch(ha){this.reject_(ha);return}"function"==typeof f?this.settleSameAsThenable_(f,e):this.fulfill_(e)};k.prototype.reject_=function(e){this.settle_(2,e)};k.prototype.fulfill_=function(e){this.settle_(1,e)};k.prototype.settle_=function(e,f){if(0!=this.state_)throw Error("Cannot settle("+e+", "+f|"): Promise already settled in state"+this.state_);this.state_=e;this.result_=f;this.executeOnSettledCallbacks_()};k.prototype.executeOnSettledCallbacks_=function(){if(null!=this.onSettledCallbacks_){for(var e=
this.onSettledCallbacks_,f=0;f<e.length;++f)e[f].call(),e[f]=null;this.onSettledCallbacks_=null}};var R=new h;k.prototype.settleSameAsPromise_=function(e){var f=this.createResolveAndReject_();e.callWhenSettled_(f.resolve,f.reject)};k.prototype.settleSameAsThenable_=function(e,f){var k=this.createResolveAndReject_();try{e.call(f,k.resolve,k.reject)}catch(pa){k.reject(pa)}};k.prototype.then=function(e,f){function h(e,f){return"function"==typeof e?function(f){try{t(e(f))}catch(qa){ia(qa)}}:f}var t,ia,
z=new k(function(e,f){t=e;ia=f});this.callWhenSettled_(h(e,t),h(f,ia));return z};k.prototype.catch=function(e){return this.then(void 0,e)};k.prototype.callWhenSettled_=function(e,f){function k(){switch(h.state_){case 1:e(h.result_);break;case 2:f(h.result_);break;default:throw Error("Unexpected state: "+h.state_);}}var h=this;null==this.onSettledCallbacks_?R.asyncExecute(k):this.onSettledCallbacks_.push(function(){R.asyncExecute(k)})};k.resolve=f;k.reject=function(e){return new k(function(f,k){k(e)})};
k.race=function(e){return new k(function(k,h){for(var t=$jscomp.makeIterator(e),z=t.next();!z.done;z=t.next())f(z.value).callWhenSettled_(k,h)})};k.all=function(e){var h=$jscomp.makeIterator(e),t=h.next();return t.done?f([]):new k(function(e,k){function z(f){return function(k){ba[f]=k;R--;0==R&&e(ba)}}var ba=[],R=0;do ba.push(void 0),R++,f(t.value).callWhenSettled_(z(ba.length-1),k),t=h.next();while(!t.done)})};return k},"es6","es3");
var DracoDecoderModule=function(e){function h(a){eval.call(null,a)}function f(a,b){a||M("Assertion failed: "+b)}function z(d){var b=a["_"+d];if(!b)try{b=eval("_"+d)}catch(c){}f(b,"Cannot call unknown function "+d+" (perhaps LLVM optimizations or closure removed it?)");return b}function k(a,b,c){b=b||"i8";"*"===b.charAt(b.length-1)&&(b="i32");switch(b){case "i1":return N[a>>0];case "i8":return N[a>>0];case "i16":return ra[a>>1];case "i32":return u[a>>2];case "i64":return u[a>>2];case "float":return xa[a>>
2];case "double":return ya[a>>3];default:M("invalid type for setValue: "+b)}return null}function R(a,b,c,g){if("number"===typeof a){var d=!0;var e=a}else d=!1,e=a.length;var S="string"===typeof b?b:null;c=4==c?g:["function"===typeof ka?ka:n.staticAlloc,n.stackAlloc,n.staticAlloc,n.dynamicAlloc][void 0===c?2:c](Math.max(e,S?1:b.length));if(d){g=c;f(0==(c&3));for(a=c+(e&-4);g<a;g+=4)u[g>>2]=0;for(a=c+e;g<a;)N[g++>>0]=0;return c}if("i8"===S)return a.subarray||a.slice?G.set(a,c):G.set(new Uint8Array(a),
c),c;g=0;for(var k,m;g<e;){var h=a[g];"function"===typeof h&&(h=n.getFunctionIndex(h));d=S||b[g];if(0===d)g++;else{"i64"==d&&(d="i32");var l=c+g,p=d;p=p||"i8";"*"===p.charAt(p.length-1)&&(p="i32");switch(p){case "i1":N[l>>0]=h;break;case "i8":N[l>>0]=h;break;case "i16":ra[l>>1]=h;break;case "i32":u[l>>2]=h;break;case "i64":tempI64=[h>>>0,(tempDouble=h,1<=+pb(tempDouble)?0<tempDouble?(qb(+rb(tempDouble/4294967296),4294967295)|0)>>>0:~~+sb((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)];u[l>>2]=
tempI64[0];u[l+4>>2]=tempI64[1];break;case "float":xa[l>>2]=h;break;case "double":ya[l>>3]=h;break;default:M("invalid type for setValue: "+p)}m!==d&&(k=n.getNativeTypeSize(d),m=d);g+=k}}return c}function t(d,b){if(0===b||!d)return"";for(var c=0,g,e=0;;){g=G[d+e>>0];c|=g;if(0==g&&!b)break;e++;if(b&&e==b)break}b||(b=e);g="";if(128>c){for(;0<b;)c=String.fromCharCode.apply(String,G.subarray(d,d+Math.min(b,1024))),g=g?g+c:c,d+=1024,b-=1024;return g}return a.UTF8ToString(d)}function Da(a,b,c,g){if(!(0<
g))return 0;var d=c;g=c+g-1;for(var e=0;e<a.length;++e){var f=a.charCodeAt(e);55296<=f&&57343>=f&&(f=65536+((f&1023)<<10)|a.charCodeAt(++e)&1023);if(127>=f){if(c>=g)break;b[c++]=f}else{if(2047>=f){if(c+1>=g)break;b[c++]=192|f>>6}else{if(65535>=f){if(c+2>=g)break;b[c++]=224|f>>12}else{if(2097151>=f){if(c+3>=g)break;b[c++]=240|f>>18}else{if(67108863>=f){if(c+4>=g)break;b[c++]=248|f>>24}else{if(c+5>=g)break;b[c++]=252|f>>30;b[c++]=128|f>>24&63}b[c++]=128|f>>18&63}b[c++]=128|f>>12&63}b[c++]=128|f>>6&
63}b[c++]=128|f&63}}b[c]=0;return c-d}function ha(a){for(var b=0,d=0;d<a.length;++d){var g=a.charCodeAt(d);55296<=g&&57343>=g&&(g=65536+((g&1023)<<10)|a.charCodeAt(++d)&1023);127>=g?++b:b=2047>=g?b+2:65535>=g?b+3:2097151>=g?b+4:67108863>=g?b+5:b+6}return b}function pa(d){return d.replace(/__Z[\w\d_]+/g,function(b){a:{var d=a.___cxa_demangle||a.__cxa_demangle;if(d)try{var g=b.substr(1),e=ha(g)+1;var f=ka(e);Da(g,G,f,e);var h=ka(4);var l=d(f,0,0,h);if(0===k(h,"i32")&&l){var m=t(l);break a}}catch(xd){}finally{f&&
Ha(f),h&&Ha(h),l&&Ha(l)}else n.warnOnce("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling");m=b}return b===m?b:b+" ["+m+"]"})}function ia(){a:{var d=Error();if(!d.stack){try{throw Error(0);}catch(b){d=b}if(!d.stack){d="(no stack trace available)";break a}}d=d.stack.toString()}a.extraStackTrace&&(d+="\n"+a.extraStackTrace());return pa(d)}function Ea(a,b){0<a%b&&(a+=b-a%b);return a}function ba(){a.HEAP8=N=new Int8Array(H);a.HEAP16=ra=new Int16Array(H);a.HEAP32=u=new Int32Array(H);
a.HEAPU8=G=new Uint8Array(H);a.HEAPU16=Va=new Uint16Array(H);a.HEAPU32=Wa=new Uint32Array(H);a.HEAPF32=xa=new Float32Array(H);a.HEAPF64=ya=new Float64Array(H)}function Ua(){var d=a.usingWasm?Ia:Xa,b=2147483648-d;if(u[Y>>2]>b)return!1;var c=x;for(x=Math.max(x,tb);x<u[Y>>2];)x=536870912>=x?Ea(2*x,d):Math.min(Ea((3*x+2147483648)/4,d),b);d=a.reallocBuffer(x);if(!d||d.byteLength!=x)return x=c,!1;a.buffer=H=d;ba();return!0}function ja(d){for(;0<d.length;){var b=d.shift();if("function"==typeof b)b();else{var c=
b.func;"number"===typeof c?void 0===b.arg?a.dynCall_v(c):a.dynCall_vi(c,b.arg):c(void 0===b.arg?null:b.arg)}}}function qa(a,b,c){c=0<c?c:ha(a)+1;c=Array(c);a=Da(a,c,0,c.length);b&&(c.length=a);return c}function Ya(d){ea++;a.monitorRunDependencies&&a.monitorRunDependencies(ea)}function Za(d){ea--;a.monitorRunDependencies&&a.monitorRunDependencies(ea);0==ea&&(null!==Ja&&(clearInterval(Ja),Ja=null),sa&&(d=sa,sa=null,d()))}function la(){return!!la.uncaught_exception}function ta(){var d=C.last;if(!d)return(n.setTempRet0(0),
0)|0;var b=C.infos[d],c=b.type;if(!c)return(n.setTempRet0(0),d)|0;var g=Array.prototype.slice.call(arguments);a.___cxa_is_pointer_type(c);ta.buffer||(ta.buffer=ka(4));u[ta.buffer>>2]=d;d=ta.buffer;for(var e=0;e<g.length;e++)if(g[e]&&a.___cxa_can_catch(g[e],c,d))return d=u[d>>2],b.adjusted=d,(n.setTempRet0(g[e]),d)|0;d=u[d>>2];return(n.setTempRet0(c),d)|0}function ua(d,b){ua.seen||(ua.seen={});d in ua.seen||(a.dynCall_v(b),ua.seen[d]=1)}function ca(d,b){r.varargs=b;try{var c=r.get(),g=r.get(),e=r.get();
d=0;ca.buffer||(ca.buffers=[null,[],[]],ca.printChar=function(b,d){var c=ca.buffers[b];f(c);if(0===d||10===d){b=1===b?a.print:a.printErr;a:{for(var g=d=0;c[g];)++g;if(16<g-d&&c.subarray&&$a)d=$a.decode(c.subarray(d,g));else for(g="";;){var e=c[d++];if(!e){d=g;break a}if(e&128){var h=c[d++]&63;if(192==(e&224))g+=String.fromCharCode((e&31)<<6|h);else{var k=c[d++]&63;if(224==(e&240))e=(e&15)<<12|h<<6|k;else{var l=c[d++]&63;if(240==(e&248))e=(e&7)<<18|h<<12|k<<6|l;else{var A=c[d++]&63;if(248==(e&252))e=
(e&3)<<24|h<<18|k<<12|l<<6|A;else{var m=c[d++]&63;e=(e&1)<<30|h<<24|k<<18|l<<12|A<<6|m}}}65536>e?g+=String.fromCharCode(e):(e-=65536,g+=String.fromCharCode(55296|e>>10,56320|e&1023))}}else g+=String.fromCharCode(e)}}b(d);c.length=0}else c.push(d)});for(b=0;b<e;b++){for(var h=u[g+8*b>>2],k=u[g+(8*b+4)>>2],l=0;l<k;l++)ca.printChar(c,G[h+l]);d+=k}return d}catch(Ga){return"undefined"!==typeof FS&&Ga instanceof FS.ErrnoError||M(Ga),-Ga.errno}}function ma(a){this.name="ExitStatus";this.message="Program terminated with exit("+
a+")";this.status=a}function Ka(d){function b(){if(!a.calledRun&&(a.calledRun=!0,!na)){za||(za=!0,ja(La));ja(ab);if(a.onRuntimeInitialized)a.onRuntimeInitialized();a._main&&bb&&a.callMain(d);if(a.postRun)for("function"==typeof a.postRun&&(a.postRun=[a.postRun]);a.postRun.length;)cb.unshift(a.postRun.shift());ja(cb)}}d=d||a.arguments;null===db&&(db=Date.now());if(!(0<ea)){if(a.preRun)for("function"==typeof a.preRun&&(a.preRun=[a.preRun]);a.preRun.length;)eb.unshift(a.preRun.shift());ja(eb);0<ea||a.calledRun||
(a.setStatus?(a.setStatus("Running..."),setTimeout(function(){setTimeout(function(){a.setStatus("")},1);b()},1)):b())}}function fb(d,b){if(!b||!a.noExitRuntime){if(!a.noExitRuntime&&(na=!0,T=ub,ja(gb),a.onExit))a.onExit(d);oa&&process.exit(d);a.quit(d,new ma(d))}}function M(d){if(a.onAbort)a.onAbort(d);void 0!==d?(a.print(d),a.printErr(d),d=JSON.stringify(d)):d="";na=!0;var b="abort("+d+") at "+ia()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.";hb&&
hb.forEach(function(a){b=a(b,d)});throw b;}function v(){}function E(a){return(a||v).__cache__}function V(a,b){var d=E(b),e=d[a];if(e)return e;e=Object.create((b||v).prototype);e.ptr=a;return d[a]=e}function Z(a){if("string"===typeof a){a=qa(a);var b=l.alloc(a,N);l.copy(a,N,b);return b}return a}function D(){throw"cannot construct a Status, no constructor in IDL";}function I(){this.ptr=vb();E(I)[this.ptr]=this}function J(){this.ptr=wb();E(J)[this.ptr]=this}function p(){this.ptr=xb();E(p)[this.ptr]=
this}function O(){this.ptr=yb();E(O)[this.ptr]=this}function B(){this.ptr=zb();E(B)[this.ptr]=this}function q(){this.ptr=Ab();E(q)[this.ptr]=this}function K(){this.ptr=Bb();E(K)[this.ptr]=this}function W(){this.ptr=Cb();E(W)[this.ptr]=this}function P(){this.ptr=Db();E(P)[this.ptr]=this}function m(){this.ptr=Eb();E(m)[this.ptr]=this}function F(){this.ptr=Fb();E(F)[this.ptr]=this}function aa(){throw"cannot construct a VoidPtr, no constructor in IDL";}function L(){this.ptr=Gb();E(L)[this.ptr]=this}function Q(){this.ptr=
Hb();E(Q)[this.ptr]=this}var a=e=e||{},ib=!1,jb=!1;a.onRuntimeInitialized=function(){ib=!0;if(jb&&"function"===typeof a.onModuleLoaded)a.onModuleLoaded(a)};a.onModuleParsed=function(){jb=!0;if(ib&&"function"===typeof a.onModuleLoaded)a.onModuleLoaded(a)};a.isVersionSupported=function(a){if("string"!==typeof a)return!1;a=a.split(".");return 2>a.length||3<a.length?!1:1==a[0]&&0==a[1]?!0:0!=a[0]||10<a[1]?!1:!0};a||(a=("undefined"!==typeof e?e:null)||{});var va={},da;for(da in a)a.hasOwnProperty(da)&&
(va[da]=a[da]);var wa=!1,fa=!1,oa=!1,Aa=!1;if(a.ENVIRONMENT)if("WEB"===a.ENVIRONMENT)wa=!0;else if("WORKER"===a.ENVIRONMENT)fa=!0;else if("NODE"===a.ENVIRONMENT)oa=!0;else if("SHELL"===a.ENVIRONMENT)Aa=!0;else throw Error("The provided Module['ENVIRONMENT'] value is not valid. It must be one of: WEB|WORKER|NODE|SHELL.");else wa="object"===typeof window,fa="function"===typeof importScripts,oa="object"===typeof process&&"function"===typeof require&&!wa&&!fa,Aa=!wa&&!oa&&!fa;if(oa){a.print||(a.print=
console.log);a.printErr||(a.printErr=console.warn);var Ma,Na;a.read=function(a,b){Ma||(Ma=require("fs"));Na||(Na=require("path"));a=Na.normalize(a);a=Ma.readFileSync(a);return b?a:a.toString()};a.readBinary=function(d){d=a.read(d,!0);d.buffer||(d=new Uint8Array(d));f(d.buffer);return d};a.load=function(a){h(read(a))};a.thisProgram||(a.thisProgram=1<process.argv.length?process.argv[1].replace(/\\/g,"/"):"unknown-program");a.arguments=process.argv.slice(2);"undefined"!==typeof module&&(module.exports=
a);process.on("uncaughtException",function(a){if(!(a instanceof ma))throw a;});a.inspect=function(){return"[Emscripten Module object]"}}else if(Aa)a.print||(a.print=print),"undefined"!=typeof printErr&&(a.printErr=printErr),a.read="undefined"!=typeof read?read:function(){throw"no read() available";},a.readBinary=function(a){if("function"===typeof readbuffer)return new Uint8Array(readbuffer(a));a=read(a,"binary");f("object"===typeof a);return a},"undefined"!=typeof scriptArgs?a.arguments=scriptArgs:
"undefined"!=typeof arguments&&(a.arguments=arguments),"function"===typeof quit&&(a.quit=function(a,b){quit(a)});else if(wa||fa)a.read=function(a){var b=new XMLHttpRequest;b.open("GET",a,!1);b.send(null);return b.responseText},fa&&(a.readBinary=function(a){var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),a.readAsync=function(a,b,c){var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";d.onload=function(){200==
d.status||0==d.status&&d.response?b(d.response):c()};d.onerror=c;d.send(null)},"undefined"!=typeof arguments&&(a.arguments=arguments),"undefined"!==typeof console?(a.print||(a.print=function(a){console.log(a)}),a.printErr||(a.printErr=function(a){console.warn(a)})):a.print||(a.print=function(a){}),fa&&(a.load=importScripts),"undefined"===typeof a.setWindowTitle&&(a.setWindowTitle=function(a){document.title=a});else throw"Unknown runtime environment. Where are we?";!a.load&&a.read&&(a.load=function(d){h(a.read(d))});
a.print||(a.print=function(){});a.printErr||(a.printErr=a.print);a.arguments||(a.arguments=[]);a.thisProgram||(a.thisProgram="./this.program");a.quit||(a.quit=function(a,b){throw b;});a.print=a.print;a.printErr=a.printErr;a.preRun=[];a.postRun=[];for(da in va)va.hasOwnProperty(da)&&(a[da]=va[da]);va=void 0;var n={setTempRet0:function(a){return tempRet0=a},getTempRet0:function(){return tempRet0},stackSave:function(){return T},stackRestore:function(a){T=a},getNativeTypeSize:function(a){switch(a){case "i1":case "i8":return 1;
case "i16":return 2;case "i32":return 4;case "i64":return 8;case "float":return 4;case "double":return 8;default:return"*"===a[a.length-1]?n.QUANTUM_SIZE:"i"===a[0]?(a=parseInt(a.substr(1)),f(0===a%8),a/8):0}},getNativeFieldSize:function(a){return Math.max(n.getNativeTypeSize(a),n.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(a,b){"double"===b||"i64"===b?a&7&&(f(4===(a&7)),a+=4):f(0===(a&3));return a},getAlignSize:function(a,b,c){return c||"i64"!=a&&"double"!=a?a?Math.min(b||(a?n.getNativeFieldSize(a):
0),n.QUANTUM_SIZE):Math.min(b,8):8},dynCall:function(d,b,c){return c&&c.length?a["dynCall_"+d].apply(null,[b].concat(c)):a["dynCall_"+d].call(null,b)},functionPointers:[],addFunction:function(a){for(var b=0;b<n.functionPointers.length;b++)if(!n.functionPointers[b])return n.functionPointers[b]=a,2*(1+b);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS.";},removeFunction:function(a){n.functionPointers[(a-2)/2]=null},warnOnce:function(d){n.warnOnce.shown||
(n.warnOnce.shown={});n.warnOnce.shown[d]||(n.warnOnce.shown[d]=1,a.printErr(d))},funcWrappers:{},getFuncWrapper:function(a,b){f(b);n.funcWrappers[b]||(n.funcWrappers[b]={});var d=n.funcWrappers[b];d[a]||(d[a]=1===b.length?function(){return n.dynCall(b,a)}:2===b.length?function(d){return n.dynCall(b,a,[d])}:function(){return n.dynCall(b,a,Array.prototype.slice.call(arguments))});return d[a]},getCompilerSetting:function(a){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work";
},stackAlloc:function(a){var b=T;T=T+a|0;T=T+15&-16;return b},staticAlloc:function(a){var b=X;X=X+a|0;X=X+15&-16;return b},dynamicAlloc:function(a){var b=u[Y>>2];a=(b+a+15|0)&-16;u[Y>>2]=a;return a>=x&&!Ua()?(u[Y>>2]=b,0):b},alignMemory:function(a,b){return Math.ceil(a/(b?b:16))*(b?b:16)},makeBigInt:function(a,b,c){return c?+(a>>>0)+4294967296*+(b>>>0):+(a>>>0)+4294967296*+(b|0)},GLOBAL_BASE:1024,QUANTUM_SIZE:4,__dummy__:0},na=0;(function(){function a(a){a=a.toString().match(e).slice(1);return{arguments:a[0],
body:a[1],returnValue:a[2]}}function b(){if(!f){f={};for(var b in c)c.hasOwnProperty(b)&&(f[b]=a(c[b]))}}var c={stackSave:function(){n.stackSave()},stackRestore:function(){n.stackRestore()},arrayToC:function(a){var b=n.stackAlloc(a.length);N.set(a,b);return b},stringToC:function(a){var b=0;if(null!==a&&void 0!==a&&0!==a){var d=(a.length<<2)+1;b=n.stackAlloc(d);Da(a,G,b,d)}return b}},e=/^function\s*[a-zA-Z$_0-9]*\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/,f=null;cwrap=function(d,
c,e){e=e||[];var g=z(d);d=e.every(function(a){return"number"===a});var h="string"!==c;if(h&&d)return g;var k=e.map(function(a,b){return"$"+b});c="(function("+k.join(",")+") {";var l=e.length;if(!d){b();c+="var stack = "+f.stackSave.body+";";for(var m=0;m<l;m++){var A=k[m],n=e[m];"number"!==n&&(n=f[n+"ToC"],c+="var "+n.arguments+" = "+A+";",c+=n.body+";",c+=A+"=("+n.returnValue+");")}}e=a(function(){return g}).returnValue;c+="var ret = "+e+"("+k.join(",")+");";h||(e=a(function(){return t}).returnValue,
c+="ret = "+e+"(ret);");d||(b(),c+=f.stackRestore.body.replace("()","(stack)")+";");return eval(c+"return ret})")}})();var $a="undefined"!==typeof TextDecoder?new TextDecoder("utf8"):void 0;"undefined"!==typeof TextDecoder&&new TextDecoder("utf-16le");var Ia=65536,Xa=16777216,tb=16777216,N,G,ra,Va,u,Wa,xa,ya,X,Oa,T,Ba,Pa,Y;var Qa=X=Oa=T=Ba=Pa=Y=0;a.reallocBuffer||(a.reallocBuffer=function(a){try{if(ArrayBuffer.transfer)var b=ArrayBuffer.transfer(H,a);else{var d=N;b=new ArrayBuffer(a);(new Int8Array(b)).set(d)}}catch(g){return!1}return Ib(b)?
b:!1});try{var Ra=Function.prototype.call.bind(Object.getOwnPropertyDescriptor(ArrayBuffer.prototype,"byteLength").get);Ra(new ArrayBuffer(4))}catch(d){Ra=function(a){return a.byteLength}}var Sa=a.TOTAL_STACK||5242880,x=a.TOTAL_MEMORY||16777216;x<Sa&&a.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+x+"! (TOTAL_STACK="+Sa+")");if(a.buffer)var H=a.buffer;else"object"===typeof WebAssembly&&"function"===typeof WebAssembly.Memory?(a.wasmMemory=new WebAssembly.Memory({initial:x/Ia}),H=
a.wasmMemory.buffer):H=new ArrayBuffer(x);ba();u[0]=1668509029;ra[1]=25459;if(115!==G[2]||99!==G[3])throw"Runtime error: expected the system to be little-endian!";a.HEAP=void 0;a.buffer=H;a.HEAP8=N;a.HEAP16=ra;a.HEAP32=u;a.HEAPU8=G;a.HEAPU16=Va;a.HEAPU32=Wa;a.HEAPF32=xa;a.HEAPF64=ya;var eb=[],La=[],ab=[],gb=[],cb=[],za=!1;Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(a,b){var d=a&65535,e=b&65535;return d*e+((a>>>16)*e+d*(b>>>16)<<16)|0});Math.imul=Math.imul;if(!Math.fround){var kb=
new Float32Array(1);Math.fround=function(a){kb[0]=a;return kb[0]}}Math.fround=Math.fround;Math.clz32||(Math.clz32=function(a){a>>>=0;for(var b=0;32>b;b++)if(a&1<<31-b)return b;return 32});Math.clz32=Math.clz32;Math.trunc||(Math.trunc=function(a){return 0>a?Math.ceil(a):Math.floor(a)});Math.trunc=Math.trunc;var pb=Math.abs,sb=Math.ceil,rb=Math.floor,qb=Math.min,ea=0,Ja=null,sa=null;a.preloadedImages={};a.preloadedAudios={};var U=null;(function(d){function b(a,b){var d=t;if(0>a.indexOf("."))d=(d||{})[a];
else{var c=a.split(".");d=(d||{})[c[0]];d=(d||{})[c[1]]}b&&(d=(d||{})[b]);void 0===d&&M("bad lookupImport to ("+a+")."+b);return d}function c(b){var c=d.buffer;b.byteLength<c.byteLength&&d.printErr("the new buffer in mergeMemory is smaller than the previous one. in native wasm, we should grow memory here");c=new Int8Array(c);var e=new Int8Array(b);U||c.set(e.subarray(d.STATIC_BASE,d.STATIC_BASE+d.STATIC_BUMP),d.STATIC_BASE);e.set(c);a.buffer=H=b;ba()}function e(){try{if(d.wasmBinary){var a=d.wasmBinary;
a=new Uint8Array(a)}else if(d.readBinary)a=d.readBinary(p);else throw"on the web, we need the wasm binary to be preloaded and set on Module['wasmBinary']. emcc.py will do that for you when generating HTML (but not JS)";return a}catch(Jb){M(Jb)}}function h(){return d.wasmBinary||"function"!==typeof fetch?new Promise(function(a,b){a(e())}):fetch(p,{credentials:"same-origin"}).then(function(a){if(!a.ok)throw"failed to load wasm binary file at '"+p+"'";return a.arrayBuffer()})}function k(a,b,c){if("function"!==
typeof d.asm||d.asm===x)d.asmPreload?d.asm=d.asmPreload:eval(d.read(v));return"function"!==typeof d.asm?(d.printErr("asm evalling did not set the module properly"),!1):d.asm(a,b,c)}function l(a,b,e){function g(a){u=a.exports;u.memory&&c(u.memory);d.asm=u;d.usingWasm=!0;Za("wasm-instantiate")}if("object"!==typeof WebAssembly)return d.printErr("no native wasm support detected"),!1;if(!(d.wasmMemory instanceof WebAssembly.Memory))return d.printErr("no native wasm Memory in use"),!1;b.memory=d.wasmMemory;
t.global={NaN:NaN,Infinity:Infinity};t["global.Math"]=a.Math;t.env=b;Ya("wasm-instantiate");if(d.instantiateWasm)try{return d.instantiateWasm(t,g)}catch(Kb){return d.printErr("Module.instantiateWasm callback failed with error: "+Kb),!1}h().then(function(a){return WebAssembly.instantiate(a,t)}).then(function(a){g(a.instance)}).catch(function(a){d.printErr("failed to asynchronously prepare wasm: "+a);M(a)});return{}}var m=d.wasmJSMethod||"native-wasm";d.wasmJSMethod=m;var n=d.wasmTextFile||"draco_decoder.wast",
p=d.wasmBinaryFile||"draco_decoder.wasm",v=d.asmjsCodeFile||"draco_decoder.temp.asm.js";"function"===typeof d.locateFile&&(n=d.locateFile(n),p=d.locateFile(p),v=d.locateFile(v));var t={global:null,env:null,asm2wasm:{"f64-rem":function(a,b){return a%b},"f64-to-int":function(a){return a|0},"i32s-div":function(a,b){return(a|0)/(b|0)|0},"i32u-div":function(a,b){return(a>>>0)/(b>>>0)>>>0},"i32s-rem":function(a,b){return(a|0)%(b|0)|0},"i32u-rem":function(a,b){return(a>>>0)%(b>>>0)>>>0},"debugger":function(){debugger}},
parent:d},u=null;d.asmPreload=d.asm;var q=d.reallocBuffer,r=function(a){a=Ea(a,d.usingWasm?Ia:Xa);var b=d.buffer,c=b.byteLength;if(d.usingWasm)try{return-1!==d.wasmMemory.grow((a-c)/65536)?d.buffer=d.wasmMemory.buffer:null}catch(yd){return null}else return u.__growWasmMemory((a-c)/65536),d.buffer!==b?d.buffer:null};d.reallocBuffer=function(a){return"asmjs"===z?q(a):r(a)};var z="";d.asm=function(a,g,h){if(!g.table){var A=d.wasmTableSize;void 0===A&&(A=1024);var p=d.wasmMaxTableSize;g.table="object"===
typeof WebAssembly&&"function"===typeof WebAssembly.Table?void 0!==p?new WebAssembly.Table({initial:A,maximum:p,element:"anyfunc"}):new WebAssembly.Table({initial:A,element:"anyfunc"}):Array(A);d.wasmTable=g.table}g.memoryBase||(g.memoryBase=d.STATIC_BASE);g.tableBase||(g.tableBase=0);var q;A=m.split(",");for(p=0;p<A.length;p++){var w=A[p];z=w;if("native-wasm"===w){if(q=l(a,g,h))break}else if("asmjs"===w){if(q=k(a,g,h))break}else if("interpret-asm2wasm"===w||"interpret-s-expr"===w||"interpret-binary"===
w){q=a;var r=g;var x=h,y=w;if("function"!==typeof WasmJS)d.printErr("WasmJS not detected - polyfill not bundled?"),w=!1;else{w=WasmJS({});w.outside=d;w.info=t;w.lookupImport=b;f(x===d.buffer);t.global=q;t.env=r;f(x===d.buffer);r.memory=x;f(r.memory instanceof ArrayBuffer);w.providedTotalMemory=d.buffer.byteLength;q="interpret-binary"===y?e():d.read("interpret-asm2wasm"==y?v:n);if("interpret-asm2wasm"==y)r=w._malloc(q.length+1),w.writeAsciiToMemory(q,r),w._load_asm2wasm(r);else if("interpret-s-expr"===
y)r=w._malloc(q.length+1),w.writeAsciiToMemory(q,r),w._load_s_expr2wasm(r);else if("interpret-binary"===y)r=w._malloc(q.length),w.HEAPU8.set(q,r),w._load_binary2wasm(r,q.length);else throw"what? "+y;w._free(r);w._instantiate(r);d.newBuffer&&(c(d.newBuffer),d.newBuffer=null);w=u=w.asmExports}if(q=w)break}else M("bad method: "+w)}if(!q)throw"no binaryen method succeeded. consider enabling more options, like interpreting, if you want that: https://github.com/kripken/emscripten/wiki/WebAssembly#binaryen-methods";
return q};var x=d.asm})(a);Qa=n.GLOBAL_BASE;X=Qa+28928;La.push();U=0<=a.wasmJSMethod.indexOf("asmjs")||0<=a.wasmJSMethod.indexOf("interpret-asm2wasm")?"draco_decoder.js.mem":null;a.STATIC_BASE=Qa;a.STATIC_BUMP=28928;var Lb=X;X+=16;var C={last:0,caught:[],infos:{},deAdjust:function(a){if(!a||C.infos[a])return a;for(var b in C.infos)if(C.infos[b].adjusted===a)return b;return a},addRef:function(a){a&&C.infos[a].refcount++},decRef:function(d){if(d){var b=C.infos[d];f(0<b.refcount);b.refcount--;0!==b.refcount||
b.rethrown||(b.destructor&&a.dynCall_vi(b.destructor,d),delete C.infos[d],___cxa_free_exception(d))}},clearRef:function(a){a&&(C.infos[a].refcount=0)}};a._memset=Mb;a._memcpy=Nb;var r={varargs:0,get:function(a){r.varargs+=4;return u[r.varargs-4>>2]},getStr:function(){return t(r.get())},get64:function(){var a=r.get(),b=r.get();0<=a?f(0===b):f(-1===b);return a},getZero:function(){f(0===r.get())}},Ca={};a._sbrk=Ob;a._memmove=Pb;var Ta=1;a._llvm_bswap_i32=Qb;gb.push(function(){var d=a._fflush;d&&d(0);
if(d=ca.printChar){var b=ca.buffers;b[1].length&&d(1,10);b[2].length&&d(2,10)}});Y=R(1,"i32",2);Oa=T=n.alignMemory(X);Ba=Oa+Sa;Pa=n.alignMemory(Ba);u[Y>>2]=Pa;a.wasmTableSize=780;a.wasmMaxTableSize=780;a.asmGlobalArg={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array,NaN:NaN,Infinity:Infinity,byteLength:Ra};a.asmLibraryArg={abort:M,assert:f,enlargeMemory:Ua,
getTotalMemory:function(){return x},abortOnCannotGrowMemory:function(){M("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+x+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")},invoke_iiii:function(d,b,c,e){try{return a.dynCall_iiii(d,b,c,e)}catch(A){if("number"!==typeof A&&"longjmp"!==A)throw A;
a.setThrew(1,0)}},invoke_viiiii:function(d,b,c,e,f,h){try{a.dynCall_viiiii(d,b,c,e,f,h)}catch(S){if("number"!==typeof S&&"longjmp"!==S)throw S;a.setThrew(1,0)}},invoke_vi:function(d,b){try{a.dynCall_vi(d,b)}catch(c){if("number"!==typeof c&&"longjmp"!==c)throw c;a.setThrew(1,0)}},invoke_vii:function(d,b,c){try{a.dynCall_vii(d,b,c)}catch(g){if("number"!==typeof g&&"longjmp"!==g)throw g;a.setThrew(1,0)}},invoke_iiiiiii:function(d,b,c,e,f,h,k){try{return a.dynCall_iiiiiii(d,b,c,e,f,h,k)}catch(y){if("number"!==
typeof y&&"longjmp"!==y)throw y;a.setThrew(1,0)}},invoke_ii:function(d,b){try{return a.dynCall_ii(d,b)}catch(c){if("number"!==typeof c&&"longjmp"!==c)throw c;a.setThrew(1,0)}},invoke_viii:function(d,b,c,e){try{a.dynCall_viii(d,b,c,e)}catch(A){if("number"!==typeof A&&"longjmp"!==A)throw A;a.setThrew(1,0)}},invoke_v:function(d){try{a.dynCall_v(d)}catch(b){if("number"!==typeof b&&"longjmp"!==b)throw b;a.setThrew(1,0)}},invoke_viiiiii:function(d,b,c,e,f,h,k){try{a.dynCall_viiiiii(d,b,c,e,f,h,k)}catch(y){if("number"!==
typeof y&&"longjmp"!==y)throw y;a.setThrew(1,0)}},invoke_iii:function(d,b,c){try{return a.dynCall_iii(d,b,c)}catch(g){if("number"!==typeof g&&"longjmp"!==g)throw g;a.setThrew(1,0)}},invoke_viiii:function(d,b,c,e,f){try{a.dynCall_viiii(d,b,c,e,f)}catch(Fa){if("number"!==typeof Fa&&"longjmp"!==Fa)throw Fa;a.setThrew(1,0)}},_pthread_getspecific:function(a){return Ca[a]||0},___syscall54:function(a,b){r.varargs=b;return 0},_pthread_setspecific:function(a,b){if(!(a in Ca))return 22;Ca[a]=b;return 0},___cxa_throw:function(a,
b,c){C.infos[a]={ptr:a,adjusted:a,type:b,destructor:c,refcount:0,caught:!1,rethrown:!1};C.last=a;"uncaught_exception"in la?la.uncaught_exception++:la.uncaught_exception=1;throw a+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch.";},___gxx_personality_v0:function(){},_abort:function(){a.abort()},___setErrNo:function(d){a.___errno_location&&(u[a.___errno_location()>>2]=d);return d},___syscall6:function(a,
b){r.varargs=b;try{var c=r.getStreamFromFD();FS.close(c);return 0}catch(g){return"undefined"!==typeof FS&&g instanceof FS.ErrnoError||M(g),-g.errno}},___cxa_begin_catch:function(a){var b=C.infos[a];b&&!b.caught&&(b.caught=!0,la.uncaught_exception--);b&&(b.rethrown=!1);C.caught.push(a);C.addRef(C.deAdjust(a));return a},___syscall146:ca,_pthread_once:ua,_emscripten_memcpy_big:function(a,b,c){G.set(G.subarray(b,b+c),a);return a},_pthread_key_create:function(a,b){if(0==a)return 22;u[a>>2]=Ta;Ca[Ta]=0;
Ta++;return 0},___syscall140:function(a,b){r.varargs=b;try{var c=r.getStreamFromFD();r.get();var d=r.get(),e=r.get(),f=r.get();FS.llseek(c,d,f);u[e>>2]=c.position;c.getdents&&0===d&&0===f&&(c.getdents=null);return 0}catch(S){return"undefined"!==typeof FS&&S instanceof FS.ErrnoError||M(S),-S.errno}},___resumeException:function(a){C.last||(C.last=a);throw a+" - Exception catching is disabled, this exception cannot be caught. Compile with -s DISABLE_EXCEPTION_CATCHING=0 or DISABLE_EXCEPTION_CATCHING=2 to catch.";
},___cxa_find_matching_catch:ta,___assert_fail:function(a,b,c,e){na=!0;throw"Assertion failed: "+t(a)+", at: "+[b?t(b):"unknown filename",c,e?t(e):"unknown function"]+" at "+ia();},___cxa_pure_virtual:function(){na=!0;throw"Pure virtual function called!";},___cxa_allocate_exception:function(a){return ka(a)},__ZSt18uncaught_exceptionv:la,DYNAMICTOP_PTR:Y,tempDoublePtr:Lb,ABORT:na,STACKTOP:T,STACK_MAX:Ba};var lb=a.asm(a.asmGlobalArg,a.asmLibraryArg,H);a.asm=lb;var Rb=a._emscripten_bind_Decoder_GetAttributeFloat_3=
function(){return a.asm._emscripten_bind_Decoder_GetAttributeFloat_3.apply(null,arguments)},Sb=a._emscripten_bind_PointAttribute_normalized_0=function(){return a.asm._emscripten_bind_PointAttribute_normalized_0.apply(null,arguments)},Tb=a._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3=function(){return a.asm._emscripten_bind_Decoder_GetAttributeFloatForAllPoints_3.apply(null,arguments)},Ub=a._emscripten_bind_DecoderBuffer_Init_2=function(){return a.asm._emscripten_bind_DecoderBuffer_Init_2.apply(null,
arguments)},Vb=a._emscripten_bind_PointAttribute_size_0=function(){return a.asm._emscripten_bind_PointAttribute_size_0.apply(null,arguments)},Wb=a._emscripten_enum_draco_GeometryAttribute_Type_POSITION=function(){return a.asm._emscripten_enum_draco_GeometryAttribute_Type_POSITION.apply(null,arguments)},Ob=a._sbrk=function(){return a.asm._sbrk.apply(null,arguments)},Nb=a._memcpy=function(){return a.asm._memcpy.apply(null,arguments)};a.stackSave=function(){return a.asm.stackSave.apply(null,arguments)};
var Xb=a._emscripten_enum_draco_GeometryAttribute_Type_INVALID=function(){return a.asm._emscripten_enum_draco_GeometryAttribute_Type_INVALID.apply(null,arguments)},Yb=a._emscripten_bind_Decoder_GetAttributeIdByName_2=function(){return a.asm._emscripten_bind_Decoder_GetAttributeIdByName_2.apply(null,arguments)},Zb=a._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD=function(){return a.asm._emscripten_enum_draco_EncodedGeometryType_POINT_CLOUD.apply(null,arguments)},$b=a._emscripten_bind_Status_ok_0=
function(){return a.asm._emscripten_bind_Status_ok_0.apply(null,arguments)},ac=a._emscripten_bind_Mesh_num_faces_0=function(){return a.asm._emscripten_bind_Mesh_num_faces_0.apply(null,arguments)};a._emscripten_get_global_libc=function(){return a.asm._emscripten_get_global_libc.apply(null,arguments)};var bc=a._emscripten_bind_Decoder_DecodeBufferToPointCloud_2=function(){return a.asm._emscripten_bind_Decoder_DecodeBufferToPointCloud_2.apply(null,arguments)},cc=a._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM=
function(){return a.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_NO_TRANSFORM.apply(null,arguments)};a.runPostSets=function(){return a.asm.runPostSets.apply(null,arguments)};var dc=a._emscripten_bind_Decoder_GetEncodedGeometryType_1=function(){return a.asm._emscripten_bind_Decoder_GetEncodedGeometryType_1.apply(null,arguments)};a.___cxa_can_catch=function(){return a.asm.___cxa_can_catch.apply(null,arguments)};var Ha=a._free=function(){return a.asm._free.apply(null,arguments)},ec=a._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE=
function(){return a.asm._emscripten_enum_draco_EncodedGeometryType_INVALID_GEOMETRY_TYPE.apply(null,arguments)},fc=a._emscripten_bind_GeometryAttribute___destroy___0=function(){return a.asm._emscripten_bind_GeometryAttribute___destroy___0.apply(null,arguments)},gc=a._emscripten_bind_PointAttribute_byte_stride_0=function(){return a.asm._emscripten_bind_PointAttribute_byte_stride_0.apply(null,arguments)},Hb=a._emscripten_bind_Metadata_Metadata_0=function(){return a.asm._emscripten_bind_Metadata_Metadata_0.apply(null,
arguments)},hc=a._emscripten_bind_AttributeQuantizationTransform_min_value_1=function(){return a.asm._emscripten_bind_AttributeQuantizationTransform_min_value_1.apply(null,arguments)},Eb=a._emscripten_bind_Decoder_Decoder_0=function(){return a.asm._emscripten_bind_Decoder_Decoder_0.apply(null,arguments)},ic=a._emscripten_bind_MetadataQuerier_GetStringEntry_2=function(){return a.asm._emscripten_bind_MetadataQuerier_GetStringEntry_2.apply(null,arguments)},jc=a._emscripten_bind_MetadataQuerier_GetIntEntry_2=
function(){return a.asm._emscripten_bind_MetadataQuerier_GetIntEntry_2.apply(null,arguments)},kc=a._emscripten_bind_Decoder___destroy___0=function(){return a.asm._emscripten_bind_Decoder___destroy___0.apply(null,arguments)},lc=a._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM=function(){return a.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_OCTAHEDRON_TRANSFORM.apply(null,arguments)};a.getTempRet0=function(){return a.asm.getTempRet0.apply(null,arguments)};
a.setThrew=function(){return a.asm.setThrew.apply(null,arguments)};var mc=a._emscripten_bind_MetadataQuerier_HasStringEntry_2=function(){return a.asm._emscripten_bind_MetadataQuerier_HasStringEntry_2.apply(null,arguments)},nc=a._emscripten_bind_AttributeTransformData___destroy___0=function(){return a.asm._emscripten_bind_AttributeTransformData___destroy___0.apply(null,arguments)},oc=a._emscripten_bind_PointAttribute_num_components_0=function(){return a.asm._emscripten_bind_PointAttribute_num_components_0.apply(null,
arguments)};a.___cxa_is_pointer_type=function(){return a.asm.___cxa_is_pointer_type.apply(null,arguments)};var yb=a._emscripten_bind_AttributeTransformData_AttributeTransformData_0=function(){return a.asm._emscripten_bind_AttributeTransformData_AttributeTransformData_0.apply(null,arguments)},pc=a._emscripten_bind_AttributeQuantizationTransform___destroy___0=function(){return a.asm._emscripten_bind_AttributeQuantizationTransform___destroy___0.apply(null,arguments)};a.stackAlloc=function(){return a.asm.stackAlloc.apply(null,
arguments)};var qc=a._emscripten_bind_DracoInt32Array___destroy___0=function(){return a.asm._emscripten_bind_DracoInt32Array___destroy___0.apply(null,arguments)},rc=a._emscripten_bind_Mesh_num_points_0=function(){return a.asm._emscripten_bind_Mesh_num_points_0.apply(null,arguments)},sc=a._emscripten_bind_PointCloud_num_attributes_0=function(){return a.asm._emscripten_bind_PointCloud_num_attributes_0.apply(null,arguments)},Fb=a._emscripten_bind_Mesh_Mesh_0=function(){return a.asm._emscripten_bind_Mesh_Mesh_0.apply(null,
arguments)},Ab=a._emscripten_bind_MetadataQuerier_MetadataQuerier_0=function(){return a.asm._emscripten_bind_MetadataQuerier_MetadataQuerier_0.apply(null,arguments)},tc=a._emscripten_bind_Decoder_GetAttributeByUniqueId_2=function(){return a.asm._emscripten_bind_Decoder_GetAttributeByUniqueId_2.apply(null,arguments)},uc=a._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3=function(){return a.asm._emscripten_bind_Decoder_GetAttributeIdByMetadataEntry_3.apply(null,arguments)},vc=a._emscripten_bind_MetadataQuerier_HasDoubleEntry_2=
function(){return a.asm._emscripten_bind_MetadataQuerier_HasDoubleEntry_2.apply(null,arguments)},wc=a._emscripten_bind_MetadataQuerier_GetDoubleEntry_2=function(){return a.asm._emscripten_bind_MetadataQuerier_GetDoubleEntry_2.apply(null,arguments)},xc=a._emscripten_bind_Mesh_num_attributes_0=function(){return a.asm._emscripten_bind_Mesh_num_attributes_0.apply(null,arguments)},yc=a._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0=function(){return a.asm._emscripten_bind_AttributeOctahedronTransform_quantization_bits_0.apply(null,
arguments)},zc=a._emscripten_bind_AttributeOctahedronTransform___destroy___0=function(){return a.asm._emscripten_bind_AttributeOctahedronTransform___destroy___0.apply(null,arguments)},Qb=a._llvm_bswap_i32=function(){return a.asm._llvm_bswap_i32.apply(null,arguments)},Ac=a._emscripten_bind_Decoder_GetAttributeMetadata_2=function(){return a.asm._emscripten_bind_Decoder_GetAttributeMetadata_2.apply(null,arguments)},Bc=a._emscripten_bind_PointAttribute_unique_id_0=function(){return a.asm._emscripten_bind_PointAttribute_unique_id_0.apply(null,
arguments)};a.establishStackSpace=function(){return a.asm.establishStackSpace.apply(null,arguments)};var Cc=a._emscripten_enum_draco_StatusCode_IO_ERROR=function(){return a.asm._emscripten_enum_draco_StatusCode_IO_ERROR.apply(null,arguments)},Dc=a._emscripten_bind_MetadataQuerier_HasIntEntry_2=function(){return a.asm._emscripten_bind_MetadataQuerier_HasIntEntry_2.apply(null,arguments)},Ec=a._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2=function(){return a.asm._emscripten_bind_Decoder_GetTriangleStripsFromMesh_2.apply(null,
arguments)},Fc=a._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD=function(){return a.asm._emscripten_enum_draco_GeometryAttribute_Type_TEX_COORD.apply(null,arguments)},Db=a._emscripten_bind_DecoderBuffer_DecoderBuffer_0=function(){return a.asm._emscripten_bind_DecoderBuffer_DecoderBuffer_0.apply(null,arguments)},Gc=a._emscripten_bind_DracoInt32Array_size_0=function(){return a.asm._emscripten_bind_DracoInt32Array_size_0.apply(null,arguments)},Hc=a._emscripten_bind_Decoder_SkipAttributeTransform_1=
function(){return a.asm._emscripten_bind_Decoder_SkipAttributeTransform_1.apply(null,arguments)},Ic=a._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM=function(){return a.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_QUANTIZATION_TRANSFORM.apply(null,arguments)},Jc=a._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3=function(){return a.asm._emscripten_bind_Decoder_GetAttributeIntForAllPoints_3.apply(null,arguments)},Kc=a._emscripten_enum_draco_StatusCode_ERROR=
function(){return a.asm._emscripten_enum_draco_StatusCode_ERROR.apply(null,arguments)},Lc=a._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0=function(){return a.asm._emscripten_bind_AttributeQuantizationTransform_quantization_bits_0.apply(null,arguments)},Mc=a._emscripten_enum_draco_StatusCode_INVALID_PARAMETER=function(){return a.asm._emscripten_enum_draco_StatusCode_INVALID_PARAMETER.apply(null,arguments)},Nc=a._emscripten_enum_draco_GeometryAttribute_Type_COLOR=function(){return a.asm._emscripten_enum_draco_GeometryAttribute_Type_COLOR.apply(null,
arguments)},Oc=a._emscripten_bind_VoidPtr___destroy___0=function(){return a.asm._emscripten_bind_VoidPtr___destroy___0.apply(null,arguments)},Mb=a._memset=function(){return a.asm._memset.apply(null,arguments)},Pc=a._emscripten_bind_PointAttribute_attribute_type_0=function(){return a.asm._emscripten_bind_PointAttribute_attribute_type_0.apply(null,arguments)},Qc=a._emscripten_bind_MetadataQuerier___destroy___0=function(){return a.asm._emscripten_bind_MetadataQuerier___destroy___0.apply(null,arguments)},
Rc=a._emscripten_bind_DracoInt32Array_GetValue_1=function(){return a.asm._emscripten_bind_DracoInt32Array_GetValue_1.apply(null,arguments)},Sc=a._emscripten_bind_DecoderBuffer___destroy___0=function(){return a.asm._emscripten_bind_DecoderBuffer___destroy___0.apply(null,arguments)},Tc=a._emscripten_bind_Decoder_GetAttribute_2=function(){return a.asm._emscripten_bind_Decoder_GetAttribute_2.apply(null,arguments)},Uc=a._emscripten_bind_PointCloud___destroy___0=function(){return a.asm._emscripten_bind_PointCloud___destroy___0.apply(null,
arguments)};a.setTempRet0=function(){return a.asm.setTempRet0.apply(null,arguments)};var Gb=a._emscripten_bind_DracoInt32Array_DracoInt32Array_0=function(){return a.asm._emscripten_bind_DracoInt32Array_DracoInt32Array_0.apply(null,arguments)},Vc=a._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION=function(){return a.asm._emscripten_enum_draco_StatusCode_UNKNOWN_VERSION.apply(null,arguments)},Wc=a._emscripten_bind_DracoFloat32Array___destroy___0=function(){return a.asm._emscripten_bind_DracoFloat32Array___destroy___0.apply(null,
arguments)},Xc=a._emscripten_bind_PointCloud_num_points_0=function(){return a.asm._emscripten_bind_PointCloud_num_points_0.apply(null,arguments)},Yc=a._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH=function(){return a.asm._emscripten_enum_draco_EncodedGeometryType_TRIANGULAR_MESH.apply(null,arguments)},Cb=a._emscripten_bind_GeometryAttribute_GeometryAttribute_0=function(){return a.asm._emscripten_bind_GeometryAttribute_GeometryAttribute_0.apply(null,arguments)},Zc=a._emscripten_bind_Decoder_GetFaceFromMesh_3=
function(){return a.asm._emscripten_bind_Decoder_GetFaceFromMesh_3.apply(null,arguments)},$c=a._emscripten_bind_PointAttribute_data_type_0=function(){return a.asm._emscripten_bind_PointAttribute_data_type_0.apply(null,arguments)},ad=a._emscripten_bind_DracoFloat32Array_size_0=function(){return a.asm._emscripten_bind_DracoFloat32Array_size_0.apply(null,arguments)},bd=a._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1=function(){return a.asm._emscripten_bind_AttributeOctahedronTransform_InitFromAttribute_1.apply(null,
arguments)},ka=a._malloc=function(){return a.asm._malloc.apply(null,arguments)},cd=a._emscripten_bind_PointAttribute_GetAttributeTransformData_0=function(){return a.asm._emscripten_bind_PointAttribute_GetAttributeTransformData_0.apply(null,arguments)},dd=a._emscripten_bind_PointAttribute_byte_offset_0=function(){return a.asm._emscripten_bind_PointAttribute_byte_offset_0.apply(null,arguments)},Ib=a._emscripten_replace_memory=function(){return a.asm._emscripten_replace_memory.apply(null,arguments)},
Pb=a._memmove=function(){return a.asm._memmove.apply(null,arguments)},vb=a._emscripten_bind_PointCloud_PointCloud_0=function(){return a.asm._emscripten_bind_PointCloud_PointCloud_0.apply(null,arguments)},ed=a._emscripten_bind_Status_error_msg_0=function(){return a.asm._emscripten_bind_Status_error_msg_0.apply(null,arguments)},fd=a._emscripten_bind_Mesh___destroy___0=function(){return a.asm._emscripten_bind_Mesh___destroy___0.apply(null,arguments)},gd=a._emscripten_bind_Metadata___destroy___0=function(){return a.asm._emscripten_bind_Metadata___destroy___0.apply(null,
arguments)},hd=a._emscripten_bind_Status___destroy___0=function(){return a.asm._emscripten_bind_Status___destroy___0.apply(null,arguments)},id=a._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM=function(){return a.asm._emscripten_enum_draco_AttributeTransformType_ATTRIBUTE_INVALID_TRANSFORM.apply(null,arguments)},zb=a._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0=function(){return a.asm._emscripten_bind_AttributeQuantizationTransform_AttributeQuantizationTransform_0.apply(null,
arguments)},jd=a._emscripten_bind_AttributeQuantizationTransform_range_0=function(){return a.asm._emscripten_bind_AttributeQuantizationTransform_range_0.apply(null,arguments)},kd=a._emscripten_bind_Decoder_GetAttributeId_2=function(){return a.asm._emscripten_bind_Decoder_GetAttributeId_2.apply(null,arguments)},ld=a._emscripten_enum_draco_StatusCode_OK=function(){return a.asm._emscripten_enum_draco_StatusCode_OK.apply(null,arguments)},md=a._emscripten_bind_AttributeTransformData_transform_type_0=function(){return a.asm._emscripten_bind_AttributeTransformData_transform_type_0.apply(null,
arguments)},nd=a._emscripten_bind_Status_code_0=function(){return a.asm._emscripten_bind_Status_code_0.apply(null,arguments)},wb=a._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0=function(){return a.asm._emscripten_bind_AttributeOctahedronTransform_AttributeOctahedronTransform_0.apply(null,arguments)},Bb=a._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0=function(){return a.asm._emscripten_bind_DracoFloat32Array_DracoFloat32Array_0.apply(null,arguments)},od=a._emscripten_enum_draco_GeometryAttribute_Type_GENERIC=
function(){return a.asm._emscripten_enum_draco_GeometryAttribute_Type_GENERIC.apply(null,arguments)},pd=a._emscripten_bind_Decoder_DecodeBufferToMesh_2=function(){return a.asm._emscripten_bind_Decoder_DecodeBufferToMesh_2.apply(null,arguments)},qd=a._emscripten_bind_DracoFloat32Array_GetValue_1=function(){return a.asm._emscripten_bind_DracoFloat32Array_GetValue_1.apply(null,arguments)},xb=a._emscripten_bind_PointAttribute_PointAttribute_0=function(){return a.asm._emscripten_bind_PointAttribute_PointAttribute_0.apply(null,
arguments)},rd=a._emscripten_bind_Decoder_GetMetadata_1=function(){return a.asm._emscripten_bind_Decoder_GetMetadata_1.apply(null,arguments)},sd=a._emscripten_enum_draco_GeometryAttribute_Type_NORMAL=function(){return a.asm._emscripten_enum_draco_GeometryAttribute_Type_NORMAL.apply(null,arguments)};a.stackRestore=function(){return a.asm.stackRestore.apply(null,arguments)};var td=a._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1=function(){return a.asm._emscripten_bind_AttributeQuantizationTransform_InitFromAttribute_1.apply(null,
arguments)},ud=a._emscripten_bind_PointAttribute___destroy___0=function(){return a.asm._emscripten_bind_PointAttribute___destroy___0.apply(null,arguments)},vd=a._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION=function(){return a.asm._emscripten_enum_draco_StatusCode_UNSUPPORTED_VERSION.apply(null,arguments)};a.dynCall_iiii=function(){return a.asm.dynCall_iiii.apply(null,arguments)};a.dynCall_viiiii=function(){return a.asm.dynCall_viiiii.apply(null,arguments)};a.dynCall_vi=function(){return a.asm.dynCall_vi.apply(null,
arguments)};a.dynCall_vii=function(){return a.asm.dynCall_vii.apply(null,arguments)};a.dynCall_iiiiiii=function(){return a.asm.dynCall_iiiiiii.apply(null,arguments)};a.dynCall_ii=function(){return a.asm.dynCall_ii.apply(null,arguments)};a.dynCall_viii=function(){return a.asm.dynCall_viii.apply(null,arguments)};a.dynCall_v=function(){return a.asm.dynCall_v.apply(null,arguments)};a.dynCall_viiiiii=function(){return a.asm.dynCall_viiiiii.apply(null,arguments)};a.dynCall_iii=function(){return a.asm.dynCall_iii.apply(null,
arguments)};a.dynCall_viiii=function(){return a.asm.dynCall_viiii.apply(null,arguments)};n.stackAlloc=a.stackAlloc;n.stackSave=a.stackSave;n.stackRestore=a.stackRestore;n.establishStackSpace=a.establishStackSpace;n.setTempRet0=a.setTempRet0;n.getTempRet0=a.getTempRet0;a.asm=lb;if(U)if("function"===typeof a.locateFile?U=a.locateFile(U):a.memoryInitializerPrefixURL&&(U=a.memoryInitializerPrefixURL+U),oa||Aa){var wd=a.readBinary(U);G.set(wd,n.GLOBAL_BASE)}else{var nb=function(){a.readAsync(U,mb,function(){throw"could not load memory initializer "+
U;})};Ya("memory initializer");var mb=function(d){d.byteLength&&(d=new Uint8Array(d));G.set(d,n.GLOBAL_BASE);a.memoryInitializerRequest&&delete a.memoryInitializerRequest.response;Za("memory initializer")};if(a.memoryInitializerRequest){var ob=function(){var d=a.memoryInitializerRequest;200!==d.status&&0!==d.status?(console.warn("a problem seems to have happened with Module.memoryInitializerRequest, status: "+d.status+", retrying "+U),nb()):mb(d.response)};a.memoryInitializerRequest.response?setTimeout(ob,
0):a.memoryInitializerRequest.addEventListener("load",ob)}else nb()}a.then=function(d){if(a.calledRun)d(a);else{var b=a.onRuntimeInitialized;a.onRuntimeInitialized=function(){b&&b();d(a)}}return a};ma.prototype=Error();ma.prototype.constructor=ma;var ub,db=null;sa=function b(){a.calledRun||Ka();a.calledRun||(sa=b)};a.callMain=a.callMain=function(b){function c(){for(var a=0;3>a;a++)f.push(0)}b=b||[];za||(za=!0,ja(La));var e=b.length+1,f=[R(qa(a.thisProgram),"i8",0)];c();for(var h=0;h<e-1;h+=1)f.push(R(qa(b[h]),
"i8",0)),c();f.push(0);f=R(f,"i32",0);try{var k=a._main(e,f,0);fb(k,!0)}catch(y){y instanceof ma||("SimulateInfiniteLoop"==y?a.noExitRuntime=!0:((b=y)&&"object"===typeof y&&y.stack&&(b=[y,y.stack]),a.printErr("exception thrown: "+b),a.quit(1,y)))}finally{}};a.run=a.run=Ka;a.exit=a.exit=fb;var hb=[];a.abort=a.abort=M;if(a.preInit)for("function"==typeof a.preInit&&(a.preInit=[a.preInit]);0<a.preInit.length;)a.preInit.pop()();var bb=!0;a.noInitialRun&&(bb=!1);Ka();v.prototype=Object.create(v.prototype);
v.prototype.constructor=v;v.prototype.__class__=v;v.__cache__={};a.WrapperObject=v;a.getCache=E;a.wrapPointer=V;a.castObject=function(a,c){return V(a.ptr,c)};a.NULL=V(0);a.destroy=function(a){if(!a.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";a.__destroy__();delete E(a.__class__)[a.ptr]};a.compare=function(a,c){return a.ptr===c.ptr};a.getPointer=function(a){return a.ptr};a.getClass=function(a){return a.__class__};var l={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(l.needed){for(var b=
0;b<l.temps.length;b++)a._free(l.temps[b]);l.temps.length=0;a._free(l.buffer);l.buffer=0;l.size+=l.needed;l.needed=0}l.buffer||(l.size+=128,l.buffer=a._malloc(l.size),f(l.buffer));l.pos=0},alloc:function(b,c){f(l.buffer);b=b.length*c.BYTES_PER_ELEMENT;b=b+7&-8;l.pos+b>=l.size?(f(0<b),l.needed+=b,c=a._malloc(b),l.temps.push(c)):(c=l.buffer+l.pos,l.pos+=b);return c},copy:function(a,c,e){switch(c.BYTES_PER_ELEMENT){case 2:e>>=1;break;case 4:e>>=2;break;case 8:e>>=3}for(var b=0;b<a.length;b++)c[e+b]=
a[b]}};D.prototype=Object.create(v.prototype);D.prototype.constructor=D;D.prototype.__class__=D;D.__cache__={};a.Status=D;D.prototype.code=D.prototype.code=function(){return nd(this.ptr)};D.prototype.ok=D.prototype.ok=function(){return!!$b(this.ptr)};D.prototype.error_msg=D.prototype.error_msg=function(){return t(ed(this.ptr))};D.prototype.__destroy__=D.prototype.__destroy__=function(){hd(this.ptr)};I.prototype=Object.create(v.prototype);I.prototype.constructor=I;I.prototype.__class__=I;I.__cache__=
{};a.PointCloud=I;I.prototype.num_attributes=I.prototype.num_attributes=function(){return sc(this.ptr)};I.prototype.num_points=I.prototype.num_points=function(){return Xc(this.ptr)};I.prototype.__destroy__=I.prototype.__destroy__=function(){Uc(this.ptr)};J.prototype=Object.create(v.prototype);J.prototype.constructor=J;J.prototype.__class__=J;J.__cache__={};a.AttributeOctahedronTransform=J;J.prototype.InitFromAttribute=J.prototype.InitFromAttribute=function(a){var b=this.ptr;a&&"object"===typeof a&&
(a=a.ptr);return!!bd(b,a)};J.prototype.quantization_bits=J.prototype.quantization_bits=function(){return yc(this.ptr)};J.prototype.__destroy__=J.prototype.__destroy__=function(){zc(this.ptr)};p.prototype=Object.create(v.prototype);p.prototype.constructor=p;p.prototype.__class__=p;p.__cache__={};a.PointAttribute=p;p.prototype.size=p.prototype.size=function(){return Vb(this.ptr)};p.prototype.GetAttributeTransformData=p.prototype.GetAttributeTransformData=function(){return V(cd(this.ptr),O)};p.prototype.attribute_type=
p.prototype.attribute_type=function(){return Pc(this.ptr)};p.prototype.data_type=p.prototype.data_type=function(){return $c(this.ptr)};p.prototype.num_components=p.prototype.num_components=function(){return oc(this.ptr)};p.prototype.normalized=p.prototype.normalized=function(){return!!Sb(this.ptr)};p.prototype.byte_stride=p.prototype.byte_stride=function(){return gc(this.ptr)};p.prototype.byte_offset=p.prototype.byte_offset=function(){return dd(this.ptr)};p.prototype.unique_id=p.prototype.unique_id=
function(){return Bc(this.ptr)};p.prototype.__destroy__=p.prototype.__destroy__=function(){ud(this.ptr)};O.prototype=Object.create(v.prototype);O.prototype.constructor=O;O.prototype.__class__=O;O.__cache__={};a.AttributeTransformData=O;O.prototype.transform_type=O.prototype.transform_type=function(){return md(this.ptr)};O.prototype.__destroy__=O.prototype.__destroy__=function(){nc(this.ptr)};B.prototype=Object.create(v.prototype);B.prototype.constructor=B;B.prototype.__class__=B;B.__cache__={};a.AttributeQuantizationTransform=
B;B.prototype.InitFromAttribute=B.prototype.InitFromAttribute=function(a){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);return!!td(b,a)};B.prototype.quantization_bits=B.prototype.quantization_bits=function(){return Lc(this.ptr)};B.prototype.min_value=B.prototype.min_value=function(a){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);return hc(b,a)};B.prototype.range=B.prototype.range=function(){return jd(this.ptr)};B.prototype.__destroy__=B.prototype.__destroy__=function(){pc(this.ptr)};q.prototype=
Object.create(v.prototype);q.prototype.constructor=q;q.prototype.__class__=q;q.__cache__={};a.MetadataQuerier=q;q.prototype.HasIntEntry=q.prototype.HasIntEntry=function(a,c){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);return!!Dc(b,a,c)};q.prototype.GetIntEntry=q.prototype.GetIntEntry=function(a,c){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);return jc(b,a,c)};q.prototype.HasDoubleEntry=q.prototype.HasDoubleEntry=
function(a,c){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);return!!vc(b,a,c)};q.prototype.GetDoubleEntry=q.prototype.GetDoubleEntry=function(a,c){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);return wc(b,a,c)};q.prototype.HasStringEntry=q.prototype.HasStringEntry=function(a,c){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);return!!mc(b,a,c)};q.prototype.GetStringEntry=
q.prototype.GetStringEntry=function(a,c){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);return t(ic(b,a,c))};q.prototype.__destroy__=q.prototype.__destroy__=function(){Qc(this.ptr)};K.prototype=Object.create(v.prototype);K.prototype.constructor=K;K.prototype.__class__=K;K.__cache__={};a.DracoFloat32Array=K;K.prototype.GetValue=K.prototype.GetValue=function(a){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);return qd(b,a)};K.prototype.size=K.prototype.size=
function(){return ad(this.ptr)};K.prototype.__destroy__=K.prototype.__destroy__=function(){Wc(this.ptr)};W.prototype=Object.create(v.prototype);W.prototype.constructor=W;W.prototype.__class__=W;W.__cache__={};a.GeometryAttribute=W;W.prototype.__destroy__=W.prototype.__destroy__=function(){fc(this.ptr)};P.prototype=Object.create(v.prototype);P.prototype.constructor=P;P.prototype.__class__=P;P.__cache__={};a.DecoderBuffer=P;P.prototype.Init=P.prototype.Init=function(a,c){var b=this.ptr;l.prepare();
if("object"==typeof a&&"object"===typeof a){var e=l.alloc(a,N);l.copy(a,N,e);a=e}c&&"object"===typeof c&&(c=c.ptr);Ub(b,a,c)};P.prototype.__destroy__=P.prototype.__destroy__=function(){Sc(this.ptr)};m.prototype=Object.create(v.prototype);m.prototype.constructor=m;m.prototype.__class__=m;m.__cache__={};a.Decoder=m;m.prototype.GetEncodedGeometryType=m.prototype.GetEncodedGeometryType=function(a){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);return dc(b,a)};m.prototype.DecodeBufferToPointCloud=m.prototype.DecodeBufferToPointCloud=
function(a,c){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);return V(bc(b,a,c),D)};m.prototype.DecodeBufferToMesh=m.prototype.DecodeBufferToMesh=function(a,c){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);return V(pd(b,a,c),D)};m.prototype.GetAttributeId=m.prototype.GetAttributeId=function(a,c){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);return kd(b,a,c)};m.prototype.GetAttributeIdByName=
m.prototype.GetAttributeIdByName=function(a,c){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);return Yb(b,a,c)};m.prototype.GetAttributeIdByMetadataEntry=m.prototype.GetAttributeIdByMetadataEntry=function(a,c,e){var b=this.ptr;l.prepare();a&&"object"===typeof a&&(a=a.ptr);c=c&&"object"===typeof c?c.ptr:Z(c);e=e&&"object"===typeof e?e.ptr:Z(e);return uc(b,a,c,e)};m.prototype.GetAttribute=m.prototype.GetAttribute=function(a,c){var b=this.ptr;a&&"object"===
typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);return V(Tc(b,a,c),p)};m.prototype.GetAttributeByUniqueId=m.prototype.GetAttributeByUniqueId=function(a,c){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);return V(tc(b,a,c),p)};m.prototype.GetMetadata=m.prototype.GetMetadata=function(a){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);return V(rd(b,a),Q)};m.prototype.GetAttributeMetadata=m.prototype.GetAttributeMetadata=function(a,c){var b=this.ptr;a&&"object"===
typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);return V(Ac(b,a,c),Q)};m.prototype.GetFaceFromMesh=m.prototype.GetFaceFromMesh=function(a,c,e){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);e&&"object"===typeof e&&(e=e.ptr);return!!Zc(b,a,c,e)};m.prototype.GetTriangleStripsFromMesh=m.prototype.GetTriangleStripsFromMesh=function(a,c){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);return Ec(b,a,c)};m.prototype.GetAttributeFloat=
m.prototype.GetAttributeFloat=function(a,c,e){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);e&&"object"===typeof e&&(e=e.ptr);return!!Rb(b,a,c,e)};m.prototype.GetAttributeFloatForAllPoints=m.prototype.GetAttributeFloatForAllPoints=function(a,c,e){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);e&&"object"===typeof e&&(e=e.ptr);return!!Tb(b,a,c,e)};m.prototype.GetAttributeIntForAllPoints=m.prototype.GetAttributeIntForAllPoints=
function(a,c,e){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);c&&"object"===typeof c&&(c=c.ptr);e&&"object"===typeof e&&(e=e.ptr);return!!Jc(b,a,c,e)};m.prototype.SkipAttributeTransform=m.prototype.SkipAttributeTransform=function(a){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);Hc(b,a)};m.prototype.__destroy__=m.prototype.__destroy__=function(){kc(this.ptr)};F.prototype=Object.create(v.prototype);F.prototype.constructor=F;F.prototype.__class__=F;F.__cache__={};a.Mesh=F;F.prototype.num_faces=
F.prototype.num_faces=function(){return ac(this.ptr)};F.prototype.num_attributes=F.prototype.num_attributes=function(){return xc(this.ptr)};F.prototype.num_points=F.prototype.num_points=function(){return rc(this.ptr)};F.prototype.__destroy__=F.prototype.__destroy__=function(){fd(this.ptr)};aa.prototype=Object.create(v.prototype);aa.prototype.constructor=aa;aa.prototype.__class__=aa;aa.__cache__={};a.VoidPtr=aa;aa.prototype.__destroy__=aa.prototype.__destroy__=function(){Oc(this.ptr)};L.prototype=
Object.create(v.prototype);L.prototype.constructor=L;L.prototype.__class__=L;L.__cache__={};a.DracoInt32Array=L;L.prototype.GetValue=L.prototype.GetValue=function(a){var b=this.ptr;a&&"object"===typeof a&&(a=a.ptr);return Rc(b,a)};L.prototype.size=L.prototype.size=function(){return Gc(this.ptr)};L.prototype.__destroy__=L.prototype.__destroy__=function(){qc(this.ptr)};Q.prototype=Object.create(v.prototype);Q.prototype.constructor=Q;Q.prototype.__class__=Q;Q.__cache__={};a.Metadata=Q;Q.prototype.__destroy__=
Q.prototype.__destroy__=function(){gd(this.ptr)};(function(){function b(){a.OK=ld();a.ERROR=Kc();a.IO_ERROR=Cc();a.INVALID_PARAMETER=Mc();a.UNSUPPORTED_VERSION=vd();a.UNKNOWN_VERSION=Vc();a.INVALID_GEOMETRY_TYPE=ec();a.POINT_CLOUD=Zb();a.TRIANGULAR_MESH=Yc();a.ATTRIBUTE_INVALID_TRANSFORM=id();a.ATTRIBUTE_NO_TRANSFORM=cc();a.ATTRIBUTE_QUANTIZATION_TRANSFORM=Ic();a.ATTRIBUTE_OCTAHEDRON_TRANSFORM=lc();a.INVALID=Xb();a.POSITION=Wb();a.NORMAL=sd();a.COLOR=Nc();a.TEX_COORD=Fc();a.GENERIC=od()}a.calledRun?
b():ab.unshift(b)})();if("function"===typeof a.onModuleParsed)a.onModuleParsed();return e};"object"===typeof module&&module.exports&&(module.exports=DracoDecoderModule);
