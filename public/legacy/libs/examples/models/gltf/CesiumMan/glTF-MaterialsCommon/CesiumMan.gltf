{"asset": {"generator": "COLLADA2GLTF", "version": "2.0"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"children": [21, 1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"mesh": 0, "skin": 0}, {"children": [11, 7, 3], "translation": [-3.352759847530251e-08, 0.00499989278614521, 0.6789997816085815], "rotation": [-0.02679471485316753, -0.026732556521892548, -0.7065614461898804, -0.706638753414154], "scale": [0.9999999403953552, 0.9999998211860656, 0.9999999403953552]}, {"children": [4], "translation": [-0.06804201006889343, -0.02857022918760777, -0.06294959783554077], "rotation": [-0.06642699986696243, -0.6115013957023621, 0.7850273251533508, 0.073387511074543], "scale": [0.9999999403953552, 1.000000238418579, 1.0]}, {"children": [5], "translation": [0.0, 0.2661114931106568, 0.0], "rotation": [0.2162912338972092, 0.12430649250745773, 0.0015752052422612906, -0.9683818817138672], "scale": [1.0, 0.9999998807907104, 1.0]}, {"children": [6], "translation": [0.0, 0.2758249044418335, -1.1175900205273592e-08], "rotation": [0.8472740650177002, -0.029564039781689644, -0.020868001505732536, -0.5299217700958252], "scale": [0.9999999403953552, 0.9999998807907104, 1.0]}, {"translation": [-0.001458480954170227, -0.06619883328676224, 0.027856720611453056], "rotation": [-0.03726436197757721, -0.31931325793266296, 0.9460535645484924, -0.040414959192276], "scale": [1.0, 1.0000003576278689, 1.0000005960464478]}, {"children": [8], "translation": [0.06803668290376663, -0.028518669307231903, -0.06296277046203613], "rotation": [0.2475697100162506, -0.5775680541992188, 0.7479144334793091, -0.2138892114162445], "scale": [1.0, 1.000000238418579, 1.0000001192092896]}, {"children": [9], "translation": [3.725289854372704e-09, 0.2661128044128418, 1.4901200273698123e-08], "rotation": [0.20902779698371887, -0.32988959550857544, -0.05559924244880676, -0.9189064502716064], "scale": [1.0, 1.0000001192092896, 1.0000001192092896]}, {"children": [10], "translation": [-7.450579708745408e-09, 0.2758241891860962, 5.5879398885849704e-09], "rotation": [0.8477396965026855, -0.004254077095538378, -0.009491981938481333, -0.5303107500076294], "scale": [0.9999998807907104, 1.0000001192092896, 1.0000001192092896]}, {"translation": [-0.002346522873267532, -0.06617332994937897, 0.027856789529323574], "rotation": [0.026573536917567253, -0.3201442956924439, 0.9445450901985168, 0.06808964908123016], "scale": [1.0, 1.000000238418579, 1.000000238418579]}, {"children": [12], "translation": [-1.024449947095718e-08, 1.4901200273698123e-08, 0.14541690051555634], "rotation": [-0.6572523713111877, -0.00017969288455788046, -0.00010428009409224616, -0.7536706328392029], "scale": [1.0, 1.0, 1.0000001192092896]}, {"children": [19, 16, 13], "translation": [4.6566100975198305e-10, 0.250516802072525, 3.725289854372704e-09], "rotation": [0.6226037740707397, 1.678345142863691e-05, -3.1824047255213372e-06, -0.7825372219085693], "scale": [1.0, 1.0, 0.9999999403953552]}, {"children": [14], "translation": [-0.09098775684833528, 6.259980000322685e-05, -6.532669794978574e-05], "rotation": [0.2964428961277008, 0.031510334461927414, -0.6522551774978638, -0.6969160437583923], "scale": [1.0000001192092896, 0.9999999403953552, 1.0000001192092896]}, {"children": [15], "translation": [0.0, 0.24200820922851565, -5.96045985901128e-08], "rotation": [-0.1887933611869812, 0.9157071709632874, -0.16780903935432437, -0.3125341236591339], "scale": [0.9999999403953552, 0.9999999403953552, 0.9999998807907104]}, {"translation": [0.0, 0.18779200315475464, 0.0], "rotation": [-0.058613914996385574, 0.2637767195701599, 0.05226854607462883, -0.9613814353942872], "scale": [1.0, 1.0000001192092896, 0.9999999403953552]}, {"children": [17], "translation": [0.0910135880112648, 1.4185899999574758e-05, -5.8054902183357626e-05], "rotation": [0.6797328591346741, 0.689685583114624, -0.2269716113805771, -0.10383165627717972], "scale": [1.0000001192092896, 1.0000001192092896, 1.0]}, {"children": [18], "translation": [1.1641500263781523e-10, 0.2420089989900589, 0.0], "rotation": [-0.013960935175418854, -0.12937255203723907, -0.2522056996822357, -0.9588848352432252], "scale": [1.0, 0.9999999403953552, 1.0]}, {"translation": [1.4901200273698123e-08, 0.18779149651527408, 5.96045985901128e-08], "rotation": [0.006119169760495424, -0.042325541377067566, -0.07877591997385025, -0.9959746599197388], "scale": [0.9999998807907104, 1.000000238418579, 0.9999999403953552]}, {"children": [20], "translation": [-8.847560017954947e-09, 5.96045985901128e-08, 0.06483662128448486], "rotation": [-0.6606296300888062, 8.344435627805069e-05, 7.10925814928487e-05, -0.750711977481842]}, {"translation": [0.0, 0.0520397387444973, 0.0], "rotation": [2.552607384131989e-06, 0.9996904730796814, -0.02487966977059841, -4.329927776325349e-07], "scale": [1.0, 1.0000001192092896, 1.0]}, {"children": [2]}], "meshes": [{"primitives": [{"attributes": {"JOINTS_0": 1, "NORMAL": 2, "POSITION": 3, "TEXCOORD_0": 4, "WEIGHTS_0": 5}, "indices": 0, "mode": 4, "material": 0}], "name": "Cesium_Man"}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 2, "path": "translation"}}, {"sampler": 1, "target": {"node": 2, "path": "rotation"}}, {"sampler": 2, "target": {"node": 2, "path": "scale"}}], "samplers": [{"input": 6, "interpolation": "LINEAR", "output": 7}, {"input": 6, "interpolation": "LINEAR", "output": 8}, {"input": 6, "interpolation": "LINEAR", "output": 9}]}, {"channels": [{"sampler": 0, "target": {"node": 11, "path": "translation"}}, {"sampler": 1, "target": {"node": 11, "path": "rotation"}}, {"sampler": 2, "target": {"node": 11, "path": "scale"}}], "samplers": [{"input": 10, "interpolation": "LINEAR", "output": 11}, {"input": 10, "interpolation": "LINEAR", "output": 12}, {"input": 10, "interpolation": "LINEAR", "output": 13}]}, {"channels": [{"sampler": 0, "target": {"node": 12, "path": "translation"}}, {"sampler": 1, "target": {"node": 12, "path": "rotation"}}, {"sampler": 2, "target": {"node": 12, "path": "scale"}}], "samplers": [{"input": 14, "interpolation": "LINEAR", "output": 15}, {"input": 14, "interpolation": "LINEAR", "output": 16}, {"input": 14, "interpolation": "LINEAR", "output": 17}]}, {"channels": [{"sampler": 0, "target": {"node": 19, "path": "translation"}}, {"sampler": 1, "target": {"node": 19, "path": "rotation"}}, {"sampler": 2, "target": {"node": 19, "path": "scale"}}], "samplers": [{"input": 18, "interpolation": "LINEAR", "output": 19}, {"input": 18, "interpolation": "LINEAR", "output": 20}, {"input": 18, "interpolation": "LINEAR", "output": 21}]}, {"channels": [{"sampler": 0, "target": {"node": 20, "path": "translation"}}, {"sampler": 1, "target": {"node": 20, "path": "rotation"}}, {"sampler": 2, "target": {"node": 20, "path": "scale"}}], "samplers": [{"input": 22, "interpolation": "LINEAR", "output": 23}, {"input": 22, "interpolation": "LINEAR", "output": 24}, {"input": 22, "interpolation": "LINEAR", "output": 25}]}, {"channels": [{"sampler": 0, "target": {"node": 16, "path": "translation"}}, {"sampler": 1, "target": {"node": 16, "path": "rotation"}}, {"sampler": 2, "target": {"node": 16, "path": "scale"}}], "samplers": [{"input": 26, "interpolation": "LINEAR", "output": 27}, {"input": 26, "interpolation": "LINEAR", "output": 28}, {"input": 26, "interpolation": "LINEAR", "output": 29}]}, {"channels": [{"sampler": 0, "target": {"node": 17, "path": "translation"}}, {"sampler": 1, "target": {"node": 17, "path": "rotation"}}, {"sampler": 2, "target": {"node": 17, "path": "scale"}}], "samplers": [{"input": 30, "interpolation": "LINEAR", "output": 31}, {"input": 30, "interpolation": "LINEAR", "output": 32}, {"input": 30, "interpolation": "LINEAR", "output": 33}]}, {"channels": [{"sampler": 0, "target": {"node": 18, "path": "translation"}}, {"sampler": 1, "target": {"node": 18, "path": "rotation"}}, {"sampler": 2, "target": {"node": 18, "path": "scale"}}], "samplers": [{"input": 34, "interpolation": "LINEAR", "output": 35}, {"input": 34, "interpolation": "LINEAR", "output": 36}, {"input": 34, "interpolation": "LINEAR", "output": 37}]}, {"channels": [{"sampler": 0, "target": {"node": 13, "path": "translation"}}, {"sampler": 1, "target": {"node": 13, "path": "rotation"}}, {"sampler": 2, "target": {"node": 13, "path": "scale"}}], "samplers": [{"input": 38, "interpolation": "LINEAR", "output": 39}, {"input": 38, "interpolation": "LINEAR", "output": 40}, {"input": 38, "interpolation": "LINEAR", "output": 41}]}, {"channels": [{"sampler": 0, "target": {"node": 14, "path": "translation"}}, {"sampler": 1, "target": {"node": 14, "path": "rotation"}}, {"sampler": 2, "target": {"node": 14, "path": "scale"}}], "samplers": [{"input": 42, "interpolation": "LINEAR", "output": 43}, {"input": 42, "interpolation": "LINEAR", "output": 44}, {"input": 42, "interpolation": "LINEAR", "output": 45}]}, {"channels": [{"sampler": 0, "target": {"node": 15, "path": "translation"}}, {"sampler": 1, "target": {"node": 15, "path": "rotation"}}, {"sampler": 2, "target": {"node": 15, "path": "scale"}}], "samplers": [{"input": 46, "interpolation": "LINEAR", "output": 47}, {"input": 46, "interpolation": "LINEAR", "output": 48}, {"input": 46, "interpolation": "LINEAR", "output": 49}]}, {"channels": [{"sampler": 0, "target": {"node": 7, "path": "translation"}}, {"sampler": 1, "target": {"node": 7, "path": "rotation"}}, {"sampler": 2, "target": {"node": 7, "path": "scale"}}], "samplers": [{"input": 50, "interpolation": "LINEAR", "output": 51}, {"input": 50, "interpolation": "LINEAR", "output": 52}, {"input": 50, "interpolation": "LINEAR", "output": 53}]}, {"channels": [{"sampler": 0, "target": {"node": 8, "path": "translation"}}, {"sampler": 1, "target": {"node": 8, "path": "rotation"}}, {"sampler": 2, "target": {"node": 8, "path": "scale"}}], "samplers": [{"input": 54, "interpolation": "LINEAR", "output": 55}, {"input": 54, "interpolation": "LINEAR", "output": 56}, {"input": 54, "interpolation": "LINEAR", "output": 57}]}, {"channels": [{"sampler": 0, "target": {"node": 9, "path": "translation"}}, {"sampler": 1, "target": {"node": 9, "path": "rotation"}}, {"sampler": 2, "target": {"node": 9, "path": "scale"}}], "samplers": [{"input": 58, "interpolation": "LINEAR", "output": 59}, {"input": 58, "interpolation": "LINEAR", "output": 60}, {"input": 58, "interpolation": "LINEAR", "output": 61}]}, {"channels": [{"sampler": 0, "target": {"node": 10, "path": "translation"}}, {"sampler": 1, "target": {"node": 10, "path": "rotation"}}, {"sampler": 2, "target": {"node": 10, "path": "scale"}}], "samplers": [{"input": 62, "interpolation": "LINEAR", "output": 63}, {"input": 62, "interpolation": "LINEAR", "output": 64}, {"input": 62, "interpolation": "LINEAR", "output": 65}]}, {"channels": [{"sampler": 0, "target": {"node": 3, "path": "translation"}}, {"sampler": 1, "target": {"node": 3, "path": "rotation"}}, {"sampler": 2, "target": {"node": 3, "path": "scale"}}], "samplers": [{"input": 66, "interpolation": "LINEAR", "output": 67}, {"input": 66, "interpolation": "LINEAR", "output": 68}, {"input": 66, "interpolation": "LINEAR", "output": 69}]}, {"channels": [{"sampler": 0, "target": {"node": 4, "path": "translation"}}, {"sampler": 1, "target": {"node": 4, "path": "rotation"}}, {"sampler": 2, "target": {"node": 4, "path": "scale"}}], "samplers": [{"input": 70, "interpolation": "LINEAR", "output": 71}, {"input": 70, "interpolation": "LINEAR", "output": 72}, {"input": 70, "interpolation": "LINEAR", "output": 73}]}, {"channels": [{"sampler": 0, "target": {"node": 5, "path": "translation"}}, {"sampler": 1, "target": {"node": 5, "path": "rotation"}}, {"sampler": 2, "target": {"node": 5, "path": "scale"}}], "samplers": [{"input": 74, "interpolation": "LINEAR", "output": 75}, {"input": 74, "interpolation": "LINEAR", "output": 76}, {"input": 74, "interpolation": "LINEAR", "output": 77}]}, {"channels": [{"sampler": 0, "target": {"node": 6, "path": "translation"}}, {"sampler": 1, "target": {"node": 6, "path": "rotation"}}, {"sampler": 2, "target": {"node": 6, "path": "scale"}}], "samplers": [{"input": 78, "interpolation": "LINEAR", "output": 79}, {"input": 78, "interpolation": "LINEAR", "output": 80}, {"input": 78, "interpolation": "LINEAR", "output": 81}]}], "skins": [{"inverseBindMatrices": 82, "skeleton": 2, "joints": [2, 11, 12, 19, 20, 16, 13, 17, 14, 18, 15, 7, 3, 8, 4, 9, 5, 10, 6], "name": "Armature"}], "accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5123, "count": 14016, "max": [3272], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 0, "componentType": 5123, "count": 3273, "max": [18, 18, 18, 18], "min": [0, 0, 0, 0], "type": "VEC4"}, {"bufferView": 2, "byteOffset": 0, "componentType": 5126, "count": 3273, "max": [1.0, 0.9999808073043824, 0.9944446086883544], "min": [-1.0, -0.9999808073043824, -1.0], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 39276, "componentType": 5126, "count": 3273, "max": [0.1809539943933487, 0.569136917591095, 1.5065499544143677], "min": [-0.13100001215934753, -0.5691370964050293, 0.0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 26184, "componentType": 5126, "count": 3273, "max": [0.990805983543396, 0.9880298972129822], "min": [0.014079390093684196, 0.008445978164672852], "type": "VEC2"}, {"bufferView": 3, "byteOffset": 0, "componentType": 5126, "count": 3273, "max": [1.0, 0.989919900894165, 0.951076328754425, 0.8741077184677124], "min": [0.010080150328576565, 0.0, 0.0, 0.0], "type": "VEC4"}, {"bufferView": 4, "byteOffset": 0, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 0, "componentType": 5126, "count": 49, "max": [2.386510011831433e-08, -0.02000010944902897, 0.7110069990158081], "min": [-7.101329746461715e-09, -0.030000120401382446, 0.6399999856948853], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 0, "componentType": 5126, "count": 49, "max": [-0.02314124070107937, -0.006974140647798777, -0.7065909504890442, -0.7031946778297424], "min": [-0.05146743357181549, -0.034400247037410736, -0.7094300389289856, -0.7066542506217957], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 588, "componentType": 5126, "count": 49, "max": [1.0000001192092896, 1.0000003576278689, 1.0000001192092896], "min": [0.9999998211860656, 0.9999998807907104, 0.9999997615814208], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 196, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 1176, "componentType": 5126, "count": 49, "max": [0.0009999829344451427, 3.725289943190546e-08, 0.1454170048236847], "min": [0.0009999759495258331, -4.470349779239769e-08, 0.14541690051555634], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 784, "componentType": 5126, "count": 49, "max": [-0.7105135917663574, 0.008397356607019901, 0.000531485362444073, -0.6789330840110779], "min": [-0.7337970733642578, -0.011321297846734524, -0.02596380189061165, -0.703567624092102], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 1764, "componentType": 5126, "count": 49, "max": [1.0000003576278689, 1.0000004768371584, 0.999999701976776], "min": [1.0, 1.0000001192092896, 0.9999992847442628], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 392, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 2352, "componentType": 5126, "count": 49, "max": [0.0, 0.25051671266555786, 0.0], "min": [0.0, 0.25051671266555786, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1568, "componentType": 5126, "count": 49, "max": [0.6358352899551392, 0.06165437772870064, -0.00338419945910573, -0.7642753720283508], "min": [0.6224426627159119, -0.1380288153886795, -0.06534028053283691, -0.782635509967804], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 2940, "componentType": 5126, "count": 49, "max": [1.0000001192092896, 1.0000007152557373, 0.999999701976776], "min": [0.9999998211860656, 1.0000003576278689, 0.9999992847442628], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 588, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 3528, "componentType": 5126, "count": 49, "max": [2.1420399676230772e-08, 8.940700269022274e-08, 0.064838707447052], "min": [-1.5832499755674693e-08, 2.98022992950564e-08, 0.06483834981918335], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 2352, "componentType": 5126, "count": 49, "max": [-0.6325308680534363, 0.024829493835568428, 0.04200226813554764, -0.7498575448989868], "min": [-0.6592774987220764, -0.03641732409596443, -0.03000717982649803, -0.7735550999641418], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 4116, "componentType": 5126, "count": 49, "max": [1.000000238418579, 0.9999998807907104, 1.0], "min": [0.9999998211860656, 0.9999995231628418, 0.999999701976776], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 784, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 4704, "componentType": 5126, "count": 49, "max": [7.450579708745408e-09, 0.0520397387444973, 0.0], "min": [7.450579708745408e-09, 0.0520397387444973, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3136, "componentType": 5126, "count": 49, "max": [0.04680429771542549, 0.9995073676109314, -0.018450811505317688, 0.002033286727964878], "min": [-0.09362706542015076, 0.995067298412323, -0.09058911353349686, -0.0025854958221316338], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 5292, "componentType": 5126, "count": 49, "max": [1.000000238418579, 1.0000001192092896, 1.0000004768371584], "min": [0.999999701976776, 0.9999998211860656, 1.0000001192092896], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 980, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 5880, "componentType": 5126, "count": 49, "max": [0.08800014853477478, 1.4096500308369288e-05, -5.573029920924455e-05], "min": [0.08799994736909866, 1.3977300113765525e-05, -5.596880146185868e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3920, "componentType": 5126, "count": 49, "max": [0.7787204384803772, 0.6963526606559753, -0.2913321256637573, -0.12775331735610965], "min": [0.41440603137016296, 0.2682091891765595, -0.6041955351829529, -0.508333683013916], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 6468, "componentType": 5126, "count": 49, "max": [1.0000005960464478, 1.0000001192092896, 1.0000004768371584], "min": [1.0, 0.9999995827674866, 1.0000001192092896], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 1176, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 7056, "componentType": 5126, "count": 49, "max": [0.0, 0.24200910329818728, 0.0], "min": [0.0, 0.24200910329818728, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4704, "componentType": 5126, "count": 49, "max": [0.005276965908706188, -0.10532382130622864, -0.0563904233276844, -0.9007523059844972], "min": [-0.08755125105381012, -0.15304648876190186, -0.4198120832443238, -0.989989936351776], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 7644, "componentType": 5126, "count": 49, "max": [0.9999999403953552, 0.9999998807907104, 0.9999998807907104], "min": [0.9999994039535524, 0.9999992847442628, 0.9999995231628418], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 1372, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 8232, "componentType": 5126, "count": 49, "max": [-2.98022992950564e-08, 0.18779130280017853, 0.0], "min": [-2.98022992950564e-08, 0.18779130280017853, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 5488, "componentType": 5126, "count": 49, "max": [0.04831664264202118, -0.03682959079742432, 0.1515040546655655, -0.9875762462615968], "min": [-0.01958325318992138, -0.043389420956373215, -0.0806758776307106, -0.9989553689956664], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 8820, "componentType": 5126, "count": 49, "max": [1.000000238418579, 1.0000009536743164, 1.0000005960464478], "min": [0.999999701976776, 1.0000005960464478, 1.000000238418579], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 1568, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 9408, "componentType": 5126, "count": 49, "max": [-0.0879998505115509, 6.264450348680839e-05, -6.240609945962206e-05], "min": [-0.0880001038312912, 6.249549915082753e-05, -6.282330286921933e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6272, "componentType": 5126, "count": 49, "max": [0.6748494505882263, 0.3152157068252564, -0.300369679927826, -0.3483264744281769], "min": [0.3661315143108368, 0.09874838590621948, -0.6449660658836365, -0.8451733589172363], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 9996, "componentType": 5126, "count": 49, "max": [1.0000001192092896, 1.0000001192092896, 1.0], "min": [0.9999997615814208, 0.9999996423721313, 0.999999701976776], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 1764, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 10584, "componentType": 5126, "count": 49, "max": [0.0, 0.24200810492038727, 0.0], "min": [0.0, 0.24200810492038727, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 7056, "componentType": 5126, "count": 49, "max": [-0.003387797623872757, 0.9475951790809632, -0.0798693522810936, -0.3012830018997193], "min": [-0.12710869312286377, 0.916045308113098, -0.2270231395959854, -0.3146948218345642], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 11172, "componentType": 5126, "count": 49, "max": [1.0000003576278689, 0.9999998807907104, 1.0000001192092896], "min": [0.9999999403953552, 0.9999995827674866, 0.9999997615814208], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 1960, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 11760, "componentType": 5126, "count": 49, "max": [0.0, 0.187792107462883, 0.0], "min": [0.0, 0.187792107462883, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 7840, "componentType": 5126, "count": 49, "max": [0.15000686049461365, 0.26206517219543457, 0.06808223575353622, -0.9487173557281494], "min": [-0.0010455237934365869, 0.25685790181159973, -0.10152826458215714, -0.9656248688697816], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 12348, "componentType": 5126, "count": 49, "max": [1.0000001192092896, 1.0000009536743164, 1.0], "min": [0.9999996423721313, 1.0000003576278689, 0.9999995231628418], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2156, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 12936, "componentType": 5126, "count": 49, "max": [0.06761906296014786, -0.02851865068078041, -0.06296355277299881], "min": [0.0676189586520195, -0.028518760576844215, -0.06296365708112717], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 8624, "componentType": 5126, "count": 49, "max": [0.301033079624176, -0.20796972513198853, 0.92630273103714, -0.08994945138692856], "min": [0.1658332496881485, -0.7997090816497803, 0.4959096908569336, -0.3118112981319428], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 13524, "componentType": 5126, "count": 49, "max": [1.0000008344650269, 1.0, 0.9999999403953552], "min": [1.0000003576278689, 0.9999995231628418, 0.9999996423721313], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2352, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 14112, "componentType": 5126, "count": 49, "max": [-1.4901200273698123e-08, 0.26611289381980896, 0.0], "min": [-1.4901200273698123e-08, 0.26611289381980896, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 9408, "componentType": 5126, "count": 49, "max": [0.8112500905990601, -0.1822210103273392, 0.032220568507909775, -0.4743982553482056], "min": [-0.03036016784608364, -0.3419179916381836, -0.289162427186966, -0.9452491998672484], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 14700, "componentType": 5126, "count": 49, "max": [0.9999994039535524, 1.000000238418579, 1.000000238418579], "min": [0.9999989867210388, 0.9999996423721313, 0.9999994039535524], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2548, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 15288, "componentType": 5126, "count": 49, "max": [0.0, 0.2758241891860962, 0.0], "min": [0.0, 0.2758241891860962, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 10192, "componentType": 5126, "count": 49, "max": [0.9907974004745485, -0.0014961245469748974, 0.02489613927900791, -0.13506969809532166], "min": [0.8542653918266296, -0.05430477112531662, -0.00011262076441198587, -0.5192484259605408], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 15876, "componentType": 5126, "count": 49, "max": [1.0000005960464478, 1.0000004768371584, 1.0000004768371584], "min": [1.000000238418579, 1.0, 1.0000001192092896], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2744, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 16464, "componentType": 5126, "count": 49, "max": [-0.0023464488331228495, -0.06617332249879837, 0.02785664983093739], "min": [-0.002346470952033997, -0.06617333739995956, 0.02785659022629261], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 10976, "componentType": 5126, "count": 49, "max": [0.02062430046498776, -0.23465925455093384, 0.9716955423355104, 0.0638260766863823], "min": [0.003326366888359189, -0.5406339168548584, 0.8410344123840332, 0.016216862946748737], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 17052, "componentType": 5126, "count": 49, "max": [1.0000001192092896, 1.0000009536743164, 1.0000005960464478], "min": [0.9999998211860656, 1.000000238418579, 0.9999998211860656], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2940, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 17640, "componentType": 5126, "count": 49, "max": [-0.06845708936452866, -0.028570100665092468, -0.062949538230896], "min": [-0.06845712661743164, -0.02857035957276821, -0.06294971704483032], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 11760, "componentType": 5126, "count": 49, "max": [-0.018168065696954727, -0.18232035636901855, 0.9812799096107484, 0.117560513317585], "min": [-0.07457219809293747, -0.902503490447998, 0.413051187992096, 0.03284534439444542], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 18228, "componentType": 5126, "count": 49, "max": [1.000000238418579, 1.000000238418579, 1.0000003576278689], "min": [0.9999998211860656, 0.9999995827674866, 0.9999996423721313], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 3136, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 18816, "componentType": 5126, "count": 49, "max": [-1.1175900205273592e-08, 0.2661114931106568, 0.0], "min": [-1.1175900205273592e-08, 0.2661114931106568, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 12544, "componentType": 5126, "count": 49, "max": [0.8507033586502075, 0.14946134388446808, 0.04984176158905029, -0.5191445350646973], "min": [-0.06756377220153809, 0.06949601322412491, -0.026268262416124344, -0.9922308921813964], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 19404, "componentType": 5126, "count": 49, "max": [1.0000005960464478, 1.0000003576278689, 1.0000005960464478], "min": [1.0000001192092896, 0.9999995231628418, 0.9999998211860656], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 3332, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 19992, "componentType": 5126, "count": 49, "max": [0.0, 0.2758249044418335, 0.0], "min": [0.0, 0.2758249044418335, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 13328, "componentType": 5126, "count": 49, "max": [0.99649316072464, -0.01710231974720955, -0.022676724940538406, -0.07459255307912827], "min": [0.8792483806610107, -0.04609288275241852, -0.06820148974657059, -0.4750169813632965], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 20580, "componentType": 5126, "count": 49, "max": [0.9999995827674866, 1.0, 1.0000003576278689], "min": [0.999999225139618, 0.9999995231628418, 1.0], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 3528, "componentType": 5126, "count": 49, "max": [2.0], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 21168, "componentType": 5126, "count": 49, "max": [-0.0014585109893232584, -0.06619886308908463, 0.02785670943558216], "min": [-0.0014585329918190837, -0.06619889289140701, 0.02785668894648552], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 14112, "componentType": 5126, "count": 49, "max": [-0.00960757862776518, -0.2635453343391419, 0.9620476961135864, 0.06995902955532074], "min": [-0.04577624797821045, -0.4899238646030426, 0.8689604997634888, -0.06424159556627274], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 21756, "componentType": 5126, "count": 49, "max": [0.9999996423721313, 1.0000003576278689, 0.9999998807907104], "min": [0.9999991655349731, 0.9999997615814208, 0.9999994039535524], "type": "VEC3"}, {"bufferView": 7, "byteOffset": 0, "componentType": 5126, "count": 19, "max": [0.780990481376648, 0.9918341040611268, 0.9992613196372986, 0.0, 1.0, 0.8904604315757751, 0.6854007244110107, 0.0, 0.79917311668396, 0.9999359250068665, 0.997134804725647, 0.0, 0.20702040195465088, 0.5989438891410828, 1.001250982284546, 1.0], "min": [-0.9985063076019288, -0.9971349835395812, -0.9999359250068665, 0.0, -1.0, -0.8904621005058289, -0.4517692029476166, 0.0, -0.18484599888324735, -0.9853218197822572, -0.997802197933197, 0.0, -0.811928927898407, -1.18982994556427, -1.058609962463379, 1.0], "type": "MAT4"}], "materials": [{"extensions": {"KHR_materials_common": {"doubleSided": false, "jointCount": 19, "technique": "PHONG", "transparent": false, "values": {"ambient": [0.0, 0.0, 0.0, 1.0], "diffuse": [0], "emission": [0.0, 0.0, 0.0, 1.0], "specular": [0.10000000149011612, 0.10000000149011612, 0.10000000149011612, 1.0], "shininess": [256.0], "transparency": [1.0]}, "name": "Cesium_Man-effect"}}, "name": "Cesium_Man-effect"}], "textures": [{"sampler": 0, "source": 0}], "images": [{"uri": "CesiumMan.jpg"}], "samplers": [{"magFilter": 9729, "minFilter": 9986, "wrapS": 10497, "wrapT": 10497}], "bufferViews": [{"buffer": 0, "byteOffset": 225468, "byteLength": 28032, "target": 34963}, {"buffer": 0, "byteOffset": 169376, "byteLength": 52368, "byteStride": 8, "target": 34962}, {"buffer": 0, "byteOffset": 90824, "byteLength": 78552, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteOffset": 16112, "byteLength": 52368, "byteStride": 16, "target": 34962}, {"buffer": 0, "byteOffset": 221744, "byteLength": 3724}, {"buffer": 0, "byteOffset": 68480, "byteLength": 22344}, {"buffer": 0, "byteOffset": 1216, "byteLength": 14896}, {"buffer": 0, "byteOffset": 0, "byteLength": 1216}], "buffers": [{"byteLength": 253500, "uri": "CesiumMan0.bin"}], "extensionsRequired": ["KHR_materials_common"], "extensionsUsed": ["KHR_materials_common"]}