{"asset": {"generator": "COLLADA2GLTF", "version": "2.0"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"children": [3, 2, 1], "matrix": [0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 0.009999999776482582, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"matrix": [-0.9546916484832764, 0.2181433141231537, -0.2024286538362503, 0.0, 0.014671952463686468, 0.7138853073120117, 0.7001089453697205, 0.0, 0.2972349226474762, 0.6654181480407715, -0.6847409009933472, 0.0, 148.6540069580078, 183.6720123291016, -292.1790161132813, 1.0]}, {"matrix": [-0.7289686799049377, 0.0, -0.6845470666885376, 0.0, -0.4252049028873444, 0.7836934328079224, 0.4527972936630249, 0.0, 0.5364750623703003, 0.6211478114128113, -0.571287989616394, 0.0, 400.1130065917969, 463.2640075683594, -431.0780334472656, 1.0], "camera": 0}, {"mesh": 0}], "cameras": [{"perspective": {"aspectRatio": 1.5, "yfov": 0.6605925559997559, "zfar": 10000.0, "znear": 1.0}, "type": "perspective"}], "meshes": [{"primitives": [{"attributes": {"NORMAL": 1, "POSITION": 2, "TEXCOORD_0": 3}, "indices": 0, "mode": 4, "material": 0}], "name": "LOD3spShape"}], "accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5123, "count": 12636, "max": [2398], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 0, "componentType": 5126, "count": 2399, "max": [0.9995989799499512, 0.999580979347229, 0.9984359741210938], "min": [-0.9990839958190918, -1.0, -0.9998319745063782], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 28788, "componentType": 5126, "count": 2399, "max": [96.17990112304688, 163.97000122070312, 53.92519760131836], "min": [-69.29850006103516, 9.929369926452637, -61.32819747924805], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 0, "componentType": 5126, "count": 2399, "max": [0.9833459854125975, 0.9800369739532472], "min": [0.026409000158309937, 0.01996302604675293], "type": "VEC2"}], "materials": [{"values": {"ambient": [0.0, 0.0, 0.0, 1.0], "diffuse": [0], "emission": [0.0, 0.0, 0.0, 1.0], "specular": [0.0, 0.0, 0.0, 1.0], "shininess": [0.30000001192092896], "transparency": [1.0]}, "technique": 0}], "textures": [{"sampler": 0, "source": 0}], "images": [{"uri": "DuckCM.png"}], "samplers": [{"magFilter": 9729, "minFilter": 9986, "wrapS": 10497, "wrapT": 10497}], "techniques": [{"attributes": {"a_normal": "normal", "a_position": "position", "a_texcoord0": "texcoord0"}, "parameters": {"ambient": {"type": 35666}, "diffuse": {"type": 35678}, "emission": {"type": 35666}, "light0Color": {"value": [1.0, 1.0, 1.0], "type": 35665}, "light0Transform": {"semantic": "MODELVIEW", "node": 1, "type": 35676}, "modelViewMatrix": {"semantic": "MODELVIEW", "type": 35676}, "normal": {"semantic": "NORMAL", "type": 35665}, "normalMatrix": {"semantic": "MODELVIEWINVERSETRANSPOSE", "type": 35675}, "position": {"semantic": "POSITION", "type": 35665}, "projectionMatrix": {"semantic": "PROJECTION", "type": 35676}, "shininess": {"type": 5126}, "specular": {"type": 35666}, "texcoord0": {"semantic": "TEXCOORD_0", "type": 35665}, "transparency": {"type": 5126}}, "program": 0, "states": {"enable": [2884, 2929]}, "uniforms": {"u_ambient": "ambient", "u_diffuse": "diffuse", "u_emission": "emission", "u_light0Color": "light0Color", "u_light0Transform": "light0Transform", "u_modelViewMatrix": "modelViewMatrix", "u_normalMatrix": "normalMatrix", "u_projectionMatrix": "projectionMatrix", "u_shininess": "shininess", "u_specular": "specular", "u_transparency": "transparency"}}], "programs": [{"attributes": ["a_normal", "a_position", "a_texcoord0"], "fragmentShader": 1, "vertexShader": 0}], "shaders": [{"type": 35633, "uri": "Duck0.vert"}, {"type": 35632, "uri": "Duck1.frag"}], "bufferViews": [{"buffer": 0, "byteOffset": 76768, "byteLength": 25272, "target": 34963}, {"buffer": 0, "byteOffset": 0, "byteLength": 57576, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteOffset": 57576, "byteLength": 19192, "byteStride": 8, "target": 34962}], "buffers": [{"byteLength": 102040, "uri": "Duck0.bin"}], "extensionsRequired": ["KHR_technique_webgl"], "extensionsUsed": ["KHR_technique_webgl"]}