{"asset": {"generator": "COLLADA2GLTF", "version": "2.0"}, "scene": 0, "scenes": [{"nodes": [0]}], "nodes": [{"children": [34, 1], "matrix": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"mesh": 0, "skin": 0}, {"children": [3], "translation": [-1.1628570556640625, 99.4341278076172, -44.13312911987305], "rotation": [0.6116809844970703, 0.354727566242218, 0.6117032766342163, 0.3547307252883911], "scale": [0.9999999403953552, 1.0000001192092896, 1.0]}, {"children": [18, 13, 8, 4], "translation": [0.0, 142.05380249023438, 0.0], "rotation": [0.009599274955689909, -0.0010376531863585117, 0.9151230454444884, 0.4030591249465943], "scale": [0.9999998807907104, 1.0000001192092896, 1.0000001192092896]}, {"children": [5], "translation": [-229.2772979736328, 51.57170867919922, 3.920083999633789], "rotation": [-0.0037880190648138527, -0.008884812705218792, -0.6532931327819824, -0.7570434212684631], "scale": [0.9999997615814208, 1.0000001192092896, 1.0]}, {"children": [6], "translation": [-7.629389983776491e-06, 275.1336059570313, 2.38419005427204e-07], "rotation": [-5.957689381830278e-07, -2.2050939207929335e-07, -0.12589137256145477, -0.9920440316200256], "scale": [0.9999999403953552, 0.9999998807907104, 0.9999999403953552]}, {"children": [7], "translation": [-2.2888199964654635e-05, 339.0890197753906, 0.0], "rotation": [8.796734505267523e-07, 6.967138688196428e-07, -0.11349057406187057, -0.9935390949249268], "scale": [1.0, 0.9999999403953552, 1.0]}, {"translation": [1.525879997643642e-05, 374.1900939941406, 0.0], "rotation": [0.0003985897928941995, 0.9999998807907104, 3.396345107375965e-07, 3.304860456410097e-06], "scale": [1.0, 0.9999998211860656, 1.0]}, {"children": [9], "translation": [-101.904296875, 96.81950378417967, 158.1428985595703], "rotation": [-0.5870404839515686, -0.4025762379169464, 0.37822479009628296, -0.5918291211128235], "scale": [1.0000005960464478, 0.9999995827674866, 1.0]}, {"children": [10], "translation": [1.525879997643642e-05, 547.8187866210938, 3.051759995287284e-05], "rotation": [0.8947640061378479, -0.0988358035683632, 0.3619275391101837, -0.2421517372131348], "scale": [0.9999999403953552, 1.0, 1.0000005960464478]}, {"children": [11], "translation": [-6.103519990574569e-05, 532.7478637695312, 3.814699812210165e-05], "rotation": [-0.6655603647232056, 0.43436089158058167, -0.24011340737342837, -0.5574095845222473], "scale": [1.0, 1.0, 0.9999998807907104]}, {"children": [12], "translation": [-1.525879997643642e-05, 286.1813049316406, -5.340580173651688e-05], "rotation": [-0.25593262910842896, 0.3620181381702423, 0.3219507336616516, -0.8365339636802673], "scale": [0.9999998807907104, 0.9999999403953552, 1.0]}, {"translation": [0.0, 166.96800231933597, 1.1444099982327316e-05], "rotation": [0.7009931206703186, -0.700989305973053, 0.09279558807611465, -0.0927983820438385], "scale": [0.9999999403953552, 0.9999999403953552, 0.9999999403953552]}, {"children": [14], "translation": [-107.67569732666016, 94.97681427001952, -155.40679931640625], "rotation": [0.5973692536354065, 0.3938325047492981, 0.3649851679801941, -0.5956777334213257], "scale": [0.9999995827674866, 1.0000005960464478, 1.0]}, {"children": [15], "translation": [-1.525879997643642e-05, 547.819091796875, 3.814699994109105e-06], "rotation": [0.8947624564170837, -0.0988353118300438, -0.3619306385517121, 0.24215207993984225], "scale": [1.0000001192092896, 0.9999998211860656, 0.9999987483024596]}, {"children": [16], "translation": [0.0, 532.7470092773438, 0.0], "rotation": [0.6655597686767578, -0.4343646466732025, -0.24011126160621643, -0.5574080944061279], "scale": [0.9999998807907104, 0.999999701976776, 0.9999999403953552]}, {"children": [17], "translation": [1.525879997643642e-05, 286.1807861328125, -2.2888199964654635e-05], "rotation": [0.25593167543411255, -0.3620148301124573, 0.3219532072544098, -0.8365347385406494], "scale": [1.0, 1.0000001192092896, 1.0]}, {"translation": [-6.103519990574569e-05, 166.9690093994141, 3.814699994109105e-06], "rotation": [0.7009877562522888, 0.7009937167167664, -0.09279301762580872, -0.09280823916196825], "scale": [0.9999999403953552, 1.0, 1.0]}, {"children": [19], "translation": [399.1947937011719, -0.31954190135002136, -0.003049075836315751], "rotation": [-0.004396272823214531, -0.0025365734472870827, -0.05076199769973755, -0.9986979365348816], "scale": [0.9999999403953552, 1.0, 1.0000001192092896]}, {"children": [32, 26, 20], "translation": [402.5515747070313, -0.10438539832830428, -0.002846478950232268], "rotation": [-4.574490503728157e-06, -1.0040300367109012e-05, 0.35299697518348694, -0.9356244802474976], "scale": [1.0, 1.0000001192092896, 1.0]}, {"children": [21], "translation": [0.003967284690588713, 0.14590449631214145, -43.16883850097656], "rotation": [0.629516065120697, 0.328596293926239, -0.1958152800798416, -0.6763061881065369], "scale": [0.999999701976776, 1.0000003576278689, 1.0]}, {"children": [22], "translation": [-0.0003051759849768132, 175.07150268554688, 7.629390165675431e-05], "rotation": [-0.5734227895736694, 0.18226787447929385, -0.23226681351661685, -0.7642098665237427], "scale": [1.0000001192092896, 1.0000003576278689, 0.999999701976776]}, {"children": [23], "translation": [0.0, 370.0174865722656, 0.0], "rotation": [-0.27098795771598816, -0.08019046485424042, 0.25969398021698, -0.923414409160614], "scale": [1.0, 1.0000001192092896, 1.0000001192092896]}, {"children": [24], "translation": [6.103519990574569e-05, 370.0173034667969, 0.0], "rotation": [0.3159535229206085, 0.3460785150527954, 0.361482322216034, -0.8060604929924011], "scale": [0.9999998807907104, 0.9999999403953552, 1.0]}, {"children": [25], "translation": [5.340580173651688e-05, 186.69309997558597, 3.051759995287284e-05], "rotation": [-0.061906907707452774, -0.6184580326080322, 0.322037935256958, -0.7141210436820984], "scale": [1.000000238418579, 1.0000001192092896, 0.9999999403953552]}, {"translation": [0.0, 167.76010131835938, 0.0], "rotation": [-0.18707998096942904, 0.18709905445575717, 0.6818875074386597, -0.6819269061088562], "scale": [0.9999999403953552, 0.9999998211860656, 1.0]}, {"children": [27], "translation": [0.0004882809880655259, 0.1427001953125, 43.16946029663086], "rotation": [-0.6269798874855042, -0.3371228575706482, -0.20376521348953247, -0.6721045970916748], "scale": [1.0000003576278689, 0.9999996423721313, 1.0000001192092896]}, {"children": [28], "translation": [0.0, 175.07080078125, -6.103519990574569e-05], "rotation": [0.5832023620605469, -0.18192623555660248, -0.20986202359199524, -0.7633713483810425], "scale": [0.9999997615814208, 1.0000001192092896, 0.9999998807907104]}, {"children": [29], "translation": [-6.103519990574569e-05, 370.0173034667969, -6.103519990574569e-05], "rotation": [0.2709869146347046, 0.0801902487874031, 0.2596951127052307, -0.923414409160614], "scale": [1.0000001192092896, 1.0000001192092896, 1.0000001192092896]}, {"children": [30], "translation": [6.103519990574569e-05, 370.01708984375, 0.0], "rotation": [-0.315950095653534, -0.3460729122161865, 0.3614855408668518, -0.8060628175735474], "scale": [0.9999998807907104, 1.0, 0.9999999403953552]}, {"children": [31], "translation": [-2.2888199964654635e-05, 186.6940155029297, 7.629389983776491e-06], "rotation": [0.0619109645485878, 0.6184656620025635, 0.32203570008277893, -0.7141150236129761], "scale": [1.0, 1.0000001192092896, 1.0]}, {"translation": [0.0, 167.7620086669922, -6.103519990574569e-05], "rotation": [-0.18710123002529144, -0.1870883852243424, -0.6819262504577637, -0.6818854212760925], "scale": [1.0, 0.9999999403953552, 1.0]}, {"children": [33], "translation": [181.73239135742188, -0.0008544919546693563, 0.0025956628378480677], "rotation": [0.00926738977432251, 0.002782774157822132, 0.3884811699390412, -0.9214058518409728], "scale": [1.0, 1.0, 1.0000001192092896]}, {"translation": [-3.051759995287284e-05, 345.1398010253906, 0.0], "rotation": [-1.0299070709152147e-05, -1.8406668459647335e-05, -0.7070866823196411, -0.7071268558502197], "scale": [1.0, 0.9999999403953552, 1.0]}, {"children": [2], "matrix": [0.02539999969303608, 0.0, 0.0, 0.0, 0.0, 0.02539999969303608, 0.0, 0.0, 0.0, 0.0, 0.02539999969303608, 0.0, 0.0, 0.0, 11.732219696044922, 1.0]}], "meshes": [{"primitives": [{"attributes": {"JOINTS_0": 1, "NORMAL": 2, "POSITION": 3, "TEXCOORD_0": 4, "WEIGHTS_0": 5}, "indices": 0, "mode": 4, "material": 0}], "name": "monster"}], "animations": [{"channels": [{"sampler": 0, "target": {"node": 2, "path": "translation"}}, {"sampler": 1, "target": {"node": 2, "path": "rotation"}}, {"sampler": 2, "target": {"node": 2, "path": "scale"}}], "samplers": [{"input": 6, "interpolation": "LINEAR", "output": 7}, {"input": 6, "interpolation": "LINEAR", "output": 8}, {"input": 6, "interpolation": "LINEAR", "output": 9}]}, {"channels": [{"sampler": 0, "target": {"node": 3, "path": "translation"}}, {"sampler": 1, "target": {"node": 3, "path": "rotation"}}, {"sampler": 2, "target": {"node": 3, "path": "scale"}}], "samplers": [{"input": 10, "interpolation": "LINEAR", "output": 11}, {"input": 10, "interpolation": "LINEAR", "output": 12}, {"input": 10, "interpolation": "LINEAR", "output": 13}]}, {"channels": [{"sampler": 0, "target": {"node": 18, "path": "translation"}}, {"sampler": 1, "target": {"node": 18, "path": "rotation"}}, {"sampler": 2, "target": {"node": 18, "path": "scale"}}], "samplers": [{"input": 14, "interpolation": "LINEAR", "output": 15}, {"input": 14, "interpolation": "LINEAR", "output": 16}, {"input": 14, "interpolation": "LINEAR", "output": 17}]}, {"channels": [{"sampler": 0, "target": {"node": 19, "path": "translation"}}, {"sampler": 1, "target": {"node": 19, "path": "rotation"}}, {"sampler": 2, "target": {"node": 19, "path": "scale"}}], "samplers": [{"input": 18, "interpolation": "LINEAR", "output": 19}, {"input": 18, "interpolation": "LINEAR", "output": 20}, {"input": 18, "interpolation": "LINEAR", "output": 21}]}, {"channels": [{"sampler": 0, "target": {"node": 32, "path": "translation"}}, {"sampler": 1, "target": {"node": 32, "path": "rotation"}}, {"sampler": 2, "target": {"node": 32, "path": "scale"}}], "samplers": [{"input": 22, "interpolation": "LINEAR", "output": 23}, {"input": 22, "interpolation": "LINEAR", "output": 24}, {"input": 22, "interpolation": "LINEAR", "output": 25}]}, {"channels": [{"sampler": 0, "target": {"node": 33, "path": "translation"}}, {"sampler": 1, "target": {"node": 33, "path": "rotation"}}, {"sampler": 2, "target": {"node": 33, "path": "scale"}}], "samplers": [{"input": 26, "interpolation": "LINEAR", "output": 27}, {"input": 26, "interpolation": "LINEAR", "output": 28}, {"input": 26, "interpolation": "LINEAR", "output": 29}]}, {"channels": [{"sampler": 0, "target": {"node": 26, "path": "translation"}}, {"sampler": 1, "target": {"node": 26, "path": "rotation"}}, {"sampler": 2, "target": {"node": 26, "path": "scale"}}], "samplers": [{"input": 30, "interpolation": "LINEAR", "output": 31}, {"input": 30, "interpolation": "LINEAR", "output": 32}, {"input": 30, "interpolation": "LINEAR", "output": 33}]}, {"channels": [{"sampler": 0, "target": {"node": 27, "path": "translation"}}, {"sampler": 1, "target": {"node": 27, "path": "rotation"}}, {"sampler": 2, "target": {"node": 27, "path": "scale"}}], "samplers": [{"input": 34, "interpolation": "LINEAR", "output": 35}, {"input": 34, "interpolation": "LINEAR", "output": 36}, {"input": 34, "interpolation": "LINEAR", "output": 37}]}, {"channels": [{"sampler": 0, "target": {"node": 28, "path": "translation"}}, {"sampler": 1, "target": {"node": 28, "path": "rotation"}}, {"sampler": 2, "target": {"node": 28, "path": "scale"}}], "samplers": [{"input": 38, "interpolation": "LINEAR", "output": 39}, {"input": 38, "interpolation": "LINEAR", "output": 40}, {"input": 38, "interpolation": "LINEAR", "output": 41}]}, {"channels": [{"sampler": 0, "target": {"node": 29, "path": "translation"}}, {"sampler": 1, "target": {"node": 29, "path": "rotation"}}, {"sampler": 2, "target": {"node": 29, "path": "scale"}}], "samplers": [{"input": 42, "interpolation": "LINEAR", "output": 43}, {"input": 42, "interpolation": "LINEAR", "output": 44}, {"input": 42, "interpolation": "LINEAR", "output": 45}]}, {"channels": [{"sampler": 0, "target": {"node": 30, "path": "translation"}}, {"sampler": 1, "target": {"node": 30, "path": "rotation"}}, {"sampler": 2, "target": {"node": 30, "path": "scale"}}], "samplers": [{"input": 46, "interpolation": "LINEAR", "output": 47}, {"input": 46, "interpolation": "LINEAR", "output": 48}, {"input": 46, "interpolation": "LINEAR", "output": 49}]}, {"channels": [{"sampler": 0, "target": {"node": 31, "path": "translation"}}, {"sampler": 1, "target": {"node": 31, "path": "rotation"}}, {"sampler": 2, "target": {"node": 31, "path": "scale"}}], "samplers": [{"input": 50, "interpolation": "LINEAR", "output": 51}, {"input": 50, "interpolation": "LINEAR", "output": 52}, {"input": 50, "interpolation": "LINEAR", "output": 53}]}, {"channels": [{"sampler": 0, "target": {"node": 20, "path": "translation"}}, {"sampler": 1, "target": {"node": 20, "path": "rotation"}}, {"sampler": 2, "target": {"node": 20, "path": "scale"}}], "samplers": [{"input": 54, "interpolation": "LINEAR", "output": 55}, {"input": 54, "interpolation": "LINEAR", "output": 56}, {"input": 54, "interpolation": "LINEAR", "output": 57}]}, {"channels": [{"sampler": 0, "target": {"node": 21, "path": "translation"}}, {"sampler": 1, "target": {"node": 21, "path": "rotation"}}, {"sampler": 2, "target": {"node": 21, "path": "scale"}}], "samplers": [{"input": 58, "interpolation": "LINEAR", "output": 59}, {"input": 58, "interpolation": "LINEAR", "output": 60}, {"input": 58, "interpolation": "LINEAR", "output": 61}]}, {"channels": [{"sampler": 0, "target": {"node": 22, "path": "translation"}}, {"sampler": 1, "target": {"node": 22, "path": "rotation"}}, {"sampler": 2, "target": {"node": 22, "path": "scale"}}], "samplers": [{"input": 62, "interpolation": "LINEAR", "output": 63}, {"input": 62, "interpolation": "LINEAR", "output": 64}, {"input": 62, "interpolation": "LINEAR", "output": 65}]}, {"channels": [{"sampler": 0, "target": {"node": 23, "path": "translation"}}, {"sampler": 1, "target": {"node": 23, "path": "rotation"}}, {"sampler": 2, "target": {"node": 23, "path": "scale"}}], "samplers": [{"input": 66, "interpolation": "LINEAR", "output": 67}, {"input": 66, "interpolation": "LINEAR", "output": 68}, {"input": 66, "interpolation": "LINEAR", "output": 69}]}, {"channels": [{"sampler": 0, "target": {"node": 24, "path": "translation"}}, {"sampler": 1, "target": {"node": 24, "path": "rotation"}}, {"sampler": 2, "target": {"node": 24, "path": "scale"}}], "samplers": [{"input": 70, "interpolation": "LINEAR", "output": 71}, {"input": 70, "interpolation": "LINEAR", "output": 72}, {"input": 70, "interpolation": "LINEAR", "output": 73}]}, {"channels": [{"sampler": 0, "target": {"node": 25, "path": "translation"}}, {"sampler": 1, "target": {"node": 25, "path": "rotation"}}, {"sampler": 2, "target": {"node": 25, "path": "scale"}}], "samplers": [{"input": 74, "interpolation": "LINEAR", "output": 75}, {"input": 74, "interpolation": "LINEAR", "output": 76}, {"input": 74, "interpolation": "LINEAR", "output": 77}]}, {"channels": [{"sampler": 0, "target": {"node": 13, "path": "translation"}}, {"sampler": 1, "target": {"node": 13, "path": "rotation"}}, {"sampler": 2, "target": {"node": 13, "path": "scale"}}], "samplers": [{"input": 78, "interpolation": "LINEAR", "output": 79}, {"input": 78, "interpolation": "LINEAR", "output": 80}, {"input": 78, "interpolation": "LINEAR", "output": 81}]}, {"channels": [{"sampler": 0, "target": {"node": 14, "path": "translation"}}, {"sampler": 1, "target": {"node": 14, "path": "rotation"}}, {"sampler": 2, "target": {"node": 14, "path": "scale"}}], "samplers": [{"input": 82, "interpolation": "LINEAR", "output": 83}, {"input": 82, "interpolation": "LINEAR", "output": 84}, {"input": 82, "interpolation": "LINEAR", "output": 85}]}, {"channels": [{"sampler": 0, "target": {"node": 15, "path": "translation"}}, {"sampler": 1, "target": {"node": 15, "path": "rotation"}}, {"sampler": 2, "target": {"node": 15, "path": "scale"}}], "samplers": [{"input": 86, "interpolation": "LINEAR", "output": 87}, {"input": 86, "interpolation": "LINEAR", "output": 88}, {"input": 86, "interpolation": "LINEAR", "output": 89}]}, {"channels": [{"sampler": 0, "target": {"node": 16, "path": "translation"}}, {"sampler": 1, "target": {"node": 16, "path": "rotation"}}, {"sampler": 2, "target": {"node": 16, "path": "scale"}}], "samplers": [{"input": 90, "interpolation": "LINEAR", "output": 91}, {"input": 90, "interpolation": "LINEAR", "output": 92}, {"input": 90, "interpolation": "LINEAR", "output": 93}]}, {"channels": [{"sampler": 0, "target": {"node": 17, "path": "translation"}}, {"sampler": 1, "target": {"node": 17, "path": "rotation"}}, {"sampler": 2, "target": {"node": 17, "path": "scale"}}], "samplers": [{"input": 94, "interpolation": "LINEAR", "output": 95}, {"input": 94, "interpolation": "LINEAR", "output": 96}, {"input": 94, "interpolation": "LINEAR", "output": 97}]}, {"channels": [{"sampler": 0, "target": {"node": 8, "path": "translation"}}, {"sampler": 1, "target": {"node": 8, "path": "rotation"}}, {"sampler": 2, "target": {"node": 8, "path": "scale"}}], "samplers": [{"input": 98, "interpolation": "LINEAR", "output": 99}, {"input": 98, "interpolation": "LINEAR", "output": 100}, {"input": 98, "interpolation": "LINEAR", "output": 101}]}, {"channels": [{"sampler": 0, "target": {"node": 9, "path": "translation"}}, {"sampler": 1, "target": {"node": 9, "path": "rotation"}}, {"sampler": 2, "target": {"node": 9, "path": "scale"}}], "samplers": [{"input": 102, "interpolation": "LINEAR", "output": 103}, {"input": 102, "interpolation": "LINEAR", "output": 104}, {"input": 102, "interpolation": "LINEAR", "output": 105}]}, {"channels": [{"sampler": 0, "target": {"node": 10, "path": "translation"}}, {"sampler": 1, "target": {"node": 10, "path": "rotation"}}, {"sampler": 2, "target": {"node": 10, "path": "scale"}}], "samplers": [{"input": 106, "interpolation": "LINEAR", "output": 107}, {"input": 106, "interpolation": "LINEAR", "output": 108}, {"input": 106, "interpolation": "LINEAR", "output": 109}]}, {"channels": [{"sampler": 0, "target": {"node": 11, "path": "translation"}}, {"sampler": 1, "target": {"node": 11, "path": "rotation"}}, {"sampler": 2, "target": {"node": 11, "path": "scale"}}], "samplers": [{"input": 110, "interpolation": "LINEAR", "output": 111}, {"input": 110, "interpolation": "LINEAR", "output": 112}, {"input": 110, "interpolation": "LINEAR", "output": 113}]}, {"channels": [{"sampler": 0, "target": {"node": 12, "path": "translation"}}, {"sampler": 1, "target": {"node": 12, "path": "rotation"}}, {"sampler": 2, "target": {"node": 12, "path": "scale"}}], "samplers": [{"input": 114, "interpolation": "LINEAR", "output": 115}, {"input": 114, "interpolation": "LINEAR", "output": 116}, {"input": 114, "interpolation": "LINEAR", "output": 117}]}, {"channels": [{"sampler": 0, "target": {"node": 4, "path": "translation"}}, {"sampler": 1, "target": {"node": 4, "path": "rotation"}}, {"sampler": 2, "target": {"node": 4, "path": "scale"}}], "samplers": [{"input": 118, "interpolation": "LINEAR", "output": 119}, {"input": 118, "interpolation": "LINEAR", "output": 120}, {"input": 118, "interpolation": "LINEAR", "output": 121}]}, {"channels": [{"sampler": 0, "target": {"node": 5, "path": "translation"}}, {"sampler": 1, "target": {"node": 5, "path": "rotation"}}, {"sampler": 2, "target": {"node": 5, "path": "scale"}}], "samplers": [{"input": 122, "interpolation": "LINEAR", "output": 123}, {"input": 122, "interpolation": "LINEAR", "output": 124}, {"input": 122, "interpolation": "LINEAR", "output": 125}]}, {"channels": [{"sampler": 0, "target": {"node": 6, "path": "translation"}}, {"sampler": 1, "target": {"node": 6, "path": "rotation"}}, {"sampler": 2, "target": {"node": 6, "path": "scale"}}], "samplers": [{"input": 126, "interpolation": "LINEAR", "output": 127}, {"input": 126, "interpolation": "LINEAR", "output": 128}, {"input": 126, "interpolation": "LINEAR", "output": 129}]}, {"channels": [{"sampler": 0, "target": {"node": 7, "path": "translation"}}, {"sampler": 1, "target": {"node": 7, "path": "rotation"}}, {"sampler": 2, "target": {"node": 7, "path": "scale"}}], "samplers": [{"input": 130, "interpolation": "LINEAR", "output": 131}, {"input": 130, "interpolation": "LINEAR", "output": 132}, {"input": 130, "interpolation": "LINEAR", "output": 133}]}], "skins": [{"inverseBindMatrices": 134, "skeleton": 2, "joints": [2, 3, 18, 19, 32, 33, 26, 27, 28, 29, 30, 31, 20, 21, 22, 23, 24, 25, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 4, 5, 6, 7], "name": "Armature"}], "accessors": [{"bufferView": 0, "byteOffset": 0, "componentType": 5123, "count": 2652, "max": [779], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 0, "componentType": 5123, "count": 780, "max": [30, 30, 29, 24], "min": [0, 0, 0, 0], "type": "VEC4"}, {"bufferView": 2, "byteOffset": 0, "componentType": 5126, "count": 780, "max": [0.9988760948181152, 0.9998818039894104, 0.9998157024383544], "min": [-0.9988760948181152, -0.9980314373970032, -0.9998273253440856], "type": "VEC3"}, {"bufferView": 2, "byteOffset": 9360, "componentType": 5126, "count": 780, "max": [7.313104152679443, 25.414928436279297, 33.43614959716797], "min": [-15.450709342956545, -33.22121047973633, -1.280419945716858], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 6240, "componentType": 5126, "count": 780, "max": [1.009609937667847, 0.9916410446166992], "min": [0.005085944663733244, -0.07434999942779541], "type": "VEC2"}, {"bufferView": 3, "byteOffset": 0, "componentType": 5126, "count": 780, "max": [1.0, 0.9999979734420776, 0.9997379779815674, 0.9927020072937012], "min": [1.9999999949504854e-06, 0.0, 0.0, 0.0], "type": "VEC4"}, {"bufferView": 4, "byteOffset": 0, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 0, "componentType": 5126, "count": 101, "max": [688.0628662109375, 119.62960052490236, 436.8376770019531], "min": [688.0628662109375, 119.62960052490236, 436.8376770019531], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 0, "componentType": 5126, "count": 101, "max": [0.3873895406723023, 0.5915361642837524, 0.5915565490722656, -0.3873957097530365], "min": [0.3873895406723023, 0.5915361642837524, 0.5915565490722656, -0.3873957097530365], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 1212, "componentType": 5126, "count": 101, "max": [1.0000003576278689, 1.0000003576278689, 1.0], "min": [1.0000003576278689, 1.0000003576278689, 1.0], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 404, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 2424, "componentType": 5126, "count": 101, "max": [-3.051759995287284e-05, 142.05380249023438, 1.525879997643642e-05], "min": [-3.051759995287284e-05, 142.05380249023438, 1.525879997643642e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 1616, "componentType": 5126, "count": 101, "max": [0.07529418170452118, 0.08668317645788193, 0.932952642440796, 0.4080679714679718], "min": [-0.05637124180793762, -0.08988404273986816, 0.9125778675079346, 0.3448300063610077], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 3636, "componentType": 5126, "count": 101, "max": [1.000001311302185, 1.000001072883606, 1.0000008344650269], "min": [0.9999995827674866, 0.9999996423721313, 0.999998927116394], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 808, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 4848, "componentType": 5126, "count": 101, "max": [399.22900390625, -0.3109740912914276, 0.033753398805856705], "min": [399.1907958984375, -0.3193970024585724, -0.04251528158783913], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 3232, "componentType": 5126, "count": 101, "max": [0.04481140151619911, 0.09621308743953703, -0.045223526656627655, -0.9873183369636536], "min": [-0.05383916571736336, -0.10072088986635208, -0.1102677807211876, -0.99887216091156], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 6060, "componentType": 5126, "count": 101, "max": [1.0000004768371584, 1.0000009536743164, 1.000001072883606], "min": [0.9999988675117492, 0.9999990463256836, 0.9999992847442628], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 1212, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 7272, "componentType": 5126, "count": 101, "max": [402.6142883300781, -0.1111449971795082, 0.005645751953125], "min": [402.5600891113281, -0.13861079514026645, -0.010498049668967724], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 4848, "componentType": 5126, "count": 101, "max": [0.02916480414569378, 0.13094337284564972, 0.3312646150588989, -0.9394612908363342], "min": [-0.029069917276501656, -0.1322847604751587, 0.12861666083335876, -0.9855769276618958], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 8484, "componentType": 5126, "count": 101, "max": [1.0000014305114748, 1.0000007152557373, 1.0000009536743164], "min": [0.9999995231628418, 0.999998927116394, 0.9999986886978148], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 1616, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 9696, "componentType": 5126, "count": 101, "max": [181.7324981689453, 0.001678466913290322, 0.0021362300030887127], "min": [181.7321929931641, 0.0012207030085846782, 0.0019226069562137127], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 6464, "componentType": 5126, "count": 101, "max": [0.11948085576295853, 0.21782369911670688, 0.75827956199646, -0.6160333156585693], "min": [-0.22238001227378848, -0.14779649674892426, 0.6617863774299622, -0.7142592668533325], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 10908, "componentType": 5126, "count": 101, "max": [1.0000007152557373, 1.0000011920928955, 1.0000008344650269], "min": [0.9999991059303284, 0.9999993443489076, 0.9999991655349731], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2020, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 12120, "componentType": 5126, "count": 101, "max": [-3.051759995287284e-05, 345.13958740234375, 6.103519990574569e-05], "min": [-3.051759995287284e-05, 345.13958740234375, 6.103519990574569e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 8080, "componentType": 5126, "count": 101, "max": [-1.3882177881896496e-05, -1.729086579871364e-05, -0.7070867419242859, -0.707126796245575], "min": [-1.3882177881896496e-05, -1.729086579871364e-05, -0.7070867419242859, -0.707126796245575], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 13332, "componentType": 5126, "count": 101, "max": [0.9999999403953552, 1.0, 1.0], "min": [0.9999999403953552, 1.0, 1.0], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2424, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 14544, "componentType": 5126, "count": 101, "max": [11.093509674072266, 2.570708990097046, 43.12179946899414], "min": [-11.20617961883545, -2.25006103515625, 41.65719985961914], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 9696, "componentType": 5126, "count": 101, "max": [-0.4268832206726074, 0.019551469013094906, -0.004996071103960276, -0.6169270277023315], "min": [-0.7079461216926575, -0.4286864697933197, -0.49198582768440247, -0.8074727654457092], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 15756, "componentType": 5126, "count": 101, "max": [1.0000009536743164, 1.0000016689300537, 1.0000014305114748], "min": [0.9999987483024596, 0.9999995231628418, 1.0000001192092896], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 2828, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 16968, "componentType": 5126, "count": 101, "max": [-0.0001220699996338226, 175.07080078125, -6.103519990574569e-05], "min": [-0.0001220699996338226, 175.07080078125, -6.103519990574569e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 11312, "componentType": 5126, "count": 101, "max": [0.5104442834854126, 0.7555050253868103, 0.02008049003779888, -0.6120374202728271], "min": [0.19047075510025024, 0.03135304525494576, -0.3887938261032105, -0.9229499697685242], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 18180, "componentType": 5126, "count": 101, "max": [1.0000011920928955, 1.0000003576278689, 1.000001311302185], "min": [0.9999988675117492, 0.9999983906745912, 0.9999996423721313], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 3232, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 19392, "componentType": 5126, "count": 101, "max": [0.0, 370.0173034667969, -1.525879997643642e-05], "min": [0.0, 370.0173034667969, -1.525879997643642e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 12928, "componentType": 5126, "count": 101, "max": [0.6011126041412354, 0.07422994077205658, 0.5760638117790222, -0.5518321394920349], "min": [0.3708511292934418, 0.04792150110006333, 0.35539764165878296, -0.8547816872596741], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 20604, "componentType": 5126, "count": 101, "max": [0.9999999403953552, 1.0000016689300537, 1.0000005960464478], "min": [0.9999990463256836, 0.9999995827674866, 0.9999995231628418], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 3636, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 21816, "componentType": 5126, "count": 101, "max": [-6.103519990574569e-05, 370.01702880859375, 3.814699994109105e-06], "min": [-6.103519990574569e-05, 370.01702880859375, 3.814699994109105e-06], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 14544, "componentType": 5126, "count": 101, "max": [0.12159590423107149, -0.592817485332489, 0.08611587435007095, -0.6480545401573181], "min": [-0.0019206339493393896, -0.6926255822181702, -0.40358710289001465, -0.7833981513977051], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 23028, "componentType": 5126, "count": 101, "max": [1.0000011920928955, 0.9999997615814208, 1.0000003576278689], "min": [0.9999993443489076, 0.9999983906745912, 0.9999982714653016], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 4040, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 24240, "componentType": 5126, "count": 101, "max": [3.051759995287284e-05, 186.6938018798828, 6.103519990574569e-05], "min": [3.051759995287284e-05, 186.6938018798828, 6.103519990574569e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 16160, "componentType": 5126, "count": 101, "max": [0.1426013559103012, 0.6280872225761414, 0.4596095085144043, -0.6589219570159912], "min": [0.035743311047554016, 0.5803613662719727, -0.23170951008796692, -0.7778217792510986], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 25452, "componentType": 5126, "count": 101, "max": [1.0000030994415283, 1.0000009536743164, 1.0000015497207642], "min": [0.9999990463256836, 0.999998152256012, 0.9999975562095642], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 4444, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 26664, "componentType": 5126, "count": 101, "max": [0.0, 167.7620086669922, 0.0], "min": [0.0, 167.7620086669922, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 17776, "componentType": 5126, "count": 101, "max": [-0.1871013194322586, -0.18708844482898715, -0.6819262504577637, -0.6818854212760925], "min": [-0.1871013194322586, -0.18708844482898715, -0.6819262504577637, -0.6818854212760925], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 27876, "componentType": 5126, "count": 101, "max": [0.9999998211860656, 1.0000001192092896, 1.0000001192092896], "min": [0.9999998211860656, 1.0000001192092896, 1.0000001192092896], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 4848, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 29088, "componentType": 5126, "count": 101, "max": [11.20654010772705, 2.539275884628296, -41.6572380065918], "min": [-11.0931396484375, -2.281677007675171, -43.12186050415039], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 19392, "componentType": 5126, "count": 101, "max": [0.7087063193321228, 0.41781315207481384, 0.0004008882970083505, -0.6171950101852417], "min": [0.4269197881221771, -0.018646057695150375, -0.4820735454559326, -0.8132827877998352], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 30300, "componentType": 5126, "count": 101, "max": [1.000001072883606, 1.0000005960464478, 1.0000015497207642], "min": [0.9999989867210388, 0.9999990463256836, 0.9999997615814208], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 5252, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 31512, "componentType": 5126, "count": 101, "max": [-0.0001220699996338226, 175.07159423828125, 0.0], "min": [-0.0001220699996338226, 175.07159423828125, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 21008, "componentType": 5126, "count": 101, "max": [-0.1727268248796463, -0.026490267366170883, -0.014631116762757301, -0.6249508857727051], "min": [-0.5201118588447571, -0.7482331991195679, -0.36269164085388184, -0.9203588366508484], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 32724, "componentType": 5126, "count": 101, "max": [1.0, 1.000001311302185, 1.0000009536743164], "min": [0.9999985098838806, 0.9999993443489076, 0.999998927116394], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 5656, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 33936, "componentType": 5126, "count": 101, "max": [0.0, 370.0174865722656, -6.103519990574569e-05], "min": [0.0, 370.0174865722656, -6.103519990574569e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 22624, "componentType": 5126, "count": 101, "max": [-0.3708538711071015, -0.04598140716552735, 0.5860890746116638, -0.5294864177703857], "min": [-0.6115797162055969, -0.07423046976327896, 0.3553973138332367, -0.8547807335853577], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 35148, "componentType": 5126, "count": 101, "max": [1.0000003576278689, 1.0000014305114748, 1.0000005960464478], "min": [0.9999993443489076, 0.999999701976776, 0.999999463558197], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 6060, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 36360, "componentType": 5126, "count": 101, "max": [2.2888199964654635e-05, 370.0171813964844, 1.525879997643642e-05], "min": [2.2888199964654635e-05, 370.0171813964844, 1.525879997643642e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 24240, "componentType": 5126, "count": 101, "max": [0.031680915504693985, 0.7147775292396545, 0.0212766882032156, -0.649734377861023], "min": [-0.10804452747106552, 0.5687218904495239, -0.41146859526634216, -0.7937191724777222], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 37572, "componentType": 5126, "count": 101, "max": [1.0000008344650269, 0.9999993443489076, 1.0000011920928955], "min": [0.999998927116394, 0.9999974966049194, 0.9999998211860656], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 6464, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 38784, "componentType": 5126, "count": 101, "max": [-3.051759995287284e-05, 186.69309997558597, 6.103519990574569e-05], "min": [-3.051759995287284e-05, 186.69309997558597, 6.103519990574569e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 25856, "componentType": 5126, "count": 101, "max": [-0.03588018566370011, -0.5803508758544922, 0.4588881731033325, -0.6592705845832825], "min": [-0.1426122784614563, -0.6280779242515564, -0.23170490562915805, -0.7778291702270508], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 39996, "componentType": 5126, "count": 101, "max": [1.0000035762786865, 1.000001311302185, 1.0000022649765017], "min": [0.9999989867210388, 0.9999991655349731, 0.9999974966049194], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 6868, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 41208, "componentType": 5126, "count": 101, "max": [0.0001220699996338226, 167.7602996826172, 0.0], "min": [0.0001220699996338226, 167.7602996826172, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 27472, "componentType": 5126, "count": 101, "max": [-0.18708005547523496, 0.18709906935691836, 0.681887686252594, -0.6819267868995667], "min": [-0.18708005547523496, 0.18709906935691836, 0.681887686252594, -0.6819267868995667], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 42420, "componentType": 5126, "count": 101, "max": [1.0, 0.9999998211860656, 0.9999998211860656], "min": [1.0, 0.9999998211860656, 0.9999998211860656], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 7272, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 43632, "componentType": 5126, "count": 101, "max": [-64.18634033203125, 123.89100646972656, -121.35540008544922], "min": [-122.68409729003906, 86.45635986328125, -181.9196929931641], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 29088, "componentType": 5126, "count": 101, "max": [0.5137400031089783, 0.5910477638244629, 0.487165242433548, -0.5562912821769714], "min": [0.2358280718326569, 0.4349341094493866, 0.06352268159389496, -0.7825927138328552], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 44844, "componentType": 5126, "count": 101, "max": [1.0000014305114748, 1.000001072883606, 1.0000015497207642], "min": [0.9999995827674866, 0.9999990463256836, 0.9999997615814208], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 7676, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 46056, "componentType": 5126, "count": 101, "max": [-1.525879997643642e-05, 547.8192138671875, 0.0], "min": [-1.525879997643642e-05, 547.8192138671875, 0.0], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 30704, "componentType": 5126, "count": 101, "max": [0.7711299657821655, -0.20974168181419373, -0.21020625531673431, 0.7666990756988525], "min": [0.5196717977523804, -0.31293177604675293, -0.31192100048065186, 0.5138788223266602], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 47268, "componentType": 5126, "count": 101, "max": [1.0, 1.0000008344650269, 1.0000003576278689], "min": [0.9999988079071044, 0.9999983906745912, 0.9999988079071044], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 8080, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 48480, "componentType": 5126, "count": 101, "max": [3.051759995287284e-05, 532.7470703125, 3.051759995287284e-05], "min": [3.051759995287284e-05, 532.7470703125, 3.051759995287284e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 32320, "componentType": 5126, "count": 101, "max": [0.7953221201896667, -0.3406173884868622, 0.024828489869832996, -0.4618827998638153], "min": [0.4422449469566345, -0.6381375789642334, -0.06888412684202194, -0.7050687074661255], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 49692, "componentType": 5126, "count": 101, "max": [1.000000238418579, 1.0000009536743164, 1.000000238418579], "min": [0.9999983906745912, 0.9999992847442628, 0.999998152256012], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 8484, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 50904, "componentType": 5126, "count": 101, "max": [-1.525879997643642e-05, 286.1808776855469, -1.525879997643642e-05], "min": [-1.525879997643642e-05, 286.1808776855469, -1.525879997643642e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 33936, "componentType": 5126, "count": 101, "max": [0.34342503547668457, -0.36089563369750977, 0.48122185468673706, -0.702274739742279], "min": [-0.055049605667591095, -0.4634150564670563, 0.027654040604829788, -0.9122520089149476], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 52116, "componentType": 5126, "count": 101, "max": [1.000001072883606, 1.0000007152557373, 1.0000005960464478], "min": [0.9999991655349731, 0.9999993443489076, 0.9999994039535524], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 8888, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 53328, "componentType": 5126, "count": 101, "max": [0.0, 166.96910095214844, 3.051759995287284e-05], "min": [0.0, 166.96910095214844, 3.051759995287284e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 35552, "componentType": 5126, "count": 101, "max": [0.7009878754615784, 0.7009936571121216, -0.0927930474281311, -0.09280822426080704], "min": [0.7009878754615784, 0.7009936571121216, -0.0927930474281311, -0.09280822426080704], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 54540, "componentType": 5126, "count": 101, "max": [1.0, 1.0, 0.9999997615814208], "min": [1.0, 1.0, 0.9999997615814208], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 9292, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 55752, "componentType": 5126, "count": 101, "max": [-57.822750091552734, 125.41189575195312, 183.63040161132812], "min": [-117.2886962890625, 87.447509765625, 124.98169708251952], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 37168, "componentType": 5126, "count": 101, "max": [-0.226774200797081, -0.4451811611652374, 0.49656349420547485, -0.551270604133606], "min": [-0.5029988884925842, -0.6104490756988525, 0.05997595936059952, -0.7728594541549683], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 56964, "componentType": 5126, "count": 101, "max": [1.0000011920928955, 1.0000030994415283, 1.0000004768371584], "min": [0.999997854232788, 1.0000004768371584, 0.9999985098838806], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 9696, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 58176, "componentType": 5126, "count": 101, "max": [5.722049900214188e-05, 547.8187866210938, -6.103519990574569e-05], "min": [5.722049900214188e-05, 547.8187866210938, -6.103519990574569e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 38784, "componentType": 5126, "count": 101, "max": [0.7720406651496887, -0.2091858983039856, 0.3122869431972504, -0.5125147700309753], "min": [0.5071884989738464, -0.31631752848625183, 0.2051556557416916, -0.7749928832054138], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 59388, "componentType": 5126, "count": 101, "max": [1.0000009536743164, 1.0000015497207642, 0.9999998211860656], "min": [0.9999998211860656, 0.9999995231628418, 0.999998152256012], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 10100, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 60600, "componentType": 5126, "count": 101, "max": [-3.051759995287284e-05, 532.7478637695312, 3.051759995287284e-05], "min": [-3.051759995287284e-05, 532.7478637695312, 3.051759995287284e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 40400, "componentType": 5126, "count": 101, "max": [-0.4422548413276673, 0.6402692198753357, 0.02487970888614655, -0.443508505821228], "min": [-0.8058043718338013, 0.33987849950790405, -0.06919090449810028, -0.7087214589118958], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 61812, "componentType": 5126, "count": 101, "max": [1.0000015497207642, 1.000002145767212, 1.0000017881393433], "min": [0.9999986886978148, 0.999999463558197, 0.9999991059303284], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 10504, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 63024, "componentType": 5126, "count": 101, "max": [-4.577639992930927e-05, 286.1813049316406, 3.051759995287284e-05], "min": [-4.577639992930927e-05, 286.1813049316406, 3.051759995287284e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 42016, "componentType": 5126, "count": 101, "max": [0.05504757910966873, 0.4637857675552368, 0.4812243282794953, -0.702268123626709], "min": [-0.34342628717422485, 0.34244170784950256, 0.027664778754115105, -0.9120306372642516], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 64236, "componentType": 5126, "count": 101, "max": [1.0000009536743164, 1.0000009536743164, 1.0000007152557373], "min": [0.9999988675117492, 0.9999990463256836, 0.9999987483024596], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 10908, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 65448, "componentType": 5126, "count": 101, "max": [6.103519990574569e-05, 166.9678955078125, -7.629389983776491e-06], "min": [6.103519990574569e-05, 166.9678955078125, -7.629389983776491e-06], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 43632, "componentType": 5126, "count": 101, "max": [0.7009931206703186, -0.7009892463684082, 0.09279564768075944, -0.09279833734035492], "min": [0.7009931206703186, -0.7009892463684082, 0.09279564768075944, -0.09279833734035492], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 66660, "componentType": 5126, "count": 101, "max": [0.9999998211860656, 0.9999998211860656, 0.9999999403953552], "min": [0.9999998211860656, 0.9999998211860656, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 11312, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 67872, "componentType": 5126, "count": 101, "max": [-216.10679626464844, 76.14641571044922, 53.84222030639649], "min": [-229.81809997558597, 48.92041015625, -46.19350051879883], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 45248, "componentType": 5126, "count": 101, "max": [0.1075008511543274, 0.04972844943404198, -0.5993947386741638, -0.7506681680679321], "min": [-0.1541447639465332, -0.08161687850952148, -0.6573381423950195, -0.7955794930458069], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 69084, "componentType": 5126, "count": 101, "max": [1.0000011920928955, 1.0000014305114748, 1.0000009536743164], "min": [0.999999701976776, 0.9999993443489076, 0.9999991655349731], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 11716, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 70296, "componentType": 5126, "count": 101, "max": [6.103519990574569e-05, 275.1336975097656, -1.525879997643642e-05], "min": [6.103519990574569e-05, 275.1336975097656, -1.525879997643642e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 46864, "componentType": 5126, "count": 101, "max": [0.039355143904685974, 0.004996879026293755, -0.12579287588596344, -0.991262674331665], "min": [-0.02954175136983395, -0.0037506259977817535, -0.12589138746261597, -0.9920437335968018], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 71508, "componentType": 5126, "count": 101, "max": [0.999998927116394, 1.0, 1.0000004768371584], "min": [0.9999986886978148, 0.9999982118606568, 0.999998927116394], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 12120, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 72720, "componentType": 5126, "count": 101, "max": [0.0, 339.0890197753906, -7.629389983776491e-06], "min": [0.0, 339.0890197753906, -7.629389983776491e-06], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 48480, "componentType": 5126, "count": 101, "max": [1.511155801381392e-06, 3.055855870570668e-07, -0.11349041759967804, -0.9935391545295716], "min": [1.511155801381392e-06, 3.055855870570668e-07, -0.11349041759967804, -0.9935391545295716], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 73932, "componentType": 5126, "count": 101, "max": [1.000000238418579, 1.0000003576278689, 1.0], "min": [1.000000238418579, 1.0000003576278689, 1.0], "type": "VEC3"}, {"bufferView": 4, "byteOffset": 12524, "componentType": 5126, "count": 101, "max": [3.333329916000366], "min": [0.0], "type": "SCALAR"}, {"bufferView": 5, "byteOffset": 75144, "componentType": 5126, "count": 101, "max": [0.0006713870097883046, 374.1894836425781, 9.918209980241954e-05], "min": [0.0006713870097883046, 374.1894836425781, 9.918209980241954e-05], "type": "VEC3"}, {"bufferView": 6, "byteOffset": 50096, "componentType": 5126, "count": 101, "max": [0.0003971835249103606, 0.9999987483024596, 8.442460170954291e-07, -1.539886227419629e-07], "min": [0.0003971835249103606, 0.9999987483024596, 8.442460170954291e-07, -1.539886227419629e-07], "type": "VEC4"}, {"bufferView": 5, "byteOffset": 76356, "componentType": 5126, "count": 101, "max": [0.9999921321868896, 0.9999988079071044, 0.9999999403953552], "min": [0.9999921321868896, 0.9999988079071044, 0.9999999403953552], "type": "VEC3"}, {"bufferView": 7, "byteOffset": 0, "componentType": 5126, "count": 32, "max": [35.948368072509766, 38.693748474121094, 39.37006759643555, 0.0, 38.92030715942383, 39.36031723022461, 30.0275993347168, 0.0, 39.36031723022461, 38.83974075317383, 38.96176910400391, 0.0, 1034.1197509765625, 867.171142578125, 611.7998657226562, 1.0], "min": [-35.94831848144531, -38.47134017944336, -39.37006759643555, 0.0, -38.920318603515625, -38.919979095458984, -30.0275993347168, 0.0, -39.3596076965332, -38.88496017456055, -38.82202911376953, 0.0, -1323.6492919921875, -1186.5914306640625, -632.5089111328125, 1.0], "type": "MAT4"}], "materials": [{"pbrMetallicRoughness": {"baseColorTexture": {"index": 0}, "metallicFactor": 0.0}, "emissiveFactor": [0.0, 0.0, 0.0], "name": "monster-effect"}], "textures": [{"sampler": 0, "source": 0}], "images": [{"uri": "Monster.jpg"}], "samplers": [{"magFilter": 9729, "minFilter": 9986, "wrapS": 10497, "wrapT": 10497}], "bufferViews": [{"buffer": 0, "byteOffset": 187936, "byteLength": 5304, "target": 34963}, {"buffer": 0, "byteOffset": 162528, "byteLength": 12480, "byteStride": 8, "target": 34962}, {"buffer": 0, "byteOffset": 143808, "byteLength": 18720, "byteStride": 12, "target": 34962}, {"buffer": 0, "byteOffset": 53760, "byteLength": 12480, "byteStride": 16, "target": 34962}, {"buffer": 0, "byteOffset": 175008, "byteLength": 12928}, {"buffer": 0, "byteOffset": 66240, "byteLength": 77568}, {"buffer": 0, "byteOffset": 2048, "byteLength": 51712}, {"buffer": 0, "byteOffset": 0, "byteLength": 2048}], "buffers": [{"byteLength": 193240, "uri": "Monster0.bin"}]}