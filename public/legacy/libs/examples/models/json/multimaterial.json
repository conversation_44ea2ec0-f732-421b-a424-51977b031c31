{"metadata": {"version": 4.5, "type": "Object", "generator": "Object3D.toJSON"}, "geometries": [{"uuid": "EFDA544D-E317-4EB2-8F10-577A709DC7AA", "type": "Geometry", "data": {"vertices": [0, 70, 0, -13.656322, 68.654968, 0, -13.39392, 68.654968, 2.664216, -12.616796, 68.654968, 5.226048, -11.354817, 68.654968, 7.587046, -9.656478, 68.654968, 9.656478, -7.587046, 68.654968, 11.354817, -5.226048, 68.654968, 12.616796, -2.664216, 68.654968, 13.39392, 0, 68.654968, 13.656322, 2.664216, 68.654968, 13.39392, 5.226048, 68.654968, 12.616796, 7.587046, 68.654968, 11.354817, 9.656478, 68.654968, 9.656478, 11.354817, 68.654968, 7.587046, 12.616796, 68.654968, 5.226048, 13.39392, 68.654968, 2.664216, 13.656322, 68.654968, 0, 13.39392, 68.654968, -2.664216, 12.616796, 68.654968, -5.226048, 11.354817, 68.654968, -7.587046, 9.656478, 68.654968, -9.656478, 7.587046, 68.654968, -11.354817, 5.226048, 68.654968, -12.616796, 2.664216, 68.654968, -13.39392, 0, 68.654968, -13.656322, -2.664216, 68.654968, -13.39392, -5.226048, 68.654968, -12.616796, -7.587046, 68.654968, -11.354817, -9.656478, 68.654968, -9.656478, -11.354817, 68.654968, -7.587046, -12.616796, 68.654968, -5.226048, -13.39392, 68.654968, -2.664216, -26.78784, 64.67157, 0, -26.273119, 64.67157, 5.226048, -24.748737, 64.67157, 10.251263, -22.273275, 64.67157, 14.882526, -18.941864, 64.67157, 18.941864, -14.882526, 64.67157, 22.273275, -10.251263, 64.67157, 24.748737, -5.226048, 64.67157, 26.273119, 0, 64.67157, 26.78784, 5.226048, 64.67157, 26.273119, 10.251263, 64.67157, 24.748737, 14.882526, 64.67157, 22.273275, 18.941864, 64.67157, 18.941864, 22.273275, 64.67157, 14.882526, 24.748737, 64.67157, 10.251263, 26.273119, 64.67157, 5.226048, 26.78784, 64.67157, 0, 26.273119, 64.67157, -5.226048, 24.748737, 64.67157, -10.251263, 22.273275, 64.67157, -14.882526, 18.941864, 64.67157, -18.941864, 14.882526, 64.67157, -22.273275, 10.251263, 64.67157, -24.748737, 5.226048, 64.67157, -26.273119, 0, 64.67157, -26.78784, -5.226048, 64.67157, -26.273119, -10.251263, 64.67157, -24.748737, -14.882526, 64.67157, -22.273275, -18.941864, 64.67157, -18.941864, -22.273275, 64.67157, -14.882526, -24.748737, 64.67157, -10.251263, -26.273119, 64.67157, -5.226048, -38.889915, 58.202873, 0, -38.142658, 58.202873, 7.587046, -35.929596, 58.202873, 14.882526, -32.335785, 58.202873, 21.606079, -27.499323, 58.202873, 27.499323, -21.606079, 58.202873, 32.335785, -14.882526, 58.202873, 35.929596, -7.587046, 58.202873, 38.142658, 0, 58.202873, 38.889915, 7.587046, 58.202873, 38.142658, 14.882526, 58.202873, 35.929596, 21.606079, 58.202873, 32.335785, 27.499323, 58.202873, 27.499323, 32.335785, 58.202873, 21.606079, 35.929596, 58.202873, 14.882526, 38.142658, 58.202873, 7.587046, 38.889915, 58.202873, 0, 38.142658, 58.202873, -7.587046, 35.929596, 58.202873, -14.882526, 32.335785, 58.202873, -21.606079, 27.499323, 58.202873, -27.499323, 21.606079, 58.202873, -32.335785, 14.882526, 58.202873, -35.929596, 7.587046, 58.202873, -38.142658, 0, 58.202873, -38.889915, -7.587046, 58.202873, -38.142658, -14.882526, 58.202873, -35.929596, -21.606079, 58.202873, -32.335785, -27.499323, 58.202873, -27.499323, -32.335785, 58.202873, -21.606079, -35.929596, 58.202873, -14.882526, -38.142658, 58.202873, -7.587046, -49.497475, 49.497475, 0, -48.546394, 49.497475, 9.656478, -45.729702, 49.497475, 18.941864, -41.155647, 49.497475, 27.499323, -35, 49.497475, 35, -27.499323, 49.497475, 41.155647, -18.941864, 49.497475, 45.729702, -9.656478, 49.497475, 48.546394, 0, 49.497475, 49.497475, 9.656478, 49.497475, 48.546394, 18.941864, 49.497475, 45.729702, 27.499323, 49.497475, 41.155647, 35, 49.497475, 35, 41.155647, 49.497475, 27.499323, 45.729702, 49.497475, 18.941864, 48.546394, 49.497475, 9.656478, 49.497475, 49.497475, 0, 48.546394, 49.497475, -9.656478, 45.729702, 49.497475, -18.941864, 41.155647, 49.497475, -27.499323, 35, 49.497475, -35, 27.499323, 49.497475, -41.155647, 18.941864, 49.497475, -45.729702, 9.656478, 49.497475, -48.546394, 0, 49.497475, -49.497475, -9.656478, 49.497475, -48.546394, -18.941864, 49.497475, -45.729702, -27.499323, 49.497475, -41.155647, -35, 49.497475, -35, -41.155647, 49.497475, -27.499323, -45.729702, 49.497475, -18.941864, -48.546394, 49.497475, -9.656478, -58.202873, 38.889915, 0, -57.084522, 38.889915, 11.354817, -53.772442, 38.889915, 22.273275, -48.393921, 38.889915, 32.335785, -41.155647, 38.889915, 41.155647, -32.335785, 38.889915, 48.393921, -22.273275, 38.889915, 53.772442, -11.354817, 38.889915, 57.084522, 0, 38.889915, 58.202873, 11.354817, 38.889915, 57.084522, 22.273275, 38.889915, 53.772442, 32.335785, 38.889915, 48.393921, 41.155647, 38.889915, 41.155647, 48.393921, 38.889915, 32.335785, 53.772442, 38.889915, 22.273275, 57.084522, 38.889915, 11.354817, 58.202873, 38.889915, 0, 57.084522, 38.889915, -11.354817, 53.772442, 38.889915, -22.273275, 48.393921, 38.889915, -32.335785, 41.155647, 38.889915, -41.155647, 32.335785, 38.889915, -48.393921, 22.273275, 38.889915, -53.772442, 11.354817, 38.889915, -57.084522, 0, 38.889915, -58.202873, -11.354817, 38.889915, -57.084522, -22.273275, 38.889915, -53.772442, -32.335785, 38.889915, -48.393921, -41.155647, 38.889915, -41.155647, -48.393921, 38.889915, -32.335785, -53.772442, 38.889915, -22.273275, -57.084522, 38.889915, -11.354817, -64.67157, 26.78784, 0, -63.428921, 26.78784, 12.616796, -59.748737, 26.78784, 24.748737, -53.772442, 26.78784, 35.929596, -45.729702, 26.78784, 45.729702, -35.929596, 26.78784, 53.772442, -24.748737, 26.78784, 59.748737, -12.616796, 26.78784, 63.428921, 0, 26.78784, 64.67157, 12.616796, 26.78784, 63.428921, 24.748737, 26.78784, 59.748737, 35.929596, 26.78784, 53.772442, 45.729702, 26.78784, 45.729702, 53.772442, 26.78784, 35.929596, 59.748737, 26.78784, 24.748737, 63.428921, 26.78784, 12.616796, 64.67157, 26.78784, 0, 63.428921, 26.78784, -12.616796, 59.748737, 26.78784, -24.748737, 53.772442, 26.78784, -35.929596, 45.729702, 26.78784, -45.729702, 35.929596, 26.78784, -53.772442, 24.748737, 26.78784, -59.748737, 12.616796, 26.78784, -63.428921, 0, 26.78784, -64.67157, -12.616796, 26.78784, -63.428921, -24.748737, 26.78784, -59.748737, -35.929596, 26.78784, -53.772442, -45.729702, 26.78784, -45.729702, -53.772442, 26.78784, -35.929596, -59.748737, 26.78784, -24.748737, -63.428921, 26.78784, -12.616796, -68.654968, 13.656322, 0, -67.335785, 13.656322, 13.39392, -63.428921, 13.656322, 26.273119, -57.084522, 13.656322, 38.142658, -48.546394, 13.656322, 48.546394, -38.142658, 13.656322, 57.084522, -26.273119, 13.656322, 63.428921, -13.39392, 13.656322, 67.335785, 0, 13.656322, 68.654968, 13.39392, 13.656322, 67.335785, 26.273119, 13.656322, 63.428921, 38.142658, 13.656322, 57.084522, 48.546394, 13.656322, 48.546394, 57.084522, 13.656322, 38.142658, 63.428921, 13.656322, 26.273119, 67.335785, 13.656322, 13.39392, 68.654968, 13.656322, 0, 67.335785, 13.656322, -13.39392, 63.428921, 13.656322, -26.273119, 57.084522, 13.656322, -38.142658, 48.546394, 13.656322, -48.546394, 38.142658, 13.656322, -57.084522, 26.273119, 13.656322, -63.428921, 13.39392, 13.656322, -67.335785, 0, 13.656322, -68.654968, -13.39392, 13.656322, -67.335785, -26.273119, 13.656322, -63.428921, -38.142658, 13.656322, -57.084522, -48.546394, 13.656322, -48.546394, -57.084522, 13.656322, -38.142658, -63.428921, 13.656322, -26.273119, -67.335785, 13.656322, -13.39392, -70, 0, 0, -68.654968, 0, 13.656322, -64.67157, 0, 26.78784, -58.202873, 0, 38.889915, -49.497475, 0, 49.497475, -38.889915, 0, 58.202873, -26.78784, 0, 64.67157, -13.656322, 0, 68.654968, 0, 0, 70, 13.656322, 0, 68.654968, 26.78784, 0, 64.67157, 38.889915, 0, 58.202873, 49.497475, 0, 49.497475, 58.202873, 0, 38.889915, 64.67157, 0, 26.78784, 68.654968, 0, 13.656322, 70, 0, 0, 68.654968, 0, -13.656322, 64.67157, 0, -26.78784, 58.202873, 0, -38.889915, 49.497475, 0, -49.497475, 38.889915, 0, -58.202873, 26.78784, 0, -64.67157, 13.656322, 0, -68.654968, 0, 0, -70, -13.656322, 0, -68.654968, -26.78784, 0, -64.67157, -38.889915, 0, -58.202873, -49.497475, 0, -49.497475, -58.202873, 0, -38.889915, -64.67157, 0, -26.78784, -68.654968, 0, -13.656322, -68.654968, -13.656322, 0, -67.335785, -13.656322, 13.39392, -63.428921, -13.656322, 26.273119, -57.084522, -13.656322, 38.142658, -48.546394, -13.656322, 48.546394, -38.142658, -13.656322, 57.084522, -26.273119, -13.656322, 63.428921, -13.39392, -13.656322, 67.335785, 0, -13.656322, 68.654968, 13.39392, -13.656322, 67.335785, 26.273119, -13.656322, 63.428921, 38.142658, -13.656322, 57.084522, 48.546394, -13.656322, 48.546394, 57.084522, -13.656322, 38.142658, 63.428921, -13.656322, 26.273119, 67.335785, -13.656322, 13.39392, 68.654968, -13.656322, 0, 67.335785, -13.656322, -13.39392, 63.428921, -13.656322, -26.273119, 57.084522, -13.656322, -38.142658, 48.546394, -13.656322, -48.546394, 38.142658, -13.656322, -57.084522, 26.273119, -13.656322, -63.428921, 13.39392, -13.656322, -67.335785, 0, -13.656322, -68.654968, -13.39392, -13.656322, -67.335785, -26.273119, -13.656322, -63.428921, -38.142658, -13.656322, -57.084522, -48.546394, -13.656322, -48.546394, -57.084522, -13.656322, -38.142658, -63.428921, -13.656322, -26.273119, -67.335785, -13.656322, -13.39392, -64.67157, -26.78784, 0, -63.428921, -26.78784, 12.616796, -59.748737, -26.78784, 24.748737, -53.772442, -26.78784, 35.929596, -45.729702, -26.78784, 45.729702, -35.929596, -26.78784, 53.772442, -24.748737, -26.78784, 59.748737, -12.616796, -26.78784, 63.428921, 0, -26.78784, 64.67157, 12.616796, -26.78784, 63.428921, 24.748737, -26.78784, 59.748737, 35.929596, -26.78784, 53.772442, 45.729702, -26.78784, 45.729702, 53.772442, -26.78784, 35.929596, 59.748737, -26.78784, 24.748737, 63.428921, -26.78784, 12.616796, 64.67157, -26.78784, 0, 63.428921, -26.78784, -12.616796, 59.748737, -26.78784, -24.748737, 53.772442, -26.78784, -35.929596, 45.729702, -26.78784, -45.729702, 35.929596, -26.78784, -53.772442, 24.748737, -26.78784, -59.748737, 12.616796, -26.78784, -63.428921, 0, -26.78784, -64.67157, -12.616796, -26.78784, -63.428921, -24.748737, -26.78784, -59.748737, -35.929596, -26.78784, -53.772442, -45.729702, -26.78784, -45.729702, -53.772442, -26.78784, -35.929596, -59.748737, -26.78784, -24.748737, -63.428921, -26.78784, -12.616796, -58.202873, -38.889915, 0, -57.084522, -38.889915, 11.354817, -53.772442, -38.889915, 22.273275, -48.393921, -38.889915, 32.335785, -41.155647, -38.889915, 41.155647, -32.335785, -38.889915, 48.393921, -22.273275, -38.889915, 53.772442, -11.354817, -38.889915, 57.084522, 0, -38.889915, 58.202873, 11.354817, -38.889915, 57.084522, 22.273275, -38.889915, 53.772442, 32.335785, -38.889915, 48.393921, 41.155647, -38.889915, 41.155647, 48.393921, -38.889915, 32.335785, 53.772442, -38.889915, 22.273275, 57.084522, -38.889915, 11.354817, 58.202873, -38.889915, 0, 57.084522, -38.889915, -11.354817, 53.772442, -38.889915, -22.273275, 48.393921, -38.889915, -32.335785, 41.155647, -38.889915, -41.155647, 32.335785, -38.889915, -48.393921, 22.273275, -38.889915, -53.772442, 11.354817, -38.889915, -57.084522, 0, -38.889915, -58.202873, -11.354817, -38.889915, -57.084522, -22.273275, -38.889915, -53.772442, -32.335785, -38.889915, -48.393921, -41.155647, -38.889915, -41.155647, -48.393921, -38.889915, -32.335785, -53.772442, -38.889915, -22.273275, -57.084522, -38.889915, -11.354817, -49.497475, -49.497475, 0, -48.546394, -49.497475, 9.656478, -45.729702, -49.497475, 18.941864, -41.155647, -49.497475, 27.499323, -35, -49.497475, 35, -27.499323, -49.497475, 41.155647, -18.941864, -49.497475, 45.729702, -9.656478, -49.497475, 48.546394, 0, -49.497475, 49.497475, 9.656478, -49.497475, 48.546394, 18.941864, -49.497475, 45.729702, 27.499323, -49.497475, 41.155647, 35, -49.497475, 35, 41.155647, -49.497475, 27.499323, 45.729702, -49.497475, 18.941864, 48.546394, -49.497475, 9.656478, 49.497475, -49.497475, 0, 48.546394, -49.497475, -9.656478, 45.729702, -49.497475, -18.941864, 41.155647, -49.497475, -27.499323, 35, -49.497475, -35, 27.499323, -49.497475, -41.155647, 18.941864, -49.497475, -45.729702, 9.656478, -49.497475, -48.546394, 0, -49.497475, -49.497475, -9.656478, -49.497475, -48.546394, -18.941864, -49.497475, -45.729702, -27.499323, -49.497475, -41.155647, -35, -49.497475, -35, -41.155647, -49.497475, -27.499323, -45.729702, -49.497475, -18.941864, -48.546394, -49.497475, -9.656478, -38.889915, -58.202873, 0, -38.142658, -58.202873, 7.587046, -35.929596, -58.202873, 14.882526, -32.335785, -58.202873, 21.606079, -27.499323, -58.202873, 27.499323, -21.606079, -58.202873, 32.335785, -14.882526, -58.202873, 35.929596, -7.587046, -58.202873, 38.142658, 0, -58.202873, 38.889915, 7.587046, -58.202873, 38.142658, 14.882526, -58.202873, 35.929596, 21.606079, -58.202873, 32.335785, 27.499323, -58.202873, 27.499323, 32.335785, -58.202873, 21.606079, 35.929596, -58.202873, 14.882526, 38.142658, -58.202873, 7.587046, 38.889915, -58.202873, 0, 38.142658, -58.202873, -7.587046, 35.929596, -58.202873, -14.882526, 32.335785, -58.202873, -21.606079, 27.499323, -58.202873, -27.499323, 21.606079, -58.202873, -32.335785, 14.882526, -58.202873, -35.929596, 7.587046, -58.202873, -38.142658, 0, -58.202873, -38.889915, -7.587046, -58.202873, -38.142658, -14.882526, -58.202873, -35.929596, -21.606079, -58.202873, -32.335785, -27.499323, -58.202873, -27.499323, -32.335785, -58.202873, -21.606079, -35.929596, -58.202873, -14.882526, -38.142658, -58.202873, -7.587046, -26.78784, -64.67157, 0, -26.273119, -64.67157, 5.226048, -24.748737, -64.67157, 10.251263, -22.273275, -64.67157, 14.882526, -18.941864, -64.67157, 18.941864, -14.882526, -64.67157, 22.273275, -10.251263, -64.67157, 24.748737, -5.226048, -64.67157, 26.273119, 0, -64.67157, 26.78784, 5.226048, -64.67157, 26.273119, 10.251263, -64.67157, 24.748737, 14.882526, -64.67157, 22.273275, 18.941864, -64.67157, 18.941864, 22.273275, -64.67157, 14.882526, 24.748737, -64.67157, 10.251263, 26.273119, -64.67157, 5.226048, 26.78784, -64.67157, 0, 26.273119, -64.67157, -5.226048, 24.748737, -64.67157, -10.251263, 22.273275, -64.67157, -14.882526, 18.941864, -64.67157, -18.941864, 14.882526, -64.67157, -22.273275, 10.251263, -64.67157, -24.748737, 5.226048, -64.67157, -26.273119, 0, -64.67157, -26.78784, -5.226048, -64.67157, -26.273119, -10.251263, -64.67157, -24.748737, -14.882526, -64.67157, -22.273275, -18.941864, -64.67157, -18.941864, -22.273275, -64.67157, -14.882526, -24.748737, -64.67157, -10.251263, -26.273119, -64.67157, -5.226048, -13.656322, -68.654968, 0, -13.39392, -68.654968, 2.664216, -12.616796, -68.654968, 5.226048, -11.354817, -68.654968, 7.587046, -9.656478, -68.654968, 9.656478, -7.587046, -68.654968, 11.354817, -5.226048, -68.654968, 12.616796, -2.664216, -68.654968, 13.39392, 0, -68.654968, 13.656322, 2.664216, -68.654968, 13.39392, 5.226048, -68.654968, 12.616796, 7.587046, -68.654968, 11.354817, 9.656478, -68.654968, 9.656478, 11.354817, -68.654968, 7.587046, 12.616796, -68.654968, 5.226048, 13.39392, -68.654968, 2.664216, 13.656322, -68.654968, 0, 13.39392, -68.654968, -2.664216, 12.616796, -68.654968, -5.226048, 11.354817, -68.654968, -7.587046, 9.656478, -68.654968, -9.656478, 7.587046, -68.654968, -11.354817, 5.226048, -68.654968, -12.616796, 2.664216, -68.654968, -13.39392, 0, -68.654968, -13.656322, -2.664216, -68.654968, -13.39392, -5.226048, -68.654968, -12.616796, -7.587046, -68.654968, -11.354817, -9.656478, -68.654968, -9.656478, -11.354817, -68.654968, -7.587046, -12.616796, -68.654968, -5.226048, -13.39392, -68.654968, -2.664216, 0, -70, 0], "normals": [-0.084638, 0.95655, -0.279015, -0.074658, 0.980785, -0.18024, -0.03806, 0.980785, -0.191342, -0.146447, 0.92388, -0.353553, -0.098013, -0.995138, -0.009653, -0.19509, -0.980785, 0, -0.191342, -0.980785, -0.03806, 0, -1, 0, 0.094246, -0.995138, 0.028589, 0.191342, -0.980785, 0.03806, 0.18024, -0.980785, 0.074658, 0, -1, 0, -0.098013, -0.995138, 0.009653, -0.191342, -0.980785, 0.03806, -0.19509, -0.980785, 0, 0, -1, 0, -0.009653, 0.995138, 0.098013, 0, 1, 0, -0.03806, 0.980785, 0.191342, 0, 0.980785, 0.19509, -0.290166, -0.95655, -0.028579, -0.37533, -0.92388, -0.074658, -0.279015, -0.95655, -0.084638, -0.353553, -0.92388, -0.146447, 0.225386, -0.95655, 0.18497, 0.270598, -0.92388, 0.270598, 0.13795, -0.980785, 0.13795, 0.162212, -0.980785, 0.108386, 0.084638, -0.95655, 0.279015, 0.074658, -0.92388, 0.37533, 0.03806, -0.980785, 0.191342, 0.074658, -0.980785, 0.18024, -0.290166, -0.95655, 0.028579, -0.37533, -0.92388, 0.074658, -0.382683, -0.92388, 0, -0.365764, -0.880972, -0.300175, -0.46194, -0.83147, -0.308658, -0.392847, -0.83147, -0.392847, -0.31819, -0.92388, -0.212608, 0.137354, -0.880972, 0.452794, 0.212608, -0.83147, 0.51328, 0.108386, -0.83147, 0.544895, 0.146447, -0.92388, 0.353553, -0.028589, 0.995138, -0.094246, -0.300175, -0.880972, 0.365764, -0.392847, -0.83147, 0.392847, -0.270598, -0.92388, 0.270598, -0.212608, -0.92388, 0.31819, -0.417297, -0.880972, 0.22305, -0.51328, -0.83147, 0.212608, -0.353553, -0.92388, 0.146447, -0.31819, -0.92388, 0.212608, -0.608827, -0.771506, -0.184686, -0.653282, -0.707107, -0.270598, -0.51328, -0.83147, -0.212608, -0.544895, -0.83147, -0.108386, 0.062361, -0.771506, -0.633159, 0, -0.707107, -0.707107, 0.13795, -0.707107, -0.69352, 0, -0.83147, -0.55557, -0.290166, 0.95655, 0.028579, -0.19509, 0.980785, 0, -0.382683, 0.92388, 0, -0.37533, 0.92388, 0.074658, 0.184686, -0.771506, 0.608827, 0.270598, -0.707107, 0.653282, 0.13795, -0.707107, 0.69352, -0.299913, -0.771506, 0.561098, -0.392847, -0.707107, 0.587938, -0.308658, -0.83147, 0.46194, -0.212608, -0.83147, 0.51328, -0.77078, -0.632563, -0.075915, -0.815493, -0.55557, -0.162212, -0.69352, -0.707107, -0.13795, -0.707107, -0.707107, 0, -0.137445, 0.95655, 0.257142, -0.074658, 0.980785, 0.18024, -0.108386, 0.980785, 0.162212, -0.146447, 0.92388, 0.353553, 0.075915, -0.632563, 0.77078, 0.162212, -0.55557, 0.815493, 0, -0.55557, 0.83147, -0.224828, -0.632563, 0.741159, -0.31819, -0.55557, 0.768178, -0.270598, -0.707107, 0.653282, -0.13795, -0.707107, 0.69352, 0.028579, 0.95655, 0.290166, 0, 0.92388, 0.382683, 0.074658, 0.92388, 0.37533, 0.084638, 0.95655, 0.279015, 0.074658, 0.980785, 0.18024, 0.03806, 0.980785, 0.191342, 0.146447, 0.92388, 0.353553, -0.365101, -0.632563, 0.683056, -0.46194, -0.55557, 0.691342, 0.878613, -0.469629, -0.086536, 0.92388, -0.382683, 0, 0.83147, -0.55557, 0, 0.815493, -0.55557, -0.162212, 0.279015, 0.95655, 0.084638, 0.18024, 0.980785, 0.074658, 0.353553, 0.92388, 0.146447, 0.37533, 0.92388, 0.074658, -0.778617, -0.469628, 0.416179, -0.853553, -0.382683, 0.353553, -0.768178, -0.55557, 0.31819, -0.691342, -0.55557, 0.46194, -0.740024, -0.289004, -0.607322, -0.69352, -0.19509, -0.69352, -0.653282, -0.382683, -0.653282, -0.768178, -0.382683, -0.51328, -0.607322, -0.289004, -0.740024, -0.544895, -0.19509, -0.815493, -0.51328, -0.382683, -0.768178, 0.18497, 0.95655, -0.225386, 0.108386, 0.980785, -0.162212, 0.13795, 0.980785, -0.13795, 0.212608, 0.92388, -0.31819, -0.916106, -0.289004, 0.277898, -0.906127, -0.19509, 0.37533, -0.96194, -0.19509, 0.191342, 0.877715, -0.09755, -0.469148, 0.92388, 0, -0.382683, 0.906127, -0.19509, -0.37533, 0.815493, -0.19509, -0.544895, 0.990438, -0.09755, -0.097549, 1, 0, 0, 0.980785, -0.19509, 0, 0.96194, -0.19509, -0.191342, 0.877715, -0.09755, 0.469149, 0.92388, 0, 0.382683, 0.83147, 0, 0.55557, 0.906127, -0.19509, 0.37533, 0.09755, -0.097549, 0.990438, 0.19509, 0, 0.980785, 0, 0, 1, 0.191342, -0.19509, 0.96194, -0.062479, -0.995138, -0.076131, -0.13795, -0.980785, -0.13795, -0.108386, -0.980785, -0.162212, 0, -1, 0, -0.631368, -0.09755, 0.769324, -0.55557, 0, 0.83147, -0.707107, 0, 0.707107, -0.544895, -0.19509, 0.815493, -0.137445, 0.95655, -0.257142, -0.108386, 0.980785, -0.162212, -0.212608, 0.92388, -0.31819, -0.990438, -0.09755, 0.097549, -1, 0, 0, -0.980785, -0.19509, 0, -0.769324, 0.09755, -0.631368, -0.815493, 0.19509, -0.544895, -0.69352, 0.19509, -0.69352, -0.83147, 0, -0.55557, 0.877715, 0.09755, -0.469148, 0.815493, 0.19509, -0.544895, 0.906127, 0.19509, -0.37533, 0.83147, 0, -0.55557, -0.257142, 0.95655, -0.137445, -0.18024, 0.980785, -0.074658, -0.162212, 0.980785, -0.108386, -0.353553, 0.92388, -0.146447, -0.097549, 0.09755, 0.990438, 0, 0.19509, 0.980785, -0.191342, 0.19509, 0.96194, -0.469148, 0.09755, 0.877715, -0.37533, 0.19509, 0.906127, -0.544895, 0.19509, 0.815493, -0.382683, 0, 0.92388, -0.769324, 0.09755, 0.631368, -0.69352, 0.19509, 0.69352, -0.815493, 0.19509, 0.544895, -0.607322, 0.289004, -0.740024, -0.51328, 0.382683, -0.768178, -0.544895, 0.19509, -0.815493, -0.452794, 0.880972, 0.137353, -0.353553, 0.92388, 0.146447, -0.51328, 0.83147, 0.212608, 0.277898, 0.289004, -0.916106, 0.353553, 0.382683, -0.853553, 0.37533, 0.19509, -0.906127, 0.191342, 0.19509, -0.96194, 0.277898, 0.289004, -0.916106, 0.18024, 0.382683, -0.906127, -0.365764, 0.880972, 0.300175, -0.270598, 0.92388, 0.270598, -0.31819, 0.92388, 0.212608, -0.392847, 0.83147, 0.392847, 0.740024, 0.289004, 0.607322, 0.653282, 0.382683, 0.653282, 0.69352, 0.19509, 0.69352, 0.815493, 0.19509, 0.544895, -0.277898, 0.289004, 0.916106, -0.353553, 0.382683, 0.853553, -0.22305, 0.880972, 0.417297, -0.212608, 0.92388, 0.31819, -0.212608, 0.83147, 0.51328, -0.952718, 0.289004, 0.093834, -0.92388, 0.382683, 0, -0.980785, 0.19509, 0, -0.96194, 0.19509, 0.191342, 0.086536, 0.469628, -0.878613, 0.162212, 0.55557, -0.815493, 0, 0.382683, -0.92388, 0.416179, 0.469628, -0.778617, 0.31819, 0.55557, -0.768178, 0.46194, 0.55557, -0.691342, 0.046379, 0.880972, 0.47089, 0.108386, 0.83147, 0.544895, 0.682463, 0.469628, 0.560083, 0.587938, 0.55557, 0.587938, 0.768178, 0.382683, 0.51328, 0.682463, 0.469628, 0.560083, 0.691342, 0.55557, 0.46194, 0.137354, 0.880972, 0.452794, 0.212608, 0.83147, 0.51328, -0.416179, 0.469628, 0.778617, -0.31819, 0.55557, 0.768178, -0.46194, 0.55557, 0.691342, 0.300175, 0.880972, 0.365764, 0.270598, 0.92388, 0.270598, 0.212608, 0.92388, 0.31819, 0.392847, 0.83147, 0.392847, -0.844848, 0.469628, 0.256282, -0.768178, 0.55557, 0.31819, -0.815493, 0.55557, 0.162212, -0.853553, 0.382683, 0.353553, -0.365101, 0.632563, -0.683056, -0.270598, 0.707107, -0.653282, -0.31819, 0.55557, -0.768178, -0.46194, 0.55557, -0.691342, -0.075915, 0.632563, -0.77078, -0.13795, 0.707107, -0.69352, 0, 0.707107, -0.707107, -0.162212, 0.55557, -0.815493, 0.491343, 0.632563, -0.598704, 0.392847, 0.707107, -0.587938, 0.5, 0.707107, -0.5, 0.365101, 0.632563, 0.683056, 0.270598, 0.707107, 0.653282, 0.31819, 0.55557, 0.768178, 0.46194, 0.55557, 0.691342, -0.299913, 0.771506, -0.561098, -0.212608, 0.83147, -0.51328, -0.392847, 0.707107, -0.587938, 0.365764, 0.880972, -0.300175, 0.31819, 0.92388, -0.212608, 0.46194, 0.83147, -0.308658, 0.392847, 0.83147, -0.392847, -0.299913, 0.771506, -0.561098, -0.308658, 0.83147, -0.46194, 0.299913, 0.771506, 0.561098, 0.308658, 0.83147, 0.46194, 0.392847, 0.707107, 0.587938, -0.633159, 0.771506, 0.062361, -0.55557, 0.83147, 0, -0.707107, 0.707107, 0, -0.69352, 0.707107, 0.13795, -0.452794, 0.880972, -0.137354, -0.51328, 0.83147, -0.212608, -0.544895, 0.83147, -0.108386, -0.22305, 0.880972, -0.417297, 0.561098, -0.771506, -0.299913, 0.653282, -0.707107, -0.270598, 0.51328, -0.83147, -0.212608, 0.46194, -0.83147, -0.308658, 0.952376, -0.09755, -0.2889, 0.980785, 0, -0.19509, 0.028589, 0.995138, 0.094246, -0.990438, -0.097549, 0.09755, -0.980785, 0, 0.19509, -0.608827, -0.771506, 0.184686, -0.653282, -0.707107, 0.270598, -0.69352, -0.707107, 0.13795, -0.225386, 0.95655, 0.18497, -0.13795, 0.980785, 0.13795, -0.162212, 0.980785, 0.108386, 0.417297, -0.880972, -0.22305, 0.353553, -0.92388, -0.146447, 0.31819, -0.92388, -0.212608, -0.741159, -0.632563, -0.224828, -0.768178, -0.55557, -0.31819, -0.598704, -0.632563, -0.491343, -0.587938, -0.55557, -0.587938, -0.5, -0.707107, -0.5, -0.587938, -0.707107, -0.392847, 0.452794, -0.880972, -0.137354, 0.544895, -0.83147, -0.108386, 0.683056, -0.632563, 0.365101, 0.691342, -0.55557, 0.46194, 0.587938, -0.707107, 0.392847, 0.653282, -0.707107, 0.270598, 0.952376, 0.09755, -0.2889, 0.96194, 0.19509, -0.191342, 0.469148, 0.09755, 0.877715, 0.544895, 0.19509, 0.815493, 0.37533, 0.19509, 0.906127, 0.55557, 0, 0.83147, 0.491343, -0.632563, 0.598704, 0.587938, -0.55557, 0.587938, 0.46194, -0.55557, 0.691342, 0.5, -0.707107, 0.5, 0.097549, 0.097549, 0.990438, 0.191342, 0.19509, 0.96194, -0.084638, 0.95655, 0.279015, -0.074658, 0.92388, 0.37533, -0.469149, 0.09755, 0.877715, 0.137353, -0.880972, 0.452794, -0.075915, -0.632563, 0.77078, -0.162212, -0.55557, 0.815493, 0, -0.707107, 0.707107, -0.952718, 0.289004, -0.093834, -0.906127, 0.382683, -0.18024, -0.96194, 0.19509, -0.191342, -0.980785, 0.19509, 0, 0.137445, -0.95655, -0.257142, 0.212608, -0.92388, -0.31819, 0.108386, -0.980785, -0.162212, 0.074658, -0.980785, -0.18024, -0.046378, -0.880972, 0.47089, 0, -0.83147, 0.55557, -0.108386, -0.83147, 0.544895, 0, -0.92388, 0.382683, -0.093835, 0.289004, -0.952718, -0.18024, 0.382683, -0.906127, -0.191342, 0.19509, -0.96194, 0.137445, -0.95655, -0.257142, 0.146447, -0.92388, -0.353553, 0.084638, 0.95655, 0.279015, -0.365101, -0.632563, 0.683056, 0.290166, -0.95655, 0.028579, 0.37533, -0.92388, 0.074658, 0.19509, -0.980785, 0, -0.093834, 0.289004, 0.952718, -0.18024, 0.382683, 0.906127, -0.491343, -0.632563, 0.598704, -0.587938, -0.55557, 0.587938, -0.086536, -0.469629, -0.878613, 0, -0.382683, -0.92388, 0, -0.55557, -0.83147, -0.162212, -0.55557, -0.815493, 0.086536, -0.469628, -0.878613, 0.18024, -0.382683, -0.906127, 0.162212, -0.55557, -0.815493, 0.257142, -0.95655, 0.137445, 0.31819, -0.92388, 0.212608, 0.256282, 0.469628, -0.844848, 0.878613, -0.469628, 0.086536, 0.906127, -0.382683, 0.18024, 0.815493, -0.55557, 0.162212, 0.682463, 0.469628, -0.560083, 0.587938, 0.55557, -0.587938, 0.691342, 0.55557, -0.46194, 0.653282, 0.382683, -0.653282, 0.844848, -0.469628, 0.256282, 0.853553, -0.382683, 0.353553, 0.768178, -0.55557, 0.31819, 0.086536, -0.469628, 0.878613, 0.18024, -0.382683, 0.906127, 0, -0.382683, 0.92388, 0.076131, -0.995138, -0.062479, 0.13795, -0.980785, -0.13795, 0.162212, -0.980785, -0.108386, 0, -1, 0, -0.682463, -0.469628, 0.560083, -0.653282, -0.382683, 0.653282, -0.768178, -0.382683, 0.51328, 0.290166, 0.95655, 0.028579, 0.191342, 0.980785, 0.03806, 0.382683, 0.92388, 0, -0.682463, 0.469628, 0.560083, -0.587938, 0.55557, 0.587938, -0.691342, 0.55557, 0.46194, -0.653282, 0.382683, 0.653282, -0.608827, -0.771506, -0.184686, -0.844848, -0.469628, 0.256282, -0.906127, -0.382683, 0.18024, -0.815493, -0.55557, 0.162212, -0.741159, 0.632563, -0.224828, -0.69352, 0.707107, -0.13795, -0.653282, 0.707107, -0.270598, -0.815493, 0.55557, -0.162212, -0.844288, -0.289004, -0.451281, -0.815493, -0.19509, -0.544895, -0.853553, -0.382683, -0.353553, -0.075915, 0.632563, -0.77078, 0, 0.55557, -0.83147, 0.062361, -0.771506, -0.633159, 0.108386, -0.83147, -0.544895, 0.365101, 0.632563, -0.683056, -0.086858, -0.995138, -0.046426, -0.18024, -0.980785, -0.074658, -0.162212, -0.980785, -0.108386, 0, -1, 0, 0.598704, 0.632563, -0.491343, 0.587938, 0.707107, -0.392847, 0.741159, 0.632563, 0.224828, 0.653282, 0.707107, 0.270598, 0.768178, 0.55557, 0.31819, 0.815493, 0.55557, 0.162212, 0.598704, 0.632563, 0.491343, 0.587938, 0.707107, 0.392847, 0.5, 0.707107, 0.5, 0.952718, -0.289004, -0.093834, 0.906127, -0.382683, -0.18024, -0.075915, 0.632563, 0.77078, -0.13795, 0.707107, 0.69352, -0.162212, 0.55557, 0.815493, 0, 0.55557, 0.83147, -0.075915, 0.632563, 0.77078, 0, 0.707107, 0.707107, -0.224828, 0.632563, 0.741159, -0.270598, 0.707107, 0.653282, -0.403615, 0.771506, -0.491806, -0.5, 0.707107, -0.5, 0.093834, -0.289004, 0.952718, 0, -0.19509, 0.980785, -0.740024, -0.289004, 0.607322, -0.815493, -0.19509, 0.544895, 0.184686, -0.771506, -0.608827, 0.270598, -0.707107, -0.653282, 0.403615, 0.771506, -0.491806, 0.308658, 0.83147, -0.46194, -0.844288, -0.289004, 0.451281, -0.062361, 0.771506, 0.633159, -0.108386, 0.83147, 0.544895, -0.608827, 0.771506, 0.184686, -0.544895, 0.83147, 0.108386, -0.653282, 0.707107, 0.270598, -0.18497, -0.95655, 0.225386, -0.13795, -0.980785, 0.13795, -0.108386, -0.980785, 0.162212, -0.877715, -0.09755, -0.469148, -0.906127, -0.19509, -0.37533, -0.365764, 0.880972, -0.300175, -0.31819, 0.92388, -0.212608, -0.270598, 0.92388, -0.270598, -0.46194, 0.83147, -0.308658, -0.631368, -0.09755, -0.769324, -0.55557, 0, -0.83147, -0.878613, 0.469629, 0.086536, -0.83147, 0.55557, 0, -0.906127, 0.382683, 0.18024, 0.877715, 0.09755, -0.469149, -0.47089, 0.880972, -0.046378, -0.37533, 0.92388, -0.074658, -0.55557, 0.83147, 0, -0.062479, -0.995138, 0.076131, 0, -1, 0, -0.279015, -0.95655, -0.084638, -0.257142, -0.95655, -0.137445, -0.491806, 0.771506, 0.403615, -0.46194, 0.83147, 0.308658, -0.587938, 0.707107, 0.392847, -0.5, 0.707107, 0.5, 0.279015, -0.95655, -0.084638, 0.37533, -0.92388, -0.074658, 0.191342, -0.980785, -0.03806, 0.18024, -0.980785, -0.074658, 0.184686, 0.771506, 0.608827, 0.13795, 0.707107, 0.69352, 0.046379, -0.880972, -0.47089, 0.074658, -0.92388, -0.37533, 0, -0.92388, -0.382683, 0.300175, 0.880972, -0.365764, 0.270598, 0.92388, -0.270598, 0.300175, -0.880972, -0.365764, 0.308658, -0.83147, -0.46194, 0.392847, -0.83147, -0.392847, 0.365764, -0.880972, -0.300175, 0.270598, -0.92388, -0.270598, 0.22305, -0.880972, 0.417297, 0.212608, -0.92388, 0.31819, -0.561098, 0.771506, -0.299913, -0.587938, 0.707107, -0.392847, -0.77078, 0.632563, 0.075915, -0.491806, -0.771506, -0.403615, -0.098013, 0.995138, -0.009653, -0.191342, 0.980785, -0.03806, -0.19509, 0.980785, 0, 0.633159, -0.771506, -0.062361, 0.707107, -0.707107, 0, 0.55557, -0.83147, 0, 0.683056, 0.632563, 0.365101, 0.683056, 0.632563, 0.365101, -0.633159, -0.771506, 0.062361, -0.707107, -0.707107, 0, -0.544895, -0.83147, 0.108386, -0.18497, 0.95655, 0.225386, -0.683056, -0.632563, -0.365101, -0.691342, -0.55557, -0.46194, 0.598704, -0.632563, -0.491343, 0.691342, -0.55557, -0.46194, 0.587938, -0.707107, -0.392847, 0.5, -0.707107, -0.5, 0.741159, -0.632563, -0.224828, 0.768178, -0.55557, -0.31819, -0.224828, 0.632563, -0.741159, 0.365764, 0.880972, 0.300175, 0.46194, 0.83147, 0.308658, 0.491343, -0.632563, 0.598704, 0.392847, -0.707107, 0.587938, 0.365101, -0.632563, 0.683056, 0.31819, -0.55557, 0.768178, 0.224828, -0.632563, 0.741159, -0.046426, -0.995138, -0.086858, -0.074658, -0.980785, -0.18024, 0, -1, 0, -0.491343, -0.632563, 0.598704, -0.5, -0.707107, 0.5, -0.22305, 0.880972, -0.417297, 0.778617, 0.469628, 0.416179, 0.853553, 0.382683, 0.353553, 0.225387, 0.95655, 0.18497, 0.13795, 0.980785, 0.13795, 0.31819, 0.92388, 0.212608, 0.560083, 0.469628, -0.682463, 0.51328, 0.382683, -0.768178, 0.256282, -0.469628, 0.844848, 0.353553, -0.382683, 0.853553, -0.256282, 0.469628, -0.844848, -0.353553, 0.382683, -0.853553, -0.844848, 0.469628, -0.256282, -0.768178, 0.55557, -0.31819, -0.853553, 0.382683, -0.353553, 0.844288, -0.289004, -0.451281, 0.853553, -0.382683, -0.353553, 0.768178, -0.382683, -0.51328, 0.844288, -0.289004, -0.451281, -0.277898, 0.289004, 0.916106, -0.277898, -0.289004, 0.916106, -0.37533, -0.19509, 0.906127, -0.353553, -0.382683, 0.853553, -0.18024, -0.382683, 0.906127, -0.277898, -0.289004, 0.916106, -0.191342, -0.19509, 0.96194, -0.469148, -0.09755, -0.877715, -0.382683, 0, -0.92388, -0.37533, -0.19509, -0.906127, -0.2889, -0.09755, -0.952376, -0.19509, 0, -0.980785, 0.097549, -0.097549, -0.990438, 0.19509, 0, -0.980785, 0.191342, -0.19509, -0.96194, 0, -0.19509, -0.980785, 0.769324, -0.09755, -0.631368, 0.707107, 0, -0.707107, 0.69352, -0.19509, -0.69352, 0.093835, 0.289004, -0.952718, 0, 0.19509, -0.980785, 0.028579, 0.95655, -0.290166, 0.03806, 0.980785, -0.191342, 0.074658, 0.92388, -0.37533, 0, 0.92388, -0.382683, -0.277898, 0.289004, -0.916106, -0.37533, 0.19509, -0.906127, -0.451281, 0.289004, -0.844288, -0.028579, 0.95655, -0.290166, 0, 0.980785, -0.19509, -0.074658, 0.92388, -0.37533, -0.47089, 0.880972, 0.046379, -0.990438, 0.097549, 0.097549, -0.952376, 0.097549, -0.2889, -0.906127, 0.19509, -0.37533, -0.980785, 0, -0.19509, -0.769324, 0.09755, -0.631368, -0.707107, 0, -0.707107, -0.18497, 0.95655, -0.225386, -0.13795, 0.980785, -0.13795, -0.631368, 0.09755, -0.769324, 0.2889, 0.097549, -0.952376, -0.598704, -0.632563, 0.491343, -0.587938, -0.707107, 0.392847, -0.224828, 0.632563, -0.741159, -0.299913, 0.771506, 0.561098, -0.308658, 0.83147, 0.46194, -0.392847, 0.707107, 0.587938, 0.47089, -0.880972, 0.046378, 0.544895, -0.83147, 0.108386, 0.382683, -0.92388, 0, -0.491343, 0.632563, -0.598704, -0.587938, 0.55557, -0.587938, 0.77078, -0.632563, 0.075915, 0.69352, -0.707107, 0.13795, 0.300175, -0.880972, 0.365764, 0.392847, -0.83147, 0.392847, 0.308658, -0.83147, 0.46194, 0.137353, 0.880972, -0.452794, 0.146447, 0.92388, -0.353553, 0.108386, 0.83147, -0.544895, 0.365101, -0.632563, 0.683056, -0.046379, -0.880972, 0.47089, -0.074658, -0.92388, 0.37533, 0.257142, -0.95655, 0.137445, 0.353553, -0.92388, 0.146447, -0.417297, -0.880972, 0.22305, -0.46194, -0.83147, 0.308658, 0.18497, 0.95655, 0.225386, 0.108386, 0.980785, 0.162212, -0.028579, 0.95655, 0.290166, -0.256282, 0.469628, 0.844848, -0.086536, 0.469628, 0.878613, 0, 0.382683, 0.92388, -0.741159, -0.632563, 0.224828, 0.086536, 0.469629, 0.878613, 0.18024, 0.382683, 0.906127, -0.682463, -0.469628, -0.560083, -0.560083, -0.469628, -0.682463, -0.46194, -0.55557, -0.691342, -0.491806, -0.771506, -0.403615, 0.225386, 0.95655, 0.18497, 0.162212, 0.980785, 0.108386, 0.878613, 0.469629, 0.086536, 0.83147, 0.55557, 0, 0.92388, 0.382683, 0, 0.256282, -0.469628, -0.844848, 0.353553, -0.382683, -0.853553, 0.31819, -0.55557, -0.768178, 0.416179, -0.469628, -0.778617, 0.51328, -0.382683, -0.768178, 0.46194, -0.55557, -0.691342, -0.028579, -0.95655, 0.290166, 0, -0.980785, 0.19509, 0.778617, 0.469628, -0.416179, 0.768178, 0.55557, -0.31819, 0.768178, 0.382683, -0.51328, 0.682463, 0.469628, -0.560083, -0.598704, 0.632563, 0.491343, 0.682463, -0.469628, 0.560083, 0.768178, -0.382683, 0.51328, 0.653282, -0.382683, 0.653282, -0.137354, 0.880972, 0.452794, 0.086536, 0.469629, -0.878613, -0.084638, -0.95655, 0.279015, -0.146447, -0.92388, 0.353553, -0.074658, -0.980785, 0.18024, -0.03806, -0.980785, 0.191342, -0.086536, 0.469629, -0.878613, 0.184686, -0.771506, -0.608827, 0.212608, -0.83147, -0.51328, 0.290166, 0.95655, -0.028579, 0.191342, 0.980785, -0.03806, 0.19509, 0.980785, 0, 0.37533, 0.92388, -0.074658, -0.682463, 0.469628, -0.560083, -0.653282, 0.382683, -0.653282, -0.768178, 0.382683, -0.51328, 0.417297, 0.880972, -0.22305, 0.353553, 0.92388, -0.146447, -0.740024, -0.289004, -0.607322, -0.878613, 0.469628, -0.086536, -0.92388, 0.382683, 0, -0.952718, 0.289004, 0.093835, 0.607322, -0.289004, -0.740024, 0.544895, -0.19509, -0.815493, 0.452794, 0.880972, -0.137354, 0.544895, 0.83147, -0.108386, 0.51328, 0.83147, -0.212608, 0.403615, -0.771506, -0.491806, 0.392847, -0.707107, -0.587938, 0.952718, -0.289004, -0.093835, -0.451281, 0.289004, 0.844288, -0.51328, 0.382683, 0.768178, 0.365101, 0.632563, 0.683056, 0.916106, -0.289004, 0.277898, 0.96194, -0.19509, 0.191342, 0.740024, -0.289004, 0.607322, 0.815493, -0.19509, 0.544895, 0.69352, -0.19509, 0.69352, 0.277898, 0.289004, 0.916106, 0.561098, -0.771506, -0.299913, 0.451281, 0.289004, 0.844288, 0.353553, 0.382683, 0.853553, -0.452794, -0.880972, -0.137354, -0.451281, -0.289004, 0.844288, -0.952718, -0.289004, 0.093835, -0.92388, -0.382683, 0, -0.952718, -0.289004, 0.093834, 0.137445, 0.95655, -0.257142, 0.952718, 0.289004, 0.093834, 0.906127, 0.382683, 0.18024, 0.96194, 0.19509, 0.191342, 0.980785, 0.19509, 0, 0.062479, -0.995138, -0.076131, 0, -1, 0, 0.916106, 0.289004, -0.277898, 0.906127, 0.382683, -0.18024, 0.046378, -0.880972, -0.47089, 0.491806, -0.771506, 0.403615, 0.46194, -0.83147, 0.308658, 0.631368, -0.09755, -0.769324, 0.55557, 0, -0.83147, -0.417297, 0.880972, 0.22305, 0.47089, 0.880972, 0.046378, 0.544895, 0.83147, 0.108386, 0.55557, 0.83147, 0, 0.491806, -0.771506, 0.403615, 0.77078, 0.632563, -0.075915, 0.707107, 0.707107, 0, 0.815493, 0.55557, -0.162212, 0.741159, 0.632563, -0.224828, 0.653282, 0.707107, -0.270598, 0.69352, 0.707107, -0.13795, 0.608827, 0.771506, -0.184686, -0.451281, 0.289004, -0.844288, -0.225387, 0.95655, 0.18497, -0.77078, -0.632563, -0.075915, -0.83147, -0.55557, 0, 0.137354, -0.880972, -0.452794, -0.469148, -0.09755, 0.877715, -0.469149, -0.09755, 0.877715, -0.769324, -0.09755, 0.631368, -0.83147, 0, 0.55557, -0.69352, -0.19509, 0.69352, -0.877715, -0.09755, 0.469148, -0.92388, 0, 0.382683, -0.952376, 0.09755, -0.2889, -0.92388, 0, -0.382683, -0.225386, -0.95655, -0.18497, -0.270598, -0.92388, -0.270598, -0.491343, -0.632563, -0.598704, -0.224828, -0.632563, -0.741159, -0.13795, -0.707107, -0.69352, -0.270598, -0.707107, -0.653282, 0.075915, -0.632563, -0.77078, -0.469149, 0.09755, -0.877715, 0.2889, 0.097549, 0.952376, 0.382683, 0, 0.92388, 0.403615, 0.771506, -0.491806, -0.086858, -0.995138, 0.046426, -0.162212, -0.980785, 0.108386, -0.18024, -0.980785, 0.074658, 0, -1, 0, 0.952376, 0.097549, -0.2889, -0.086536, -0.469629, 0.878613, 0.403615, 0.771506, 0.491806, -0.416179, -0.469628, 0.778617, -0.51328, -0.382683, 0.768178, 0.046379, -0.880972, 0.47089, -0.560083, 0.469628, -0.682463, 0.417297, 0.880972, -0.22305, -0.878613, -0.469629, 0.086536, -0.83147, -0.55557, 0, -0.137445, -0.95655, 0.257142, -0.137445, -0.95655, 0.257142, -0.046426, 0.995138, -0.086858, -0.18497, -0.95655, 0.225387, -0.607322, -0.289004, -0.740024, -0.451281, -0.289004, -0.844288, 0.451281, -0.289004, -0.844288, 0.451281, -0.289004, -0.844288, 0.37533, -0.19509, -0.906127, -0.844288, 0.289004, 0.451281, -0.906127, 0.19509, 0.37533, -0.452794, -0.880972, 0.137353, -0.77078, 0.632563, 0.075915, -0.365101, 0.632563, -0.683056, -0.290166, -0.95655, 0.028579, -0.561098, -0.771506, -0.299913, -0.47089, -0.880972, -0.046379, -0.382683, -0.92388, 0, -0.300175, 0.880972, 0.365764, 0.916106, -0.289004, 0.277898, 0.608827, 0.771506, 0.184686, 0.51328, 0.83147, 0.212608, 0.69352, 0.707107, 0.13795, -0.076131, -0.995138, -0.062479, 0, -1, 0, -0.417297, -0.880972, -0.22305, -0.491343, 0.632563, 0.598704, 0.075915, -0.632563, 0.77078, -0.300175, -0.880972, -0.365764, -0.308658, -0.83147, -0.46194, -0.075915, -0.632563, 0.77078, 0.18497, 0.95655, -0.225387, -0.094246, -0.995138, -0.028589, 0, -1, 0, -0.224828, -0.632563, 0.741159, -0.18497, -0.95655, -0.225387, -0.212608, -0.92388, -0.31819, -0.046379, 0.880972, -0.47089, -0.108386, 0.83147, -0.544895, -0.560083, 0.469628, 0.682463, 0.952718, 0.289004, -0.093834, -0.290166, 0.95655, 0.028579, -0.191342, 0.980785, 0.03806, -0.683056, -0.632563, 0.365101, -0.097549, -0.09755, -0.990438, 0, 0, -1, -0.191342, -0.19509, -0.96194, -0.225386, 0.95655, -0.18497, 0.2889, -0.09755, -0.952376, 0.382683, 0, -0.92388, 0.2889, -0.097549, -0.952376, 0.224828, 0.632563, 0.741159, 0.162212, 0.55557, 0.815493, 0.290166, -0.95655, -0.028579, 0.279015, -0.95655, 0.084638, -0.778617, -0.469628, -0.416179, 0.877715, -0.09755, -0.469149, 0.560083, 0.469628, 0.682463, 0.51328, 0.382683, 0.768178, 0.608827, -0.771506, -0.184686, 0.69352, -0.707107, -0.13795, 0.990438, -0.097549, 0.097549, 0.980785, 0, 0.19509, 0.009653, 0.995138, 0.098013, 0.608827, -0.771506, 0.184686, 0.51328, -0.83147, 0.212608, 0.952376, -0.09755, 0.2889, 0.028579, -0.95655, 0.290166, 0.086858, -0.995138, 0.046426, 0, -1, 0, 0.062479, 0.995138, -0.076131, 0.417297, -0.880972, -0.22305, 0.878613, 0.469629, -0.086536, 0.77078, 0.632563, 0.075915, 0.47089, -0.880972, -0.046378, 0.778617, -0.469628, 0.416179, -0.009653, -0.995138, 0.098013, 0, -1, 0, -0.877715, 0.09755, -0.469149, 0.417297, -0.880972, 0.22305, -0.403615, -0.771506, 0.491806, -0.631368, 0.09755, -0.769324, -0.046379, 0.880972, 0.47089, 0, 0.83147, 0.55557, -0.2889, 0.09755, 0.952376, -0.19509, 0, 0.980785, 0.560083, -0.469628, 0.682463, 0.51328, -0.382683, 0.768178, -0.2889, 0.09755, -0.952376, 0.09755, 0.09755, 0.990438, -0.491806, -0.771506, 0.403615, 0.09755, 0.097549, -0.990438, 0.2889, 0.09755, -0.952376, 0.631368, 0.09755, 0.769324, 0.707107, 0, 0.707107, -0.062361, 0.771506, -0.633159, 0, 0.83147, -0.55557, 0.769324, 0.09755, -0.631368, 0.69352, 0.19509, -0.69352, -0.184686, 0.771506, -0.608827, 0.952376, 0.097549, 0.2889, 0.906127, 0.19509, 0.37533, -0.086858, 0.995138, 0.046426, -0.18024, 0.980785, 0.074658, -0.607322, -0.289004, 0.740024, -0.084638, 0.95655, 0.279015, -0.365764, 0.880972, 0.300175, 0.028589, -0.995138, -0.094246, 0.03806, -0.980785, -0.191342, 0, -1, 0, 0.257142, 0.95655, 0.137445, -0.491343, 0.632563, 0.598704, 0.365764, 0.880972, 0.300175, -0.769324, -0.09755, -0.631368, -0.062361, -0.771506, 0.633159, -0.076131, 0.995138, 0.062479, -0.77078, 0.632563, -0.075915, -0.83147, 0.55557, 0, 0.916106, 0.289004, -0.277898, 0.853553, 0.382683, -0.353553, -0.028579, 0.95655, 0.290166, 0.256282, 0.469628, -0.844848, -0.365101, 0.632563, 0.683056, -0.257142, 0.95655, 0.137445, 0.09755, -0.09755, -0.990438, 0.416179, -0.469628, 0.778617, 0.046378, -0.880972, 0.47089, 0.451281, 0.289004, -0.844288, -0.778617, 0.469628, 0.416179, -0.768178, 0.382683, 0.51328, 0.028579, 0.95655, 0.290166, 0.683056, 0.632563, -0.365101, -0.416179, -0.469628, 0.778617, -0.224828, 0.632563, 0.741159, 0.952376, -0.097549, -0.2889, -0.257142, 0.95655, 0.137445, -0.098013, 0.995138, 0.009653, -0.778617, -0.469628, 0.416179, -0.009653, 0.995138, -0.098013, -0.778617, 0.469628, -0.416179, -0.691342, 0.55557, -0.46194, -0.137445, -0.95655, -0.257142, -0.146447, -0.92388, -0.353553, 0.769324, -0.09755, 0.631368, 0.2889, -0.097549, 0.952376, 0.37533, -0.19509, 0.906127, 0.2889, -0.09755, 0.952376, 0.097549, -0.09755, 0.990438, 0.279015, 0.95655, -0.084638, 0.18024, 0.980785, -0.074658, -0.09755, -0.09755, 0.990438, -0.916106, 0.289004, -0.277898, -0.2889, -0.09755, 0.952376, 0.075915, 0.632563, 0.77078, -0.256282, 0.469628, 0.844848, -0.741159, -0.632563, 0.224828, 0.137445, -0.95655, 0.257142, 0.108386, -0.980785, 0.162212, 0.491806, -0.771506, -0.403615, 0.098013, 0.995138, -0.009653, -0.844848, -0.469628, -0.256282, -0.906127, -0.382683, -0.18024, -0.084638, -0.95655, -0.279015, -0.074658, -0.92388, -0.37533, -0.03806, -0.980785, -0.191342, -0.778617, -0.469628, -0.416179, -0.631368, 0.09755, 0.769324, 0.416179, 0.469628, 0.778617, 0.740024, -0.289004, -0.607322, 0.653282, -0.382683, -0.653282, -0.682463, -0.469628, -0.560083, -0.633159, 0.771506, -0.062361, -0.707107, 0.707107, 0, 0.137353, 0.880972, 0.452794, 0.062479, -0.995138, 0.076131, 0, -1, 0, -0.2889, 0.097549, -0.952376, 0.491343, 0.632563, 0.598704, 0.561098, 0.771506, -0.299913, 0.2889, 0.09755, 0.952376, -0.093835, 0.289004, 0.952718, 0.844848, 0.469628, 0.256282, 0.300175, -0.880972, -0.365764, 0.631368, 0.09755, 0.769324, -0.561098, -0.771506, -0.299913, 0.607322, -0.289004, 0.740024, 0.544895, -0.19509, 0.815493, 0.469149, 0.09755, -0.877715, 0.544895, 0.19509, -0.815493, 0.491806, 0.771506, -0.403615, 0.608827, 0.771506, 0.184686, 0.22305, -0.880972, 0.417297, 0.607322, 0.289004, 0.740024, 0.682463, -0.469628, -0.560083, 0.587938, -0.55557, -0.587938, 0.299913, 0.771506, -0.561098, 0.270598, 0.707107, -0.653282, 0.452794, -0.880972, -0.137353, -0.18497, 0.95655, -0.225387, -0.028589, -0.995138, -0.094246, 0, -1, 0, 0.631368, 0.09755, -0.769324, 0.631368, 0.09755, -0.769324, 0.769324, 0.09755, 0.631368, -0.417297, 0.880972, -0.22305, 0.086858, -0.995138, -0.046426, 0, -1, 0, -0.09755, 0.09755, -0.990438, 0.094246, -0.995138, -0.028589, 0, -1, 0, 0.076131, -0.995138, 0.062479, 0, -1, 0, -0.290166, 0.95655, -0.028579, -0.382683, 0.92388, 0, 0.009653, -0.995138, 0.098013, 0, -1, 0, -0.046426, -0.995138, 0.086858, 0, -1, 0, -0.290166, 0.95655, -0.028579, -0.877715, 0.09755, 0.469149, -0.094246, -0.995138, 0.028589, 0, -1, 0, 0.046426, 0.995138, 0.086858, -0.769324, -0.09755, 0.631368, -0.631368, -0.09755, 0.769324, -0.097549, -0.097549, 0.990438, -0.299913, 0.771506, 0.561098, -0.740024, 0.289004, -0.607322, 0.631368, -0.09755, 0.769324, 0.084638, -0.95655, -0.279015, 0.740024, 0.289004, -0.607322, 0.22305, 0.880972, -0.417297, 0.212608, 0.83147, -0.51328, 0.844288, 0.289004, -0.451281, -0.631368, -0.09755, -0.769324, 0.22305, 0.880972, -0.417297, -0.257142, -0.95655, 0.137445, -0.279015, -0.95655, 0.084638, 0.086858, 0.995138, -0.046426, 0.162212, 0.980785, -0.108386, -0.452794, -0.880972, -0.137353, 0.633159, 0.771506, 0.062361, -0.22305, -0.880972, -0.417297, -0.212608, -0.83147, -0.51328, 0.740024, 0.289004, 0.607322, 0.225386, 0.95655, -0.18497, 0.952376, 0.09755, 0.2889, 0.990438, 0.09755, -0.09755, -0.300175, 0.880972, 0.365764, 0.365764, -0.880972, 0.300175, 0.257142, 0.95655, -0.137445, 0.028589, 0.995138, -0.094246, 0.074658, 0.980785, -0.18024, 0.009653, 0.995138, -0.098013, 0.740024, -0.289004, -0.607322, -0.47089, -0.880972, 0.046379, -0.55557, -0.83147, 0, -0.451281, -0.289004, -0.844288, -0.353553, -0.382683, -0.853553, -0.633159, -0.771506, -0.062361, -0.55557, -0.83147, 0, -0.952718, -0.289004, -0.093835, -0.96194, -0.19509, -0.191342, -0.92388, -0.382683, 0, -0.878613, -0.469628, 0.086536, 0.290166, 0.95655, -0.028579, -0.184686, -0.771506, -0.608827, 0.452794, 0.880972, -0.137353, 0.491806, -0.771506, -0.403615, -0.279015, 0.95655, 0.084638, 0.256282, -0.469628, 0.844848, 0.633159, -0.771506, -0.062361, 0.561098, -0.771506, 0.299913, 0.741159, 0.632563, 0.224828, 0.844848, -0.469628, 0.256282, 0.778617, 0.469628, -0.416179, 0.778617, -0.469628, -0.416179, 0.741159, 0.632563, -0.224828, -0.561098, -0.771506, 0.299913, 0.416179, -0.469628, -0.778617, -0.365101, -0.632563, -0.683056, -0.31819, -0.55557, -0.768178, -0.392847, -0.707107, -0.587938, 0.075915, 0.632563, -0.77078, 0.13795, 0.707107, -0.69352, -0.416179, -0.469628, -0.778617, 0.778617, 0.469628, 0.416179, 0.046378, 0.880972, 0.47089, 0.683056, -0.632563, -0.365101, -0.77078, -0.632563, 0.075915, -0.77078, -0.632563, 0.075915, 0.77078, -0.632563, 0.075915, -0.137445, 0.95655, 0.257142, 0.598704, -0.632563, 0.491343, 0.028579, -0.95655, -0.290166, 0, -0.980785, -0.19509, 0.277898, -0.289004, 0.916106, 0.451281, -0.289004, 0.844288, -0.076131, -0.995138, 0.062479, 0, -1, 0, 0.47089, -0.880972, -0.046379, 0.062361, 0.771506, -0.633159, 0.452794, -0.880972, 0.137353, 0.417297, -0.880972, 0.22305, 0.631368, -0.09755, 0.769324, -0.046426, 0.995138, 0.086858, 0.769324, -0.09755, 0.631368, 0.300175, -0.880972, 0.365764, 0.916106, -0.289004, -0.277898, 0.062479, 0.995138, 0.076131, 0.076131, 0.995138, 0.062479, 0.990438, -0.09755, 0.09755, -0.740024, 0.289004, 0.607322, -0.137353, -0.880972, 0.452794, 0.277898, -0.289004, -0.916106, -0.062479, 0.995138, -0.076131, 0.257142, -0.95655, -0.137445, -0.277898, -0.289004, -0.916106, -0.18024, -0.382683, -0.906127, -0.47089, -0.880972, 0.046378, -0.452794, 0.880972, 0.137354, 0.093834, 0.289004, -0.952718, -0.878613, 0.469629, -0.086536, 0.279015, 0.95655, -0.084638, -0.916106, -0.289004, -0.277898, -0.417297, 0.880972, 0.22305, 0.290166, -0.95655, 0.028579, 0.607322, 0.289004, -0.740024, -0.299913, -0.771506, -0.561098, -0.046378, 0.880972, -0.47089, -0.952376, -0.09755, 0.2889, -0.256282, -0.469628, 0.844848, 0.299913, -0.771506, -0.561098, 0.403615, -0.771506, -0.491806, -0.09755, -0.097549, -0.990438, 0.225387, -0.95655, 0.18497, 0.086536, -0.469629, 0.878613, -0.877715, 0.09755, 0.469148, 0.608827, -0.771506, -0.184686, -0.469149, -0.09755, -0.877715, 0.560083, -0.469628, 0.682463, 0.560083, 0.469628, -0.682463, 0.137445, -0.95655, 0.257142, -0.491806, 0.771506, 0.403615, 0.062361, 0.771506, 0.633159, 0.952718, 0.289004, 0.093835, 0.77078, 0.632563, -0.075915, -0.184686, -0.771506, 0.608827, 0.084638, -0.95655, 0.279015, 0.769324, 0.09755, 0.631368, -0.299913, -0.771506, 0.561098, 0.491806, 0.771506, 0.403615, -0.561098, -0.771506, 0.299913, -0.403615, 0.771506, 0.491806, -0.608827, -0.771506, 0.184686, 0.452794, 0.880972, 0.137354, 0.365101, 0.632563, -0.683056, -0.741159, -0.632563, -0.224828, -0.086536, -0.469628, -0.878613, 0.224828, 0.632563, -0.741159, -0.137354, 0.880972, -0.452794, -0.952718, 0.289004, -0.093835, -0.257142, 0.95655, -0.137445, -0.009653, -0.995138, -0.098013, 0, -1, 0, -0.740024, -0.289004, 0.607322, 0.225387, 0.95655, -0.18497, 0.77078, -0.632563, -0.075915, -0.300175, -0.880972, -0.365764, -0.844288, 0.289004, -0.451281, -0.469148, 0.09755, -0.877715, 0.741159, -0.632563, 0.224828, -0.683056, -0.632563, 0.365101, -0.137354, -0.880972, -0.452794, -0.108386, -0.83147, -0.544895, -0.084638, -0.95655, -0.279015, 0.598704, -0.632563, 0.491343, -0.598704, 0.632563, -0.491343, -0.682463, 0.469628, 0.560083, -0.683056, 0.632563, -0.365101, -0.683056, 0.632563, -0.365101, -0.741159, 0.632563, -0.224828, -0.778617, 0.469628, 0.416179, -0.878613, 0.469628, 0.086536, -0.683056, 0.632563, 0.365101, 0.279015, -0.95655, 0.084638, -0.403615, -0.771506, -0.491806, -0.560083, 0.469628, -0.682463, -0.046379, -0.880972, -0.47089, -0.682463, -0.469628, 0.560083, 0.451281, 0.289004, 0.844288, -0.062361, -0.771506, -0.633159, -0.365101, 0.632563, 0.683056, 0.633159, 0.771506, -0.062361, -0.093835, -0.289004, 0.952718, -0.740024, 0.289004, -0.607322, 0.22305, -0.880972, -0.417297, 0.607322, 0.289004, -0.740024, 0.469148, 0.09755, -0.877715, 0.184686, 0.771506, -0.608827, 0.47089, 0.880972, -0.046378, 0.990438, 0.097549, 0.09755, -0.300175, 0.880972, -0.365764, -0.392847, 0.83147, -0.392847, 0.416179, -0.469628, 0.778617, 0.607322, -0.289004, 0.740024, 0.046426, 0.995138, -0.086858, 0.046426, -0.995138, 0.086858, 0, -1, 0, -0.365764, 0.880972, -0.300175, 0.778617, -0.469628, 0.416179, 0.18497, -0.95655, 0.225387, -0.952376, 0.09755, 0.2889, -0.028579, 0.95655, -0.290166, 0.062361, -0.771506, 0.633159, 0.877715, -0.09755, 0.469148, 0.952718, -0.289004, 0.093835, -0.184686, -0.771506, 0.608827, 0.844848, -0.469628, -0.256282, 0.844848, -0.469628, -0.256282, -0.990438, 0.09755, 0.09755, 0.778617, -0.469628, -0.416179, -0.2889, -0.097549, 0.952376, -0.094246, 0.995138, 0.028589, -0.184686, 0.771506, -0.608827, 0.028579, -0.95655, 0.290166, 0.225387, -0.95655, -0.18497, -0.403615, 0.771506, -0.491806, 0.990438, -0.097549, -0.09755, -0.952376, -0.09755, -0.2889, 0.086536, -0.469629, -0.878613, -0.093834, 0.289004, -0.952718, -0.365764, -0.880972, 0.300175, -0.916106, 0.289004, 0.277898, -0.257142, -0.95655, 0.137445, -0.916106, 0.289004, -0.277898, -0.608827, 0.771506, -0.184686, 0.075915, -0.632563, -0.77078, 0.224828, -0.632563, -0.741159, -0.633159, 0.771506, -0.062361, -0.093834, -0.289004, -0.952718, 0.560083, 0.469628, 0.682463, 0.491343, -0.632563, -0.598704, 0.256282, 0.469628, 0.844848, 0.256282, 0.469628, 0.844848, 0.844288, 0.289004, 0.451281, -0.277898, -0.289004, -0.916106, 0.990438, 0.097549, -0.097549, 0.137445, 0.95655, -0.257142, 0.097549, 0.09755, -0.990438, -0.47089, 0.880972, -0.046379, 0.279015, -0.95655, -0.084638, 0.257142, 0.95655, -0.137445, -0.561098, 0.771506, 0.299913, -0.184686, 0.771506, 0.608827, -0.076131, 0.995138, -0.062479, -0.741159, 0.632563, 0.224828, -0.916106, -0.289004, -0.277898, 0.451281, 0.289004, -0.844288, -0.952718, -0.289004, -0.093834, -0.980785, -0.19509, 0, 0.086858, 0.995138, 0.046426, -0.877715, -0.09755, 0.469149, -0.741159, 0.632563, 0.224828, -0.952376, -0.097549, 0.2889, 0.094246, 0.995138, -0.028589, 0.137445, 0.95655, 0.257142, -0.560083, 0.469628, 0.682463, -0.598704, -0.632563, 0.491343, 0.769324, 0.09755, -0.631368, -0.086536, 0.469629, 0.878613, -0.878613, -0.469628, -0.086536, 0.683056, -0.632563, -0.365101, -0.844848, -0.469628, -0.256282, 0.491343, -0.632563, -0.598704, 0.365101, -0.632563, -0.683056, 0.224828, -0.632563, -0.741159, -0.256282, -0.469628, -0.844848, 0.18497, 0.95655, 0.225387, -0.491343, -0.632563, -0.598704, 0.598704, 0.632563, -0.491343, 0.683056, 0.632563, -0.365101, 0.878613, -0.469628, -0.086536, -0.028589, -0.995138, 0.094246, 0, -1, 0, -0.769324, 0.09755, 0.631368, 0.561098, -0.771506, 0.299913, 0.608827, -0.771506, 0.184686, 0.598704, 0.632563, 0.491343, -0.137353, 0.880972, 0.452794, 0.491343, 0.632563, 0.598704, 0.279015, 0.95655, 0.084638, 0.137354, 0.880972, -0.452794, -0.028589, 0.995138, 0.094246, 0.299913, -0.771506, -0.561098, -0.416179, 0.469628, -0.778617, -0.598704, 0.632563, 0.491343, -0.77078, 0.632563, -0.075915, -0.561098, 0.771506, 0.299913, 0.22305, 0.880972, 0.417297, -0.916106, 0.289004, 0.277898, -0.452794, -0.880972, 0.137354, -0.403615, 0.771506, 0.491806, -0.365764, -0.880972, 0.300175, -0.300175, -0.880972, 0.365764, -0.608827, 0.771506, -0.184686, -0.22305, -0.880972, 0.417297, -0.028579, -0.95655, -0.290166, 0.469148, -0.09755, 0.877715, -0.491806, 0.771506, -0.403615, -0.740024, 0.289004, 0.607322, -0.607322, 0.289004, 0.740024, 0.365764, 0.880972, -0.300175, 0.916106, -0.289004, -0.277898, -0.844848, 0.469628, 0.256282, 0.093835, 0.289004, 0.952718, -0.062361, 0.771506, -0.633159, -0.607322, 0.289004, -0.740024, 0.491806, 0.771506, -0.403615, 0.093835, -0.289004, 0.952718, 0.137353, -0.880972, -0.452794, 0.608827, 0.771506, -0.184686, -0.046378, -0.880972, -0.47089, 0.607322, 0.289004, 0.740024, 0.769324, -0.09755, -0.631368, 0.633159, 0.771506, -0.062361, 0.290166, -0.95655, -0.028579, -0.844288, -0.289004, 0.451281, -0.062361, 0.771506, 0.633159, 0.094246, 0.995138, 0.028589, 0.740024, 0.289004, -0.607322, 0.18497, -0.95655, 0.225386, -0.560083, -0.469628, 0.682463, -0.184686, -0.771506, -0.608827, 0.290166, 0.95655, 0.028579, -0.299913, -0.771506, -0.561098, -0.682463, 0.469628, -0.560083, -0.137445, 0.95655, -0.257142, -0.683056, 0.632563, 0.365101, 0.47089, 0.880972, 0.046379, -0.952376, 0.097549, 0.2889, -0.18497, -0.95655, -0.225386, -0.028579, -0.95655, 0.290166, -0.403615, -0.771506, 0.491806, 0.028589, -0.995138, 0.094246, 0, -1, 0, -0.844288, 0.289004, -0.451281, -0.631368, 0.09755, 0.769324, -0.844288, 0.289004, 0.451281, 0.77078, -0.632563, -0.075915, 0.257142, 0.95655, 0.137445, 0.878613, -0.469629, 0.086536, -0.877715, 0.09755, -0.469148, 0.184686, -0.771506, 0.608827, -0.491806, 0.771506, -0.403615, 0.299913, -0.771506, 0.561098, -0.09755, 0.097549, 0.990438, 0.741159, -0.632563, -0.224828, 0.682463, -0.469628, 0.560083, -0.224828, -0.632563, -0.741159, -0.256282, -0.469628, -0.844848, 0.952718, -0.289004, 0.093834, 0.952376, -0.097549, 0.2889, -0.365101, -0.632563, -0.683056, 0.633159, -0.771506, 0.062361, -0.633159, 0.771506, 0.062361, 0.18497, -0.95655, -0.225386, 0.277898, 0.289004, 0.916106, 0.451281, -0.289004, 0.844288, 0.184686, 0.771506, -0.608827, -0.598704, 0.632563, -0.491343, 0.137445, 0.95655, 0.257142, -0.184686, 0.771506, 0.608827, -0.086536, 0.469628, -0.878613, -0.598704, -0.632563, -0.491343, -0.093834, -0.289004, 0.952718, -0.683056, -0.632563, -0.365101, 0.224828, 0.632563, -0.741159, -0.137353, -0.880972, -0.452794, -0.452794, 0.880972, -0.137353, -0.607322, -0.289004, 0.740024, -0.256282, -0.469628, 0.844848, -0.416179, 0.469628, -0.778617, 0.076131, 0.995138, -0.062479, 0.631368, -0.09755, -0.769324, 0.469149, -0.09755, -0.877715, 0.878613, 0.469628, 0.086536, 0.633159, 0.771506, 0.062361, 0.491343, 0.632563, -0.598704, -0.094246, 0.995138, -0.028589, 0.561098, 0.771506, 0.299913, -0.279015, -0.95655, 0.084638, 0.046379, 0.880972, -0.47089, -0.062361, -0.771506, -0.633159, 0.741159, -0.632563, 0.224828, 0.062361, 0.771506, 0.633159, 0.300175, 0.880972, 0.365764, -0.47089, 0.880972, 0.046378, 0.682463, -0.469628, -0.560083, -0.225387, 0.95655, -0.18497, 0.300175, 0.880972, -0.365764, 0.598704, -0.632563, -0.491343, 0.417297, 0.880972, 0.22305, 0.416179, 0.469628, 0.778617, -0.062361, -0.771506, 0.633159, 0.877715, 0.09755, 0.469148, 0.062361, -0.771506, 0.633159, -0.18497, 0.95655, 0.225387, 0.844288, -0.289004, 0.451281, 0.062361, 0.771506, -0.633159, 0.47089, -0.880972, 0.046379, 0.844288, -0.289004, 0.451281, 0.365101, -0.632563, -0.683056, 0.093834, 0.289004, 0.952718, 0.046426, -0.995138, -0.086858, 0, -1, 0, -0.279015, 0.95655, -0.084638, -0.403615, -0.771506, -0.491806, 0.365764, -0.880972, -0.300175, 0.225386, -0.95655, -0.18497, 0.257142, -0.95655, -0.137445, 0.403615, -0.771506, 0.491806, 0.028579, 0.95655, -0.290166, 0.224828, -0.632563, 0.741159, 0.77078, 0.632563, 0.075915, -0.778617, 0.469628, -0.416179, -0.046378, 0.880972, 0.47089, 0.417297, 0.880972, 0.22305, -0.416179, -0.469628, -0.778617, -0.633159, -0.771506, -0.062361, -0.451281, -0.289004, 0.844288, -0.084638, 0.95655, -0.279015, 0.046378, 0.880972, -0.47089, -0.093835, -0.289004, -0.952718, -0.279015, 0.95655, -0.084638, -0.075915, -0.632563, -0.77078, 0.093834, -0.289004, -0.952718, 0.633159, -0.771506, 0.062361, 0.416179, 0.469628, -0.778617, 0.878613, 0.469628, -0.086536, 0.560083, -0.469628, -0.682463, 0.469148, -0.09755, -0.877715, 0.560083, -0.469628, -0.682463, -0.22305, -0.880972, 0.417297, -0.916106, -0.289004, 0.277898, 0.098013, -0.995138, -0.009653, 0, -1, 0, -0.491806, -0.771506, 0.403615, 0.607322, -0.289004, -0.740024, -0.990438, -0.097549, -0.097549, -0.990438, -0.09755, -0.09755, -1, 0, 0, 0.098013, 0.995138, 0.009653, -0.491343, 0.632563, -0.598704, -0.2889, -0.097549, -0.952376, 0.075915, 0.632563, -0.77078, 0.491806, 0.771506, 0.403615, 0.403615, 0.771506, 0.491806, 0.075915, 0.632563, 0.77078, 0.299913, 0.771506, 0.561098, 0.916106, 0.289004, 0.277898, -0.256282, 0.469628, -0.844848, -0.877715, -0.09755, -0.469149, 0.184686, 0.771506, 0.608827, -0.769324, -0.09755, -0.631368, -0.086858, 0.995138, -0.046426, -0.084638, -0.95655, 0.279015, 0.952718, 0.289004, -0.093835, -0.952376, -0.097549, -0.2889, -0.225386, -0.95655, 0.18497, 0.844288, 0.289004, -0.451281, -0.225387, -0.95655, 0.18497, 0.916106, 0.289004, 0.277898, 0.561098, 0.771506, 0.299913, -0.47089, -0.880972, -0.046378, 0.844288, 0.289004, 0.451281, -0.417297, -0.880972, -0.22305, -0.365764, -0.880972, -0.300175, -0.137353, 0.880972, -0.452794, -0.22305, -0.880972, -0.417297, 0.084638, 0.95655, -0.279015, 0.561098, 0.771506, -0.299913, 0.22305, -0.880972, -0.417297, 0.299913, 0.771506, -0.561098, -0.277898, 0.289004, -0.916106, 0.277898, -0.289004, 0.916106, 0.18497, -0.95655, -0.225387, 0.740024, -0.289004, 0.607322, 0.452794, -0.880972, 0.137354, 0.365764, -0.880972, 0.300175, -0.607322, 0.289004, 0.740024, 0.084638, -0.95655, -0.279015, 0.028579, -0.95655, -0.290166, -0.561098, 0.771506, -0.299913, -0.028579, -0.95655, -0.290166, -0.137354, -0.880972, 0.452794, 0.469149, -0.09755, 0.877715, 0.277898, -0.289004, -0.916106, 0.093835, -0.289004, -0.952718, -0.137445, -0.95655, -0.257142, -0.22305, 0.880972, 0.417297, -0.844288, -0.289004, -0.451281, -0.225387, -0.95655, -0.18497, 0.084638, 0.95655, -0.279015, -0.844848, 0.469628, -0.256282, -0.257142, -0.95655, -0.137445, -0.844848, -0.469628, 0.256282, -0.560083, -0.469628, 0.682463, -0.290166, -0.95655, -0.028579, -0.608827, 0.771506, 0.184686, 0.224828, 0.632563, 0.741159, -0.990438, 0.097549, -0.09755, -0.086536, -0.469628, 0.878613, -0.990438, 0.09755, -0.097549, 0.47089, 0.880972, -0.046379, 0.403615, -0.771506, 0.491806, 0.299913, -0.771506, 0.561098, -0.279015, 0.95655, 0.084638, 0.844848, 0.469628, -0.256282, 0.844848, 0.469628, -0.256282, -0.633159, -0.771506, 0.062361, 0.256282, -0.469628, -0.844848, -0.2889, 0.097549, 0.952376, 0.844848, 0.469628, 0.256282, 0.452794, 0.880972, 0.137353, 0.098013, -0.995138, 0.009653, 0, -1, 0, -0.075915, -0.632563, -0.77078, -0.560083, -0.469628, -0.682463, -0.097549, 0.097549, -0.990438, -0.062479, 0.995138, 0.076131, -0.417297, 0.880972, -0.22305, -0.878613, -0.469629, -0.086536, 0.086536, 0.469628, 0.878613, 0.009653, -0.995138, -0.098013, 0, -1, 0, 0.469149, 0.09755, 0.877715, -0.416179, 0.469628, 0.778617, 0.683056, -0.632563, 0.365101, 0.22305, 0.880972, 0.417297, -0.300175, 0.880972, -0.365764, 0.877715, 0.09755, 0.469149, 0.990438, 0.09755, 0.097549, -0.451281, 0.289004, 0.844288], "uvs": [[0.8125, 0.9375, 0.78125, 0.9375, 0.8125, 0.875, 1, 0.0625, 0.96875, 0.0625, 1, 0, 0.46875, 0.0625, 0.4375, 0.0625, 0.46875, 0, 0.03125, 0.0625, 0, 0.0625, 0.03125, 0, 0.21875, 1, 0.21875, 0.9375, 0.25, 0.9375, 0.96875, 0.125, 0.9375, 0.125, 0.375, 0.125, 0.375, 0.0625, 0.40625, 0.0625, 0.28125, 0.125, 0.28125, 0.0625, 0.3125, 0.0625, 0.03125, 0.125, 0, 0.125, 0.90625, 0.1875, 0.875, 0.1875, 0.90625, 0.125, 0.3125, 0.1875, 0.28125, 0.1875, 0.3125, 0.125, 0.78125, 1, 0.125, 0.1875, 0.125, 0.125, 0.15625, 0.125, 0.0625, 0.1875, 0.0625, 0.125, 0.09375, 0.125, 0.9375, 0.25, 0.9375, 0.1875, 0.96875, 0.1875, 0.75, 0.25, 0.71875, 0.25, 0.75, 0.1875, 0, 0.9375, 0, 0.875, 0.03125, 0.875, 0.3125, 0.25, 0.28125, 0.25, 0.15625, 0.25, 0.15625, 0.1875, 0.1875, 0.1875, 0.96875, 0.3125, 0.96875, 0.25, 1, 0.25, 0.1875, 0.9375, 0.15625, 0.9375, 0.1875, 0.875, 0.28125, 0.3125, 0.25, 0.3125, 0.1875, 0.3125, 0.1875, 0.25, 0.21875, 0.25, 0.25, 0.875, 0.28125, 0.875, 0.3125, 0.9375, 0.28125, 0.9375, 0.3125, 0.875, 0.15625, 0.3125, 0.5, 0.375, 0.5, 0.3125, 0.53125, 0.3125, 0.4375, 0.9375, 0.4375, 0.875, 0.46875, 0.875, 0.0625, 0.375, 0.0625, 0.3125, 0.09375, 0.3125, 0.875, 0.4375, 0.875, 0.375, 0.90625, 0.375, 0.84375, 0.4375, 0.84375, 0.375, 0.65625, 0.9375, 0.625, 0.9375, 0.65625, 0.875, 0.0625, 0.4375, 0.03125, 0.4375, 0.5625, 0.5, 0.5625, 0.4375, 0.59375, 0.4375, 0.5, 0.5, 0.5, 0.4375, 0.53125, 0.4375, 0.4375, 0.5, 0.40625, 0.5, 0.4375, 0.4375, 0.28125, 0.5, 0.25, 0.5, 0.28125, 0.4375, 0.875, 0.0625, 0.84375, 0.0625, 0.875, 0, 0.15625, 0.5, 0.125, 0.5, 0.15625, 0.4375, 0.84375, 0.9375, 0.84375, 0.875, 0, 0.5, 0, 0.4375, 0.90625, 0.5625, 0.875, 0.5625, 0.90625, 0.5, 0.59375, 0.5625, 0.5625, 0.5625, 0.59375, 0.5, 0.9375, 0.9375, 0.90625, 0.9375, 0.9375, 0.875, 0.25, 0.5625, 0.21875, 0.5625, 0.1875, 0.5625, 0.15625, 0.5625, 0.1875, 0.5, 0.125, 0.5625, 0.09375, 0.5625, 0.84375, 0.625, 0.84375, 0.5625, 0.0625, 0.875, 0.0625, 0.8125, 0.6875, 0.625, 0.6875, 0.5625, 0.71875, 0.5625, 0.71875, 0.625, 0.125, 0.875, 0.09375, 0.875, 0.125, 0.8125, 0.375, 0.625, 0.375, 0.5625, 0.40625, 0.5625, 0.1875, 0.625, 0.15625, 0.875, 0.1875, 0.8125, 0, 0.625, 0, 0.5625, 0.03125, 0.5625, 0.71875, 0.6875, 0.75, 0.625, 0.6875, 0.6875, 0.65625, 0.6875, 0.28125, 0.8125, 0.375, 0.6875, 0.40625, 0.625, 0.40625, 0.6875, 0.3125, 0.8125, 0.1875, 0.6875, 0.15625, 0.6875, 0.375, 0.875, 0.34375, 0.875, 0.375, 0.8125, 0.0625, 0.6875, 0.03125, 0.6875, 0.0625, 0.625, 0.8125, 0.75, 0.8125, 0.6875, 0.84375, 0.6875, 0.78125, 0.75, 0.75, 0.75, 0.78125, 0.6875, 0.65625, 0.75, 0.625, 0.75, 0.3125, 0.75, 0.3125, 0.6875, 0.34375, 0.6875, 0.8125, 0.8125, 0.84375, 0.75, 0.59375, 0.875, 0.59375, 0.8125, 0.625, 0.8125, 0.84375, 0.8125, 0.34375, 0.8125, 0.34375, 0.75, 0, 0.8125, 0, 0.75, 0.03125, 0.75, 0.9375, 0.8125, 0.96875, 0.8125, 0.5625, 0.25, 0.5625, 0.1875, 0.59375, 0.1875, 0.53125, 0.5, 0.28125, 1, 0.03125, 0.5, 0.0625, 0.25, 0.03125, 0.25, 0.125, 0.9375, 0.09375, 0.9375, 0.5625, 0.125, 0.59375, 0.125, 0.9375, 0.3125, 0.875, 0.3125, 0.875, 0.25, 0.90625, 0.25, 0.53125, 0.1875, 0.40625, 0.3125, 0.40625, 0.25, 0.4375, 0.25, 0.53125, 0.5625, 0.34375, 0.5625, 0.3125, 0.5625, 0.34375, 0.5, 0.375, 0.3125, 0.34375, 0.3125, 0.375, 0.25, 0.28125, 0.5625, 0.21875, 0.875, 0.21875, 0.3125, 0.25, 0.25, 0.96875, 0.625, 0.96875, 0.5625, 1, 0.5625, 0.65625, 0.125, 0.65625, 0.0625, 0.6875, 0.0625, 0.25, 0.1875, 0.21875, 0.1875, 0.25, 0.125, 0.78125, 0.625, 0.78125, 0.5625, 0.6875, 0.125, 0.46875, 0.125, 0.5, 0.0625, 0.21875, 0.625, 0.125, 0.3125, 0.75, 0.375, 0.75, 0.3125, 0.78125, 0.3125, 0.71875, 0.375, 0.71875, 0.3125, 0.40625, 0.125, 0.46875, 0.375, 0.46875, 0.3125, 0.625, 0.6875, 0.59375, 0.6875, 0.625, 0.625, 0.4375, 0.375, 0.4375, 0.3125, 0.28125, 0.375, 0.25, 0.375, 0.625, 0.0625, 0.59375, 0.0625, 0.625, 0, 0.125, 0.375, 0.09375, 0.375, 0.46875, 0.9375, 0.5, 0.875, 0.125, 0.6875, 0.09375, 0.6875, 0.125, 0.625, 0.03125, 0.375, 0.03125, 0.3125, 0.96875, 0.75, 0.9375, 0.75, 0.96875, 0.6875, 0.90625, 0.4375, 0.9375, 0.375, 0.75, 0.6875, 0.71875, 0.1875, 0.9375, 0.0625, 0.90625, 0.0625, 0.9375, 0, 0.59375, 0.75, 0.4375, 0.75, 0.4375, 0.6875, 0.46875, 0.6875, 0.40625, 0.75, 0.375, 0.75, 0.53125, 0.375, 0.21875, 0.75, 0.21875, 0.6875, 0.25, 0.6875, 0.25, 0.75, 0.1875, 0.75, 0.875, 0.75, 0.25, 0.4375, 0.09375, 0.4375, 0.6875, 0.25, 0.65625, 0.8125, 0.21875, 0.8125, 0.03125, 0.8125, 0.0625, 0.75, 0.125, 0.0625, 0.15625, 0.0625, 0.9375, 0.4375, 0.90625, 0.875, 0.875, 0.875, 0.90625, 0.8125, 0.84375, 0.5, 0, 0.6875, 0.03125, 0.625, 0.96875, 0.875, 1, 0.8125, 0.15625, 0, 0.09375, 0.8125, 0.09375, 0.75, 0.125, 0.75, 0.53125, 0.125, 0.53125, 0.0625, 0.5625, 0.0625, 0.28125, 0.75, 0.71875, 0.125, 0.75, 0.125, 0.625, 0.875, 0.65625, 0.1875, 0.625, 0.1875, 0.625, 0.125, 0.34375, 0.125, 0.90625, 0.75, 0.96875, 1, 0.96875, 0.9375, 1, 0.9375, 0.5, 0.25, 0.5, 0.1875, 0, 0.25, 0.03125, 0.1875, 0.90625, 0.3125, 0.59375, 0.3125, 0.59375, 0.25, 0.625, 0.25, 0.5625, 0.3125, 0.40625, 0.8125, 0.34375, 0.25, 0.3125, 0.3125, 0.8125, 0.0625, 0.84375, 0, 0.125, 0.25, 0.4375, 0.625, 0.375, 0.9375, 0.40625, 0.875, 0.65625, 0.625, 0.3125, 0.375, 0.8125, 0.625, 0.9375, 0.6875, 0.9375, 0.625, 0.5625, 0.375, 0.59375, 0.375, 0.1875, 0.4375, 0.1875, 0.375, 0.21875, 0.375, 0.21875, 0.4375, 0.8125, 0.5, 0.8125, 0.4375, 0.78125, 0.5, 0.71875, 0.5, 0.71875, 0.4375, 0.75, 0.4375, 0.625, 0.5, 0.625, 0.4375, 0.75, 0.5625, 0.71875, 0.9375, 0.71875, 0.875, 0.75, 0.875, 0.8125, 0.5625, 0.75, 0.9375, 0.78125, 0.875, 0.9375, 0.5625, 0.96875, 0.5, 0.875, 0.5, 0.875, 0.9375, 0.09375, 0.25, 0.15625, 0.8125, 0.15625, 0.75, 0.46875, 0.1875, 0.5, 0.125, 0.875, 0.6875, 0.46875, 0.25, 0.375, 0.1875, 0.34375, 0.1875, 0.6875, 0.875, 0.71875, 0.8125, 0.21875, 0.125, 0.4375, 0.125, 0.09375, 0.1875, 0.34375, 0.9375, 0.25, 0.625, 0.28125, 0.625, 0.84375, 0.3125, 0.40625, 0.9375, 0.5, 0.6875, 0.5, 0.625, 0.6875, 0.375, 0.6875, 0.3125, 0.65625, 0.375, 0.65625, 0.3125, 0.25, 0.0625, 0.5625, 0.6875, 0.59375, 0.625, 0.40625, 0.375, 0.375, 0.375, 0.1875, 0.125, 0.1875, 0.0625, 0.21875, 0.0625, 0.6875, 0.1875, 0.53125, 0.9375, 0.5, 0.9375, 0.53125, 0.875, 0.875, 0.625, 0.90625, 0.625, 0.5625, 0.875, 1, 0.625, 0.65625, 0.4375, 0.53125, 0.8125, 0.5625, 0.8125, 0.65625, 0.25, 0.15625, 0.625, 0.46875, 0.4375, 0.40625, 0.4375, 0.375, 0.4375, 0.3125, 0.625, 0, 0.375, 0.46875, 0.625, 0.46875, 0.5625, 0.5, 0.5625, 0.65625, 0, 0.53125, 0.625, 0.40625, 0.1875, 0.65625, 0.5, 0.46875, 0.8125, 0.5, 0.8125, 0.5, 0.75, 0.53125, 0.6875, 0.5625, 0.75, 0.53125, 0.75, 1, 0.3125, 0.09375, 0.5, 0.125, 0.4375, 0.0625, 0.5, 0.9375, 0.5, 0.875, 0.125, 0.78125, 0.25, 0.8125, 0.25, 0.3125, 0.5, 0.09375, 0.0625, 0.0625, 0.0625, 0.09375, 0, 0.15625, 0.375, 0, 0.3125, 0.8125, 1, 0.6875, 0.4375, 0.0625, 0.5625, 1, 0.125, 0.4375, 0.8125, 0.46875, 0.75, 0.90625, 0, 0.84375, 0.1875, 0.96875, 0, 0.84375, 0.125, 0.78125, 0.8125, 0.03125, 0.9375, 0.75, 0.5, 0.78125, 0.4375, 0.6875, 0.5, 0.28125, 0.6875, 0.34375, 0.625, 0.53125, 0.25, 0.46875, 0.5, 0.25, 1, 0.4375, 0.1875, 0.4375, 0, 0.625, 1, 0.25, 0, 0.25, 0.8125, 0.21875, 0.5, 0.34375, 0.375, 0.375, 0.5, 0.75, 0.8125, 0.625, 0.5625, 0.4375, 0.5625, 0.0625, 1, 0.0625, 0.9375, 0.71875, 0.0625, 0.71875, 0, 0.09375, 1, 1, 0.6875, 0.5625, 0.625, 0.09375, 0.625, 0, 1, 0.75, 1, 0.90625, 0.6875, 0.8125, 0.125, 0.3125, 0.4375, 0.5625, 0.9375, 0.34375, 0.0625, 0.5, 1, 0.96875, 0.375, 0.78125, 0.125, 0.78125, 0.0625, 0.625, 0.375, 1, 0.75, 0.375, 0, 0.34375, 0.4375, 0.65625, 0.5625, 0.625, 0.3125, 0.6875, 0.75, 0.8125, 0, 0.59375, 0, 0.5625, 0, 0.40625, 0, 1, 0.875, 0.28125, 0, 0.1875, 0, 0.0625, 0, 0.3125, 1, 0.6875, 0.8125, 0.5625, 1, 0.59375, 0.9375, 0.8125, 0.1875, 0.6875, 1, 0.6875, 0.9375, 0.71875, 1, 0, 0.1875, 0.8125, 0.375, 1, 0.1875, 0.96875, 0.4375, 1, 0.375, 0.8125, 0.3125, 0.84375, 0.25, 0.71875, 0.75, 0.75, 0.0625, 0.125, 0, 0.15625, 1, 0.34375, 1, 0.375, 1, 0.84375, 1, 0.78125, 0.375, 0.78125, 0, 0.78125, 0.1875, 0.875, 0.8125, 0.65625, 1, 0.34375, 0, 0.03125, 1, 0.875, 1, 1, 0.4375, 0.40625, 1, 0.53125, 1, 0.21875, 0, 0.1875, 1, 0.4375, 1, 0.3125, 0, 0.59375, 1, 0.9375, 1, 0.6875, 0, 0.53125, 0, 1, 0.5, 0.46875, 1, 0.90625, 1, 0.5, 0, 0.125, 1, 0.75, 0]], "faces": [58, 27, 26, 59, 0, 0, 1, 2, 0, 1, 2, 3, 58, 449, 480, 481, 0, 3, 4, 5, 4, 5, 6, 7, 58, 464, 463, 481, 0, 6, 7, 8, 8, 9, 10, 11, 58, 450, 449, 481, 0, 9, 10, 11, 12, 13, 14, 15, 58, 0, 8, 9, 0, 12, 13, 14, 16, 17, 18, 19, 58, 448, 480, 449, 0, 15, 4, 3, 20, 21, 6, 5, 58, 448, 447, 480, 0, 15, 16, 4, 22, 21, 23, 6, 58, 429, 461, 462, 0, 17, 18, 19, 24, 25, 26, 27, 58, 426, 458, 459, 0, 20, 21, 22, 28, 29, 30, 31, 58, 418, 417, 450, 0, 23, 24, 9, 32, 33, 34, 13, 58, 414, 413, 446, 0, 25, 26, 27, 35, 36, 37, 38, 58, 395, 394, 427, 0, 28, 29, 30, 39, 40, 41, 42, 58, 0, 26, 27, 0, 31, 1, 0, 43, 17, 2, 1, 58, 389, 421, 422, 0, 32, 33, 34, 44, 45, 46, 47, 58, 387, 419, 420, 0, 35, 36, 37, 48, 49, 50, 51, 58, 383, 415, 416, 0, 38, 39, 40, 52, 53, 54, 55, 58, 377, 376, 409, 0, 41, 42, 43, 56, 57, 58, 59, 58, 1, 33, 34, 0, 44, 45, 46, 60, 61, 62, 63, 58, 363, 362, 395, 0, 47, 48, 28, 64, 65, 66, 40, 58, 358, 390, 391, 0, 49, 50, 51, 67, 68, 69, 70, 58, 352, 384, 353, 0, 52, 53, 54, 71, 72, 73, 74, 58, 7, 6, 39, 0, 55, 56, 57, 75, 76, 77, 78, 58, 330, 329, 362, 0, 58, 59, 48, 79, 80, 81, 66, 58, 327, 359, 360, 0, 60, 61, 62, 82, 83, 84, 85, 58, 9, 41, 42, 0, 14, 63, 64, 86, 19, 87, 88, 58, 11, 10, 43, 0, 65, 66, 67, 89, 90, 91, 92, 58, 327, 326, 359, 0, 60, 68, 61, 93, 83, 94, 84, 58, 305, 337, 338, 0, 69, 70, 71, 95, 96, 97, 98, 58, 15, 47, 48, 0, 72, 73, 74, 99, 100, 101, 102, 58, 291, 323, 324, 0, 75, 76, 77, 103, 104, 105, 106, 58, 285, 317, 318, 0, 78, 79, 80, 107, 108, 109, 110, 58, 284, 316, 317, 0, 81, 82, 79, 111, 112, 113, 109, 58, 22, 21, 54, 0, 83, 84, 85, 114, 115, 116, 117, 58, 259, 258, 291, 0, 86, 87, 75, 118, 119, 120, 104, 58, 243, 275, 276, 0, 88, 89, 90, 121, 122, 123, 124, 58, 241, 273, 274, 0, 91, 92, 93, 125, 126, 127, 128, 58, 239, 238, 271, 0, 94, 95, 96, 129, 130, 131, 132, 58, 234, 233, 266, 0, 97, 98, 99, 133, 134, 135, 136, 58, 477, 476, 481, 0, 100, 101, 102, 137, 138, 139, 140, 58, 230, 229, 262, 0, 103, 104, 105, 141, 142, 143, 144, 58, 28, 27, 60, 0, 106, 0, 107, 145, 146, 1, 147, 58, 225, 257, 258, 0, 108, 109, 87, 148, 149, 150, 120, 58, 222, 221, 254, 0, 110, 111, 112, 151, 152, 153, 154, 58, 212, 211, 244, 0, 113, 114, 115, 155, 156, 157, 158, 58, 31, 30, 63, 0, 116, 117, 118, 159, 160, 161, 162, 58, 201, 200, 233, 0, 119, 120, 98, 163, 164, 165, 135, 58, 199, 198, 231, 0, 121, 122, 123, 166, 167, 168, 169, 58, 197, 196, 229, 0, 124, 125, 104, 170, 171, 172, 143, 58, 188, 220, 221, 0, 126, 127, 111, 173, 174, 175, 153, 58, 35, 34, 67, 0, 128, 46, 129, 176, 177, 63, 178, 58, 183, 215, 216, 0, 130, 131, 132, 179, 180, 181, 182, 58, 184, 183, 216, 0, 133, 130, 132, 183, 184, 180, 182, 58, 37, 36, 69, 0, 134, 135, 136, 185, 186, 187, 188, 58, 173, 205, 206, 0, 137, 138, 139, 189, 190, 191, 192, 58, 167, 199, 200, 0, 140, 121, 120, 193, 194, 167, 165, 58, 39, 38, 71, 0, 57, 141, 142, 195, 78, 196, 197, 58, 161, 193, 194, 0, 143, 144, 145, 198, 199, 200, 201, 58, 152, 184, 185, 0, 146, 133, 147, 202, 203, 184, 204, 58, 151, 150, 183, 0, 148, 149, 130, 205, 206, 207, 180, 58, 42, 41, 74, 0, 64, 63, 150, 208, 88, 87, 209, 58, 141, 173, 174, 0, 151, 137, 152, 210, 211, 190, 212, 58, 142, 141, 174, 0, 153, 151, 152, 213, 214, 211, 212, 58, 42, 74, 75, 0, 64, 150, 154, 215, 88, 209, 216, 58, 135, 134, 167, 0, 155, 156, 140, 217, 218, 219, 194, 58, 45, 44, 77, 0, 157, 158, 159, 220, 221, 222, 223, 58, 131, 130, 163, 0, 160, 161, 162, 224, 225, 226, 227, 58, 123, 155, 156, 0, 163, 164, 165, 228, 229, 230, 231, 58, 122, 121, 154, 0, 166, 167, 168, 232, 233, 234, 235, 58, 118, 117, 150, 0, 169, 170, 149, 236, 237, 238, 207, 58, 107, 139, 140, 0, 171, 172, 173, 239, 240, 241, 242, 58, 91, 123, 124, 0, 174, 163, 175, 243, 244, 229, 245, 58, 52, 84, 85, 0, 176, 177, 178, 246, 247, 248, 249, 58, 92, 91, 124, 0, 179, 174, 175, 250, 251, 244, 245, 58, 76, 75, 108, 0, 180, 154, 181, 252, 253, 216, 254, 58, 65, 97, 98, 0, 182, 183, 184, 255, 256, 257, 258, 58, 63, 95, 96, 0, 118, 185, 186, 259, 162, 260, 261, 58, 60, 59, 92, 0, 107, 2, 179, 262, 147, 3, 251, 58, 371, 403, 404, 1, 187, 188, 189, 263, 264, 265, 266, 58, 243, 242, 275, 1, 88, 190, 89, 267, 122, 268, 123, 58, 0, 10, 11, 1, 191, 66, 65, 269, 17, 91, 90, 58, 226, 225, 258, 1, 192, 108, 87, 270, 271, 149, 120, 58, 355, 354, 387, 1, 193, 194, 35, 272, 273, 274, 49, 58, 5, 4, 37, 1, 195, 196, 134, 275, 276, 277, 186, 58, 403, 435, 436, 1, 188, 197, 198, 278, 265, 279, 280, 58, 352, 351, 384, 1, 52, 199, 53, 281, 72, 282, 73, 58, 349, 381, 382, 1, 200, 201, 202, 283, 284, 285, 286, 58, 403, 402, 435, 1, 188, 203, 197, 287, 265, 288, 279, 58, 334, 366, 367, 1, 204, 205, 206, 289, 290, 291, 292, 58, 211, 210, 243, 1, 114, 207, 88, 293, 157, 294, 122, 58, 204, 203, 236, 1, 208, 209, 210, 295, 296, 297, 298, 58, 333, 332, 365, 1, 211, 212, 213, 299, 300, 301, 302, 58, 202, 201, 234, 1, 214, 119, 97, 303, 304, 164, 134, 58, 7, 39, 40, 1, 55, 57, 215, 305, 76, 78, 306, 58, 198, 230, 231, 1, 122, 103, 123, 307, 168, 142, 169, 58, 394, 426, 427, 1, 29, 20, 30, 308, 41, 29, 42, 58, 329, 328, 361, 1, 59, 216, 217, 309, 81, 310, 311, 58, 192, 224, 193, 1, 218, 219, 220, 312, 313, 314, 315, 58, 438, 470, 471, 1, 221, 222, 223, 316, 317, 318, 319, 58, 393, 392, 425, 1, 224, 225, 226, 320, 321, 322, 323, 58, 186, 185, 218, 1, 227, 147, 228, 324, 325, 204, 326, 58, 439, 438, 471, 1, 229, 221, 223, 327, 328, 317, 319, 58, 10, 42, 43, 1, 66, 64, 67, 329, 91, 88, 92, 58, 326, 358, 359, 1, 68, 49, 61, 330, 94, 68, 84, 58, 432, 464, 465, 1, 230, 6, 231, 331, 332, 9, 333, 58, 168, 200, 201, 1, 232, 120, 119, 334, 335, 165, 164, 58, 326, 325, 358, 1, 68, 233, 49, 336, 94, 337, 68, 58, 313, 345, 346, 1, 234, 235, 236, 338, 339, 340, 341, 58, 312, 344, 345, 1, 237, 238, 235, 342, 343, 344, 340, 58, 430, 462, 463, 1, 239, 19, 7, 345, 346, 27, 10, 58, 151, 183, 184, 1, 148, 130, 133, 347, 206, 180, 184, 58, 304, 336, 337, 1, 240, 241, 70, 348, 349, 350, 97, 58, 149, 148, 181, 1, 242, 243, 244, 351, 352, 353, 354, 58, 303, 335, 336, 1, 245, 246, 241, 355, 356, 357, 350, 58, 298, 297, 330, 1, 247, 248, 58, 358, 359, 360, 80, 58, 469, 468, 481, 1, 249, 250, 251, 361, 362, 363, 364, 58, 293, 292, 325, 1, 252, 253, 233, 365, 366, 367, 337, 58, 16, 48, 49, 1, 254, 74, 255, 368, 369, 102, 370, 58, 133, 132, 165, 1, 256, 257, 258, 371, 372, 373, 374, 58, 384, 383, 416, 1, 53, 38, 40, 375, 73, 53, 55, 58, 290, 322, 323, 1, 259, 260, 76, 376, 377, 378, 105, 58, 128, 127, 160, 1, 261, 262, 263, 379, 380, 381, 382, 58, 286, 318, 319, 1, 264, 80, 265, 383, 384, 110, 385, 58, 121, 153, 154, 1, 167, 266, 168, 386, 234, 387, 235, 58, 376, 408, 409, 1, 42, 267, 43, 388, 58, 389, 59, 58, 118, 150, 151, 1, 169, 149, 148, 390, 237, 207, 206, 58, 479, 478, 481, 1, 268, 269, 270, 391, 392, 393, 394, 58, 117, 116, 149, 1, 170, 271, 242, 395, 238, 396, 352, 58, 111, 143, 144, 1, 272, 273, 274, 397, 398, 399, 400, 58, 110, 109, 142, 1, 275, 276, 153, 401, 402, 403, 214, 58, 274, 273, 306, 1, 93, 92, 277, 404, 128, 127, 405, 58, 104, 136, 137, 1, 278, 279, 280, 406, 407, 408, 409, 58, 105, 104, 137, 1, 281, 278, 280, 410, 411, 407, 409, 58, 103, 135, 136, 1, 282, 155, 279, 412, 413, 218, 408, 58, 92, 124, 125, 1, 179, 175, 283, 414, 251, 245, 415, 58, 266, 265, 298, 1, 99, 284, 247, 416, 136, 417, 359, 58, 260, 292, 293, 1, 285, 253, 252, 418, 419, 367, 366, 58, 376, 375, 408, 1, 42, 286, 267, 420, 58, 421, 389, 58, 86, 85, 118, 1, 287, 178, 169, 422, 423, 249, 237, 58, 260, 259, 292, 1, 285, 86, 253, 424, 419, 119, 367, 58, 72, 104, 105, 1, 288, 278, 281, 425, 426, 407, 411, 58, 67, 66, 99, 1, 129, 289, 290, 427, 178, 428, 429, 58, 421, 453, 454, 1, 33, 291, 292, 430, 46, 431, 432, 58, 254, 286, 287, 1, 112, 264, 293, 433, 154, 384, 434, 58, 62, 61, 94, 1, 294, 295, 296, 435, 436, 437, 438, 58, 252, 284, 285, 1, 297, 81, 78, 439, 440, 112, 108, 58, 129, 161, 162, 2, 298, 143, 299, 441, 442, 199, 443, 58, 211, 243, 244, 2, 114, 88, 115, 444, 157, 122, 158, 58, 64, 96, 65, 2, 300, 186, 301, 445, 446, 261, 447, 58, 454, 453, 481, 2, 292, 291, 302, 448, 432, 431, 449, 58, 447, 479, 480, 2, 16, 268, 4, 450, 23, 392, 6, 58, 447, 446, 479, 2, 16, 27, 268, 451, 23, 38, 392, 58, 68, 100, 101, 2, 303, 304, 305, 452, 453, 454, 455, 58, 434, 466, 467, 2, 306, 307, 308, 456, 457, 458, 459, 58, 74, 106, 107, 2, 150, 309, 171, 460, 209, 461, 240, 58, 408, 440, 441, 2, 267, 310, 311, 462, 389, 463, 464, 58, 53, 85, 86, 2, 312, 178, 287, 465, 466, 249, 423, 58, 406, 405, 438, 2, 313, 314, 221, 467, 468, 469, 317, 58, 404, 436, 437, 2, 189, 198, 315, 470, 266, 280, 471, 58, 395, 427, 428, 2, 28, 30, 316, 472, 40, 42, 473, 58, 94, 126, 127, 2, 296, 317, 262, 474, 438, 475, 381, 58, 97, 129, 130, 2, 183, 298, 161, 476, 257, 442, 226, 58, 382, 381, 414, 2, 202, 201, 25, 477, 286, 285, 36, 58, 0, 32, 1, 2, 318, 319, 320, 478, 17, 479, 480, 58, 369, 401, 402, 2, 321, 322, 203, 481, 482, 483, 288, 58, 111, 110, 143, 2, 272, 275, 273, 484, 398, 402, 399, 58, 110, 142, 143, 2, 275, 153, 273, 485, 402, 214, 399, 58, 354, 353, 386, 2, 194, 323, 324, 486, 274, 487, 488, 58, 6, 5, 38, 2, 56, 195, 141, 489, 77, 276, 196, 58, 350, 382, 383, 2, 325, 202, 38, 490, 491, 286, 53, 58, 340, 372, 373, 2, 326, 327, 328, 492, 493, 494, 495, 58, 339, 338, 371, 2, 329, 71, 187, 496, 497, 98, 264, 58, 122, 154, 155, 2, 166, 168, 164, 498, 233, 235, 230, 58, 45, 77, 78, 2, 157, 159, 330, 499, 221, 223, 500, 58, 332, 364, 365, 2, 212, 331, 213, 501, 301, 502, 302, 58, 331, 363, 364, 2, 332, 47, 331, 503, 504, 65, 502, 58, 331, 330, 363, 2, 332, 58, 47, 505, 504, 80, 65, 58, 476, 475, 481, 2, 101, 333, 334, 506, 139, 507, 508, 58, 325, 357, 358, 2, 233, 335, 49, 509, 337, 510, 68, 58, 59, 91, 92, 2, 2, 174, 179, 511, 3, 244, 251, 58, 142, 174, 175, 2, 153, 152, 336, 512, 214, 212, 513, 58, 13, 45, 46, 2, 337, 157, 338, 514, 515, 221, 516, 58, 150, 149, 182, 2, 149, 242, 339, 517, 207, 352, 518, 58, 299, 298, 331, 2, 340, 247, 332, 519, 520, 359, 504, 58, 154, 186, 187, 2, 168, 227, 341, 521, 235, 325, 522, 58, 159, 191, 192, 2, 342, 343, 218, 523, 524, 525, 313, 58, 275, 307, 308, 2, 89, 344, 345, 526, 123, 527, 528, 58, 276, 275, 308, 2, 90, 89, 345, 529, 124, 123, 528, 58, 168, 167, 200, 2, 232, 140, 120, 530, 335, 194, 165, 58, 263, 295, 296, 2, 346, 347, 348, 531, 532, 533, 534, 58, 264, 263, 296, 2, 349, 346, 348, 535, 536, 532, 534, 58, 251, 283, 284, 2, 350, 351, 81, 537, 538, 539, 112, 58, 251, 250, 283, 2, 350, 352, 351, 540, 538, 541, 539, 58, 248, 280, 281, 2, 353, 354, 355, 542, 543, 544, 545, 58, 245, 244, 277, 2, 356, 115, 357, 546, 547, 158, 548, 58, 185, 184, 217, 2, 147, 133, 358, 549, 204, 184, 550, 58, 24, 56, 57, 2, 359, 360, 361, 551, 552, 553, 554, 58, 187, 186, 219, 2, 341, 227, 362, 555, 522, 325, 556, 58, 188, 187, 220, 2, 126, 341, 127, 557, 174, 522, 175, 58, 25, 57, 58, 2, 363, 361, 364, 558, 559, 554, 560, 58, 34, 33, 66, 2, 46, 45, 289, 561, 63, 62, 428, 58, 194, 193, 226, 2, 145, 144, 192, 562, 201, 200, 271, 58, 224, 223, 256, 2, 219, 365, 366, 563, 314, 564, 565, 58, 221, 253, 254, 2, 111, 367, 112, 566, 153, 567, 154, 58, 29, 28, 61, 2, 368, 106, 295, 568, 569, 146, 437, 58, 221, 220, 253, 2, 111, 127, 367, 570, 153, 175, 567, 58, 216, 215, 248, 2, 132, 131, 353, 571, 182, 181, 543, 58, 324, 356, 357, 3, 77, 369, 335, 572, 106, 573, 510, 58, 123, 122, 155, 3, 163, 166, 164, 574, 229, 233, 230, 58, 70, 102, 103, 3, 370, 371, 282, 575, 576, 577, 413, 58, 401, 400, 433, 3, 322, 372, 373, 578, 483, 579, 580, 58, 125, 124, 157, 3, 283, 175, 374, 581, 415, 245, 582, 58, 336, 368, 369, 3, 241, 375, 321, 583, 350, 584, 482, 58, 397, 396, 429, 3, 376, 377, 17, 585, 586, 587, 25, 58, 56, 55, 88, 3, 360, 378, 379, 588, 553, 589, 590, 58, 332, 331, 364, 3, 212, 332, 331, 591, 301, 504, 502, 58, 392, 424, 425, 3, 225, 380, 226, 592, 322, 593, 323, 58, 431, 430, 463, 3, 381, 239, 7, 594, 595, 346, 10, 58, 388, 387, 420, 3, 382, 35, 37, 596, 597, 49, 51, 58, 13, 12, 45, 3, 337, 383, 157, 598, 515, 599, 221, 58, 8, 40, 41, 3, 13, 215, 63, 600, 18, 306, 87, 58, 136, 135, 168, 3, 279, 155, 232, 601, 408, 218, 335, 58, 136, 168, 169, 3, 279, 232, 384, 602, 408, 335, 603, 58, 323, 322, 355, 3, 76, 260, 193, 604, 105, 378, 273, 58, 137, 169, 170, 3, 280, 384, 385, 605, 409, 603, 606, 58, 318, 317, 350, 3, 80, 79, 325, 607, 110, 109, 491, 58, 316, 348, 349, 3, 82, 386, 200, 608, 113, 609, 284, 58, 381, 413, 414, 3, 201, 26, 25, 610, 285, 37, 36, 58, 14, 13, 46, 3, 387, 337, 338, 611, 612, 515, 516, 58, 145, 144, 177, 3, 388, 274, 389, 613, 614, 400, 615, 58, 311, 343, 344, 3, 390, 391, 238, 616, 617, 618, 344, 58, 310, 342, 343, 3, 392, 393, 391, 619, 620, 621, 618, 58, 425, 424, 457, 3, 226, 380, 394, 622, 323, 593, 623, 58, 148, 147, 180, 3, 243, 395, 396, 624, 353, 625, 626, 58, 148, 180, 181, 3, 243, 396, 244, 627, 353, 626, 354, 58, 101, 100, 133, 3, 305, 304, 256, 628, 455, 454, 372, 58, 302, 301, 334, 3, 397, 398, 204, 629, 630, 631, 290, 58, 39, 71, 72, 3, 57, 142, 288, 632, 78, 197, 426, 58, 153, 152, 185, 3, 266, 146, 147, 633, 387, 203, 204, 58, 423, 455, 456, 3, 399, 400, 401, 634, 635, 636, 637, 58, 153, 185, 186, 3, 266, 147, 227, 638, 387, 204, 325, 58, 375, 407, 408, 3, 286, 402, 267, 639, 421, 640, 389, 58, 18, 17, 50, 3, 403, 404, 405, 641, 642, 643, 644, 58, 157, 189, 190, 3, 374, 406, 407, 645, 582, 646, 647, 58, 52, 51, 84, 3, 176, 408, 177, 648, 247, 649, 248, 58, 286, 285, 318, 3, 264, 78, 80, 650, 384, 108, 110, 58, 160, 192, 161, 3, 263, 218, 409, 651, 382, 313, 652, 58, 162, 161, 194, 3, 299, 143, 145, 653, 443, 199, 201, 58, 278, 277, 310, 3, 410, 357, 392, 654, 655, 548, 620, 58, 50, 82, 83, 3, 405, 411, 412, 656, 644, 657, 658, 58, 374, 373, 406, 3, 413, 328, 313, 659, 660, 495, 468, 58, 273, 305, 306, 3, 92, 69, 277, 661, 127, 96, 405, 58, 166, 198, 199, 3, 414, 122, 121, 662, 663, 168, 167, 58, 108, 107, 140, 3, 181, 171, 173, 664, 254, 240, 242, 58, 272, 271, 304, 3, 415, 96, 240, 665, 666, 132, 349, 58, 270, 269, 302, 3, 416, 417, 397, 667, 668, 669, 630, 58, 170, 202, 203, 3, 385, 214, 209, 670, 606, 304, 297, 58, 372, 371, 404, 3, 327, 187, 189, 671, 494, 264, 266, 58, 171, 203, 204, 3, 418, 209, 208, 672, 673, 297, 296, 58, 416, 415, 448, 3, 40, 39, 15, 674, 55, 54, 21, 58, 263, 262, 295, 3, 346, 105, 347, 675, 532, 144, 533, 58, 257, 289, 290, 3, 109, 419, 259, 676, 150, 677, 377, 58, 258, 257, 290, 3, 87, 109, 259, 678, 120, 150, 377, 58, 22, 54, 55, 3, 83, 85, 378, 679, 115, 117, 589, 58, 176, 208, 209, 3, 420, 421, 422, 680, 681, 682, 683, 58, 470, 469, 481, 3, 222, 249, 423, 684, 318, 362, 685, 58, 178, 210, 211, 3, 424, 207, 114, 686, 687, 294, 157, 58, 409, 408, 441, 3, 43, 267, 311, 688, 59, 389, 464, 58, 365, 397, 398, 3, 213, 376, 425, 689, 302, 586, 690, 58, 246, 245, 278, 3, 426, 356, 410, 691, 692, 547, 655, 58, 35, 67, 68, 3, 128, 129, 303, 693, 177, 178, 453, 58, 48, 80, 81, 3, 74, 427, 428, 694, 102, 695, 696, 58, 366, 365, 398, 3, 205, 213, 425, 697, 291, 302, 690, 58, 113, 145, 146, 3, 429, 388, 430, 698, 699, 614, 700, 58, 115, 114, 147, 3, 431, 432, 395, 701, 702, 703, 625, 58, 82, 114, 115, 3, 411, 432, 431, 704, 657, 703, 702, 58, 187, 219, 220, 3, 341, 362, 127, 705, 522, 556, 175, 58, 4, 36, 37, 3, 196, 135, 134, 706, 277, 187, 186, 58, 321, 352, 353, 3, 433, 52, 54, 707, 708, 72, 74, 58, 408, 407, 440, 3, 267, 402, 310, 709, 389, 640, 463, 58, 230, 262, 263, 3, 103, 105, 346, 710, 142, 144, 532, 58, 231, 230, 263, 3, 123, 103, 346, 711, 169, 142, 532, 58, 229, 228, 261, 3, 104, 434, 435, 712, 143, 713, 714, 58, 227, 259, 260, 3, 436, 86, 285, 715, 716, 119, 419, 58, 223, 255, 256, 3, 365, 437, 366, 717, 564, 718, 565, 58, 445, 477, 478, 3, 438, 100, 269, 719, 720, 138, 393, 58, 349, 348, 381, 3, 200, 386, 201, 721, 284, 609, 285, 58, 346, 378, 379, 3, 236, 439, 440, 722, 341, 723, 724, 58, 344, 376, 377, 3, 238, 42, 41, 725, 344, 58, 57, 58, 219, 251, 252, 3, 362, 350, 297, 726, 556, 538, 440, 58, 202, 234, 235, 3, 214, 97, 441, 727, 304, 134, 728, 58, 85, 117, 118, 3, 178, 170, 169, 729, 249, 238, 237, 58, 452, 451, 481, 3, 442, 443, 444, 730, 731, 732, 733, 58, 210, 242, 243, 3, 207, 190, 88, 734, 294, 268, 122, 58, 297, 296, 329, 4, 248, 348, 59, 735, 360, 534, 81, 58, 77, 76, 109, 4, 159, 180, 276, 736, 223, 253, 403, 58, 295, 294, 327, 4, 347, 445, 60, 737, 533, 738, 83, 58, 393, 425, 426, 4, 224, 226, 20, 739, 321, 323, 29, 58, 156, 188, 189, 4, 165, 126, 406, 740, 231, 174, 646, 58, 51, 83, 84, 4, 408, 412, 177, 741, 649, 658, 248, 58, 289, 321, 322, 4, 419, 446, 260, 742, 677, 743, 378, 58, 422, 454, 455, 4, 34, 292, 400, 744, 47, 432, 636, 58, 423, 422, 455, 4, 399, 34, 400, 745, 635, 47, 636, 58, 0, 27, 28, 4, 447, 0, 106, 746, 17, 1, 146, 58, 422, 421, 454, 4, 34, 33, 292, 747, 47, 46, 432, 58, 285, 284, 317, 4, 78, 81, 79, 748, 108, 112, 109, 58, 284, 283, 316, 4, 81, 351, 82, 749, 112, 539, 113, 58, 278, 310, 311, 4, 410, 392, 390, 750, 655, 620, 617, 58, 279, 278, 311, 4, 448, 410, 390, 751, 752, 655, 617, 58, 163, 195, 196, 4, 162, 449, 125, 753, 227, 754, 172, 58, 386, 418, 419, 4, 324, 23, 36, 755, 488, 33, 50, 58, 98, 97, 130, 4, 184, 183, 161, 756, 258, 257, 226, 58, 124, 123, 156, 4, 175, 163, 165, 757, 245, 229, 231, 58, 417, 449, 450, 4, 24, 10, 9, 758, 34, 14, 13, 58, 382, 414, 415, 4, 202, 25, 39, 759, 286, 36, 54, 58, 416, 448, 417, 4, 40, 15, 450, 760, 55, 21, 761, 58, 37, 69, 70, 4, 134, 136, 370, 762, 186, 188, 576, 58, 271, 303, 304, 4, 96, 245, 240, 763, 132, 356, 349, 58, 79, 111, 112, 4, 451, 272, 452, 764, 765, 398, 766, 58, 478, 477, 481, 4, 269, 100, 453, 767, 393, 138, 768, 58, 414, 446, 447, 4, 25, 27, 16, 769, 36, 38, 23, 58, 101, 133, 134, 4, 305, 256, 156, 770, 455, 372, 219, 58, 329, 361, 362, 4, 59, 217, 48, 771, 81, 311, 66, 58, 413, 412, 445, 4, 26, 454, 438, 772, 37, 773, 720, 58, 328, 360, 361, 4, 216, 62, 217, 774, 310, 85, 311, 58, 21, 53, 54, 4, 84, 312, 85, 775, 116, 466, 117, 58, 480, 479, 481, 4, 4, 268, 455, 776, 6, 392, 777, 58, 328, 327, 360, 4, 216, 60, 62, 778, 310, 83, 85, 58, 445, 444, 477, 4, 438, 456, 100, 779, 720, 780, 138, 58, 58, 57, 90, 4, 364, 361, 457, 781, 560, 554, 782, 58, 133, 165, 166, 4, 256, 258, 414, 783, 372, 374, 663, 58, 177, 209, 210, 4, 389, 422, 207, 784, 615, 683, 294, 58, 2, 1, 34, 4, 458, 44, 46, 785, 786, 61, 63, 58, 323, 355, 356, 4, 76, 193, 369, 787, 105, 273, 573, 58, 249, 281, 282, 4, 459, 355, 460, 788, 789, 545, 790, 58, 30, 29, 62, 4, 117, 368, 294, 791, 161, 569, 436, 58, 247, 279, 280, 4, 461, 448, 354, 792, 793, 752, 544, 58, 248, 247, 280, 4, 353, 461, 354, 794, 543, 793, 544, 58, 106, 138, 139, 4, 309, 462, 172, 795, 461, 796, 241, 58, 433, 465, 466, 4, 373, 231, 307, 797, 580, 333, 458, 58, 432, 431, 464, 4, 230, 381, 6, 798, 332, 595, 9, 58, 318, 350, 351, 4, 80, 325, 199, 799, 110, 491, 282, 58, 244, 243, 276, 4, 115, 88, 90, 800, 158, 122, 124, 58, 140, 172, 173, 4, 173, 463, 137, 801, 242, 802, 190, 58, 370, 402, 403, 4, 464, 203, 188, 803, 804, 288, 265, 58, 240, 272, 273, 4, 465, 415, 92, 805, 806, 666, 127, 58, 0, 9, 10, 4, 466, 14, 66, 807, 17, 19, 91, 58, 367, 399, 400, 4, 206, 467, 372, 808, 292, 809, 579, 58, 239, 271, 272, 4, 94, 96, 415, 810, 130, 132, 666, 58, 425, 457, 458, 4, 226, 394, 21, 811, 323, 623, 30, 58, 463, 462, 481, 4, 7, 19, 468, 812, 10, 27, 813, 58, 0, 21, 22, 4, 469, 84, 83, 814, 17, 116, 115, 58, 404, 403, 436, 4, 189, 188, 198, 815, 266, 265, 280, 58, 145, 177, 178, 4, 388, 389, 424, 816, 614, 615, 687, 58, 113, 112, 145, 4, 429, 452, 388, 817, 699, 766, 614, 58, 402, 401, 434, 4, 203, 322, 306, 818, 288, 483, 457, 58, 302, 334, 335, 4, 397, 204, 246, 819, 630, 290, 357, 58, 457, 456, 481, 4, 394, 401, 470, 820, 623, 637, 821, 58, 222, 254, 255, 4, 110, 112, 437, 822, 152, 154, 718, 58, 398, 430, 431, 4, 425, 239, 381, 823, 690, 346, 595, 58, 358, 357, 390, 4, 49, 335, 50, 824, 68, 510, 69, 58, 220, 252, 253, 4, 127, 297, 367, 825, 175, 440, 567, 58, 41, 40, 73, 4, 63, 215, 471, 826, 87, 306, 827, 58, 199, 231, 232, 4, 121, 123, 472, 828, 167, 169, 829, 58, 301, 300, 333, 4, 398, 473, 211, 830, 631, 831, 300, 58, 219, 218, 251, 4, 362, 228, 350, 832, 556, 326, 538, 58, 201, 233, 234, 4, 119, 98, 97, 833, 164, 135, 134, 58, 357, 356, 389, 4, 335, 369, 32, 834, 510, 573, 45, 58, 216, 248, 249, 4, 132, 353, 459, 835, 182, 543, 789, 58, 215, 247, 248, 4, 131, 461, 353, 836, 181, 793, 543, 58, 204, 236, 237, 4, 208, 210, 474, 837, 296, 298, 838, 58, 89, 121, 122, 4, 475, 167, 166, 839, 840, 234, 233, 58, 213, 212, 245, 4, 476, 113, 356, 841, 842, 156, 547, 58, 91, 90, 123, 4, 174, 457, 163, 843, 244, 782, 229, 58, 208, 207, 240, 4, 421, 477, 465, 844, 682, 845, 806, 58, 0, 3, 4, 4, 478, 479, 196, 846, 17, 847, 277, 58, 262, 261, 294, 5, 105, 435, 445, 848, 144, 714, 738, 58, 8, 7, 40, 5, 13, 55, 215, 849, 18, 76, 306, 58, 36, 68, 69, 5, 135, 303, 136, 850, 187, 453, 188, 58, 472, 471, 481, 5, 480, 223, 481, 851, 852, 319, 853, 58, 14, 46, 47, 5, 387, 338, 73, 854, 612, 516, 101, 58, 102, 101, 134, 5, 371, 305, 156, 855, 577, 455, 219, 58, 46, 45, 78, 5, 338, 157, 330, 856, 516, 221, 500, 58, 254, 253, 286, 5, 112, 367, 264, 857, 154, 567, 384, 58, 361, 360, 393, 5, 217, 62, 224, 858, 311, 85, 321, 58, 0, 4, 5, 5, 482, 196, 195, 859, 17, 277, 276, 58, 128, 160, 129, 5, 261, 263, 483, 860, 380, 382, 861, 58, 179, 178, 211, 5, 484, 424, 114, 862, 863, 687, 157, 58, 9, 8, 41, 5, 14, 13, 63, 864, 19, 18, 87, 58, 152, 151, 184, 5, 146, 148, 133, 865, 203, 206, 184, 58, 103, 102, 135, 5, 282, 371, 155, 866, 413, 577, 218, 58, 4, 3, 36, 5, 196, 479, 135, 867, 277, 847, 187, 58, 249, 248, 281, 5, 459, 353, 355, 868, 789, 543, 545, 58, 300, 299, 332, 5, 473, 340, 212, 869, 831, 520, 301, 58, 394, 393, 426, 5, 29, 224, 20, 870, 41, 321, 29, 58, 183, 182, 215, 5, 130, 339, 131, 871, 180, 518, 181, 58, 132, 131, 164, 5, 257, 160, 485, 872, 373, 225, 873, 58, 10, 9, 42, 5, 66, 14, 64, 874, 91, 19, 88, 58, 115, 147, 148, 5, 431, 395, 243, 875, 702, 625, 353, 58, 294, 326, 327, 5, 445, 68, 60, 876, 738, 94, 83, 58, 104, 103, 136, 5, 278, 282, 279, 877, 407, 413, 408, 58, 242, 274, 275, 5, 190, 93, 89, 878, 268, 128, 123, 58, 3, 35, 36, 5, 479, 128, 135, 879, 847, 177, 187, 58, 0, 1, 2, 5, 486, 44, 458, 880, 17, 61, 786, 58, 292, 291, 324, 5, 253, 75, 77, 881, 367, 104, 106, 58, 0, 25, 26, 5, 487, 363, 1, 882, 17, 559, 2, 58, 159, 158, 191, 5, 342, 488, 343, 883, 524, 884, 525, 58, 444, 443, 476, 5, 456, 489, 101, 885, 780, 886, 139, 58, 238, 237, 270, 5, 95, 474, 416, 887, 131, 838, 668, 58, 234, 266, 267, 5, 97, 99, 490, 888, 134, 136, 889, 58, 235, 234, 267, 5, 441, 97, 490, 890, 728, 134, 889, 58, 233, 265, 266, 5, 98, 284, 99, 891, 135, 417, 136, 58, 19, 18, 51, 5, 491, 403, 408, 892, 893, 642, 649, 58, 233, 232, 265, 5, 98, 472, 284, 894, 135, 829, 417, 58, 191, 223, 224, 5, 343, 365, 219, 895, 525, 564, 314, 58, 231, 263, 264, 5, 123, 346, 349, 896, 169, 532, 536, 58, 106, 105, 138, 5, 309, 281, 462, 897, 461, 411, 796, 58, 135, 167, 168, 5, 155, 140, 232, 898, 218, 194, 335, 58, 322, 354, 355, 5, 260, 194, 193, 899, 378, 274, 273, 58, 428, 427, 460, 5, 316, 30, 492, 900, 473, 42, 901, 58, 372, 404, 405, 5, 327, 189, 314, 902, 494, 266, 469, 58, 0, 17, 18, 5, 493, 404, 403, 903, 17, 643, 642, 58, 320, 319, 352, 5, 494, 265, 52, 904, 905, 385, 72, 58, 442, 474, 475, 5, 495, 496, 333, 906, 907, 908, 507, 58, 319, 318, 351, 5, 265, 80, 199, 909, 385, 110, 282, 58, 198, 197, 230, 5, 122, 124, 103, 910, 168, 171, 142, 58, 139, 171, 172, 5, 172, 418, 463, 911, 241, 673, 802, 58, 277, 276, 309, 5, 357, 90, 497, 912, 548, 124, 913, 58, 317, 349, 350, 5, 79, 200, 325, 914, 109, 284, 491, 58, 96, 128, 97, 5, 186, 261, 498, 915, 261, 380, 916, 58, 43, 42, 75, 5, 67, 64, 154, 917, 92, 88, 216, 58, 461, 460, 481, 5, 18, 492, 499, 918, 26, 901, 919, 58, 218, 250, 251, 5, 228, 352, 350, 920, 326, 541, 538, 58, 108, 140, 141, 5, 181, 173, 151, 921, 254, 242, 211, 58, 83, 115, 116, 5, 412, 431, 271, 922, 658, 702, 396, 58, 203, 202, 235, 5, 209, 214, 441, 923, 297, 304, 728, 58, 169, 168, 201, 5, 384, 232, 119, 924, 603, 335, 164, 58, 144, 143, 176, 5, 274, 273, 420, 925, 400, 399, 681, 58, 405, 437, 438, 5, 314, 315, 221, 926, 469, 471, 317, 58, 205, 204, 237, 5, 138, 208, 474, 927, 191, 296, 838, 58, 383, 382, 415, 5, 38, 202, 39, 928, 53, 286, 54, 58, 269, 268, 301, 5, 417, 500, 398, 929, 669, 930, 631, 58, 214, 246, 247, 5, 501, 426, 461, 931, 932, 692, 793, 58, 84, 116, 117, 5, 177, 271, 170, 933, 248, 396, 238, 58, 80, 79, 112, 5, 427, 451, 452, 934, 695, 765, 766, 58, 396, 395, 428, 5, 377, 28, 316, 935, 587, 40, 473, 58, 173, 172, 205, 5, 137, 463, 138, 936, 190, 802, 191, 58, 309, 308, 341, 5, 497, 345, 502, 937, 913, 528, 938, 58, 86, 118, 119, 6, 287, 169, 503, 939, 423, 237, 940, 58, 402, 434, 435, 6, 203, 306, 197, 941, 288, 457, 279, 58, 28, 60, 61, 6, 106, 107, 295, 942, 146, 147, 437, 58, 475, 474, 481, 6, 333, 496, 504, 943, 507, 908, 944, 58, 214, 213, 246, 6, 501, 476, 426, 945, 932, 842, 692, 58, 213, 245, 246, 6, 476, 356, 426, 946, 842, 547, 692, 58, 206, 205, 238, 6, 139, 138, 95, 947, 192, 191, 131, 58, 63, 62, 95, 6, 118, 294, 185, 948, 162, 436, 260, 58, 468, 467, 481, 6, 250, 308, 505, 949, 363, 459, 950, 58, 217, 249, 250, 6, 358, 459, 352, 951, 550, 789, 541, 58, 467, 466, 481, 6, 308, 307, 506, 952, 459, 458, 953, 58, 462, 461, 481, 6, 19, 18, 507, 954, 27, 26, 955, 58, 1, 32, 33, 6, 320, 319, 508, 956, 480, 479, 957, 58, 458, 457, 481, 6, 21, 394, 509, 958, 30, 623, 959, 58, 455, 454, 481, 6, 400, 292, 510, 960, 636, 432, 961, 58, 32, 64, 33, 6, 319, 300, 508, 962, 479, 446, 957, 58, 195, 227, 228, 6, 449, 436, 434, 963, 754, 716, 713, 58, 451, 450, 481, 6, 443, 9, 511, 964, 732, 13, 965, 58, 0, 11, 12, 6, 512, 65, 383, 966, 17, 90, 599, 58, 228, 260, 261, 6, 434, 285, 435, 967, 713, 419, 714, 58, 229, 261, 262, 6, 104, 435, 105, 968, 143, 714, 144, 58, 232, 264, 265, 6, 472, 349, 284, 969, 829, 536, 417, 58, 71, 70, 103, 6, 142, 370, 282, 970, 197, 576, 413, 58, 189, 221, 222, 6, 406, 111, 110, 971, 646, 153, 152, 58, 237, 236, 269, 6, 474, 210, 417, 972, 838, 298, 669, 58, 440, 439, 472, 6, 310, 229, 480, 973, 463, 328, 852, 58, 180, 212, 213, 6, 396, 113, 476, 974, 626, 156, 842, 58, 54, 86, 87, 6, 85, 287, 513, 975, 117, 423, 976, 58, 180, 179, 212, 6, 396, 484, 113, 977, 626, 863, 156, 58, 253, 252, 285, 6, 367, 297, 78, 978, 567, 440, 108, 58, 55, 54, 87, 6, 378, 85, 513, 979, 589, 117, 976, 58, 419, 451, 452, 6, 36, 443, 442, 980, 50, 732, 731, 58, 418, 450, 451, 6, 23, 9, 443, 981, 33, 13, 732, 58, 0, 19, 20, 6, 514, 491, 515, 982, 17, 893, 983, 58, 415, 447, 448, 6, 39, 16, 15, 984, 54, 23, 21, 58, 80, 112, 113, 6, 427, 452, 429, 985, 695, 766, 699, 58, 411, 443, 444, 6, 516, 489, 456, 986, 987, 886, 780, 58, 174, 173, 206, 6, 152, 137, 139, 988, 212, 190, 192, 58, 21, 20, 53, 6, 84, 515, 312, 989, 116, 983, 466, 58, 207, 239, 240, 6, 477, 94, 465, 990, 845, 130, 806, 58, 209, 241, 242, 6, 422, 91, 190, 991, 683, 126, 268, 58, 38, 37, 70, 6, 141, 134, 370, 992, 196, 186, 576, 58, 398, 397, 430, 6, 425, 376, 239, 993, 690, 586, 346, 58, 19, 51, 52, 6, 491, 408, 176, 994, 893, 649, 247, 58, 0, 23, 24, 6, 517, 518, 359, 995, 17, 996, 552, 58, 0, 24, 25, 6, 519, 359, 363, 997, 17, 552, 559, 58, 276, 308, 309, 6, 90, 345, 497, 998, 124, 528, 913, 58, 385, 417, 418, 6, 520, 24, 23, 999, 1000, 34, 33, 58, 283, 315, 316, 6, 351, 521, 82, 1001, 539, 1002, 113, 58, 384, 416, 385, 6, 53, 40, 522, 1003, 73, 55, 1004, 58, 288, 320, 289, 6, 523, 494, 524, 1005, 1006, 905, 1007, 58, 290, 289, 322, 6, 259, 419, 260, 1008, 377, 677, 378, 58, 17, 49, 50, 6, 404, 255, 405, 1009, 643, 370, 644, 58, 379, 378, 411, 6, 440, 439, 516, 1010, 724, 723, 987, 58, 51, 50, 83, 6, 408, 405, 412, 1011, 649, 644, 658, 58, 373, 372, 405, 6, 328, 327, 314, 1012, 495, 494, 469, 58, 3, 2, 35, 6, 479, 458, 128, 1013, 847, 786, 177, 58, 298, 330, 331, 6, 247, 58, 332, 1014, 359, 80, 504, 58, 370, 369, 402, 6, 464, 321, 203, 1015, 804, 482, 288, 58, 367, 366, 399, 6, 206, 205, 467, 1016, 292, 291, 809, 58, 112, 111, 144, 6, 452, 272, 274, 1017, 766, 398, 400, 58, 304, 303, 336, 6, 240, 245, 241, 1018, 349, 356, 350, 58, 147, 179, 180, 6, 395, 484, 396, 1019, 625, 863, 626, 58, 308, 307, 340, 6, 345, 344, 326, 1020, 528, 527, 493, 58, 114, 146, 147, 6, 432, 430, 395, 1021, 703, 700, 625, 58, 355, 387, 388, 6, 193, 35, 382, 1022, 273, 49, 597, 58, 311, 310, 343, 6, 390, 392, 391, 1023, 617, 620, 618, 58, 348, 347, 380, 6, 386, 525, 526, 1024, 609, 1025, 1026, 58, 120, 152, 153, 6, 527, 146, 266, 1027, 1028, 203, 387, 58, 316, 315, 348, 6, 82, 521, 386, 1029, 113, 1002, 609, 58, 143, 142, 175, 6, 273, 153, 336, 1030, 399, 214, 513, 58, 41, 73, 74, 6, 63, 471, 150, 1031, 87, 827, 209, 58, 339, 371, 372, 6, 329, 187, 327, 1032, 497, 264, 494, 58, 322, 321, 354, 6, 260, 446, 194, 1033, 378, 743, 274, 58, 321, 353, 354, 6, 446, 323, 194, 1034, 743, 487, 274, 58, 337, 336, 369, 6, 70, 241, 321, 1035, 97, 350, 482, 58, 6, 38, 39, 6, 56, 141, 57, 1036, 77, 196, 78, 58, 333, 365, 366, 6, 211, 213, 205, 1037, 300, 302, 291, 58, 440, 472, 473, 7, 310, 480, 528, 1038, 463, 852, 1039, 58, 266, 298, 299, 7, 99, 247, 340, 1040, 136, 359, 520, 58, 267, 299, 300, 7, 490, 340, 473, 1041, 889, 520, 831, 58, 453, 452, 481, 7, 291, 442, 529, 1042, 431, 731, 1043, 58, 401, 433, 434, 7, 322, 373, 306, 1044, 483, 580, 457, 58, 89, 88, 121, 7, 475, 379, 167, 1045, 840, 590, 234, 58, 399, 431, 432, 7, 467, 381, 230, 1046, 809, 595, 332, 58, 399, 398, 431, 7, 467, 425, 381, 1047, 809, 690, 595, 58, 236, 268, 269, 7, 210, 500, 417, 1048, 298, 930, 669, 58, 0, 6, 7, 7, 530, 56, 55, 1049, 17, 77, 76, 58, 237, 269, 270, 7, 474, 417, 416, 1050, 838, 669, 668, 58, 396, 428, 429, 7, 377, 316, 17, 1051, 587, 473, 25, 58, 275, 274, 307, 7, 89, 93, 344, 1052, 123, 128, 527, 58, 0, 12, 13, 7, 531, 383, 337, 1053, 17, 599, 515, 58, 0, 13, 14, 7, 532, 337, 387, 1054, 17, 515, 612, 58, 241, 240, 273, 7, 91, 465, 92, 1055, 126, 806, 127, 58, 165, 164, 197, 7, 258, 485, 124, 1056, 374, 873, 171, 58, 391, 423, 424, 7, 51, 399, 380, 1057, 70, 635, 593, 58, 280, 279, 312, 7, 354, 448, 237, 1058, 544, 752, 343, 58, 0, 28, 29, 7, 533, 106, 368, 1059, 17, 146, 569, 58, 436, 435, 468, 7, 198, 197, 250, 1060, 280, 279, 363, 58, 282, 314, 315, 7, 460, 534, 521, 1061, 790, 1062, 1002, 58, 386, 385, 418, 7, 324, 520, 23, 1063, 488, 1000, 33, 58, 34, 66, 67, 7, 46, 289, 129, 1064, 63, 428, 178, 58, 184, 216, 217, 7, 133, 132, 358, 1065, 184, 182, 550, 58, 129, 160, 161, 7, 483, 263, 409, 1066, 861, 382, 652, 58, 18, 50, 51, 7, 403, 405, 408, 1067, 642, 644, 649, 58, 287, 319, 320, 7, 293, 265, 494, 1068, 434, 385, 905, 58, 36, 35, 68, 7, 135, 128, 303, 1069, 187, 177, 453, 58, 433, 432, 465, 7, 373, 230, 231, 1070, 580, 332, 333, 58, 181, 213, 214, 7, 244, 476, 501, 1071, 354, 842, 932, 58, 380, 379, 412, 7, 526, 440, 454, 1072, 1026, 724, 773, 58, 57, 89, 90, 7, 361, 475, 457, 1073, 554, 840, 782, 58, 227, 226, 259, 7, 436, 192, 86, 1074, 716, 271, 119, 58, 296, 295, 328, 7, 348, 347, 216, 1075, 534, 533, 310, 58, 374, 406, 407, 7, 413, 313, 402, 1076, 660, 468, 640, 58, 373, 405, 406, 7, 328, 314, 313, 1077, 495, 469, 468, 58, 250, 249, 282, 7, 352, 459, 460, 1078, 541, 789, 790, 58, 430, 429, 462, 7, 239, 17, 19, 1079, 346, 25, 27, 58, 297, 329, 330, 7, 248, 59, 58, 1080, 360, 81, 80, 58, 196, 195, 228, 7, 125, 449, 434, 1081, 172, 754, 713, 58, 371, 370, 403, 7, 187, 464, 188, 1082, 264, 804, 265, 58, 252, 251, 284, 7, 297, 350, 81, 1083, 440, 538, 112, 58, 300, 332, 333, 7, 473, 212, 211, 1084, 831, 301, 300, 58, 149, 181, 182, 7, 242, 244, 339, 1085, 352, 354, 518, 58, 427, 459, 460, 7, 30, 22, 492, 1086, 42, 31, 901, 58, 69, 68, 101, 7, 136, 303, 305, 1087, 188, 453, 455, 58, 74, 73, 106, 7, 150, 471, 309, 1088, 209, 827, 461, 58, 177, 176, 209, 7, 389, 420, 422, 1089, 615, 681, 683, 58, 114, 113, 146, 7, 432, 429, 430, 1090, 703, 699, 700, 58, 359, 391, 392, 7, 61, 51, 225, 1091, 84, 70, 322, 58, 427, 426, 459, 7, 30, 20, 22, 1092, 42, 29, 31, 58, 205, 237, 238, 7, 138, 474, 95, 1093, 191, 838, 131, 58, 359, 358, 391, 7, 61, 49, 51, 1094, 84, 68, 70, 58, 77, 109, 110, 7, 159, 276, 275, 1095, 223, 403, 402, 58, 356, 355, 388, 7, 369, 193, 382, 1096, 573, 273, 597, 58, 70, 69, 102, 7, 370, 136, 371, 1097, 576, 188, 577, 58, 354, 386, 387, 7, 194, 324, 35, 1098, 274, 488, 49, 58, 47, 79, 80, 7, 73, 451, 427, 1099, 101, 765, 695, 58, 119, 118, 151, 7, 503, 169, 148, 1100, 940, 237, 206, 58, 351, 383, 384, 7, 199, 38, 53, 1101, 282, 53, 73, 58, 314, 313, 346, 7, 534, 234, 236, 1102, 1062, 339, 341, 58, 119, 151, 152, 7, 503, 148, 146, 1103, 940, 206, 203, 58, 58, 90, 91, 7, 364, 457, 174, 1104, 560, 782, 244, 58, 161, 192, 193, 7, 409, 218, 220, 1105, 652, 313, 315, 58, 30, 62, 63, 7, 117, 294, 118, 1106, 161, 436, 162, 58, 474, 473, 481, 7, 496, 528, 535, 1107, 908, 1039, 1108, 58, 261, 260, 293, 7, 435, 285, 252, 1109, 714, 419, 366, 58, 20, 52, 53, 7, 515, 176, 312, 1110, 983, 247, 466, 58, 338, 337, 370, 7, 71, 70, 464, 1111, 98, 97, 804, 58, 412, 444, 445, 7, 454, 456, 438, 1112, 773, 780, 720, 58, 191, 190, 223, 7, 343, 407, 365, 1113, 525, 647, 564, 58, 220, 219, 252, 7, 127, 362, 297, 1114, 175, 556, 440, 58, 336, 335, 368, 7, 241, 246, 375, 1115, 350, 357, 584, 58, 324, 323, 356, 7, 77, 76, 369, 1116, 106, 105, 573, 58, 411, 410, 443, 7, 516, 536, 489, 1117, 987, 1118, 886, 58, 443, 442, 475, 7, 489, 495, 333, 1119, 886, 907, 507, 58, 334, 333, 366, 7, 204, 211, 205, 1120, 290, 300, 291, 58, 126, 125, 158, 7, 317, 283, 488, 1121, 475, 415, 884, 58, 132, 164, 165, 7, 257, 485, 258, 1122, 373, 873, 374, 58, 127, 126, 159, 7, 262, 317, 342, 1123, 381, 475, 524, 58, 126, 158, 159, 7, 317, 488, 342, 1124, 475, 884, 524, 58, 127, 159, 160, 7, 262, 342, 263, 1125, 381, 524, 382, 58, 131, 163, 164, 7, 160, 162, 485, 1126, 225, 227, 873, 58, 130, 129, 162, 7, 161, 298, 299, 1127, 226, 442, 443, 58, 99, 131, 132, 8, 290, 160, 257, 1128, 429, 225, 373, 58, 431, 463, 464, 8, 381, 7, 6, 1129, 595, 10, 9, 58, 380, 412, 413, 8, 526, 454, 26, 1130, 1026, 773, 37, 58, 157, 156, 189, 8, 374, 165, 406, 1131, 582, 231, 646, 58, 409, 441, 442, 8, 43, 311, 495, 1132, 59, 464, 907, 58, 292, 324, 325, 8, 253, 77, 233, 1133, 367, 106, 337, 58, 172, 171, 204, 8, 463, 418, 208, 1134, 802, 673, 296, 58, 378, 377, 410, 8, 439, 41, 536, 1135, 723, 57, 1118, 58, 102, 134, 135, 8, 371, 156, 155, 1136, 577, 219, 218, 58, 81, 113, 114, 8, 428, 429, 432, 1137, 696, 699, 703, 58, 264, 296, 297, 8, 349, 348, 248, 1138, 536, 534, 360, 58, 190, 189, 222, 8, 407, 406, 110, 1139, 647, 646, 152, 58, 406, 438, 439, 8, 313, 221, 229, 1140, 468, 317, 328, 58, 182, 181, 214, 8, 339, 244, 501, 1141, 518, 354, 932, 58, 215, 214, 247, 8, 131, 501, 461, 1142, 181, 932, 793, 58, 87, 119, 120, 8, 513, 503, 527, 1143, 976, 940, 1028, 58, 49, 81, 82, 8, 255, 428, 411, 1144, 370, 696, 657, 58, 208, 240, 241, 8, 421, 465, 91, 1145, 682, 806, 126, 58, 61, 60, 93, 8, 295, 107, 537, 1146, 437, 147, 1147, 58, 299, 331, 332, 8, 340, 332, 212, 1148, 520, 504, 301, 58, 268, 300, 301, 8, 500, 473, 398, 1149, 930, 831, 631, 58, 0, 22, 23, 8, 538, 83, 518, 1150, 17, 115, 996, 58, 460, 459, 481, 8, 492, 22, 539, 1151, 901, 31, 1152, 58, 61, 93, 94, 8, 295, 537, 296, 1153, 437, 1147, 438, 58, 303, 302, 335, 8, 245, 397, 246, 1154, 356, 630, 357, 58, 429, 428, 461, 8, 17, 316, 18, 1155, 25, 473, 26, 58, 195, 194, 227, 8, 449, 145, 436, 1156, 754, 201, 716, 58, 26, 25, 58, 8, 1, 363, 364, 1157, 2, 559, 560, 58, 362, 361, 394, 8, 48, 217, 29, 1158, 66, 311, 41, 58, 238, 270, 271, 8, 95, 416, 96, 1159, 131, 668, 132, 58, 272, 304, 305, 8, 415, 240, 69, 1160, 666, 349, 96, 58, 360, 359, 392, 8, 62, 61, 225, 1161, 85, 84, 322, 58, 307, 306, 339, 8, 344, 277, 329, 1162, 527, 405, 497, 58, 306, 338, 339, 8, 277, 71, 329, 1163, 405, 98, 497, 58, 193, 225, 226, 8, 144, 108, 192, 1164, 200, 149, 271, 58, 307, 339, 340, 8, 344, 329, 326, 1165, 527, 497, 493, 58, 232, 231, 264, 8, 472, 123, 349, 1166, 829, 169, 536, 58, 0, 2, 3, 8, 540, 458, 479, 1167, 17, 786, 847, 58, 90, 122, 123, 8, 457, 166, 163, 1168, 782, 233, 229, 58, 426, 425, 458, 8, 20, 226, 21, 1169, 29, 323, 30, 58, 437, 436, 469, 8, 315, 198, 249, 1170, 471, 280, 362, 58, 93, 92, 125, 8, 537, 179, 283, 1171, 1147, 251, 415, 58, 242, 241, 274, 8, 190, 91, 93, 1172, 268, 126, 128, 58, 255, 287, 288, 8, 437, 293, 523, 1173, 718, 434, 1006, 58, 313, 312, 345, 8, 234, 237, 235, 1174, 339, 343, 340, 58, 185, 217, 218, 8, 147, 358, 228, 1175, 204, 550, 326, 58, 388, 420, 421, 8, 382, 37, 33, 1176, 597, 51, 46, 58, 162, 194, 195, 8, 299, 145, 449, 1177, 443, 201, 754, 58, 420, 419, 452, 8, 37, 36, 442, 1178, 51, 50, 731, 58, 192, 191, 224, 8, 218, 343, 219, 1179, 313, 525, 314, 58, 95, 127, 128, 8, 185, 262, 261, 1180, 260, 381, 380, 58, 345, 344, 377, 8, 235, 238, 41, 1181, 340, 344, 57, 58, 343, 375, 376, 8, 391, 286, 42, 1182, 618, 421, 58, 58, 65, 96, 97, 8, 301, 186, 498, 1183, 447, 261, 916, 58, 282, 281, 314, 8, 460, 355, 534, 1184, 790, 545, 1062, 58, 141, 140, 173, 8, 151, 173, 137, 1185, 211, 242, 190, 58, 342, 341, 374, 8, 393, 502, 413, 1186, 621, 938, 660, 58, 138, 170, 171, 8, 462, 385, 418, 1187, 796, 606, 673, 58, 139, 138, 171, 8, 172, 462, 418, 1188, 241, 796, 673, 58, 174, 206, 207, 8, 152, 139, 477, 1189, 212, 192, 845, 58, 283, 282, 315, 8, 351, 460, 521, 1190, 539, 790, 1002, 58, 210, 209, 242, 8, 207, 422, 190, 1191, 294, 683, 268, 58, 23, 22, 55, 8, 518, 83, 378, 1192, 996, 115, 589, 58, 217, 216, 249, 8, 358, 132, 459, 1193, 550, 182, 789, 58, 33, 64, 65, 8, 508, 300, 301, 1194, 957, 446, 447, 58, 435, 434, 467, 8, 197, 306, 308, 1195, 279, 457, 459, 58, 20, 19, 52, 8, 515, 491, 176, 1196, 983, 893, 247, 58, 67, 99, 100, 8, 129, 290, 304, 1197, 178, 429, 454, 58, 71, 103, 104, 8, 142, 282, 278, 1198, 197, 413, 407, 58, 0, 29, 30, 8, 541, 368, 117, 1199, 17, 569, 161, 58, 99, 98, 131, 8, 290, 184, 160, 1200, 429, 258, 225, 58, 288, 287, 320, 8, 523, 293, 494, 1201, 1006, 434, 905, 58, 182, 214, 215, 8, 339, 501, 131, 1202, 518, 932, 181, 58, 257, 288, 289, 8, 542, 523, 524, 1203, 1204, 1006, 1007, 58, 0, 14, 15, 8, 543, 387, 72, 1205, 17, 612, 100, 58, 228, 227, 260, 8, 434, 436, 285, 1206, 713, 716, 419, 58, 98, 130, 131, 8, 184, 161, 160, 1207, 258, 226, 225, 58, 226, 258, 259, 9, 192, 87, 86, 1208, 271, 120, 119, 58, 0, 18, 19, 9, 544, 403, 491, 1209, 17, 642, 893, 58, 11, 43, 44, 9, 65, 67, 158, 1210, 90, 92, 222, 58, 134, 133, 166, 9, 156, 256, 414, 1211, 219, 372, 663, 58, 325, 324, 357, 9, 233, 77, 335, 1212, 337, 106, 510, 58, 212, 244, 245, 9, 113, 115, 356, 1213, 156, 158, 547, 58, 137, 136, 169, 9, 280, 279, 384, 1214, 409, 408, 603, 58, 320, 352, 321, 9, 494, 52, 433, 1215, 905, 72, 708, 58, 340, 339, 372, 9, 326, 329, 327, 1216, 493, 497, 494, 58, 319, 351, 352, 9, 265, 199, 52, 1217, 385, 282, 72, 58, 341, 373, 374, 9, 502, 328, 413, 1218, 938, 495, 660, 58, 342, 374, 375, 9, 393, 413, 286, 1219, 621, 660, 421, 58, 344, 343, 376, 9, 238, 391, 42, 1220, 344, 618, 58, 58, 314, 346, 347, 9, 534, 236, 525, 1221, 1062, 341, 1025, 58, 12, 44, 45, 9, 383, 158, 157, 1222, 599, 222, 221, 58, 348, 380, 381, 9, 386, 526, 201, 1223, 609, 1026, 285, 58, 116, 148, 149, 9, 271, 243, 242, 1224, 396, 353, 352, 58, 116, 115, 148, 9, 271, 431, 243, 1225, 396, 702, 353, 58, 306, 305, 338, 9, 277, 69, 71, 1226, 405, 96, 98, 58, 456, 455, 481, 9, 401, 400, 545, 1227, 637, 636, 1228, 58, 196, 228, 229, 9, 125, 434, 104, 1229, 172, 713, 143, 58, 366, 398, 399, 9, 205, 425, 467, 1230, 291, 690, 809, 58, 368, 367, 400, 9, 375, 206, 372, 1231, 584, 292, 579, 58, 109, 141, 142, 9, 276, 151, 153, 1232, 403, 211, 214, 58, 40, 39, 72, 9, 215, 57, 288, 1233, 306, 78, 426, 58, 109, 108, 141, 9, 276, 181, 151, 1234, 403, 254, 211, 58, 16, 15, 48, 9, 254, 72, 74, 1235, 369, 100, 102, 58, 55, 87, 88, 9, 378, 513, 379, 1236, 589, 976, 590, 58, 0, 7, 8, 9, 546, 55, 13, 1237, 17, 76, 18, 58, 375, 374, 407, 9, 286, 413, 402, 1238, 421, 660, 640, 58, 155, 187, 188, 9, 164, 341, 126, 1239, 230, 522, 174, 58, 100, 132, 133, 9, 304, 257, 256, 1240, 454, 373, 372, 58, 97, 128, 129, 9, 498, 261, 483, 1241, 916, 380, 861, 58, 68, 67, 100, 9, 303, 129, 304, 1242, 453, 178, 454, 58, 43, 75, 76, 9, 67, 154, 180, 1243, 92, 216, 253, 58, 163, 162, 195, 9, 162, 299, 449, 1244, 227, 443, 754, 58, 387, 386, 419, 9, 35, 324, 36, 1245, 49, 488, 50, 58, 69, 101, 102, 9, 136, 305, 371, 1246, 188, 455, 577, 58, 389, 388, 421, 9, 32, 382, 33, 1247, 45, 597, 46, 58, 390, 389, 422, 9, 50, 32, 34, 1248, 69, 45, 47, 58, 96, 95, 128, 9, 186, 185, 261, 1249, 261, 260, 380, 58, 390, 422, 423, 9, 50, 34, 399, 1250, 69, 47, 635, 58, 441, 473, 474, 9, 311, 528, 496, 1251, 464, 1039, 908, 58, 235, 267, 268, 9, 441, 490, 500, 1252, 728, 889, 930, 58, 94, 93, 126, 9, 296, 537, 317, 1253, 438, 1147, 475, 58, 164, 196, 197, 9, 485, 125, 124, 1254, 873, 172, 171, 58, 165, 197, 198, 9, 258, 124, 122, 1255, 374, 171, 168, 58, 53, 52, 85, 9, 312, 176, 178, 1256, 466, 247, 249, 58, 274, 306, 307, 9, 93, 277, 344, 1257, 128, 405, 527, 58, 130, 162, 163, 9, 161, 299, 162, 1258, 226, 443, 227, 58, 170, 169, 202, 9, 385, 384, 214, 1259, 606, 603, 304, 58, 90, 89, 122, 9, 457, 475, 166, 1260, 782, 840, 233, 58, 189, 188, 221, 9, 406, 126, 111, 1261, 646, 174, 153, 58, 85, 84, 117, 9, 178, 177, 170, 1262, 249, 248, 238, 58, 265, 297, 298, 9, 284, 248, 247, 1263, 417, 360, 359, 58, 407, 439, 440, 9, 402, 229, 310, 1264, 640, 328, 463, 58, 83, 82, 115, 9, 412, 411, 431, 1265, 658, 657, 702, 58, 410, 409, 442, 9, 536, 43, 495, 1266, 1118, 59, 907, 58, 172, 204, 205, 9, 463, 208, 138, 1267, 802, 296, 191, 58, 244, 276, 277, 9, 115, 90, 357, 1268, 158, 124, 548, 58, 82, 81, 114, 9, 411, 428, 432, 1269, 657, 696, 703, 58, 434, 433, 466, 9, 306, 373, 307, 1270, 457, 580, 458, 58, 259, 291, 292, 9, 86, 75, 253, 1271, 119, 104, 367, 58, 73, 72, 105, 9, 471, 288, 281, 1272, 827, 426, 411, 58, 0, 15, 16, 9, 547, 72, 254, 1273, 17, 100, 369, 58, 181, 180, 213, 9, 244, 396, 476, 1274, 354, 626, 842, 58, 428, 460, 461, 9, 316, 492, 18, 1275, 473, 901, 26, 58, 293, 325, 326, 10, 252, 233, 68, 1276, 366, 337, 94, 58, 378, 410, 411, 10, 439, 536, 516, 1277, 723, 1118, 987, 58, 17, 16, 49, 10, 404, 254, 255, 1278, 643, 369, 370, 58, 379, 411, 412, 10, 440, 516, 454, 1279, 724, 987, 773, 58, 158, 157, 190, 10, 488, 374, 407, 1280, 884, 582, 647, 58, 27, 59, 60, 10, 0, 2, 107, 1281, 1, 3, 147, 58, 100, 99, 132, 10, 304, 290, 257, 1282, 454, 429, 373, 58, 49, 48, 81, 10, 255, 74, 428, 1283, 370, 102, 696, 58, 194, 226, 227, 10, 145, 192, 436, 1284, 201, 271, 716, 58, 444, 476, 477, 10, 456, 101, 100, 1285, 780, 139, 138, 58, 424, 456, 457, 10, 380, 401, 394, 1286, 593, 637, 623, 58, 357, 389, 390, 10, 335, 32, 50, 1287, 510, 45, 69, 58, 459, 458, 481, 10, 22, 21, 548, 1288, 31, 30, 1289, 58, 190, 222, 223, 10, 407, 110, 365, 1290, 647, 152, 564, 58, 197, 229, 230, 10, 124, 104, 103, 1291, 171, 143, 142, 58, 164, 163, 196, 10, 485, 162, 125, 1292, 873, 227, 172, 58, 337, 369, 370, 10, 70, 321, 464, 1293, 97, 482, 804, 58, 15, 14, 47, 10, 72, 387, 73, 1294, 100, 612, 101, 58, 305, 304, 337, 10, 69, 240, 70, 1295, 96, 349, 97, 58, 223, 222, 255, 10, 365, 110, 437, 1296, 564, 152, 718, 58, 362, 394, 395, 10, 48, 29, 28, 1297, 66, 41, 40, 58, 93, 125, 126, 10, 537, 283, 317, 1298, 1147, 415, 475, 58, 364, 363, 396, 10, 331, 47, 377, 1299, 502, 65, 587, 58, 200, 232, 233, 10, 120, 472, 98, 1300, 165, 829, 135, 58, 338, 370, 371, 10, 71, 464, 187, 1301, 98, 804, 264, 58, 301, 333, 334, 10, 398, 211, 204, 1302, 631, 300, 290, 58, 347, 346, 379, 10, 525, 236, 440, 1303, 1025, 341, 724, 58, 315, 314, 347, 10, 521, 534, 525, 1304, 1002, 1062, 1025, 58, 273, 272, 305, 10, 92, 415, 69, 1305, 127, 666, 96, 58, 240, 239, 272, 10, 465, 94, 415, 1306, 806, 130, 666, 58, 347, 379, 380, 10, 525, 440, 526, 1307, 1025, 724, 1026, 58, 369, 368, 401, 10, 321, 375, 322, 1308, 482, 584, 483, 58, 66, 65, 98, 10, 289, 182, 184, 1309, 428, 256, 258, 58, 437, 469, 470, 10, 315, 249, 222, 1310, 471, 362, 318, 58, 171, 170, 203, 10, 418, 385, 209, 1311, 673, 606, 297, 58, 268, 267, 300, 10, 500, 490, 473, 1312, 930, 889, 831, 58, 88, 87, 120, 10, 379, 513, 527, 1313, 590, 976, 1028, 58, 125, 157, 158, 10, 283, 374, 488, 1314, 415, 582, 884, 58, 12, 11, 44, 10, 383, 65, 158, 1315, 599, 90, 222, 58, 72, 71, 104, 10, 288, 142, 278, 1316, 426, 197, 407, 58, 154, 153, 186, 10, 168, 266, 227, 1317, 235, 387, 325, 58, 350, 349, 382, 10, 325, 200, 202, 1318, 491, 284, 286, 58, 265, 264, 297, 10, 284, 349, 248, 1319, 417, 536, 360, 58, 351, 350, 383, 10, 199, 325, 38, 1320, 282, 491, 53, 58, 120, 119, 152, 10, 527, 503, 146, 1321, 1028, 940, 203, 58, 410, 442, 443, 10, 536, 495, 489, 1322, 1118, 907, 886, 58, 64, 63, 96, 10, 300, 118, 186, 1323, 446, 162, 261, 58, 261, 293, 294, 10, 435, 252, 445, 1324, 714, 366, 738, 58, 295, 327, 328, 10, 347, 60, 216, 1325, 533, 83, 310, 58, 156, 155, 188, 10, 165, 164, 126, 1326, 231, 230, 174, 58, 0, 20, 21, 10, 549, 515, 84, 1327, 17, 983, 116, 58, 245, 277, 278, 10, 356, 357, 410, 1328, 547, 548, 655, 58, 247, 246, 279, 10, 461, 426, 448, 1329, 793, 692, 752, 58, 144, 176, 177, 10, 274, 420, 389, 1330, 400, 681, 615, 58, 81, 80, 113, 10, 428, 427, 429, 1331, 696, 695, 699, 58, 117, 149, 150, 10, 170, 242, 149, 1332, 238, 352, 207, 58, 0, 31, 32, 10, 550, 116, 319, 1333, 17, 160, 479, 58, 79, 78, 111, 10, 451, 330, 272, 1334, 765, 500, 398, 58, 419, 418, 451, 10, 36, 23, 443, 1335, 50, 33, 732, 58, 57, 56, 89, 10, 361, 360, 475, 1336, 554, 553, 840, 58, 377, 409, 410, 10, 41, 43, 536, 1337, 57, 59, 1118, 58, 335, 367, 368, 10, 246, 206, 375, 1338, 357, 292, 584, 58, 73, 105, 106, 10, 471, 281, 309, 1339, 827, 411, 461, 58, 44, 76, 77, 11, 158, 180, 159, 1340, 222, 253, 223, 58, 33, 65, 66, 11, 45, 182, 289, 1341, 62, 256, 428, 58, 308, 340, 341, 11, 345, 326, 502, 1342, 528, 493, 938, 58, 29, 61, 62, 11, 368, 295, 294, 1343, 569, 437, 436, 58, 54, 53, 86, 11, 85, 312, 287, 1344, 117, 466, 423, 58, 341, 340, 373, 11, 502, 326, 328, 1345, 938, 493, 495, 58, 46, 78, 79, 11, 338, 330, 451, 1346, 516, 500, 765, 58, 140, 139, 172, 11, 173, 172, 463, 1347, 242, 241, 802, 58, 360, 392, 393, 11, 62, 225, 224, 1348, 85, 322, 321, 58, 207, 206, 239, 11, 477, 139, 94, 1349, 845, 192, 130, 58, 361, 393, 394, 11, 217, 224, 29, 1350, 311, 321, 41, 58, 5, 37, 38, 11, 195, 134, 141, 1351, 276, 186, 196, 58, 270, 302, 303, 11, 416, 397, 245, 1352, 668, 630, 356, 58, 88, 120, 121, 11, 379, 527, 167, 1353, 590, 1028, 234, 58, 400, 432, 433, 11, 372, 230, 373, 1354, 579, 332, 580, 58, 271, 270, 303, 11, 96, 416, 245, 1355, 132, 668, 356, 58, 343, 342, 375, 11, 391, 393, 286, 1356, 618, 621, 421, 58, 169, 201, 202, 11, 384, 119, 214, 1357, 603, 164, 304, 58, 471, 470, 481, 11, 223, 222, 551, 1358, 319, 318, 1359, 58, 32, 31, 64, 11, 319, 116, 300, 1360, 479, 160, 446, 58, 381, 380, 413, 11, 201, 526, 26, 1361, 285, 1026, 37, 58, 405, 404, 437, 11, 314, 189, 315, 1362, 469, 266, 471, 58, 436, 468, 469, 11, 198, 250, 249, 1363, 280, 363, 362, 58, 435, 467, 468, 11, 197, 308, 250, 1364, 279, 459, 363, 58, 365, 364, 397, 11, 213, 331, 376, 1365, 302, 502, 586, 58, 25, 24, 57, 11, 363, 359, 361, 1366, 559, 552, 554, 58, 330, 362, 363, 11, 58, 48, 47, 1367, 80, 66, 65, 58, 112, 144, 145, 11, 452, 274, 388, 1368, 766, 400, 614, 58, 158, 190, 191, 11, 488, 407, 343, 1369, 884, 647, 525, 58, 40, 72, 73, 11, 215, 288, 471, 1370, 306, 426, 827, 58, 47, 46, 79, 11, 73, 338, 451, 1371, 101, 516, 765, 58, 315, 347, 348, 11, 521, 525, 386, 1372, 1002, 1025, 609, 58, 353, 384, 385, 11, 54, 53, 522, 1373, 74, 73, 1004, 58, 262, 294, 295, 11, 105, 445, 347, 1374, 144, 738, 533, 58, 26, 58, 59, 11, 1, 364, 2, 1375, 2, 560, 3, 58, 56, 88, 89, 11, 360, 379, 475, 1376, 553, 590, 840, 58, 281, 313, 314, 11, 355, 234, 534, 1377, 545, 339, 1062, 58, 31, 63, 64, 11, 116, 118, 300, 1378, 160, 162, 446, 58, 346, 345, 378, 11, 236, 235, 439, 1379, 341, 340, 723, 58, 281, 280, 313, 11, 355, 354, 234, 1380, 545, 544, 339, 58, 368, 400, 401, 11, 375, 372, 322, 1381, 584, 579, 483, 58, 150, 182, 183, 11, 149, 339, 130, 1382, 207, 518, 180, 58, 146, 145, 178, 11, 430, 388, 424, 1383, 700, 614, 687, 58, 309, 341, 342, 11, 497, 502, 393, 1384, 913, 938, 621, 58, 246, 278, 279, 11, 426, 410, 448, 1385, 692, 655, 752, 58, 310, 309, 342, 11, 392, 497, 393, 1386, 620, 913, 621, 58, 391, 390, 423, 11, 51, 50, 399, 1387, 70, 69, 635, 58, 258, 290, 291, 11, 87, 259, 75, 1388, 120, 377, 104, 58, 466, 465, 481, 11, 307, 231, 552, 1389, 458, 333, 1390, 58, 356, 388, 389, 11, 369, 382, 32, 1391, 573, 597, 45, 58, 277, 309, 310, 11, 357, 497, 392, 1392, 548, 913, 620, 58, 256, 288, 257, 11, 366, 523, 542, 1393, 565, 1006, 1204, 58, 225, 256, 257, 11, 553, 366, 542, 1394, 1395, 565, 1204, 58, 0, 16, 17, 11, 554, 254, 404, 1396, 17, 369, 643, 58, 124, 156, 157, 11, 175, 165, 374, 1397, 245, 231, 582, 58, 250, 282, 283, 11, 352, 460, 351, 1398, 541, 790, 539, 58, 121, 120, 153, 11, 167, 527, 266, 1399, 234, 1028, 387, 58, 78, 77, 110, 11, 330, 159, 275, 1400, 500, 223, 402, 58, 76, 108, 109, 11, 180, 181, 276, 1401, 253, 254, 403, 58, 105, 137, 138, 11, 281, 280, 462, 1402, 411, 409, 796, 58, 75, 107, 108, 11, 154, 171, 181, 1403, 216, 240, 254, 58, 175, 207, 208, 11, 336, 477, 421, 1404, 513, 845, 682, 58, 155, 154, 187, 11, 164, 168, 341, 1405, 230, 235, 522, 58, 255, 254, 287, 11, 437, 112, 293, 1406, 718, 154, 434, 58, 75, 74, 107, 11, 154, 150, 171, 1407, 216, 209, 240, 58, 253, 285, 286, 11, 367, 78, 264, 1408, 567, 108, 384, 58, 0, 30, 31, 12, 555, 117, 116, 1409, 17, 161, 160, 58, 424, 423, 456, 12, 380, 399, 401, 1410, 593, 635, 637, 58, 178, 177, 210, 12, 424, 389, 207, 1411, 687, 615, 294, 58, 256, 255, 288, 12, 366, 437, 523, 1412, 565, 718, 1006, 58, 420, 452, 453, 12, 37, 442, 291, 1413, 51, 731, 431, 58, 179, 211, 212, 12, 484, 114, 113, 1414, 863, 157, 156, 58, 421, 420, 453, 12, 33, 37, 291, 1415, 46, 51, 431, 58, 176, 175, 208, 12, 420, 336, 421, 1416, 681, 513, 682, 58, 78, 110, 111, 12, 330, 275, 272, 1417, 500, 402, 398, 58, 385, 416, 417, 12, 522, 40, 450, 1418, 1004, 55, 761, 58, 175, 174, 207, 12, 336, 152, 477, 1419, 513, 212, 845, 58, 415, 414, 447, 12, 39, 25, 16, 1420, 54, 36, 23, 58, 413, 445, 446, 12, 26, 438, 27, 1421, 37, 720, 38, 58, 59, 58, 91, 12, 2, 364, 174, 1422, 3, 560, 244, 58, 412, 411, 444, 12, 454, 516, 456, 1423, 773, 987, 780, 58, 23, 55, 56, 12, 518, 378, 360, 1424, 996, 589, 553, 58, 84, 83, 116, 12, 177, 412, 271, 1425, 248, 658, 396, 58, 407, 406, 439, 12, 402, 313, 229, 1426, 640, 468, 328, 58, 87, 86, 119, 12, 513, 287, 503, 1427, 976, 423, 940, 58, 186, 218, 219, 12, 227, 228, 362, 1428, 325, 326, 556, 58, 267, 266, 299, 12, 490, 99, 340, 1429, 889, 136, 520, 58, 438, 437, 470, 12, 221, 315, 222, 1430, 317, 471, 318, 58, 269, 301, 302, 12, 417, 398, 397, 1431, 669, 631, 630, 58, 400, 399, 432, 12, 372, 467, 230, 1432, 579, 809, 332, 58, 397, 429, 430, 12, 376, 17, 239, 1433, 586, 25, 346, 58, 166, 165, 198, 12, 414, 258, 122, 1434, 663, 374, 168, 58, 439, 471, 472, 12, 229, 223, 480, 1435, 328, 319, 852, 58, 441, 440, 473, 12, 311, 310, 528, 1436, 464, 463, 1039, 58, 95, 94, 127, 12, 185, 296, 262, 1437, 260, 438, 381, 58, 442, 441, 474, 12, 495, 311, 496, 1438, 907, 464, 908, 58, 392, 391, 424, 12, 225, 51, 380, 1439, 322, 70, 593, 58, 236, 235, 268, 12, 210, 441, 500, 1440, 298, 728, 930, 58, 279, 311, 312, 12, 448, 390, 237, 1441, 752, 617, 343, 58, 280, 312, 313, 12, 354, 237, 234, 1442, 544, 343, 339, 58, 443, 475, 476, 12, 489, 333, 101, 1443, 886, 507, 139, 58, 38, 70, 71, 12, 141, 370, 142, 1444, 196, 576, 197, 58, 287, 286, 319, 12, 293, 264, 265, 1445, 434, 384, 385, 58, 446, 445, 478, 12, 27, 438, 269, 1446, 38, 720, 393, 58, 24, 23, 56, 12, 359, 518, 360, 1447, 552, 996, 553, 58, 160, 159, 192, 12, 263, 342, 218, 1448, 382, 524, 313, 58, 446, 478, 479, 12, 27, 269, 268, 1449, 38, 393, 392, 58, 291, 290, 323, 12, 75, 259, 76, 1450, 104, 377, 105, 58, 294, 293, 326, 12, 445, 252, 68, 1451, 738, 366, 94, 58, 417, 448, 449, 12, 450, 15, 3, 1452, 761, 21, 5, 58, 66, 98, 99, 12, 289, 184, 290, 1453, 428, 258, 429, 58, 107, 106, 139, 12, 171, 309, 172, 1454, 240, 461, 241, 58, 224, 256, 225, 12, 219, 366, 553, 1455, 314, 565, 1395, 58, 296, 328, 329, 12, 348, 216, 59, 1456, 534, 310, 81, 58, 193, 224, 225, 12, 220, 219, 553, 1457, 315, 314, 1395, 58, 50, 49, 82, 12, 405, 255, 411, 1458, 644, 370, 657, 58, 364, 396, 397, 12, 331, 377, 376, 1459, 502, 587, 586, 58, 363, 395, 396, 12, 47, 28, 377, 1460, 65, 40, 587, 58, 2, 34, 35, 12, 458, 46, 128, 1461, 786, 63, 177, 58, 146, 178, 179, 12, 430, 424, 484, 1462, 700, 687, 863, 58, 147, 146, 179, 12, 395, 430, 484, 1463, 625, 700, 863, 58, 353, 385, 386, 12, 323, 520, 324, 1464, 487, 1000, 488, 58, 312, 311, 344, 12, 237, 390, 238, 1465, 343, 617, 344, 58, 200, 199, 232, 12, 120, 121, 472, 1466, 165, 167, 829, 58, 143, 175, 176, 12, 273, 336, 420, 1467, 399, 513, 681, 58, 48, 47, 80, 12, 74, 73, 427, 1468, 102, 101, 695, 58, 465, 464, 481, 12, 231, 6, 556, 1469, 333, 9, 1470, 58, 345, 377, 378, 12, 235, 41, 439, 1471, 340, 57, 723, 58, 317, 316, 349, 12, 79, 82, 200, 1472, 109, 113, 284, 58, 218, 217, 250, 12, 228, 358, 352, 1473, 326, 550, 541, 58, 0, 5, 6, 12, 557, 195, 56, 1474, 17, 276, 77, 58, 62, 94, 95, 12, 294, 296, 185, 1475, 436, 438, 260, 58, 289, 320, 321, 12, 524, 494, 433, 1476, 1007, 905, 708, 58, 138, 137, 170, 12, 462, 280, 385, 1477, 796, 409, 606, 58, 473, 472, 481, 12, 528, 480, 558, 1478, 1039, 852, 1479, 58, 203, 235, 236, 12, 209, 441, 210, 1480, 297, 728, 298, 58, 134, 166, 167, 12, 156, 414, 140, 1481, 219, 663, 194, 58, 335, 334, 367, 12, 246, 204, 206, 1482, 357, 290, 292, 58, 44, 43, 76, 12, 158, 67, 180, 1483, 222, 92, 253, 58, 60, 92, 93, 12, 107, 179, 537, 1484, 147, 251, 1147, 58, 206, 238, 239, 12, 139, 95, 94, 1485, 192, 131, 130, 58, 209, 208, 241, 12, 422, 421, 91, 1486, 683, 682, 126, 58, 167, 166, 199, 12, 140, 414, 121, 1487, 194, 663, 167]}}], "materials": [{"uuid": "0D636E31-83BA-40AD-AE8B-52F08BFC9A7D", "type": "MeshLambertMaterial", "color": 16777215, "emissive": 0, "map": "00B7C61C-9502-4342-A762-9E239A7D91BE", "transparent": true, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "4C3BE4BA-C675-48FA-A726-9074C85B93E2", "type": "MeshLambertMaterial", "color": 14540253, "emissive": 0, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "6D52D811-A23F-40C4-8838-6F65B0E11927", "type": "MeshPhongMaterial", "color": 14540253, "emissive": 0, "specular": 39168, "shininess": 30, "flatShading": true, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "10CE8E49-125D-4968-9724-D6D93097F5B1", "type": "MeshNormalMaterial", "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "E134A5CB-5F22-476E-9E97-DF74153B544C", "type": "MeshBasicMaterial", "color": 16755200, "blending": 2, "transparent": true, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "D7FA74C4-9052-42B7-8650-3938C1430071", "type": "MeshLambertMaterial", "color": 14540253, "emissive": 0, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "A5A7CB1A-C3B6-489E-BC19-2F7B4DDE0644", "type": "MeshPhongMaterial", "color": 14540253, "emissive": 0, "specular": 39168, "shininess": 30, "map": "00B7C61C-9502-4342-A762-9E239A7D91BE", "transparent": true, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "14774C90-CE64-4613-8DC2-D457095708FA", "type": "MeshNormalMaterial", "flatShading": true, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "6057FCEA-02B7-4D1B-B1CC-5D3048644932", "type": "MeshBasicMaterial", "color": 16755200, "depthFunc": 3, "depthTest": true, "depthWrite": true, "wireframe": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "ACE4389B-277D-4A1D-A97A-BF437828EB84", "type": "MeshDepthMaterial", "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "59F5B71C-D6CE-437B-9961-626393B6186C", "type": "MeshLambertMaterial", "color": 6710886, "emissive": 3607808, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "913C4583-81EC-41DE-B7C1-119C876F1006", "type": "MeshPhongMaterial", "color": 0, "emissive": 33451, "specular": 6710886, "shininess": 10, "opacity": 0.9, "transparent": true, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}, {"uuid": "2EDB4067-DD57-44FC-9448-571A2B91A94E", "type": "MeshBasicMaterial", "color": 16777215, "map": "00B7C61C-9502-4342-A762-9E239A7D91BE", "transparent": true, "depthFunc": 3, "depthTest": true, "depthWrite": true, "skinning": false, "morphTargets": false, "dithering": false}], "textures": [{"uuid": "00B7C61C-9502-4342-A762-9E239A7D91BE", "name": "", "mapping": 300, "repeat": [1, 1], "offset": [0, 0], "wrap": [1001, 1001], "minFilter": 1008, "magFilter": 1006, "anisotropy": 1, "flipY": true, "image": "535EE7B4-A94A-4396-B9FD-B77CF4825EEF"}], "images": [{"uuid": "535EE7B4-A94A-4396-B9FD-B77CF4825EEF", "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAAEACAYAAABccqhmAAAfzklEQVR4Xu2dC9ht9TbG/9Nxv3Pcj1uSJEmSJEmSJEmSJEmSJEmSJMm2JUmSJEmSJEmSJEmybUmSJEmSbNv9uB05yJbPM77W+s5ac6/5H3PO35pzzPXN9zxPT2c//z3mO8dljdaac7zvKNbW1oqiKNbW1tbekFJaSynp34qD6mAknwP75L9dSsmawGvWDFJK72B/Tim9Nvm3/qx4qB426efBGsD0v/j2VcC+CbzT5JuA/VfA/tGfX4+D4qF62HSfB/vEv/3kv/TW5e2bwKuTbwLrf04pvcvseUrp1dKfdf56nKb/KD7z8VB9DLg+rAHYV/zZ33y7Ss8E3q18XvqzzkvxU3zm6kn1MeD6sAbwjqXf/Ou//WeeCbzHovOZZwQ6n39GMH12Mv234qP4zD5DGVR9WAMo/8bf+L07+SbwXqVnArO/h+3/63z+GYHiMx8P1ceA68MawDuXftPP/p61bwJvKT0TmDtPKb1Pzj6l9BbnXPbzvxHL8VX88vFR/YD6sQZgD2ly731fceYE3ujZO+eyd+Kv+GXrU/UD6scawLs67/3f6swJvH8d+8xcgezzcwbr8Vf8KudSVD+gfqwBTJ/SVr3n/q/pM4CKOYEPdOYGNuwr5gpkn58zUPzy8VH9gPqxBvDuznv+uffaC+YEPriJ/YI5Atnn3xOX5wrKf1b8FL/cHEq2fqwB2GuquTkA78+lZwJv8v6+cy77hvEvxVPxU/wafX5n68cawHs67/nL7y3LcwIf1sZ+5jet7PPvyRfGX/HbeCag+gH1Yw3Ae09bfq9dnhP4CGdOoNJ+Yif7/HtixS8fH9UPqB9rAO/tvKcvv5cuzwnscOYEsvYppT0IfkppB7QXfv49spc/xZ/FL7T+rAHYIAXhf++EegJ7UnxoL3yYf8UffX5C688awPtC/v/fQD2BvZaBD/QLhM/0Dtbzr/i31s8IrT9rANNJqrZ8979z5gQ8PYG9of7ABn5L/QLhM70DxZ/FL7T+rAG8H+T7/wPUE9hnmfgt9AqEz/jqc/lX/BvrZYTWnzUAG6XMvUf0+NxvhnoC+y4bvyEfX/hLzr/i30gPIbT+rAF8AOT7/zPUE9ivC/wGegXCZ3z9hflX/DeeCXh6EKH1Zw2gPEvdlM/+r1VzADX1BPaHegOV+JPrenMOwmd8dcWfxS+0/qwBfBDk82f56jX0BA7oEr+GHoHwAZ+8Rnw9PQPFPzD+1gCMTJLVA6DnzpzAgfT60F74HeffyY/iHxh/awAfAvn8Hl/d0xM4qA/8zHtq4QM+eUqpVv4V/8o5gdD6swYwZZO5egAV79k9vrqnJ3Aw1BOojV9x/8IHfPKUkuLP4hdaf9YAPhTy+T2+uqcncEif+AveUwuf8ekb5V/x321OILT+rAEYnbI1nziltKupfemZwKFN7Zf894Xfc/5L+VP8A+NvDeDDIZ/f46t7egKHReDP/CYVPuCTz8SxVR2klBT/wPhbA6B8ao+v7ukJHA71BFrjT3CFD/jkS8id4h8Yf2sAHwn59B5f3NMT2Ab1BBB+SumISP9TStuC8eU/4/PT+gvNvzUAEyQgegDT7cJt/70d6gm0xZ3aHRntfzC+/A+u/8j8WwP4KMjntuWihA/+GNQToPhHwfun+Ov+Az49xZf/sfEPzb81gKkiSVs9AI/v750/DvUEvOt750dDPQLv+t75hv8t9Qy863vn8p/x+b34eueh+bcG8NGQj0/3vz8B9QQo/jFD8r8Fn17+Mz2DUeffGoBJEhE9AE8vwDt/EuoJeNf3zo8dmv8N+fSef965/B9Y/feZf2sAHwP5+B7f2Tt/CuoJeNf3zo8bov8N+PSef965/Gd6CF58vfOF9d9X/q0BlDXJmuoBeHx77/xpqCfgXd87Px7qEXjX984r/a+pZ+Bd3zuX/4zP78XXOw/NvzWAj4V8fLqf/Zkcfg09AYp/wpD9r8G3l/+MTz/q/FsDMFFCogdA97M/6+E7cwIU/0QP3zmn+K7/HePL/4HXf5f5twbwcZCPT/ezP1cHvyiK1yYTg+X33hT/pDr4mff0FL+W/x3iy3+mh7DS+bcGMFUlbasHQPezP19XD2DyTaD8XpXin1wXv+I9PcWv7X9H+PKf8flXOv/WAD4e8vHpfvoXmuAXRfFqiTtA8U9pgr/gPT3Fb+R/B/jyn+khrHT+rQGYLDHRA6D76V9sil96JkDxT22KX/r7FL+x/0vGl/8rVv/LzL81gE+AfHy6n/2lNvgzzwQo/mlt8Gd+k1P8Vv4vEV/+Mz7+SuffGoCnS+7x7amewMttOeWTbwIU//S2+BM7it/a/yXhy3/Gx1/p/FsD+ETIR6f74ZF9URQ7oJ7AGavsf0oJxS+lJP+ZHgCNf6i9NQBbzED0AHZG20M9gTOj7z8YX/6veP2T+rEG8EmQj073wy/FPjMn4PHlz9oM/gM9AfnP9ACWUr8gfwjfGsB0M0tbPQC6H35p9hVzAh4f+2xnDsCzX9r9V7zn7xpf/jM9gJXOvzWAT4Z8eLoffqn2C+YEPL78OZvJ/xZ6AvKf6QkstX5b5A/hWwOw1URED+DNQ7MvPRPw+PDnDu3+S/fj3T+Nv/zfZPXfpH6sAXwK5MPT/fCd2M88E/D42OdtRv8b8MnlP9MD6KR+G+QP4VsDKO8ma6oHQPfDd2Y/+Sbg8bHPd+YAPPvO7n9yX13jy3+mB7DS+bcG8KmQD+/tfw89r6EncMFm9r+GnoD8Z3oCofVdI7/Z+7MGYMsJiR7AK0O3d+YELhz6/Xd8f/J/k9d/rn6sAXwa5MPT/fC92GfmBC4ag/+Z98zyn+kB9FK/mfwhfGsA0+2sbfUA6H743uwr5gQuhnoAvd1/xZwAxZf/TA+Axj/U3hrAp0M+PN0P36v9gjmBS8bk/4L3zPKf6QH0Wr8L8ofwrQHYemaiB7Br1exLzwQuXbX7X/L9yv+R1f9s/VgD+AzIh2+7Fz7UbuaZwGVj9H/mN6X8Z3oAoXU8k8dW92ENIHo/u6c30Nn55JvA5VAPoLP76+m+5D/TA1jp/FsD+EzIh6f70UPti6LYAvUEQu9/CbnbsoRrrHIMRu2/NYAjCJ84pfSGFbffCvUEVt7/Fc8fjf/WMftvDeCzIB/e49sP/fwK8x/oCQzdP+/+1v0HfHTv+kM/H7X/1gCOhHx4j68+9PMrp/631BMYun/e/W3431KPwLv+0M9H7b81gM+GfHiPbz/086tm/W+hJzB0/7z7m/O/BR/du/7Qz0ftvzWAoyAf3uOrD/386rL/DfUEhu6fd3+7+d+ET55S8q4/9PNR+28N4HMgH97j2w/9/JpF/jfQExi6f979LfS/AR/du/7Qz0ftvzWAoyEf3uOrD/382ir/a+oJDN0/7/4q/a+pR+Bdf+jno/bfGsDnQj483U8fbX9dzv8aegLR90/xs/7X4JtT/Gj7UftvDeAYyDd/44rbX+/dvzMnsOn9d+Ij/5meQGj8rAF8HuTD0/3o0fY31PE/MycQff8Uv5b/mTkBih9tP2r/rQEcC/nwdD96tP2Ndf2vmBOIvn+KX9v/ijkBih9tP2r/rQF8PuTD0/3o0fY3NfF/wZxA9P1T/Eb+L5gToPjR9qP23xrAcZBf/qYVt7+56f2XngmMzv9SvOQ/0xMIjZ81gC+AfHi6Hz3a/pY2/s88E4i+f4rfyv+ZZwIUP9p+1P5bAzge8s7pfvRo+1vb+j/5JhB9/xS/tf+TuFH8aPtR+28N4AshH5zuN98jGP82gl8UxQ6oJ7DS/qeURp3/VfffGsAJkA+9E9rvCe0p/u0UH+oJrLz/MH7yPz9H4OkdoPq3BvBFkA+O9pOnlPYKxr9jGfhAT2BT+A/0BOQ/02NAnz9rACdCPQC6H33vYPw7l4XfUk9g0/jfUk9A/uf3Enh6CujzZw3gi6EeANpPnlLaJxj/rmXit9AT2FT+t9ATkP/5vQSengL6/FkDOAnqAdD99PsG49+9bPyGegKbzv+GegLyPz9H4OkpoM+fNYAvgXoAaD95Smm/YPx7usBvoCewKf1voCcg//N7CTw9BfT5swZwMtQDoPvR9w/Gv7cr/Jp6ApvW/5p6AvI/v5fA01NAnz9rAF8K9QDofvQDgvHv6xK/hp7Apva/hp6A/J9/BlDesUA/X1l7awCneHz4js8P7Pj6rzjXv79rfGdOYNP778RX/jM9Aa++s+fWAL6sDh++q/3kKaWDgvEf6AM/MycwCv8z9SP/83MAb+2yPq0BnFqXD1/xnpfuNz84GP/BvvAr5gRG439F/cj//BwA/Xxl7a0BfHkTPvyy95OnlA4Jxn+oT/wFcwKj8n9B/cj//BzAq13WpzWA05z34Ls6Pj+04+t79/9w3/ilZwKj878Ub/nP9AS8+s6eWwP4ijZ8eLqXfMb+sGD8RyLwZ54JjNL/sed/KP5bAzjdeQ/e9f7zw4PxH43Cn3wTGK3/k7jL//wcQKefP2sAX0n48Euw3baEa5D99KH4RVFsg3oCxHezDfVf+LHxtwZwhvOe1uMj0/PtY8eHegKKP+PTj7r+rAF8FeTD0/3vjwk/FUBPQPFnfPpR1581gDMhH97jK3vnjws/rf/Oa6kn4MXXO1f8GR/fi693Hhp/awBfDfnwHl/ZO39C+Gnjd3wLPQEvvt654s/4+F58vfPQ+FsDOAvy4T2+snf+pPDXvwFMf8vvaqgn4MXXO1f8GR/fi693Hhp/awBfA/nwHl/ZO39K+OvfAF6bjUMDPQEvvt654s/4+F58vfPQ+FsDOBvy4T2+snf+tPBffwZQ/qemnoAXX+9c8Wd8fC++3nlo/K0BfC3kw9P97s8I/23PAMqxqKEnoPgzPv2o688awDmQD0/3mz8r/LlnAOX3+q84cwKKP+PTj7r+rAF8HeQb0/3uzwl/7hlA+b3+Oh88Myeg+DM+/ajrzxrAuZAPT/e7Py/8ud//5ffGG3zuijkBxZ/x6Uddf9YAvh7yjel+9xeEP/cMIMv/XjAnoPgzPv2o688awHmQD0/3m78o/Pk5AC8epWcCij/j04+6/qwBfAPkw9P97i8Jf/c5AE9vYeaZgOKff48/N1+xIK6jrj9rAOdDPjzd7/6y8BfPAXhxmXwTUPwZn37U9WcN4BshH5/uh5d9/j12lu9fFMUOqCeg+IP4p5RWOn7WAC6AfHy0nzylJHvGZ98J9QQUfxj/Vf78WAP4JsjHR/vJU0qyZ3z29fgBPQHFfwnxz+w98PQaQuNvDeBCyMdH+8lTSrJnfPSN+LXUE1D8lxT/yefI4/+Xz0Pjbw3gmyEfH+0nTynJnvHR5+LXQk9A8V9i/BfsPfD0AELjbw3gIsjHR/vJU0qyZ3z03eLXUE9A8V9y/EufJ08PIDT+1gC+BfLx0X7ylJLsGR99Yfwa6Ako/h3Ef+aZgKcHEBp/awAXQz4+2k+eUpI946NXxq+mnoDi31H8J58rTw8gNP7WAL4V8vE73V9eY7+88DPvsWvoCSh+TE9gpeNnDeASyMdH+8lTSrJnfHY3fs6cgGuv+sjrNaxyfKwBfBvk43e6vzylpOszvrunJ6D4LiG+mTmAQcfXGsClkI/f6f7ylJKuz/junp6A4ruk+FbMAQw6vtYAvh3y8TvdX77gvarwAP99wZyA4gniuer1aQ3gMo9/rvNmfP2hx6v0TADtl08pyZ7pEYTGzxrAd0A+vse31jnjq3cSv5k5gU6u7+kZ6Hx+D0RUPKwBXO7xznXejq8/9LhNvgl0un9+6DEY+/1ZA/hOqAdA99PLnvHRUfyMRgj1BBC+aq96J0QfsbHkb4F8ZrqfXvaMj47jB/UEML7qLztn0Gl8rQF8F9QD8PjOOmd8817iB/QEerk/wLfX/WXqzxrAVqgH0JT/rL/P+Oedxa+lnkBn99OSX6/7aVBf1gC+G+oBeHxnnTO+ea/xa6En0Ov9teDb6/4y9WcN4AqoB+DxnXXO+Oa9x6+hnkDv99eQb6/7y9SfNYDvgXoAHt9Z54xvHhK/BnoCIffXgG+v+8vUnzWAK6EegMd31jnjm4fFr6aeQNj91eTb6/4y9WcN4HuhHgDdTy97xkfvNH419AQ6xa+hByF8UD/WAK6CfGa6n172TA+g8/g5cwKd4zv1KXxQP9YAvg/qAdD99LJnfPRe4peZE+gFPzMHIHxQP9YAroZ6AHQ/vewZH723+FXMCfSGXzEXIHxQP9YAvh/qAdD99LJnfPRe47dgTqBX/AVzAMIH9WMN4BrIX6f76WXP+OS9x6/0TKB3/FK9Ch/UjzWAH4B6AHQ/veyZXkBI/GaeCYTgzzwTED6oH2sA10JONN1PL3u23z4sfpNvAmH4k7oVPqgfawA/CHnHdD/6HsJHnPDQ+BdFsQPqCSj/TA8C5d8awHWQj033y+8pfMQHD48/1BNQ/pkeBMq/NYAfgnoAdL/5XsJf/wYw1eZryl8fRPyBnoDyz/QiUP6tAVwP9QDofvO9hT+nOdiUzz6Y+LfUE1D+G/D3U0rl+kD5twbww1APgO4330f4c88Ayjr9Hp99UPFvoSeg/DO9CJR/awA3QD0Aut98X+Fn9w54fPbBxb+hnoDyz/QiUP6tAfwI1AOg+833E/7cM4CyTr/HZx9k/BvoCSj/TC8C5d8awI1QD4DuN99f+Nm9Ax6ffbDxr6knoPwzvQiUf2sAPwr1AOh+9AOEn50DoPH17DuNfw09gU7xa+gJjBrfGsBNUA+A7pc/UPih++c7j78zJ9A5vlNfo8a3BvBjUA+A7j8/SPjZOQAaX8++l/hn5gR6wc/MWYwa3xrAzVAPgO4/P1j42TkAGl/Pvrf4V8wJ9IZfoScwanxrAD8O9QDofvlDhJ+dA6Dx9ex7jf+COYFe8RfoCYwa3xrALVAPgO43P1T42TkAGl/Pvvf4l54J9I5fqrdR41sD+AmoB0D3yx8m/OwcAI2vZx8S/5lnAiH4M88ERo1vDeBWqAdA98sfLvzsHACNr2cfFv/JN4Ew/EndjRrfGsBPQj4+3Q+/LRj/iGD8UftfFMU2qCdA62/U+bcGcBvk49P95duD8Y8Mxh+9/1BPgNbfqPNvDeCnIB+/KX+9/PcfC8Y/Khhf/qdUAD0BWn+jzr81gNshH78pf7389x8Pxj86GF/+T2bhW+oJ0Pobdf6tAfw05ON7fHXv/Ilg/GOC8eX/DB++hZ6AV1/e+ajzbw3gDsjH9/jq3vmTwfjHBuPL/xIfvqGegFdf3vmo828N4GcgH9/jq3vnTwXjHxeML/8X8OEb6Al49eWdjzr/1gDuhHx8j6/unT8djH98ML78r+DD19QT8OrLOx91/q0B/Czk49P97M8E458QjC//M7r4NfQEaP2NOv/WAO6CfHy6n/3ZYPwTg/Hlv7Pf3pkToPU36vxbA/g5yMen+9mfC8Y/KRhf/tfYb5+ZE6D1N+r8WwO4G/Lx6X7254PxTw7Gl/8199tXzAnQ+ht1/q0B/Dzk49P97C8E458SjC//G+y3XzAnQOtv1Pm3BnAP5OPT/ewvBuOfGowv/xvuty89E6D1N+r8WwP4BcjHp/vZXwrGPy0YX/632G8/80yA1t+o828N4F7Ix6f72V8Oxj89GF/+t9xvP/kmQOtv1Pm3BvCLkA+P9pOnlKLtz5D/2b0EHt8+NH9FUeyAegKjzr81gPsgHx7tJ08pRdufKf+zewk8vn10/nZCPYFR598awC9BPjzaT55SirY/S/5n9xJ4fPvo/K3jAz2BUeffGsD9kA+P9pOnlKLtz5b/2b0EHt8+On8b+C31BEadf2sAvwz58Gg/eUop2v4c+Z/dS+Dx6aPzN4ffQk9g1Pm3BvAA5MOj/eQppWj7c+V/di+Bx6ePzt9u+A31BEadf2sAvwL58Gg/eUop2v48+Z/dS+Dx6aPztxC/gZ7AqPNvDeBByIdH+8lTStH258v/7F4Cj08fnb9K/Jp6AqPOvzWAX4V8eG///NDPL5D/2TmAoecve3819ARGnX9rAA9BPvwrK25/4YrfP43/pvffmRPY9P7n6tsawK9BPry3f37o5xfJ/+wcwNDzV+v+MnMCo86/NYCHIR/e2z8/9POL5X92DmDo+at9fxVzAqPOvzWAX4d8eG///NDPL5H/2TmAoeev0f0tmBMYdf6tATwC+fDe/vmhn18q/7NzAEPPX+P7Kz0TGHX+rQH8BuTDe/vnh35+mfzPzgEMPX+t7m/mmcCo828N4FHIh/f2zw/9/HL5n50DGHr+Wt/f5JvAqPNvDeA3IR/e44sP/XyL/Ed6AEPPb/b+iqLYAvUEVtv/tbW1bZAP7/HFh36+Vf4jPYCh59e7v61QT8C7/qDP7RvAb0E+vMcXH/r5FfIf6QEMPb/e/a3nH+gJeNcf9Lk1gO2QD+/xxYd+fqX8R3oAQ8+vd38b+W+pJ+Bdf9Dn1gB+G/LhPb740M+vkv9ID2Do+fXuby7/LfQEvOsP+twawGOQD+/xxYd+frX8R3oAQ8+vd3+75b+hnoB3/UGfWwP4HciH9/jiQz+/Rv4jPYCh59e7v4X5b6An4F1/0OfWAB6HfHiPLz7082vlP9IDGHp+vfurzH9NPQHv+oM+twbwu5APT/ezR9tfJ/+RHkB0/ih+Nv819AQofqi9NYAnIB+e7mePtr9e/mfnADy9gej8UXw3/86cAMUPtbcG8HuQD0/3s0fb3yD/kR5AdP4ofq38Z+YEKH6ovTWAJyEfnu5nj7a/Uf4jPYDo/FH82vmvmBOg+KH21gB+H/Lh6X72aPub5D/SA4jOH8VvlP8FcwIUP9TeGsBTkA9P97NH298s/5EeQHT+KH7j/JeeCVD8UHtrAH8A+fB0P3u0/S3yH+kBROeP4rfK/8wzAYofam8N4GnIh6f72aPtb5X/SA8gOn8Uv3X+J98EKH6ovTWAP4R8eLoffo9g/NuC8eV/QnoEtP5Q/oui2AH1BELzbw3gGciHp/vh9wzGvz0YX/4npEdA6w/nH+oJhObfGsAfQT483Q+/VzD+HcH48v/1bwBTbb+m/Hlaf0vJP9ATCM2/NYBnIR+e7offOxj/zmB8+f/6N4DpP03587T+lpb/lnoCofm3BvDHkA9P98PvE4x/VzC+/J9/BlDW+ff49LT+lpr/FnoCofm3BvAc5MPT/fD7BuPfHYwv/+efAZR1/j0+Pa2/pee/oZ5AaP6tAfwJ5MPT/fD7BePfE4wv/+efAZR1/j0+Pa2/TvLfQE8gNP/WAJ6HfHi6H37/YPx7g/Hl//wzgLLOv8enp/XXWf5r6gmE5t8awJ9CPjzdH39AMP59wfjyPz8HQOvLs+80/zX0BELzbw3gBciH9/ji3vmBwfj3B+PL//wcgFc/9Lzz/DtzAqH5twbwZ5APX2s/e+Y970HB+A8E48v//BwArS/Pvpf8Z+YEQvNvDeBFyIevvZ99glN+z3twMP6DwfjyPz8HQOvLs+8t/xVzAqH5twbw55AP32g/e0qp/PcPCcZ/KBhf/ufnAGh9efa95n/BnEBo/q0BvAT58I33s5fwDg3GfzgYX/7n5wBofXn2vee/9EwgNP/WAP4C8uFb7WefeSZwWDD+I8H48j8/B0Dry7MPyf/MM4HQ/FsDeBny4VvvZ5/gHh6M/2gwvvzPzwHQ+vLsw/I/+SYQmn9rAH8J+fB0P7qtJ6fXIPbCV/xJ/SDboii2QT0Bhr+2tmaCCtYlo/aYT7cTCz8mD4p/TNyn9b4d6gmgz419A/gryIdvyt8u/31bTkr44MJn8VP8WfyWUn9ATwDhWwOYKqq05WM35W+X/355NyG9XlN74TM+ftN4K//z8d6ov5Z6Aij+1gD+GvLhPb62d26ryWZ/xzTlg3vX986Fr/gPpv5a6Al49Z09twZgkkqzzwCa8rE9vrZ3Pt1MNP0tI/z5fHjxo+eK/8Dqv6GeAMq/NYC/hXx4j6/tndtiktlnAE354N71vXPhK/6Dq78GegJefWfPrQGUNdWa8rE9vrZ37u0l8OzpufAZH1/xZ/GrrL+aegIo/tYA/h7y4el+c5Mlz73L9PjcwmfxU/xZ/Dqtvxp6AgjfGoCJKubmADy+Nd1vPlUlrnqfKfx8fhR/Vr+Drz9nTgDl3xrAP0I+PN1vbqKkuTkAj88tfBY/xZ/Fr5f6y8wJIHxrAFNV1ao5AI9PTfeblzUJy+81hZ9/T6/4Mz2Blam/ijkBlH9rAP8E+fB0v7lJkuXew3p8buGz+Cn+LH691t+COQGEbw3AZJVzcwAen5ruN58qElXNAQg/nx/Fn9XvytVf6ZkAyr81gH+BfHi639wESXLvYT0+t/BZ/BR/Fr+Q+pt5JoDwrQF4uuoen5ruN6d6BMJnfHrFn8UvrP4m3wQQvjWAf4N8fLqfXfaMj6/4jTh+RVHsIHoC1gBs0IboAdD97LJX/FV/7fU4dhI9AWsA/w75+HQ/u+wZH13xU/yKtnoC1gCmk3Zt9QDofnbZMz6+4qf4rX922+gJWAP4D8jHp/vZZc/4+Iqf4rcxR9NUT8AagI3aEj0Aup9d9oq/6u9tzwCa6mHs9vlpoidgDeA/IR+f7meXPePjK36K325zNHX1BKwBlGftm+oBeHMEHl9Z9oxPrvgpfgtnderoCVgD+G/Ix/f4+jpnfHPFT/FrrZfh6QlYAzCyDdED8Pj6Old8VV/V7/k7/3zk5gSsAfwP5ON7fH2dM7654qf4Eb2M9fqpmhOwBjB96thWD8Dj6+uc8dUVP8Vv9jd+U72MjfpZNCdgDeB/IR/f4+vrnPHNFT/Fj+hlzNVPeU7AGoDRbYkegMfX17niq/qqfs/f++dj9pmANYD/g3x8j6+vc8Y3V/wUP6KXsbB+ps8ErAF4fH+dM7644qf4DbIG7JvAtAEk/U8RUATGF4H/B9kB/0/tGYLRAAAAAElFTkSuQmCC"}], "object": {"uuid": "A7099C2C-7068-49C1-9BB8-1B3B7112FBEA", "type": "<PERSON><PERSON>", "matrix": [0.032357, 0.698616, 0.714765, 0, 0.041524, -0.715462, 0.697417, 0, 0.998613, 0.007114, -0.05216, 0, 0, 0, 0, 1], "geometry": "EFDA544D-E317-4EB2-8F10-577A709DC7AA", "material": ["0D636E31-83BA-40AD-AE8B-52F08BFC9A7D", "4C3BE4BA-C675-48FA-A726-9074C85B93E2", "6D52D811-A23F-40C4-8838-6F65B0E11927", "10CE8E49-125D-4968-9724-D6D93097F5B1", "E134A5CB-5F22-476E-9E97-DF74153B544C", "D7FA74C4-9052-42B7-8650-3938C1430071", "A5A7CB1A-C3B6-489E-BC19-2F7B4DDE0644", "14774C90-CE64-4613-8DC2-D457095708FA", "6057FCEA-02B7-4D1B-B1CC-5D3048644932", "ACE4389B-277D-4A1D-A97A-BF437828EB84", "59F5B71C-D6CE-437B-9961-626393B6186C", "913C4583-81EC-41DE-B7C1-119C876F1006", "2EDB4067-DD57-44FC-9448-571A2B91A94E"]}}