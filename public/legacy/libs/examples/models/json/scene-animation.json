{"object": {"type": "Scene", "uuid": "23A0EBA7-4499-47FB-999D-BEA811775B19", "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1], "children": [{"name": "lighthaus-test-trnsfrm01", "uuid": "E351F331-4C15-3BFA-B542-7FE152815F23", "matrix": [-1, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0, 1], "visible": true, "type": "Object", "children": [{"name": "cylinder001", "uuid": "7935BB30-4CBC-3BEE-A497-8994876785B6", "matrix": [1.22253, 0, 0, 0, 0, 0.29514, -1.18637, 0, 0, 1.18637, 0.29514, 0, 0, 38.7012, -1.71412, 1], "visible": true, "type": "<PERSON><PERSON>", "material": "541CFE3C-B1BD-37FD-8A82-3870870BB8A8", "geometry": "CAEC0410-6CA0-3646-AF22-DE6D5D34C387"}, {"name": "cylinder002", "uuid": "7272467F-DBF7-3588-95CC-53FB473AC1C1", "matrix": [1.22253, 0, 0, 0, 0, -0.295142, -1.18637, 0, 0, 1.18637, -0.295142, 0, 0, 19.6808, 17.1202, 1], "visible": true, "type": "<PERSON><PERSON>", "material": "3701AB15-0042-3531-BDFA-EB0FA575D7D5", "geometry": "0AF705E6-E495-351F-944E-8E96CEB82A7B"}, {"name": "cylinder003", "uuid": "C154AF75-187E-33B5-907D-2E33CF3B29BE", "matrix": [1, 0, 0, 0, 0, 0, -1, 0, 0, 1, 0, 0, -2e-06, -6.71175, 3.01972, 1], "visible": true, "type": "<PERSON><PERSON>", "material": "D95A0291-848F-32DE-B7CF-F93015785600", "geometry": "84174F78-6B50-3C0D-A3D3-D1203D616F79"}]}, {"name": "ambientlight", "uuid": "217f2d71-80f0-44cd-9d97-43e5611050a3", "type": "AmbientLight", "color": 5592405}, {"name": "pointlight", "uuid": "FCC1C4DA-037A-33FF-97AE-339CD1CB618C", "matrix": [-1, 0, 0, 0, 0, 1, 0, 0, 0, 0, -1, 0, -22.1326, 52.6576, -28.8763, 1], "type": "PointLight", "color": 16777215, "intensity": 1}, {"name": "pointlight1", "uuid": "D9440A28-5F71-3A2A-94B3-************", "matrix": [-1, 0, 0, 0, 0, 1, 0, 0, 0, 0, -1, 0, 18.3723, 34.5427, 79.0829, 1], "type": "PointLight", "color": 16777215, "intensity": 1}, {"name": "camera", "uuid": "e2b22508-5e7c-4609-8505-d781754ba105", "matrix": [0.70711, 0, 0.70711, 0, 0, 1, 0, 0, -0.70711, 0, 0.70711, 0, -200, 0, 200, 1], "type": "PerspectiveCamera", "fov": 30, "aspect": 1.77778, "near": 1, "far": 10000}]}, "images": [], "animations": [{"fps": 29, "name": "default", "tracks": [{"type": "vector3", "keys": [{"time": 5, "value": [0, 38.7012, -1.71412]}, {"time": 6, "value": [0, 38.7012, -1.71412]}, {"time": 7, "value": [0, 38.7012, -1.71412]}, {"time": 8, "value": [0, 38.7012, -1.71412]}, {"time": 9, "value": [0, 38.7012, -1.71412]}, {"time": 10, "value": [0, 38.7012, -1.71412]}, {"time": 11, "value": [0, 38.7012, -1.71412]}, {"time": 12, "value": [0, 38.7012, -1.71412]}, {"time": 13, "value": [0, 38.7012, -1.71412]}, {"time": 14, "value": [0, 38.7012, -1.71412]}, {"time": 15, "value": [0, 38.7012, -1.71412]}, {"time": 16, "value": [0, 38.7012, -1.71412]}, {"time": 17, "value": [0, 38.7012, -1.71412]}, {"time": 18, "value": [0, 38.7012, -1.71412]}, {"time": 19, "value": [0, 38.7012, -1.71412]}, {"time": 20, "value": [0, 38.7012, -1.71412]}, {"time": 21, "value": [0, 38.7012, -1.71412]}, {"time": 22, "value": [0, 38.7012, -1.71412]}, {"time": 23, "value": [0, 38.7012, -1.71412]}, {"time": 24, "value": [0, 38.7012, -1.71412]}, {"time": 25, "value": [0, 38.7012, -1.71412]}, {"time": 26, "value": [0, 38.7012, -1.71412]}, {"time": 27, "value": [0, 38.7012, -1.71412]}, {"time": 28, "value": [0, 38.7012, -1.71412]}, {"time": 29, "value": [0, 38.7012, -1.71412]}, {"time": 30, "value": [0, 38.7012, -1.71412]}, {"time": 31, "value": [0, 38.7012, -1.71412]}, {"time": 32, "value": [0, 38.7012, -1.71412]}, {"time": 33, "value": [0, 38.7012, -1.71412]}, {"time": 34, "value": [0, 38.7012, -1.71412]}, {"time": 35, "value": [0, 38.7012, -1.71412]}, {"time": 36, "value": [0, 38.7012, -1.71412]}, {"time": 37, "value": [0, 38.7012, -1.71412]}, {"time": 38, "value": [0, 38.7012, -1.71412]}, {"time": 39, "value": [0, 38.7012, -1.71412]}, {"time": 40, "value": [0, 38.7012, -1.71412]}, {"time": 41, "value": [0, 38.7012, -1.71412]}, {"time": 42, "value": [0, 38.7012, -1.71412]}, {"time": 43, "value": [0, 38.7012, -1.71412]}, {"time": 44, "value": [0, 38.7012, -1.71412]}, {"time": 45, "value": [0, 38.7012, -1.71412]}, {"time": 46, "value": [0, 38.7012, -1.71412]}, {"time": 47, "value": [0, 38.7012, -1.71412]}, {"time": 48, "value": [0, 38.7012, -1.71412]}, {"time": 49, "value": [0, 38.7012, -1.71412]}, {"time": 50, "value": [0, 38.7012, -1.71412]}, {"time": 51, "value": [0, 38.7012, -1.71412]}, {"time": 52, "value": [0, 38.7012, -1.71412]}, {"time": 53, "value": [0, 38.7012, -1.71412]}, {"time": 54, "value": [0, 38.7012, -1.71412]}, {"time": 55, "value": [0, 38.7012, -1.71412]}, {"time": 56, "value": [0, 38.7012, -1.71412]}, {"time": 57, "value": [0, 38.7012, -1.71412]}, {"time": 58, "value": [0, 38.7012, -1.71412]}, {"time": 59, "value": [0, 38.7012, -1.71412]}, {"time": 60, "value": [0, 38.7012, -1.71412]}, {"time": 61, "value": [0, 38.7012, -1.71412]}, {"time": 62, "value": [0, 38.7012, -1.71412]}, {"time": 63, "value": [0, 38.7012, -1.71412]}, {"time": 64, "value": [0, 38.7012, -1.71412]}, {"time": 65, "value": [0, 38.7012, -1.71412]}, {"time": 66, "value": [0, 38.7012, -1.71412]}, {"time": 67, "value": [0, 38.7012, -1.71412]}, {"time": 68, "value": [0, 38.7012, -1.71412]}, {"time": 69, "value": [0, 38.7012, -1.71412]}, {"time": 70, "value": [0, 38.7012, -1.71412]}, {"time": 71, "value": [0, 38.7012, -1.71412]}, {"time": 72, "value": [0, 38.7012, -1.71412]}, {"time": 73, "value": [0, 38.7012, -1.71412]}, {"time": 74, "value": [0, 38.7012, -1.71412]}, {"time": 75, "value": [0, 38.7012, -1.71412]}, {"time": 76, "value": [0, 38.7012, -1.71412]}, {"time": 77, "value": [0, 38.7012, -1.71412]}, {"time": 78, "value": [0, 38.7012, -1.71412]}, {"time": 80, "value": [0, 38.7012, -1.71412]}, {"time": 81, "value": [0, 38.7012, -1.71412]}, {"time": 82, "value": [0, 38.7012, -1.71412]}, {"time": 83, "value": [0, 38.7012, -1.71412]}, {"time": 84, "value": [0, 38.7012, -1.71412]}, {"time": 85, "value": [0, 38.7012, -1.71412]}, {"time": 86, "value": [0, 38.7012, -1.71412]}, {"time": 87, "value": [0, 38.7012, -1.71412]}, {"time": 88, "value": [0, 38.7012, -1.71412]}, {"time": 89, "value": [0, 38.7012, -1.71412]}, {"time": 90, "value": [0, 38.7012, -1.71412]}, {"time": 91, "value": [0, 38.7012, -1.71412]}, {"time": 92, "value": [0, 38.7012, -1.71412]}, {"time": 93, "value": [0, 38.7012, -1.71412]}, {"time": 94, "value": [0, 38.7012, -1.71412]}, {"time": 95, "value": [0, 38.7012, -1.71412]}, {"time": 96, "value": [0, 38.7012, -1.71412]}, {"time": 97, "value": [0, 38.7012, -1.71412]}, {"time": 98, "value": [0, 38.7012, -1.71412]}, {"time": 99, "value": [0, 38.7012, -1.71412]}, {"time": 100, "value": [0, 38.7012, -1.71412]}, {"time": 101, "value": [0, 38.7012, -1.71412]}, {"time": 102, "value": [0, 38.7012, -1.71412]}, {"time": 103, "value": [0, 38.7012, -1.71412]}, {"time": 104, "value": [0, 38.7012, -1.71412]}, {"time": 105, "value": [0, 38.7012, -1.71412]}, {"time": 106, "value": [0, 38.7012, -1.71412]}, {"time": 107, "value": [0, 38.7012, -1.71412]}, {"time": 108, "value": [0, 38.7012, -1.71412]}, {"time": 109, "value": [0, 38.7012, -1.71412]}, {"time": 110, "value": [0, 38.7012, -1.71412]}, {"time": 111, "value": [0, 38.7012, -1.71412]}, {"time": 112, "value": [0, 38.7012, -1.71412]}, {"time": 113, "value": [0, 38.7012, -1.71412]}, {"time": 114, "value": [0, 38.7012, -1.71412]}, {"time": 115, "value": [0, 38.7012, -1.71412]}, {"time": 116, "value": [0, 38.7012, -1.71412]}, {"time": 117, "value": [0, 38.7012, -1.71412]}, {"time": 118, "value": [0, 38.7012, -1.71412]}, {"time": 119, "value": [0, 38.7012, -1.71412]}, {"time": 120, "value": [0, 38.7012, -1.71412]}, {"time": 121, "value": [0, 38.7012, -1.71412]}, {"time": 122, "value": [0, 38.7012, -1.71412]}, {"time": 123, "value": [0, 38.7012, -1.71412]}, {"time": 124, "value": [0, 38.7012, -1.71412]}, {"time": 125, "value": [0, 38.7012, -1.71412]}, {"time": 126, "value": [0, 38.7012, -1.71412]}, {"time": 127, "value": [0, 38.7012, -1.71412]}, {"time": 128, "value": [0, 38.7012, -1.71412]}, {"time": 129, "value": [0, 38.7012, -1.71412]}, {"time": 130, "value": [0, 38.7012, -1.71412]}, {"time": 131, "value": [0, 38.7012, -1.71412]}, {"time": 132, "value": [0, 38.7012, -1.71412]}, {"time": 133, "value": [0, 38.7012, -1.71412]}, {"time": 134, "value": [0, 38.7012, -1.71412]}, {"time": 135, "value": [0, 38.7012, -1.71412]}, {"time": 136, "value": [0, 38.7012, -1.71412]}, {"time": 137, "value": [0, 38.7012, -1.71412]}, {"time": 138, "value": [0, 38.7012, -1.71412]}, {"time": 139, "value": [0, 38.7012, -1.71412]}, {"time": 140, "value": [0, 38.7012, -1.71412]}, {"time": 141, "value": [0, 38.7012, -1.71412]}, {"time": 142, "value": [0, 38.7012, -1.71412]}, {"time": 143, "value": [0, 38.7012, -1.71412]}, {"time": 144, "value": [0, 38.7012, -1.71412]}, {"time": 145, "value": [0, 38.7012, -1.71412]}], "name": "cylinder001.position"}, {"type": "vector3", "keys": [{"time": 5, "value": [1.22253, 1.22253, 1.22253]}, {"time": 6, "value": [1.22253, 1.22253, 1.22253]}, {"time": 7, "value": [1.22253, 1.22253, 1.22253]}, {"time": 8, "value": [1.22253, 1.22253, 1.22253]}, {"time": 9, "value": [1.22253, 1.22253, 1.22253]}, {"time": 10, "value": [1.22253, 1.22253, 1.22253]}, {"time": 11, "value": [1.22253, 1.22253, 1.22253]}, {"time": 12, "value": [1.22253, 1.22253, 1.22253]}, {"time": 13, "value": [1.22253, 1.22253, 1.22253]}, {"time": 14, "value": [1.22253, 1.22253, 1.22253]}, {"time": 15, "value": [1.22253, 1.22253, 1.22253]}, {"time": 16, "value": [1.22253, 1.22253, 1.22253]}, {"time": 17, "value": [1.22253, 1.22253, 1.22253]}, {"time": 18, "value": [1.22253, 1.22253, 1.22253]}, {"time": 19, "value": [1.22253, 1.22253, 1.22253]}, {"time": 20, "value": [1.22253, 1.22253, 1.22253]}, {"time": 21, "value": [1.22253, 1.22253, 1.22253]}, {"time": 22, "value": [1.22253, 1.22253, 1.22253]}, {"time": 23, "value": [1.22253, 1.22253, 1.22253]}, {"time": 24, "value": [1.22253, 1.22253, 1.22253]}, {"time": 25, "value": [1.22253, 1.22253, 1.22253]}, {"time": 26, "value": [1.22253, 1.22253, 1.22253]}, {"time": 27, "value": [1.22253, 1.22253, 1.22253]}, {"time": 28, "value": [1.22253, 1.22253, 1.22253]}, {"time": 29, "value": [1.22253, 1.22253, 1.22253]}, {"time": 30, "value": [1.22253, 1.22253, 1.22253]}, {"time": 31, "value": [1.22253, 1.22253, 1.22253]}, {"time": 32, "value": [1.22253, 1.22253, 1.22253]}, {"time": 33, "value": [1.22253, 1.22253, 1.22253]}, {"time": 34, "value": [1.22253, 1.22253, 1.22253]}, {"time": 35, "value": [1.22253, 1.22253, 1.22253]}, {"time": 36, "value": [1.22253, 1.22253, 1.22253]}, {"time": 37, "value": [1.22253, 1.22253, 1.22253]}, {"time": 38, "value": [1.22253, 1.22253, 1.22253]}, {"time": 39, "value": [1.22253, 1.22253, 1.22253]}, {"time": 40, "value": [1.22253, 1.22253, 1.22253]}, {"time": 41, "value": [1.22253, 1.22253, 1.22253]}, {"time": 42, "value": [1.22253, 1.22253, 1.22253]}, {"time": 43, "value": [1.22253, 1.22253, 1.22253]}, {"time": 44, "value": [1.22253, 1.22253, 1.22253]}, {"time": 45, "value": [1.22253, 1.22253, 1.22253]}, {"time": 46, "value": [1.22253, 1.22253, 1.22253]}, {"time": 47, "value": [1.22253, 1.22253, 1.22253]}, {"time": 48, "value": [1.22253, 1.22253, 1.22253]}, {"time": 49, "value": [1.22253, 1.22253, 1.22253]}, {"time": 50, "value": [1.22253, 1.22253, 1.22253]}, {"time": 51, "value": [1.22253, 1.22253, 1.22253]}, {"time": 52, "value": [1.22253, 1.22253, 1.22253]}, {"time": 53, "value": [1.22253, 1.22253, 1.22253]}, {"time": 54, "value": [1.22253, 1.22253, 1.22253]}, {"time": 55, "value": [1.22253, 1.22253, 1.22253]}, {"time": 56, "value": [1.22253, 1.22253, 1.22253]}, {"time": 57, "value": [1.22253, 1.22253, 1.22253]}, {"time": 58, "value": [1.22253, 1.22253, 1.22253]}, {"time": 59, "value": [1.22253, 1.22253, 1.22253]}, {"time": 60, "value": [1.22253, 1.22253, 1.22253]}, {"time": 61, "value": [1.22253, 1.22253, 1.22253]}, {"time": 62, "value": [1.22253, 1.22253, 1.22253]}, {"time": 63, "value": [1.22253, 1.22253, 1.22253]}, {"time": 64, "value": [1.22253, 1.22253, 1.22253]}, {"time": 65, "value": [1.22253, 1.22253, 1.22253]}, {"time": 66, "value": [1.22253, 1.22253, 1.22253]}, {"time": 67, "value": [1.22253, 1.22253, 1.22253]}, {"time": 68, "value": [1.22253, 1.22253, 1.22253]}, {"time": 69, "value": [1.22253, 1.22253, 1.22253]}, {"time": 70, "value": [1.22253, 1.22253, 1.22253]}, {"time": 71, "value": [1.22253, 1.22253, 1.22253]}, {"time": 72, "value": [1.22253, 1.22253, 1.22253]}, {"time": 73, "value": [1.22253, 1.22253, 1.22253]}, {"time": 74, "value": [1.22253, 1.22253, 1.22253]}, {"time": 75, "value": [1.22253, 1.22253, 1.22253]}, {"time": 76, "value": [1.22253, 1.22253, 1.22253]}, {"time": 77, "value": [1.22253, 1.22253, 1.22253]}, {"time": 78, "value": [1.22253, 1.22253, 1.22253]}, {"time": 80, "value": [1.22253, 1.22253, 1.22253]}, {"time": 81, "value": [1.22253, 1.22253, 1.22253]}, {"time": 82, "value": [1.22253, 1.22253, 1.22253]}, {"time": 83, "value": [1.22253, 1.22253, 1.22253]}, {"time": 84, "value": [1.22253, 1.22253, 1.22253]}, {"time": 85, "value": [1.22253, 1.22253, 1.22253]}, {"time": 86, "value": [1.22253, 1.22253, 1.22253]}, {"time": 87, "value": [1.22253, 1.22253, 1.22253]}, {"time": 88, "value": [1.22253, 1.22253, 1.22253]}, {"time": 89, "value": [1.22253, 1.22253, 1.22253]}, {"time": 90, "value": [1.22253, 1.22253, 1.22253]}, {"time": 91, "value": [1.22253, 1.22253, 1.22253]}, {"time": 92, "value": [1.22253, 1.22253, 1.22253]}, {"time": 93, "value": [1.22253, 1.22253, 1.22253]}, {"time": 94, "value": [1.22253, 1.22253, 1.22253]}, {"time": 95, "value": [1.22253, 1.22253, 1.22253]}, {"time": 96, "value": [1.22253, 1.22253, 1.22253]}, {"time": 97, "value": [1.22253, 1.22253, 1.22253]}, {"time": 98, "value": [1.22253, 1.22253, 1.22253]}, {"time": 99, "value": [1.22253, 1.22253, 1.22253]}, {"time": 100, "value": [1.22253, 1.22253, 1.22253]}, {"time": 101, "value": [1.22253, 1.22253, 1.22253]}, {"time": 102, "value": [1.22253, 1.22253, 1.22253]}, {"time": 103, "value": [1.22253, 1.22253, 1.22253]}, {"time": 104, "value": [1.22253, 1.22253, 1.22253]}, {"time": 105, "value": [1.22253, 1.22253, 1.22253]}, {"time": 106, "value": [1.22253, 1.22253, 1.22253]}, {"time": 107, "value": [1.22253, 1.22253, 1.22253]}, {"time": 108, "value": [1.22253, 1.22253, 1.22253]}, {"time": 109, "value": [1.22253, 1.22253, 1.22253]}, {"time": 110, "value": [1.22253, 1.22253, 1.22253]}, {"time": 111, "value": [1.22253, 1.22253, 1.22253]}, {"time": 112, "value": [1.22253, 1.22253, 1.22253]}, {"time": 113, "value": [1.22253, 1.22253, 1.22253]}, {"time": 114, "value": [1.22253, 1.22253, 1.22253]}, {"time": 115, "value": [1.22253, 1.22253, 1.22253]}, {"time": 116, "value": [1.22253, 1.22253, 1.22253]}, {"time": 117, "value": [1.22253, 1.22253, 1.22253]}, {"time": 118, "value": [1.22253, 1.22253, 1.22253]}, {"time": 119, "value": [1.22253, 1.22253, 1.22253]}, {"time": 120, "value": [1.22253, 1.22253, 1.22253]}, {"time": 121, "value": [1.22253, 1.22253, 1.22253]}, {"time": 122, "value": [1.22253, 1.22253, 1.22253]}, {"time": 123, "value": [1.22253, 1.22253, 1.22253]}, {"time": 124, "value": [1.22253, 1.22253, 1.22253]}, {"time": 125, "value": [1.22253, 1.22253, 1.22253]}, {"time": 126, "value": [1.22253, 1.22253, 1.22253]}, {"time": 127, "value": [1.22253, 1.22253, 1.22253]}, {"time": 128, "value": [1.22253, 1.22253, 1.22253]}, {"time": 129, "value": [1.22253, 1.22253, 1.22253]}, {"time": 130, "value": [1.22253, 1.22253, 1.22253]}, {"time": 131, "value": [1.22253, 1.22253, 1.22253]}, {"time": 132, "value": [1.22253, 1.22253, 1.22253]}, {"time": 133, "value": [1.22253, 1.22253, 1.22253]}, {"time": 134, "value": [1.22253, 1.22253, 1.22253]}, {"time": 135, "value": [1.22253, 1.22253, 1.22253]}, {"time": 136, "value": [1.22253, 1.22253, 1.22253]}, {"time": 137, "value": [1.22253, 1.22253, 1.22253]}, {"time": 138, "value": [1.22253, 1.22253, 1.22253]}, {"time": 139, "value": [1.22253, 1.22253, 1.22253]}, {"time": 140, "value": [1.22253, 1.22253, 1.22253]}, {"time": 141, "value": [1.22253, 1.22253, 1.22253]}, {"time": 142, "value": [1.22253, 1.22253, 1.22253]}, {"time": 143, "value": [1.22253, 1.22253, 1.22253]}, {"time": 144, "value": [1.22253, 1.22253, 1.22253]}, {"time": 145, "value": [1.22253, 1.22253, 1.22253]}], "name": "cylinder001.scale"}, {"type": "quaternion", "keys": [{"time": 5, "value": [-0.707107, 0, 0, 0.707107]}, {"time": 6, "value": [-0.724247, 0, 0, 0.689541]}, {"time": 7, "value": [-0.740951, 0, 0, 0.671559]}, {"time": 8, "value": [-0.757209, 0, 0, 0.653173]}, {"time": 9, "value": [-0.77301, 0, 0, 0.634393]}, {"time": 10, "value": [-0.788346, 0, 0, 0.615232]}, {"time": 11, "value": [-0.803207, 0, 0, 0.595699]}, {"time": 12, "value": [-0.817585, 0, 0, 0.575808]}, {"time": 13, "value": [-0.83147, 0, 0, 0.55557]}, {"time": 14, "value": [-0.844854, 0, 0, 0.534998]}, {"time": 15, "value": [-0.857729, 0, 0, 0.514103]}, {"time": 16, "value": [-0.870087, 0, 0, 0.492898]}, {"time": 17, "value": [-0.881921, 0, 0, 0.471397]}, {"time": 18, "value": [-0.893224, 0, 0, 0.449611]}, {"time": 19, "value": [-0.903989, 0, 0, 0.427555]}, {"time": 20, "value": [-0.91421, 0, 0, 0.405241]}, {"time": 21, "value": [-0.92388, 0, 0, 0.382683]}, {"time": 22, "value": [-0.932993, 0, 0, 0.359895]}, {"time": 23, "value": [-0.941544, 0, 0, 0.33689]}, {"time": 24, "value": [-0.949528, 0, 0, 0.313682]}, {"time": 25, "value": [-0.95694, 0, 0, 0.290284]}, {"time": 26, "value": [-0.963776, 0, 0, 0.266713]}, {"time": 27, "value": [-0.970031, 0, 0, 0.24298]}, {"time": 28, "value": [-0.975702, 0, 0, 0.219101]}, {"time": 29, "value": [-0.980785, 0, 0, 0.19509]}, {"time": 30, "value": [-0.985278, 0, 0, 0.170962]}, {"time": 31, "value": [-0.989177, 0, 0, 0.146731]}, {"time": 32, "value": [-0.99248, 0, 0, 0.12241]}, {"time": 33, "value": [-0.995185, 0, 0, 0.098017]}, {"time": 34, "value": [-0.99729, 0, 0, 0.073565]}, {"time": 35, "value": [-0.998796, 0, 0, 0.049067]}, {"time": 36, "value": [-0.999699, 0, 0, 0.02454]}, {"time": 37, "value": [1, 0, 0, 0]}, {"time": 38, "value": [0.999733, 0, 0, 0.023095]}, {"time": 39, "value": [0.998933, 0, 0, 0.046182]}, {"time": 40, "value": [0.9976, 0, 0, 0.069244]}, {"time": 41, "value": [0.995734, 0, 0, 0.092268]}, {"time": 42, "value": [0.993337, 0, 0, 0.115243]}, {"time": 43, "value": [0.99041, 0, 0, 0.138156]}, {"time": 44, "value": [0.986955, 0, 0, 0.160996]}, {"time": 45, "value": [0.982973, 0, 0, 0.183749]}, {"time": 46, "value": [0.978467, 0, 0, 0.206405]}, {"time": 47, "value": [0.973438, 0, 0, 0.22895]}, {"time": 48, "value": [0.96789, 0, 0, 0.251374]}, {"time": 49, "value": [0.961826, 0, 0, 0.273663]}, {"time": 50, "value": [0.955248, 0, 0, 0.295806]}, {"time": 51, "value": [0.948161, 0, 0, 0.317791]}, {"time": 52, "value": [0.940567, 0, 0, 0.339607]}, {"time": 53, "value": [0.932472, 0, 0, 0.361242]}, {"time": 54, "value": [0.92388, 0, 0, 0.382683]}, {"time": 55, "value": [0.91421, 0, 0, 0.405241]}, {"time": 56, "value": [0.903989, 0, 0, 0.427555]}, {"time": 57, "value": [0.893224, 0, 0, 0.449611]}, {"time": 58, "value": [0.881921, 0, 0, 0.471397]}, {"time": 59, "value": [0.870087, 0, 0, 0.492898]}, {"time": 60, "value": [0.857729, 0, 0, 0.514103]}, {"time": 61, "value": [0.844854, 0, 0, 0.534998]}, {"time": 62, "value": [0.83147, 0, 0, 0.55557]}, {"time": 63, "value": [0.817585, 0, 0, 0.575808]}, {"time": 64, "value": [0.803208, 0, 0, 0.595699]}, {"time": 65, "value": [0.788347, 0, 0, 0.615231]}, {"time": 66, "value": [0.773011, 0, 0, 0.634393]}, {"time": 67, "value": [0.757209, 0, 0, 0.653173]}, {"time": 68, "value": [0.740951, 0, 0, 0.671559]}, {"time": 69, "value": [0.724247, 0, 0, 0.68954]}, {"time": 70, "value": [0.707107, 0, 0, 0.707107]}, {"time": 71, "value": [0.707107, 0, 0, 0.707107]}, {"time": 72, "value": [0.707107, 0, 0, 0.707107]}, {"time": 73, "value": [0.707107, 0, 0, 0.707107]}, {"time": 74, "value": [0.707107, 0, 0, 0.707107]}, {"time": 75, "value": [0.707107, 0, 0, 0.707107]}, {"time": 76, "value": [0.707107, 0, 0, 0.707107]}, {"time": 77, "value": [0.707107, 0, 0, 0.707107]}, {"time": 78, "value": [0.707107, 0, 0, 0.707107]}, {"time": 80, "value": [0.707107, 0, 0, 0.707107]}, {"time": 81, "value": [0.724247, 0, 0, 0.68954]}, {"time": 82, "value": [0.740951, 0, 0, 0.671559]}, {"time": 83, "value": [0.757209, 0, 0, 0.653173]}, {"time": 84, "value": [0.773011, 0, 0, 0.634393]}, {"time": 85, "value": [0.788347, 0, 0, 0.615231]}, {"time": 86, "value": [0.803208, 0, 0, 0.595699]}, {"time": 87, "value": [0.817585, 0, 0, 0.575808]}, {"time": 88, "value": [0.83147, 0, 0, 0.55557]}, {"time": 89, "value": [0.844854, 0, 0, 0.534998]}, {"time": 90, "value": [0.857729, 0, 0, 0.514103]}, {"time": 91, "value": [0.870087, 0, 0, 0.492898]}, {"time": 92, "value": [0.881921, 0, 0, 0.471397]}, {"time": 93, "value": [0.893224, 0, 0, 0.449611]}, {"time": 94, "value": [0.903989, 0, 0, 0.427555]}, {"time": 95, "value": [0.91421, 0, 0, 0.405241]}, {"time": 96, "value": [0.92388, 0, 0, 0.382683]}, {"time": 97, "value": [0.932993, 0, 0, 0.359895]}, {"time": 98, "value": [0.941544, 0, 0, 0.33689]}, {"time": 99, "value": [0.949528, 0, 0, 0.313681]}, {"time": 100, "value": [0.95694, 0, 0, 0.290285]}, {"time": 101, "value": [0.963776, 0, 0, 0.266713]}, {"time": 102, "value": [0.970031, 0, 0, 0.24298]}, {"time": 103, "value": [0.975702, 0, 0, 0.219101]}, {"time": 104, "value": [0.980785, 0, 0, 0.19509]}, {"time": 105, "value": [0.985278, 0, 0, 0.170962]}, {"time": 106, "value": [0.989177, 0, 0, 0.14673]}, {"time": 107, "value": [0.99248, 0, 0, 0.122411]}, {"time": 108, "value": [0.995185, 0, 0, 0.098017]}, {"time": 109, "value": [0.99729, 0, 0, 0.073565]}, {"time": 110, "value": [0.998795, 0, 0, 0.049068]}, {"time": 111, "value": [0.999699, 0, 0, 0.024541]}, {"time": 112, "value": [1, 0, 0, 0]}, {"time": 113, "value": [-0.999733, 0, 0, 0.023095]}, {"time": 114, "value": [-0.998933, 0, 0, 0.046183]}, {"time": 115, "value": [-0.9976, 0, 0, 0.069245]}, {"time": 116, "value": [-0.995734, 0, 0, 0.092268]}, {"time": 117, "value": [-0.993337, 0, 0, 0.115243]}, {"time": 118, "value": [-0.99041, 0, 0, 0.138156]}, {"time": 119, "value": [-0.986955, 0, 0, 0.160996]}, {"time": 120, "value": [-0.982973, 0, 0, 0.18375]}, {"time": 121, "value": [-0.978467, 0, 0, 0.206405]}, {"time": 122, "value": [-0.973438, 0, 0, 0.228951]}, {"time": 123, "value": [-0.96789, 0, 0, 0.251374]}, {"time": 124, "value": [-0.961826, 0, 0, 0.273663]}, {"time": 125, "value": [-0.955248, 0, 0, 0.295806]}, {"time": 126, "value": [-0.948161, 0, 0, 0.317791]}, {"time": 127, "value": [-0.940567, 0, 0, 0.339607]}, {"time": 128, "value": [-0.932472, 0, 0, 0.361242]}, {"time": 129, "value": [-0.92388, 0, 0, 0.382683]}, {"time": 130, "value": [-0.913626, 0, 0, 0.406556]}, {"time": 131, "value": [-0.901681, 0, 0, 0.432402]}, {"time": 132, "value": [-0.888125, 0, 0, 0.459602]}, {"time": 133, "value": [-0.873095, 0, 0, 0.48755]}, {"time": 134, "value": [-0.856791, 0, 0, 0.515664]}, {"time": 135, "value": [-0.839477, 0, 0, 0.543395]}, {"time": 136, "value": [-0.821485, 0, 0, 0.57023]}, {"time": 137, "value": [-0.803208, 0, 0, 0.595699]}, {"time": 138, "value": [-0.785091, 0, 0, 0.61938]}, {"time": 139, "value": [-0.767631, 0, 0, 0.640892]}, {"time": 140, "value": [-0.751355, 0, 0, 0.659898]}, {"time": 141, "value": [-0.736817, 0, 0, 0.676093]}, {"time": 142, "value": [-0.724578, 0, 0, 0.689193]}, {"time": 143, "value": [-0.715195, 0, 0, 0.698925]}, {"time": 144, "value": [-0.709205, 0, 0, 0.705002]}, {"time": 145, "value": [-0.707107, 0, 0, 0.707107]}], "name": "cylinder001.quaternion"}, {"type": "vector3", "keys": [{"time": 5, "value": [0, 19.6808, 17.1202]}, {"time": 6, "value": [0, 19.6808, 17.1202]}, {"time": 7, "value": [0, 19.6808, 17.1202]}, {"time": 8, "value": [0, 19.6808, 17.1202]}, {"time": 9, "value": [0, 19.6808, 17.1202]}, {"time": 10, "value": [0, 19.6808, 17.1202]}, {"time": 11, "value": [0, 19.6808, 17.1202]}, {"time": 12, "value": [0, 19.6808, 17.1202]}, {"time": 13, "value": [0, 19.6808, 17.1202]}, {"time": 14, "value": [0, 19.6808, 17.1202]}, {"time": 15, "value": [0, 19.6808, 17.1202]}, {"time": 16, "value": [0, 19.6808, 17.1202]}, {"time": 17, "value": [0, 19.6808, 17.1202]}, {"time": 18, "value": [0, 19.6808, 17.1202]}, {"time": 19, "value": [0, 19.6808, 17.1202]}, {"time": 20, "value": [0, 19.6808, 17.1202]}, {"time": 21, "value": [0, 19.6808, 17.1202]}, {"time": 22, "value": [0, 19.6808, 17.1202]}, {"time": 23, "value": [0, 19.6808, 17.1202]}, {"time": 24, "value": [0, 19.6808, 17.1202]}, {"time": 25, "value": [0, 19.6808, 17.1202]}, {"time": 26, "value": [0, 19.6808, 17.1202]}, {"time": 27, "value": [0, 19.6808, 17.1202]}, {"time": 28, "value": [0, 19.6808, 17.1202]}, {"time": 29, "value": [0, 19.6808, 17.1202]}, {"time": 30, "value": [0, 19.6808, 17.1202]}, {"time": 31, "value": [0, 19.6808, 17.1202]}, {"time": 32, "value": [0, 19.6808, 17.1202]}, {"time": 33, "value": [0, 19.6808, 17.1202]}, {"time": 34, "value": [0, 19.6808, 17.1202]}, {"time": 35, "value": [0, 19.6808, 17.1202]}, {"time": 36, "value": [0, 19.6808, 17.1202]}, {"time": 37, "value": [0, 19.6808, 17.1202]}, {"time": 38, "value": [0, 19.6808, 17.1202]}, {"time": 39, "value": [0, 19.6808, 17.1202]}, {"time": 40, "value": [0, 19.6808, 17.1202]}, {"time": 41, "value": [0, 19.6808, 17.1202]}, {"time": 42, "value": [0, 19.6808, 17.1202]}, {"time": 43, "value": [0, 19.6808, 17.1202]}, {"time": 44, "value": [0, 19.6808, 17.1202]}, {"time": 45, "value": [0, 19.6808, 17.1202]}, {"time": 46, "value": [0, 19.6808, 17.1202]}, {"time": 47, "value": [0, 19.6808, 17.1202]}, {"time": 48, "value": [0, 19.6808, 17.1202]}, {"time": 49, "value": [0, 19.6808, 17.1202]}, {"time": 50, "value": [0, 19.6808, 17.1202]}, {"time": 51, "value": [0, 19.6808, 17.1202]}, {"time": 52, "value": [0, 19.6808, 17.1202]}, {"time": 53, "value": [0, 19.6808, 17.1202]}, {"time": 54, "value": [0, 19.6808, 17.1202]}, {"time": 55, "value": [0, 19.6808, 17.1202]}, {"time": 56, "value": [0, 19.6808, 17.1202]}, {"time": 57, "value": [0, 19.6808, 17.1202]}, {"time": 58, "value": [0, 19.6808, 17.1202]}, {"time": 59, "value": [0, 19.6808, 17.1202]}, {"time": 60, "value": [0, 19.6808, 17.1202]}, {"time": 61, "value": [0, 19.6808, 17.1202]}, {"time": 62, "value": [0, 19.6808, 17.1202]}, {"time": 63, "value": [0, 19.6808, 17.1202]}, {"time": 64, "value": [0, 19.6808, 17.1202]}, {"time": 65, "value": [0, 19.6808, 17.1202]}, {"time": 66, "value": [0, 19.6808, 17.1202]}, {"time": 67, "value": [0, 19.6808, 17.1202]}, {"time": 68, "value": [0, 19.6808, 17.1202]}, {"time": 69, "value": [0, 19.6808, 17.1202]}, {"time": 70, "value": [0, 19.6808, 17.1202]}, {"time": 71, "value": [0, 19.6808, 17.1202]}, {"time": 72, "value": [0, 19.6808, 17.1202]}, {"time": 73, "value": [0, 19.6808, 17.1202]}, {"time": 74, "value": [0, 19.6808, 17.1202]}, {"time": 75, "value": [0, 19.6808, 17.1202]}, {"time": 80, "value": [0, 19.6808, 17.1202]}, {"time": 81, "value": [0, 19.6808, 17.1202]}, {"time": 82, "value": [0, 19.6808, 17.1202]}, {"time": 83, "value": [0, 19.6808, 17.1202]}, {"time": 84, "value": [0, 19.6808, 17.1202]}, {"time": 85, "value": [0, 19.6808, 17.1202]}, {"time": 86, "value": [0, 19.6808, 17.1202]}, {"time": 87, "value": [0, 19.6808, 17.1202]}, {"time": 88, "value": [0, 19.6808, 17.1202]}, {"time": 89, "value": [0, 19.6808, 17.1202]}, {"time": 90, "value": [0, 19.6808, 17.1202]}, {"time": 91, "value": [0, 19.6808, 17.1202]}, {"time": 92, "value": [0, 19.6808, 17.1202]}, {"time": 93, "value": [0, 19.6808, 17.1202]}, {"time": 94, "value": [0, 19.6808, 17.1202]}, {"time": 95, "value": [0, 19.6808, 17.1202]}, {"time": 96, "value": [0, 19.6808, 17.1202]}, {"time": 97, "value": [0, 19.6808, 17.1202]}, {"time": 98, "value": [0, 19.6808, 17.1202]}, {"time": 99, "value": [0, 19.6808, 17.1202]}, {"time": 100, "value": [0, 19.6808, 17.1202]}, {"time": 101, "value": [0, 19.6808, 17.1202]}, {"time": 102, "value": [0, 19.6808, 17.1202]}, {"time": 103, "value": [0, 19.6808, 17.1202]}, {"time": 104, "value": [0, 19.6808, 17.1202]}, {"time": 105, "value": [0, 19.6808, 17.1202]}, {"time": 106, "value": [0, 19.6808, 17.1202]}, {"time": 107, "value": [0, 19.6808, 17.1202]}, {"time": 108, "value": [0, 19.6808, 17.1202]}, {"time": 109, "value": [0, 19.6808, 17.1202]}, {"time": 110, "value": [0, 19.6808, 17.1202]}, {"time": 111, "value": [0, 19.6808, 17.1202]}, {"time": 112, "value": [0, 19.6808, 17.1202]}, {"time": 113, "value": [0, 19.6808, 17.1202]}, {"time": 114, "value": [0, 19.6808, 17.1202]}, {"time": 115, "value": [0, 19.6808, 17.1202]}, {"time": 116, "value": [0, 19.6808, 17.1202]}, {"time": 117, "value": [0, 19.6808, 17.1202]}, {"time": 118, "value": [0, 19.6808, 17.1202]}, {"time": 119, "value": [0, 19.6808, 17.1202]}, {"time": 120, "value": [0, 19.6808, 17.1202]}, {"time": 121, "value": [0, 19.6808, 17.1202]}, {"time": 122, "value": [0, 19.6808, 17.1202]}, {"time": 123, "value": [0, 19.6808, 17.1202]}, {"time": 124, "value": [0, 19.6808, 17.1202]}, {"time": 125, "value": [0, 19.6808, 17.1202]}, {"time": 126, "value": [0, 19.6808, 17.1202]}, {"time": 127, "value": [0, 19.6808, 17.1202]}, {"time": 128, "value": [0, 19.6808, 17.1202]}, {"time": 129, "value": [0, 19.6808, 17.1202]}, {"time": 130, "value": [0, 19.6808, 17.1202]}, {"time": 131, "value": [0, 19.6808, 17.1202]}, {"time": 132, "value": [0, 19.6808, 17.1202]}, {"time": 133, "value": [0, 19.6808, 17.1202]}, {"time": 134, "value": [0, 19.6808, 17.1202]}, {"time": 135, "value": [0, 19.6808, 17.1202]}, {"time": 136, "value": [0, 19.6808, 17.1202]}, {"time": 137, "value": [0, 19.6808, 17.1202]}, {"time": 138, "value": [0, 19.6808, 17.1202]}, {"time": 139, "value": [0, 19.6808, 17.1202]}, {"time": 140, "value": [0, 19.6808, 17.1202]}, {"time": 141, "value": [0, 19.6808, 17.1202]}, {"time": 142, "value": [0, 19.6808, 17.1202]}, {"time": 143, "value": [0, 19.6808, 17.1202]}, {"time": 144, "value": [0, 19.6808, 17.1202]}, {"time": 145, "value": [0, 19.6808, 17.1202]}], "name": "cylinder002.position"}, {"type": "vector3", "keys": [{"time": 5, "value": [1.22253, 1.22253, 1.22253]}, {"time": 6, "value": [1.22253, 1.22253, 1.22253]}, {"time": 7, "value": [1.22253, 1.22253, 1.22253]}, {"time": 8, "value": [1.22253, 1.22253, 1.22253]}, {"time": 9, "value": [1.22253, 1.22253, 1.22253]}, {"time": 10, "value": [1.22253, 1.22253, 1.22253]}, {"time": 11, "value": [1.22253, 1.22253, 1.22253]}, {"time": 12, "value": [1.22253, 1.22253, 1.22253]}, {"time": 13, "value": [1.22253, 1.22253, 1.22253]}, {"time": 14, "value": [1.22253, 1.22253, 1.22253]}, {"time": 15, "value": [1.22253, 1.22253, 1.22253]}, {"time": 16, "value": [1.22253, 1.22253, 1.22253]}, {"time": 17, "value": [1.22253, 1.22253, 1.22253]}, {"time": 18, "value": [1.22253, 1.22253, 1.22253]}, {"time": 19, "value": [1.22253, 1.22253, 1.22253]}, {"time": 20, "value": [1.22253, 1.22253, 1.22253]}, {"time": 21, "value": [1.22253, 1.22253, 1.22253]}, {"time": 22, "value": [1.22253, 1.22253, 1.22253]}, {"time": 23, "value": [1.22253, 1.22253, 1.22253]}, {"time": 24, "value": [1.22253, 1.22253, 1.22253]}, {"time": 25, "value": [1.22253, 1.22253, 1.22253]}, {"time": 26, "value": [1.22253, 1.22253, 1.22253]}, {"time": 27, "value": [1.22253, 1.22253, 1.22253]}, {"time": 28, "value": [1.22253, 1.22253, 1.22253]}, {"time": 29, "value": [1.22253, 1.22253, 1.22253]}, {"time": 30, "value": [1.22253, 1.22253, 1.22253]}, {"time": 31, "value": [1.22253, 1.22253, 1.22253]}, {"time": 32, "value": [1.22253, 1.22253, 1.22253]}, {"time": 33, "value": [1.22253, 1.22253, 1.22253]}, {"time": 34, "value": [1.22253, 1.22253, 1.22253]}, {"time": 35, "value": [1.22253, 1.22253, 1.22253]}, {"time": 36, "value": [1.22253, 1.22253, 1.22253]}, {"time": 37, "value": [1.22253, 1.22253, 1.22253]}, {"time": 38, "value": [1.22253, 1.22253, 1.22253]}, {"time": 39, "value": [1.22253, 1.22253, 1.22253]}, {"time": 40, "value": [1.22253, 1.22253, 1.22253]}, {"time": 41, "value": [1.22253, 1.22253, 1.22253]}, {"time": 42, "value": [1.22253, 1.22253, 1.22253]}, {"time": 43, "value": [1.22253, 1.22253, 1.22253]}, {"time": 44, "value": [1.22253, 1.22253, 1.22253]}, {"time": 45, "value": [1.22253, 1.22253, 1.22253]}, {"time": 46, "value": [1.22253, 1.22253, 1.22253]}, {"time": 47, "value": [1.22253, 1.22253, 1.22253]}, {"time": 48, "value": [1.22253, 1.22253, 1.22253]}, {"time": 49, "value": [1.22253, 1.22253, 1.22253]}, {"time": 50, "value": [1.22253, 1.22253, 1.22253]}, {"time": 51, "value": [1.22253, 1.22253, 1.22253]}, {"time": 52, "value": [1.22253, 1.22253, 1.22253]}, {"time": 53, "value": [1.22253, 1.22253, 1.22253]}, {"time": 54, "value": [1.22253, 1.22253, 1.22253]}, {"time": 55, "value": [1.22253, 1.22253, 1.22253]}, {"time": 56, "value": [1.22253, 1.22253, 1.22253]}, {"time": 57, "value": [1.22253, 1.22253, 1.22253]}, {"time": 58, "value": [1.22253, 1.22253, 1.22253]}, {"time": 59, "value": [1.22253, 1.22253, 1.22253]}, {"time": 60, "value": [1.22253, 1.22253, 1.22253]}, {"time": 61, "value": [1.22253, 1.22253, 1.22253]}, {"time": 62, "value": [1.22253, 1.22253, 1.22253]}, {"time": 63, "value": [1.22253, 1.22253, 1.22253]}, {"time": 64, "value": [1.22253, 1.22253, 1.22253]}, {"time": 65, "value": [1.22253, 1.22253, 1.22253]}, {"time": 66, "value": [1.22253, 1.22253, 1.22253]}, {"time": 67, "value": [1.22253, 1.22253, 1.22253]}, {"time": 68, "value": [1.22253, 1.22253, 1.22253]}, {"time": 69, "value": [1.22253, 1.22253, 1.22253]}, {"time": 70, "value": [1.22253, 1.22253, 1.22253]}, {"time": 71, "value": [1.22253, 1.22253, 1.22253]}, {"time": 72, "value": [1.22253, 1.22253, 1.22253]}, {"time": 73, "value": [1.22253, 1.22253, 1.22253]}, {"time": 74, "value": [1.22253, 1.22253, 1.22253]}, {"time": 75, "value": [1.22253, 1.22253, 1.22253]}, {"time": 80, "value": [1.22253, 1.22253, 1.22253]}, {"time": 81, "value": [1.22253, 1.22253, 1.22253]}, {"time": 82, "value": [1.22253, 1.22253, 1.22253]}, {"time": 83, "value": [1.22253, 1.22253, 1.22253]}, {"time": 84, "value": [1.22253, 1.22253, 1.22253]}, {"time": 85, "value": [1.22253, 1.22253, 1.22253]}, {"time": 86, "value": [1.22253, 1.22253, 1.22253]}, {"time": 87, "value": [1.22253, 1.22253, 1.22253]}, {"time": 88, "value": [1.22253, 1.22253, 1.22253]}, {"time": 89, "value": [1.22253, 1.22253, 1.22253]}, {"time": 90, "value": [1.22253, 1.22253, 1.22253]}, {"time": 91, "value": [1.22253, 1.22253, 1.22253]}, {"time": 92, "value": [1.22253, 1.22253, 1.22253]}, {"time": 93, "value": [1.22253, 1.22253, 1.22253]}, {"time": 94, "value": [1.22253, 1.22253, 1.22253]}, {"time": 95, "value": [1.22253, 1.22253, 1.22253]}, {"time": 96, "value": [1.22253, 1.22253, 1.22253]}, {"time": 97, "value": [1.22253, 1.22253, 1.22253]}, {"time": 98, "value": [1.22253, 1.22253, 1.22253]}, {"time": 99, "value": [1.22253, 1.22253, 1.22253]}, {"time": 100, "value": [1.22253, 1.22253, 1.22253]}, {"time": 101, "value": [1.22253, 1.22253, 1.22253]}, {"time": 102, "value": [1.22253, 1.22253, 1.22253]}, {"time": 103, "value": [1.22253, 1.22253, 1.22253]}, {"time": 104, "value": [1.22253, 1.22253, 1.22253]}, {"time": 105, "value": [1.22253, 1.22253, 1.22253]}, {"time": 106, "value": [1.22253, 1.22253, 1.22253]}, {"time": 107, "value": [1.22253, 1.22253, 1.22253]}, {"time": 108, "value": [1.22253, 1.22253, 1.22253]}, {"time": 109, "value": [1.22253, 1.22253, 1.22253]}, {"time": 110, "value": [1.22253, 1.22253, 1.22253]}, {"time": 111, "value": [1.22253, 1.22253, 1.22253]}, {"time": 112, "value": [1.22253, 1.22253, 1.22253]}, {"time": 113, "value": [1.22253, 1.22253, 1.22253]}, {"time": 114, "value": [1.22253, 1.22253, 1.22253]}, {"time": 115, "value": [1.22253, 1.22253, 1.22253]}, {"time": 116, "value": [1.22253, 1.22253, 1.22253]}, {"time": 117, "value": [1.22253, 1.22253, 1.22253]}, {"time": 118, "value": [1.22253, 1.22253, 1.22253]}, {"time": 119, "value": [1.22253, 1.22253, 1.22253]}, {"time": 120, "value": [1.22253, 1.22253, 1.22253]}, {"time": 121, "value": [1.22253, 1.22253, 1.22253]}, {"time": 122, "value": [1.22253, 1.22253, 1.22253]}, {"time": 123, "value": [1.22253, 1.22253, 1.22253]}, {"time": 124, "value": [1.22253, 1.22253, 1.22253]}, {"time": 125, "value": [1.22253, 1.22253, 1.22253]}, {"time": 126, "value": [1.22253, 1.22253, 1.22253]}, {"time": 127, "value": [1.22253, 1.22253, 1.22253]}, {"time": 128, "value": [1.22253, 1.22253, 1.22253]}, {"time": 129, "value": [1.22253, 1.22253, 1.22253]}, {"time": 130, "value": [1.22253, 1.22253, 1.22253]}, {"time": 131, "value": [1.22253, 1.22253, 1.22253]}, {"time": 132, "value": [1.22253, 1.22253, 1.22253]}, {"time": 133, "value": [1.22253, 1.22253, 1.22253]}, {"time": 134, "value": [1.22253, 1.22253, 1.22253]}, {"time": 135, "value": [1.22253, 1.22253, 1.22253]}, {"time": 136, "value": [1.22253, 1.22253, 1.22253]}, {"time": 137, "value": [1.22253, 1.22253, 1.22253]}, {"time": 138, "value": [1.22253, 1.22253, 1.22253]}, {"time": 139, "value": [1.22253, 1.22253, 1.22253]}, {"time": 140, "value": [1.22253, 1.22253, 1.22253]}, {"time": 141, "value": [1.22253, 1.22253, 1.22253]}, {"time": 142, "value": [1.22253, 1.22253, 1.22253]}, {"time": 143, "value": [1.22253, 1.22253, 1.22253]}, {"time": 144, "value": [1.22253, 1.22253, 1.22253]}, {"time": 145, "value": [1.22253, 1.22253, 1.22253]}], "name": "cylinder002.scale"}, {"type": "quaternion", "keys": [{"time": 5, "value": [-0.707107, 0, 0, 0.707107]}, {"time": 6, "value": [-0.689541, 0, 0, 0.724247]}, {"time": 7, "value": [-0.671559, 0, 0, 0.740951]}, {"time": 8, "value": [-0.653173, 0, 0, 0.757209]}, {"time": 9, "value": [-0.634393, 0, 0, 0.77301]}, {"time": 10, "value": [-0.615232, 0, 0, 0.788346]}, {"time": 11, "value": [-0.595699, 0, 0, 0.803208]}, {"time": 12, "value": [-0.575808, 0, 0, 0.817585]}, {"time": 13, "value": [-0.55557, 0, 0, 0.83147]}, {"time": 14, "value": [-0.534998, 0, 0, 0.844854]}, {"time": 15, "value": [-0.514103, 0, 0, 0.857729]}, {"time": 16, "value": [-0.492898, 0, 0, 0.870087]}, {"time": 17, "value": [-0.471397, 0, 0, 0.881921]}, {"time": 18, "value": [-0.449611, 0, 0, 0.893224]}, {"time": 19, "value": [-0.427555, 0, 0, 0.903989]}, {"time": 20, "value": [-0.405241, 0, 0, 0.91421]}, {"time": 21, "value": [-0.382683, 0, 0, 0.92388]}, {"time": 22, "value": [-0.359895, 0, 0, 0.932993]}, {"time": 23, "value": [-0.33689, 0, 0, 0.941544]}, {"time": 24, "value": [-0.313682, 0, 0, 0.949528]}, {"time": 25, "value": [-0.290285, 0, 0, 0.95694]}, {"time": 26, "value": [-0.266713, 0, 0, 0.963776]}, {"time": 27, "value": [-0.24298, 0, 0, 0.970031]}, {"time": 28, "value": [-0.219101, 0, 0, 0.975702]}, {"time": 29, "value": [-0.19509, 0, 0, 0.980785]}, {"time": 30, "value": [-0.170962, 0, 0, 0.985278]}, {"time": 31, "value": [-0.14673, 0, 0, 0.989177]}, {"time": 32, "value": [-0.122411, 0, 0, 0.99248]}, {"time": 33, "value": [-0.098017, 0, 0, 0.995185]}, {"time": 34, "value": [-0.073565, 0, 0, 0.99729]}, {"time": 35, "value": [-0.049068, 0, 0, 0.998796]}, {"time": 36, "value": [-0.024541, 0, 0, 0.999699]}, {"time": 37, "value": [0, 0, 0, 1]}, {"time": 38, "value": [0.023098, 0, 0, 0.999733]}, {"time": 39, "value": [0.046184, 0, 0, 0.998933]}, {"time": 40, "value": [0.069245, 0, 0, 0.9976]}, {"time": 41, "value": [0.092269, 0, 0, 0.995734]}, {"time": 42, "value": [0.115243, 0, 0, 0.993337]}, {"time": 43, "value": [0.138157, 0, 0, 0.99041]}, {"time": 44, "value": [0.160996, 0, 0, 0.986955]}, {"time": 45, "value": [0.18375, 0, 0, 0.982973]}, {"time": 46, "value": [0.206405, 0, 0, 0.978467]}, {"time": 47, "value": [0.22895, 0, 0, 0.973438]}, {"time": 48, "value": [0.251374, 0, 0, 0.96789]}, {"time": 49, "value": [0.273663, 0, 0, 0.961826]}, {"time": 50, "value": [0.295806, 0, 0, 0.955248]}, {"time": 51, "value": [0.317791, 0, 0, 0.948161]}, {"time": 52, "value": [0.339607, 0, 0, 0.940567]}, {"time": 53, "value": [0.361242, 0, 0, 0.932472]}, {"time": 54, "value": [0.382683, 0, 0, 0.92388]}, {"time": 55, "value": [0.405241, 0, 0, 0.91421]}, {"time": 56, "value": [0.427555, 0, 0, 0.903989]}, {"time": 57, "value": [0.449612, 0, 0, 0.893224]}, {"time": 58, "value": [0.471397, 0, 0, 0.881921]}, {"time": 59, "value": [0.492898, 0, 0, 0.870087]}, {"time": 60, "value": [0.514103, 0, 0, 0.857729]}, {"time": 61, "value": [0.534998, 0, 0, 0.844854]}, {"time": 62, "value": [0.55557, 0, 0, 0.83147]}, {"time": 63, "value": [0.575808, 0, 0, 0.817585]}, {"time": 64, "value": [0.595699, 0, 0, 0.803208]}, {"time": 65, "value": [0.615231, 0, 0, 0.788347]}, {"time": 66, "value": [0.634393, 0, 0, 0.773011]}, {"time": 67, "value": [0.653173, 0, 0, 0.757209]}, {"time": 68, "value": [0.671559, 0, 0, 0.740951]}, {"time": 69, "value": [0.68954, 0, 0, 0.724247]}, {"time": 70, "value": [0.707107, 0, 0, 0.707107]}, {"time": 71, "value": [0.707107, 0, 0, 0.707107]}, {"time": 72, "value": [0.707107, 0, 0, 0.707107]}, {"time": 73, "value": [0.707107, 0, 0, 0.707107]}, {"time": 74, "value": [0.707107, 0, 0, 0.707107]}, {"time": 75, "value": [0.707107, 0, 0, 0.707107]}, {"time": 80, "value": [0.707107, 0, 0, 0.707107]}, {"time": 81, "value": [0.689541, 0, 0, 0.724247]}, {"time": 82, "value": [0.671559, 0, 0, 0.740951]}, {"time": 83, "value": [0.653173, 0, 0, 0.757209]}, {"time": 84, "value": [0.634393, 0, 0, 0.773011]}, {"time": 85, "value": [0.615231, 0, 0, 0.788347]}, {"time": 86, "value": [0.595699, 0, 0, 0.803208]}, {"time": 87, "value": [0.575808, 0, 0, 0.817585]}, {"time": 88, "value": [0.55557, 0, 0, 0.83147]}, {"time": 89, "value": [0.534998, 0, 0, 0.844854]}, {"time": 90, "value": [0.514103, 0, 0, 0.857729]}, {"time": 91, "value": [0.492898, 0, 0, 0.870087]}, {"time": 92, "value": [0.471397, 0, 0, 0.881921]}, {"time": 93, "value": [0.449611, 0, 0, 0.893224]}, {"time": 94, "value": [0.427555, 0, 0, 0.903989]}, {"time": 95, "value": [0.405241, 0, 0, 0.91421]}, {"time": 96, "value": [0.382683, 0, 0, 0.92388]}, {"time": 97, "value": [0.359895, 0, 0, 0.932993]}, {"time": 98, "value": [0.33689, 0, 0, 0.941544]}, {"time": 99, "value": [0.313682, 0, 0, 0.949528]}, {"time": 100, "value": [0.290285, 0, 0, 0.95694]}, {"time": 101, "value": [0.266712, 0, 0, 0.963776]}, {"time": 102, "value": [0.24298, 0, 0, 0.970031]}, {"time": 103, "value": [0.219101, 0, 0, 0.975702]}, {"time": 104, "value": [0.19509, 0, 0, 0.980785]}, {"time": 105, "value": [0.170962, 0, 0, 0.985278]}, {"time": 106, "value": [0.146731, 0, 0, 0.989177]}, {"time": 107, "value": [0.122411, 0, 0, 0.99248]}, {"time": 108, "value": [0.098017, 0, 0, 0.995185]}, {"time": 109, "value": [0.073565, 0, 0, 0.997291]}, {"time": 110, "value": [0.049068, 0, 0, 0.998796]}, {"time": 111, "value": [0.024541, 0, 0, 0.999699]}, {"time": 112, "value": [0, 0, 0, 1]}, {"time": 113, "value": [-0.023098, 0, 0, 0.999733]}, {"time": 114, "value": [-0.046183, 0, 0, 0.998933]}, {"time": 115, "value": [-0.069244, 0, 0, 0.9976]}, {"time": 116, "value": [-0.092268, 0, 0, 0.995734]}, {"time": 117, "value": [-0.115243, 0, 0, 0.993337]}, {"time": 118, "value": [-0.138156, 0, 0, 0.990411]}, {"time": 119, "value": [-0.160996, 0, 0, 0.986955]}, {"time": 120, "value": [-0.183749, 0, 0, 0.982973]}, {"time": 121, "value": [-0.206405, 0, 0, 0.978467]}, {"time": 122, "value": [-0.22895, 0, 0, 0.973438]}, {"time": 123, "value": [-0.251374, 0, 0, 0.96789]}, {"time": 124, "value": [-0.273663, 0, 0, 0.961826]}, {"time": 125, "value": [-0.295806, 0, 0, 0.955248]}, {"time": 126, "value": [-0.317791, 0, 0, 0.948161]}, {"time": 127, "value": [-0.339607, 0, 0, 0.940567]}, {"time": 128, "value": [-0.361242, 0, 0, 0.932472]}, {"time": 129, "value": [-0.382683, 0, 0, 0.92388]}, {"time": 130, "value": [-0.406556, 0, 0, 0.913626]}, {"time": 131, "value": [-0.432402, 0, 0, 0.901681]}, {"time": 132, "value": [-0.459602, 0, 0, 0.888125]}, {"time": 133, "value": [-0.48755, 0, 0, 0.873095]}, {"time": 134, "value": [-0.515664, 0, 0, 0.856791]}, {"time": 135, "value": [-0.543395, 0, 0, 0.839477]}, {"time": 136, "value": [-0.57023, 0, 0, 0.821485]}, {"time": 137, "value": [-0.595699, 0, 0, 0.803208]}, {"time": 138, "value": [-0.61938, 0, 0, 0.785092]}, {"time": 139, "value": [-0.640892, 0, 0, 0.767631]}, {"time": 140, "value": [-0.659898, 0, 0, 0.751355]}, {"time": 141, "value": [-0.676093, 0, 0, 0.736817]}, {"time": 142, "value": [-0.689193, 0, 0, 0.724578]}, {"time": 143, "value": [-0.698925, 0, 0, 0.715195]}, {"time": 144, "value": [-0.705002, 0, 0, 0.709205]}, {"time": 145, "value": [-0.707107, 0, 0, 0.707107]}], "name": "cylinder002.quaternion"}, {"type": "vector3", "keys": [{"time": 0, "value": [-2e-06, -6.71175, 3.01972]}, {"time": 5, "value": [-2e-06, -6.71175, 3.01972]}, {"time": 70, "value": [-2e-06, -6.52962, -38.1906]}, {"time": 80, "value": [-2e-06, -6.52962, -38.1906]}, {"time": 145, "value": [-2e-06, -6.71175, 3.01972]}], "name": "cylinder003.position"}, {"type": "vector3", "keys": [{"time": 0, "value": [1, 1, 1]}, {"time": 5, "value": [1, 1, 1]}, {"time": 70, "value": [1, 1, 1]}, {"time": 80, "value": [1, 1, 1]}, {"time": 145, "value": [1, 1, 1]}], "name": "cylinder003.scale"}, {"type": "quaternion", "keys": [{"time": 0, "value": [-0.707107, 0, 0, 0.707107]}, {"time": 5, "value": [-0.707107, 0, 0, 0.707107]}, {"time": 70, "value": [-0.707107, 0, 0, 0.707107]}, {"time": 80, "value": [-0.707107, 0, 0, 0.707107]}, {"time": 145, "value": [-0.707107, 0, 0, 0.707107]}], "name": "cylinder003.quaternion"}]}], "materials": [{"vertexColors": false, "specular": 1907997, "name": "bluephong", "color": 8396, "depthTest": true, "depthWrite": true, "emissive": 0, "ambient": 8396, "shininess": 50, "type": "MeshPhongMaterial", "uuid": "3701AB15-0042-3531-BDFA-EB0FA575D7D5", "blending": "NormalBlending"}, {"vertexColors": false, "specular": 1579032, "name": "greenphong", "color": 1887232, "depthTest": true, "depthWrite": true, "emissive": 0, "ambient": 1887232, "shininess": 50, "type": "MeshPhongMaterial", "uuid": "541CFE3C-B1BD-37FD-8A82-3870870BB8A8", "blending": "NormalBlending"}, {"vertexColors": false, "specular": 1579032, "name": "redphong", "color": 13369344, "depthTest": true, "depthWrite": true, "emissive": 0, "ambient": 13369344, "shininess": 50, "type": "MeshPhongMaterial", "uuid": "D95A0291-848F-32DE-B7CF-F93015785600", "blending": "NormalBlending"}], "textures": [], "metadata": {"version": 4.4, "type": "Object", "generator": "io_three", "sourceFile": "webgl-node-animation-threejs-example.blend"}, "geometries": [{"type": "Geometry", "uuid": "84174F78-6B50-3C0D-A3D3-D1203D616F79", "data": {"normals": [0, -0.002441, 0.999969, 0, 0.999969, 0.000855, -1, 0, 0, -0.999969, 0, 0, 0, -0.999969, -0.000855, 1, 0, 0, 0, -0.999939, -0.008423, 0.999969, 0, 0, 0, 0.002441, -0.999969, 0, 0.001953, 0.999969, 0, -0.001953, -0.999969, 0, -0.999939, 0.008942, 0, 0.002411, -0.999969, 0, 0.001984, 0.999969, 0, -0.001984, -0.999969, 0, -0.999939, -0.008454, 0, 0.001923, 0.999969, 0, -0.001923, -0.999969, 0, 0.999969, 0, 0, 1, 0, 0, -1, 0, 0, -0.999969, 0], "name": "cylinder003Geometry", "uvs": [[0.083333, 0, 0.166667, 0, 0.166667, 1, 0.083333, 1, 0.066987, 0.25, 0.25, 0.066987]], "faces": [40, 0, 1, 2, 0, 1, 2, 0, 0, 0, 40, 3, 4, 5, 2, 3, 0, 0, 0, 0, 40, 6, 7, 8, 3, 0, 0, 1, 1, 1, 40, 9, 10, 11, 0, 3, 3, 1, 1, 1, 40, 12, 13, 14, 4, 5, 1, 2, 3, 2, 40, 15, 16, 17, 1, 0, 4, 2, 2, 2, 40, 18, 19, 20, 1, 2, 2, 4, 4, 4, 40, 21, 22, 23, 2, 1, 1, 4, 4, 4, 40, 24, 25, 26, 5, 4, 3, 5, 5, 5, 40, 27, 28, 29, 3, 2, 5, 5, 5, 5, 40, 30, 31, 32, 0, 1, 2, 0, 0, 0, 40, 33, 34, 35, 2, 3, 0, 0, 0, 0, 40, 36, 37, 38, 3, 0, 0, 1, 1, 1, 40, 39, 40, 41, 0, 3, 3, 1, 1, 1, 40, 42, 43, 44, 4, 5, 1, 3, 2, 3, 40, 45, 46, 47, 1, 0, 4, 3, 2, 2, 40, 48, 49, 50, 1, 2, 2, 6, 6, 6, 40, 51, 52, 53, 2, 1, 1, 6, 6, 6, 40, 54, 55, 56, 5, 4, 3, 7, 7, 5, 40, 57, 58, 59, 3, 2, 5, 7, 5, 7, 40, 60, 61, 62, 5, 4, 4, 2, 3, 2, 40, 63, 64, 65, 4, 5, 5, 2, 3, 2, 40, 66, 67, 68, 4, 5, 5, 5, 7, 5, 40, 69, 70, 71, 5, 4, 4, 5, 7, 5, 40, 72, 73, 74, 5, 4, 4, 3, 2, 3, 40, 75, 76, 77, 4, 5, 5, 2, 2, 3, 40, 78, 79, 80, 4, 5, 5, 5, 5, 7, 40, 81, 82, 83, 5, 4, 4, 5, 7, 7, 40, 84, 85, 86, 5, 1, 4, 8, 8, 8, 40, 87, 88, 89, 4, 3, 5, 8, 8, 8, 40, 90, 91, 92, 5, 1, 4, 8, 8, 8, 40, 93, 94, 95, 4, 3, 5, 8, 8, 8, 40, 96, 97, 98, 2, 1, 0, 9, 9, 9, 40, 99, 100, 101, 0, 3, 2, 9, 9, 9, 40, 102, 103, 104, 5, 5, 4, 3, 3, 3, 40, 105, 106, 107, 4, 4, 5, 2, 2, 2, 40, 108, 109, 110, 1, 5, 3, 10, 10, 10, 40, 111, 112, 113, 3, 4, 1, 10, 10, 10, 40, 114, 115, 116, 5, 5, 4, 5, 5, 5, 40, 117, 118, 119, 4, 4, 5, 7, 5, 5, 40, 120, 121, 122, 0, 1, 2, 0, 0, 0, 40, 123, 124, 125, 2, 3, 0, 0, 0, 0, 40, 126, 127, 128, 3, 0, 0, 1, 1, 1, 40, 129, 130, 131, 0, 3, 3, 1, 1, 1, 40, 132, 133, 134, 4, 5, 1, 2, 3, 3, 40, 135, 136, 137, 1, 0, 4, 2, 2, 3, 40, 138, 139, 140, 1, 2, 2, 11, 11, 11, 40, 141, 142, 143, 2, 1, 1, 11, 11, 11, 40, 144, 145, 146, 5, 4, 3, 5, 5, 7, 40, 147, 148, 149, 3, 2, 5, 5, 5, 5, 40, 150, 151, 152, 5, 4, 4, 2, 2, 2, 40, 153, 154, 155, 4, 5, 5, 2, 2, 2, 40, 156, 157, 158, 4, 5, 5, 7, 7, 5, 40, 159, 160, 161, 5, 4, 4, 5, 5, 5, 40, 162, 163, 164, 5, 1, 4, 12, 12, 12, 40, 165, 166, 167, 4, 3, 5, 12, 12, 12, 40, 168, 169, 170, 2, 1, 0, 13, 13, 13, 40, 171, 172, 173, 0, 3, 2, 13, 13, 13, 40, 174, 175, 176, 5, 5, 4, 2, 2, 2, 40, 177, 178, 179, 4, 4, 5, 2, 2, 2, 40, 180, 181, 182, 1, 5, 3, 14, 14, 14, 40, 183, 184, 185, 3, 4, 1, 14, 14, 14, 40, 186, 187, 188, 5, 5, 4, 5, 5, 5, 40, 189, 190, 191, 4, 4, 5, 5, 7, 5, 40, 192, 193, 194, 0, 1, 2, 0, 0, 0, 40, 195, 196, 197, 2, 3, 0, 0, 0, 0, 40, 198, 199, 200, 3, 0, 0, 1, 1, 1, 40, 201, 202, 203, 0, 3, 3, 1, 1, 1, 40, 204, 205, 206, 4, 5, 1, 2, 2, 2, 40, 207, 208, 209, 1, 0, 4, 3, 2, 2, 40, 210, 211, 212, 1, 2, 2, 15, 15, 15, 40, 213, 214, 215, 2, 1, 1, 6, 6, 6, 40, 216, 217, 218, 5, 4, 3, 5, 5, 7, 40, 219, 220, 221, 3, 2, 5, 7, 5, 5, 40, 222, 223, 224, 5, 4, 4, 2, 2, 3, 40, 225, 226, 227, 4, 5, 5, 3, 2, 2, 40, 228, 229, 230, 4, 5, 5, 5, 5, 5, 40, 231, 232, 233, 5, 4, 4, 5, 5, 5, 40, 234, 235, 236, 5, 1, 4, 8, 8, 8, 40, 237, 238, 239, 4, 3, 5, 8, 8, 8, 40, 240, 241, 242, 2, 1, 0, 16, 16, 16, 40, 243, 244, 245, 0, 3, 2, 16, 16, 16, 40, 246, 247, 248, 5, 5, 4, 2, 2, 2, 40, 249, 250, 251, 4, 4, 5, 3, 2, 2, 40, 252, 253, 254, 1, 5, 3, 17, 17, 17, 40, 255, 256, 257, 3, 4, 1, 17, 17, 17, 40, 258, 259, 260, 5, 5, 4, 5, 7, 5, 40, 261, 262, 263, 4, 4, 5, 5, 5, 5, 40, 264, 265, 266, 0, 1, 2, 0, 0, 0, 40, 267, 268, 269, 2, 3, 0, 0, 0, 0, 40, 270, 271, 272, 3, 0, 0, 1, 1, 1, 40, 273, 274, 275, 0, 3, 3, 1, 1, 1, 40, 276, 277, 278, 4, 5, 1, 2, 2, 2, 40, 279, 280, 281, 1, 0, 4, 2, 2, 2, 40, 282, 283, 284, 1, 2, 2, 4, 4, 4, 40, 285, 286, 287, 2, 1, 1, 4, 4, 4, 40, 288, 289, 290, 5, 4, 3, 5, 5, 5, 40, 291, 292, 293, 3, 2, 5, 5, 7, 5, 40, 294, 295, 296, 5, 4, 4, 2, 2, 2, 40, 297, 298, 299, 4, 5, 5, 2, 2, 2, 40, 300, 301, 302, 4, 5, 5, 5, 7, 5, 40, 303, 304, 305, 5, 4, 4, 5, 5, 5, 40, 306, 307, 308, 5, 1, 4, 8, 8, 8, 40, 309, 310, 311, 4, 3, 5, 8, 8, 8, 40, 312, 313, 314, 2, 1, 0, 13, 13, 13, 40, 315, 316, 317, 0, 3, 2, 13, 13, 13, 40, 318, 319, 320, 5, 5, 4, 2, 3, 2, 40, 321, 322, 323, 4, 4, 5, 2, 2, 2, 40, 324, 325, 326, 1, 5, 3, 14, 14, 14, 40, 327, 328, 329, 3, 4, 1, 14, 14, 14, 40, 330, 331, 332, 5, 5, 4, 5, 5, 5, 40, 333, 334, 335, 4, 4, 5, 5, 5, 5, 40, 336, 337, 338, 4, 4, 4, 18, 19, 19, 40, 339, 340, 341, 4, 3, 4, 18, 19, 19, 40, 342, 343, 344, 5, 5, 5, 20, 20, 20, 40, 345, 346, 347, 5, 1, 5, 21, 21, 20], "metadata": {"version": 3, "faces": 116, "uvs": 1, "generator": "io_three", "normals": 22, "vertices": 348}, "vertices": [-2.86946, 3.06908, 14.3402, -2.86946, -3.13919, 14.325, 2.86946, -3.13919, 14.325, 2.86946, -3.13919, 14.325, 2.86946, 3.06908, 14.3402, -2.86946, 3.06908, 14.3402, 2.86946, 3.0758, 6.56424, -2.86946, 3.0758, 6.56424, -2.86946, 3.06908, 14.3402, -2.86946, 3.06908, 14.3402, 2.86946, 3.06908, 14.3402, 2.86946, 3.0758, 6.56424, -2.86946, 3.0758, 6.56424, -2.86946, -3.13247, 6.54906, -2.86946, -3.13919, 14.325, -2.86946, -3.13919, 14.325, -2.86946, 3.06908, 14.3402, -2.86946, 3.0758, 6.56424, -2.86946, -3.13247, 6.54906, 2.86946, -3.13247, 6.54906, 2.86946, -3.13919, 14.325, 2.86946, -3.13919, 14.325, -2.86946, -3.13919, 14.325, -2.86946, -3.13247, 6.54906, 2.86946, -3.13247, 6.54906, 2.86946, 3.0758, 6.56424, 2.86946, 3.06908, 14.3402, 2.86946, 3.06908, 14.3402, 2.86946, -3.13919, 14.325, 2.86946, -3.13247, 6.54906, -2.86946, -10.842, 14.3402, -2.86946, -17.0503, 14.325, 2.86946, -17.0503, 14.325, 2.86946, -17.0503, 14.325, 2.86946, -10.842, 14.3402, -2.86946, -10.842, 14.3402, 2.86946, -10.8353, 6.56424, -2.86946, -10.8353, 6.56424, -2.86946, -10.842, 14.3402, -2.86946, -10.842, 14.3402, 2.86946, -10.842, 14.3402, 2.86946, -10.8353, 6.56424, -2.86946, -10.8353, 6.56424, -2.86946, -16.9845, 6.54906, -2.86946, -17.0503, 14.325, -2.86946, -17.0503, 14.325, -2.86946, -10.842, 14.3402, -2.86946, -10.8353, 6.56424, -2.86946, -16.9845, 6.54906, 2.86946, -16.9845, 6.54906, 2.86946, -17.0503, 14.325, 2.86946, -17.0503, 14.325, -2.86946, -17.0503, 14.325, -2.86946, -16.9845, 6.54906, 2.86946, -16.9845, 6.54906, 2.86946, -10.8353, 6.56424, 2.86946, -10.842, 14.3402, 2.86946, -10.842, 14.3402, 2.86946, -17.0503, 14.325, 2.86946, -16.9845, 6.54906, -2.86946, -3.13247, 6.54906, -2.86946, 3.0758, 6.56424, -2.86946, 3.0758, -0.993256, -2.86946, 3.0758, -0.993256, -2.86946, -3.13247, -1.00844, -2.86946, -3.13247, 6.54906, 2.86946, 3.0758, 6.56424, 2.86946, -3.13247, 6.54906, 2.86946, -3.13247, -1.00844, 2.86946, -3.13247, -1.00844, 2.86946, 3.0758, -0.993257, 2.86946, 3.0758, 6.56424, -2.86946, -16.9845, 6.54906, -2.86946, -10.8353, 6.56424, -2.86946, -10.8353, -0.993254, -2.86946, -10.8353, -0.993254, -2.86946, -16.9845, -1.00844, -2.86946, -16.9845, 6.54906, 2.86946, -10.8353, 6.56424, 2.86946, -16.9845, 6.54906, 2.86946, -16.9845, -1.00844, 2.86946, -16.9845, -1.00844, 2.86946, -10.8353, -0.993255, 2.86946, -10.8353, 6.56424, 2.86946, -3.13247, -1.00844, -2.86946, -3.13247, -1.00844, -2.86946, 3.0758, -0.993256, -2.86946, 3.0758, -0.993256, 2.86946, 3.0758, -0.993257, 2.86946, -3.13247, -1.00844, 2.86946, -16.9845, -1.00844, -2.86946, -16.9845, -1.00844, -2.86946, -10.8353, -0.993254, -2.86946, -10.8353, -0.993254, 2.86946, -10.8353, -0.993255, 2.86946, -16.9845, -1.00844, 2.86946, -3.13247, 6.54906, -2.86946, -3.13247, 6.54906, -2.86946, -10.8353, 6.56424, -2.86946, -10.8353, 6.56424, 2.86946, -10.8353, 6.56424, 2.86946, -3.13247, 6.54906, -2.86946, -3.13247, 6.54906, -2.86946, -3.13247, -1.00844, -2.86946, -10.8353, -0.993254, -2.86946, -10.8353, -0.993254, -2.86946, -10.8353, 6.56424, -2.86946, -3.13247, 6.54906, -2.86946, -3.13247, -1.00844, 2.86946, -3.13247, -1.00844, 2.86946, -10.8353, -0.993255, 2.86946, -10.8353, -0.993255, -2.86946, -10.8353, -0.993254, -2.86946, -3.13247, -1.00844, 2.86946, -3.13247, -1.00844, 2.86946, -3.13247, 6.54906, 2.86946, -10.8353, 6.56424, 2.86946, -10.8353, 6.56424, 2.86946, -10.8353, -0.993255, 2.86946, -3.13247, -1.00844, -2.86946, -24.6351, 14.3402, -2.86946, -30.8433, 14.325, 2.86946, -30.8433, 14.325, 2.86946, -30.8433, 14.325, 2.86946, -24.6351, 14.3402, -2.86946, -24.6351, 14.3402, 2.86946, -24.6283, 6.56424, -2.86946, -24.6283, 6.56424, -2.86946, -24.6351, 14.3402, -2.86946, -24.6351, 14.3402, 2.86946, -24.6351, 14.3402, 2.86946, -24.6283, 6.56424, -2.86946, -24.6283, 6.56424, -2.86946, -30.9131, 6.54906, -2.86946, -30.8433, 14.325, -2.86946, -30.8433, 14.325, -2.86946, -24.6351, 14.3402, -2.86946, -24.6283, 6.56424, -2.86946, -30.9131, 6.54906, 2.86946, -30.9131, 6.54906, 2.86946, -30.8433, 14.325, 2.86946, -30.8433, 14.325, -2.86946, -30.8433, 14.325, -2.86946, -30.9131, 6.54906, 2.86946, -30.9131, 6.54906, 2.86946, -24.6283, 6.56424, 2.86946, -24.6351, 14.3402, 2.86946, -24.6351, 14.3402, 2.86946, -30.8433, 14.325, 2.86946, -30.9131, 6.54906, -2.86946, -30.9131, 6.54906, -2.86946, -24.6283, 6.56424, -2.86946, -24.6283, -0.993253, -2.86946, -24.6283, -0.993253, -2.86946, -30.9131, -1.00844, -2.86946, -30.9131, 6.54906, 2.86946, -24.6283, 6.56424, 2.86946, -30.9131, 6.54906, 2.86946, -30.9131, -1.00844, 2.86946, -30.9131, -1.00844, 2.86946, -24.6283, -0.993255, 2.86946, -24.6283, 6.56424, 2.86946, -30.9131, -1.00844, -2.86946, -30.9131, -1.00844, -2.86946, -24.6283, -0.993253, -2.86946, -24.6283, -0.993253, 2.86946, -24.6283, -0.993255, 2.86946, -30.9131, -1.00844, 2.86946, -16.9845, 6.54906, -2.86946, -16.9845, 6.54906, -2.86946, -24.6283, 6.56424, -2.86946, -24.6283, 6.56424, 2.86946, -24.6283, 6.56424, 2.86946, -16.9845, 6.54906, -2.86946, -16.9845, 6.54906, -2.86946, -16.9845, -1.00844, -2.86946, -24.6283, -0.993253, -2.86946, -24.6283, -0.993253, -2.86946, -24.6283, 6.56424, -2.86946, -16.9845, 6.54906, -2.86946, -16.9845, -1.00844, 2.86946, -16.9845, -1.00844, 2.86946, -24.6283, -0.993255, 2.86946, -24.6283, -0.993255, -2.86946, -24.6283, -0.993253, -2.86946, -16.9845, -1.00844, 2.86946, -16.9845, -1.00844, 2.86946, -16.9845, 6.54906, 2.86946, -24.6283, 6.56424, 2.86946, -24.6283, 6.56424, 2.86946, -24.6283, -0.993255, 2.86946, -16.9845, -1.00844, -2.86945, -38.6991, 14.3402, -2.86945, -44.9074, 14.325, 2.86946, -44.9074, 14.325, 2.86946, -44.9074, 14.325, 2.86946, -38.6991, 14.3402, -2.86945, -38.6991, 14.3402, 2.86946, -38.6924, 6.56424, -2.86946, -38.6924, 6.56424, -2.86945, -38.6991, 14.3402, -2.86945, -38.6991, 14.3402, 2.86946, -38.6991, 14.3402, 2.86946, -38.6924, 6.56424, -2.86946, -38.6924, 6.56424, -2.86945, -44.8417, 6.54906, -2.86945, -44.9074, 14.325, -2.86945, -44.9074, 14.325, -2.86945, -38.6991, 14.3402, -2.86946, -38.6924, 6.56424, -2.86945, -44.8417, 6.54906, 2.86946, -44.8417, 6.54906, 2.86946, -44.9074, 14.325, 2.86946, -44.9074, 14.325, -2.86945, -44.9074, 14.325, -2.86945, -44.8417, 6.54906, 2.86946, -44.8417, 6.54906, 2.86946, -38.6924, 6.56424, 2.86946, -38.6991, 14.3402, 2.86946, -38.6991, 14.3402, 2.86946, -44.9074, 14.325, 2.86946, -44.8417, 6.54906, -2.86945, -44.8417, 6.54906, -2.86946, -38.6924, 6.56424, -2.86946, -38.6924, -0.993251, -2.86946, -38.6924, -0.993251, -2.86946, -44.8417, -1.00843, -2.86945, -44.8417, 6.54906, 2.86946, -38.6924, 6.56424, 2.86946, -44.8417, 6.54906, 2.86946, -44.8417, -1.00844, 2.86946, -44.8417, -1.00844, 2.86946, -38.6924, -0.993253, 2.86946, -38.6924, 6.56424, 2.86946, -44.8417, -1.00844, -2.86946, -44.8417, -1.00843, -2.86946, -38.6924, -0.993251, -2.86946, -38.6924, -0.993251, 2.86946, -38.6924, -0.993253, 2.86946, -44.8417, -1.00844, 2.86946, -30.9131, 6.54906, -2.86946, -30.9131, 6.54906, -2.86946, -38.6924, 6.56424, -2.86946, -38.6924, 6.56424, 2.86946, -38.6924, 6.56424, 2.86946, -30.9131, 6.54906, -2.86946, -30.9131, 6.54906, -2.86946, -30.9131, -1.00844, -2.86946, -38.6924, -0.993251, -2.86946, -38.6924, -0.993251, -2.86946, -38.6924, 6.56424, -2.86946, -30.9131, 6.54906, -2.86946, -30.9131, -1.00844, 2.86946, -30.9131, -1.00844, 2.86946, -38.6924, -0.993253, 2.86946, -38.6924, -0.993253, -2.86946, -38.6924, -0.993251, -2.86946, -30.9131, -1.00844, 2.86946, -30.9131, -1.00844, 2.86946, -30.9131, 6.54906, 2.86946, -38.6924, 6.56424, 2.86946, -38.6924, 6.56424, 2.86946, -38.6924, -0.993253, 2.86946, -30.9131, -1.00844, -2.86945, -52.4922, 14.3402, -2.86945, -58.7005, 14.325, 2.86946, -58.7005, 14.325, 2.86946, -58.7005, 14.325, 2.86946, -52.4922, 14.3402, -2.86945, -52.4922, 14.3402, 2.86946, -52.4855, 6.56424, -2.86945, -52.4855, 6.56424, -2.86945, -52.4922, 14.3402, -2.86945, -52.4922, 14.3402, 2.86946, -52.4922, 14.3402, 2.86946, -52.4855, 6.56424, -2.86945, -52.4855, 6.56424, -2.86945, -58.6937, 6.54906, -2.86945, -58.7005, 14.325, -2.86945, -58.7005, 14.325, -2.86945, -52.4922, 14.3402, -2.86945, -52.4855, 6.56424, -2.86945, -58.6937, 6.54906, 2.86946, -58.6937, 6.54906, 2.86946, -58.7005, 14.325, 2.86946, -58.7005, 14.325, -2.86945, -58.7005, 14.325, -2.86945, -58.6937, 6.54906, 2.86946, -58.6937, 6.54906, 2.86946, -52.4855, 6.56424, 2.86946, -52.4922, 14.3402, 2.86946, -52.4922, 14.3402, 2.86946, -58.7005, 14.325, 2.86946, -58.6937, 6.54906, -2.86945, -58.6937, 6.54906, -2.86945, -52.4855, 6.56424, -2.86945, -52.4855, -0.99325, -2.86945, -52.4855, -0.99325, -2.86945, -58.6937, -1.00843, -2.86945, -58.6937, 6.54906, 2.86946, -52.4855, 6.56424, 2.86946, -58.6937, 6.54906, 2.86946, -58.6937, -1.00843, 2.86946, -58.6937, -1.00843, 2.86946, -52.4855, -0.993253, 2.86946, -52.4855, 6.56424, 2.86946, -58.6937, -1.00843, -2.86945, -58.6937, -1.00843, -2.86945, -52.4855, -0.99325, -2.86945, -52.4855, -0.99325, 2.86946, -52.4855, -0.993253, 2.86946, -58.6937, -1.00843, 2.86946, -44.8417, 6.54906, -2.86945, -44.8417, 6.54906, -2.86945, -52.4855, 6.56424, -2.86945, -52.4855, 6.56424, 2.86946, -52.4855, 6.56424, 2.86946, -44.8417, 6.54906, -2.86945, -44.8417, 6.54906, -2.86946, -44.8417, -1.00843, -2.86945, -52.4855, -0.99325, -2.86945, -52.4855, -0.99325, -2.86945, -52.4855, 6.56424, -2.86945, -44.8417, 6.54906, -2.86946, -44.8417, -1.00843, 2.86946, -44.8417, -1.00844, 2.86946, -52.4855, -0.993253, 2.86946, -52.4855, -0.993253, -2.86945, -52.4855, -0.99325, -2.86946, -44.8417, -1.00843, 2.86946, -44.8417, -1.00844, 2.86946, -44.8417, 6.54906, 2.86946, -52.4855, 6.56424, 2.86946, -52.4855, 6.56424, 2.86946, -52.4855, -0.993253, 2.86946, -44.8417, -1.00844, 2.86946, 3.0758, -0.993257, -2.86946, 3.0758, -0.993256, -2.86946, 3.0758, 6.56424, -2.86946, 3.0758, 6.56424, 2.86946, 3.0758, 6.56424, 2.86946, 3.0758, -0.993257, -2.86945, -58.6937, -1.00843, 2.86946, -58.6937, -1.00843, 2.86946, -58.6937, 6.54906, 2.86946, -58.6937, 6.54906, -2.86945, -58.6937, 6.54906, -2.86945, -58.6937, -1.00843]}}, {"type": "Geometry", "uuid": "CAEC0410-6CA0-3646-AF22-DE6D5D34C387", "data": {"normals": [0, 1, 0, 0, 0.999969, 0, 0, 0.866024, 0.499985, 0, 0.499985, 0.866024, 0, 0, 1, 0, -0.499985, 0.866024, 0, -0.866024, 0.499985, 0, -1, 0, 0, -0.999969, 0, 0, -0.866024, -0.499985, 0, -0.499985, -0.866024, 0, 0, -1, 0, 0.499985, -0.866024, 0, 0.866024, -0.499985, -1, 0, 0, -0.999969, 0, 0, 1, 0, 0, 0.999969, 0, 0], "name": "cylinder001Geometry", "uvs": [[0.75, 0, 0.833333, 0, 0.833333, 1, 0.75, 1, 0.916667, 0, 0.916667, 1, 1, 0, 1, 1, 0, 0, 0.083333, 0, 0.083333, 1, 0, 1, 0.166667, 0, 0.166667, 1, 0.25, 0, 0.25, 1, 0.333333, 0, 0.333333, 1, 0.416667, 0, 0.416667, 1, 0.5, 0, 0.5, 1, 0.583333, 0, 0.583333, 1, 0.666667, 0, 0.666667, 1, 0.933013, 0.75, 1, 0.5, 0.933013, 0.25, 0.75, 0.066987, 0.5, 0, 0.25, 0.066987, 0.066987, 0.25, 0, 0.5, 0.066987, 0.75, 0.25, 0.933013, 0.5, 1, 0.75, 0.933013]], "faces": [40, 0, 1, 2, 0, 1, 2, 0, 0, 1, 40, 3, 4, 5, 2, 3, 0, 0, 0, 0, 40, 6, 7, 8, 1, 4, 5, 2, 2, 2, 40, 9, 10, 11, 5, 2, 1, 2, 2, 2, 40, 12, 13, 14, 4, 6, 7, 3, 3, 3, 40, 15, 16, 17, 7, 5, 4, 3, 3, 3, 40, 18, 19, 20, 8, 9, 10, 4, 4, 4, 40, 21, 22, 23, 10, 11, 8, 4, 4, 4, 40, 24, 25, 26, 9, 12, 13, 5, 5, 5, 40, 27, 28, 29, 13, 10, 9, 5, 5, 5, 40, 30, 31, 32, 12, 14, 15, 6, 6, 6, 40, 33, 34, 35, 15, 13, 12, 6, 6, 6, 40, 36, 37, 38, 14, 16, 17, 7, 7, 8, 40, 39, 40, 41, 17, 15, 14, 7, 7, 8, 40, 42, 43, 44, 16, 18, 19, 9, 9, 9, 40, 45, 46, 47, 19, 17, 16, 9, 9, 9, 40, 48, 49, 50, 18, 20, 21, 10, 10, 10, 40, 51, 52, 53, 21, 19, 18, 10, 10, 10, 40, 54, 55, 56, 20, 22, 23, 11, 11, 11, 40, 57, 58, 59, 23, 21, 20, 11, 11, 11, 40, 60, 61, 62, 22, 24, 25, 12, 12, 12, 40, 63, 64, 65, 25, 23, 22, 12, 12, 12, 40, 66, 67, 68, 24, 0, 3, 13, 13, 13, 40, 69, 70, 71, 3, 25, 24, 13, 13, 13, 40, 72, 73, 74, 26, 27, 28, 14, 14, 14, 40, 75, 76, 77, 28, 29, 30, 14, 14, 14, 40, 78, 79, 80, 30, 31, 32, 14, 14, 14, 40, 81, 82, 83, 28, 30, 32, 14, 14, 14, 40, 84, 85, 86, 32, 33, 34, 14, 14, 14, 40, 87, 88, 89, 34, 35, 36, 14, 14, 14, 40, 90, 91, 92, 32, 34, 36, 14, 14, 14, 40, 93, 94, 95, 28, 32, 36, 14, 14, 14, 40, 96, 97, 98, 26, 28, 36, 14, 14, 14, 40, 99, 100, 101, 37, 26, 36, 15, 14, 14, 40, 102, 103, 104, 35, 34, 33, 16, 16, 16, 40, 105, 106, 107, 33, 32, 31, 16, 16, 16, 40, 108, 109, 110, 31, 30, 29, 16, 16, 16, 40, 111, 112, 113, 33, 31, 29, 16, 16, 16, 40, 114, 115, 116, 29, 28, 27, 16, 16, 16, 40, 117, 118, 119, 27, 26, 37, 16, 16, 16, 40, 120, 121, 122, 29, 27, 37, 16, 16, 16, 40, 123, 124, 125, 33, 29, 37, 16, 16, 16, 40, 126, 127, 128, 35, 33, 37, 16, 16, 16, 40, 129, 130, 131, 36, 35, 37, 16, 16, 16, 40, 132, 133, 134, 36, 35, 1, 14, 14, 14, 40, 135, 136, 137, 1, 0, 36, 14, 14, 14, 40, 138, 139, 140, 1, 2, 2, 4, 4, 4, 40, 141, 142, 143, 2, 1, 1, 4, 4, 4, 40, 144, 145, 146, 35, 36, 3, 16, 16, 16, 40, 147, 148, 149, 3, 2, 35, 16, 16, 16, 40, 150, 151, 152, 3, 0, 0, 11, 11, 11, 40, 153, 154, 155, 0, 3, 3, 11, 11, 11, 40, 156, 157, 158, 5, 4, 4, 13, 13, 13, 40, 159, 160, 161, 4, 5, 5, 13, 13, 13, 40, 162, 163, 164, 34, 33, 6, 14, 14, 14, 40, 165, 166, 167, 6, 4, 34, 15, 14, 14, 40, 168, 169, 170, 8, 11, 7, 6, 6, 6, 40, 171, 172, 173, 7, 6, 8, 6, 6, 6, 40, 174, 175, 176, 33, 34, 5, 16, 17, 16, 40, 177, 178, 179, 5, 7, 33, 16, 16, 16, 40, 180, 181, 182, 10, 9, 9, 2, 2, 2, 40, 183, 184, 185, 9, 10, 10, 2, 2, 2, 40, 186, 187, 188, 32, 31, 12, 14, 14, 14, 40, 189, 190, 191, 12, 9, 32, 14, 14, 14, 40, 192, 193, 194, 12, 13, 13, 9, 9, 9, 40, 195, 196, 197, 13, 12, 12, 9, 9, 9, 40, 198, 199, 200, 31, 32, 10, 16, 16, 16, 40, 201, 202, 203, 10, 13, 31, 16, 17, 16, 40, 204, 205, 206, 15, 14, 14, 4, 4, 4, 40, 207, 208, 209, 14, 15, 15, 4, 4, 4, 40, 210, 211, 212, 30, 29, 16, 14, 14, 14, 40, 213, 214, 215, 16, 14, 30, 14, 14, 14, 40, 216, 217, 218, 16, 17, 17, 11, 11, 11, 40, 219, 220, 221, 17, 16, 16, 11, 11, 11, 40, 222, 223, 224, 29, 30, 15, 17, 16, 16, 40, 225, 226, 227, 15, 17, 29, 17, 16, 16, 40, 228, 229, 230, 19, 18, 18, 6, 6, 6, 40, 231, 232, 233, 18, 19, 19, 6, 6, 6, 40, 234, 235, 236, 28, 27, 20, 14, 14, 14, 40, 237, 238, 239, 20, 18, 28, 14, 14, 14, 40, 240, 241, 242, 20, 21, 21, 13, 13, 13, 40, 243, 244, 245, 21, 20, 20, 13, 13, 13, 40, 246, 247, 248, 27, 28, 19, 16, 16, 16, 40, 249, 250, 251, 19, 21, 27, 16, 16, 16, 40, 252, 253, 254, 23, 22, 22, 9, 9, 9, 40, 255, 256, 257, 22, 23, 23, 9, 9, 9, 40, 258, 259, 260, 26, 37, 24, 14, 14, 14, 40, 261, 262, 263, 24, 22, 26, 14, 14, 14, 40, 264, 265, 266, 24, 25, 25, 2, 2, 2, 40, 267, 268, 269, 25, 24, 24, 2, 2, 2, 40, 270, 271, 272, 37, 26, 23, 16, 16, 16, 40, 273, 274, 275, 23, 25, 37, 16, 16, 16], "metadata": {"version": 3, "faces": 92, "uvs": 1, "generator": "io_three", "normals": 18, "vertices": 276}, "vertices": [-2.34716, 11.7238, -2.53913, -2.34716, 11.7238, 2.53912, 2.34716, 11.7238, 2.53912, 2.34716, 11.7238, 2.53912, 2.34716, 11.7238, -2.53913, -2.34716, 11.7238, -2.53913, -2.34716, 9.47615, 2.53913, -2.34716, 6.93703, 6.93703, 2.34716, 6.93703, 6.93703, 2.34716, 6.93703, 6.93703, 2.34716, 9.47615, 2.53913, -2.34716, 9.47615, 2.53913, -2.34716, 8.06086, 8.88356, -2.34716, 3.66296, 11.4227, 2.34716, 3.66296, 11.4227, 2.34716, 3.66296, 11.4227, 2.34716, 8.06086, 8.88356, -2.34716, 8.06086, 8.88356, -2.34716, 2.53913, 9.47615, -2.34716, -2.53913, 9.47615, 2.34716, -2.53913, 9.47615, 2.34716, -2.53913, 9.47615, 2.34716, 2.53913, 9.47615, -2.34716, 2.53913, 9.47615, -2.34716, -3.66296, 11.4227, -2.34716, -8.06086, 8.88357, 2.34716, -8.06086, 8.88357, 2.34716, -8.06086, 8.88357, 2.34716, -3.66296, 11.4227, -2.34716, -3.66296, 11.4227, -2.34716, -6.93702, 6.93703, -2.34716, -9.47615, 2.53913, 2.34716, -9.47615, 2.53913, 2.34716, -9.47615, 2.53913, 2.34716, -6.93702, 6.93703, -2.34716, -6.93702, 6.93703, -2.34716, -11.7238, 2.53913, -2.34716, -11.7238, -2.53912, 2.34716, -11.7238, -2.53912, 2.34716, -11.7238, -2.53912, 2.34716, -11.7238, 2.53913, -2.34716, -11.7238, 2.53913, -2.34716, -9.47615, -2.53912, -2.34716, -6.93703, -6.93702, 2.34716, -6.93703, -6.93702, 2.34716, -6.93703, -6.93702, 2.34716, -9.47615, -2.53912, -2.34716, -9.47615, -2.53912, -2.34716, -8.06086, -8.88356, -2.34716, -3.66297, -11.4227, 2.34716, -3.66297, -11.4227, 2.34716, -3.66297, -11.4227, 2.34716, -8.06086, -8.88356, -2.34716, -8.06086, -8.88356, -2.34716, -2.53913, -9.47615, -2.34716, 2.53912, -9.47615, 2.34716, 2.53912, -9.47615, 2.34716, 2.53912, -9.47615, 2.34716, -2.53913, -9.47615, -2.34716, -2.53913, -9.47615, -2.34716, 3.66295, -11.4227, -2.34716, 8.06085, -8.88357, 2.34716, 8.06085, -8.88357, 2.34716, 8.06085, -8.88357, 2.34716, 3.66296, -11.4227, -2.34716, 3.66295, -11.4227, -2.34716, 6.93702, -6.93703, -2.34716, 9.47615, -2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 6.93702, -6.93703, -2.34716, 6.93702, -6.93703, -2.34716, 2.53912, -9.47615, -2.34716, -2.53913, -9.47615, -2.34716, -6.93703, -6.93702, -2.34716, -6.93703, -6.93702, -2.34716, -9.47615, -2.53912, -2.34716, -9.47615, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -6.93702, 6.93703, -2.34716, -2.53913, 9.47615, -2.34716, -6.93703, -6.93702, -2.34716, -9.47615, 2.53913, -2.34716, -2.53913, 9.47615, -2.34716, -2.53913, 9.47615, -2.34716, 2.53913, 9.47615, -2.34716, 6.93703, 6.93703, -2.34716, 6.93703, 6.93703, -2.34716, 9.47615, 2.53913, -2.34716, 9.47615, -2.53913, -2.34716, -2.53913, 9.47615, -2.34716, 6.93703, 6.93703, -2.34716, 9.47615, -2.53913, -2.34716, -6.93703, -6.93702, -2.34716, -2.53913, 9.47615, -2.34716, 9.47615, -2.53913, -2.34716, 2.53912, -9.47615, -2.34716, -6.93703, -6.93702, -2.34716, 9.47615, -2.53913, -2.34716, 6.93702, -6.93703, -2.34716, 2.53912, -9.47615, -2.34716, 9.47615, -2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 6.93703, 6.93703, 2.34716, 2.53913, 9.47615, 2.34716, 2.53913, 9.47615, 2.34716, -2.53913, 9.47615, 2.34716, -6.93702, 6.93703, 2.34716, -6.93702, 6.93703, 2.34716, -9.47615, 2.53913, 2.34716, -9.47615, -2.53912, 2.34716, 2.53913, 9.47615, 2.34716, -6.93702, 6.93703, 2.34716, -9.47615, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -6.93703, -6.93702, 2.34716, -2.53913, -9.47615, 2.34716, -2.53913, -9.47615, 2.34716, 2.53912, -9.47615, 2.34716, 6.93702, -6.93703, 2.34716, -9.47615, -2.53912, 2.34716, -2.53913, -9.47615, 2.34716, 6.93702, -6.93703, 2.34716, 2.53913, 9.47615, 2.34716, -9.47615, -2.53912, 2.34716, 6.93702, -6.93703, 2.34716, 9.47615, 2.53913, 2.34716, 2.53913, 9.47615, 2.34716, 6.93702, -6.93703, 2.34716, 9.47615, -2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 6.93702, -6.93703, -2.34716, 9.47615, -2.53913, -2.34716, 9.47615, 2.53913, -2.34716, 11.7238, 2.53912, -2.34716, 11.7238, 2.53912, -2.34716, 11.7238, -2.53913, -2.34716, 9.47615, -2.53913, -2.34716, 9.47615, 2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 11.7238, 2.53912, 2.34716, 11.7238, 2.53912, -2.34716, 11.7238, 2.53912, -2.34716, 9.47615, 2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 11.7238, -2.53913, 2.34716, 11.7238, -2.53913, 2.34716, 11.7238, 2.53912, 2.34716, 9.47615, 2.53913, 2.34716, 9.47615, -2.53913, -2.34716, 9.47615, -2.53913, -2.34716, 11.7238, -2.53913, -2.34716, 11.7238, -2.53913, 2.34716, 11.7238, -2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 6.93703, 6.93703, -2.34716, 6.93703, 6.93703, -2.34716, 8.06086, 8.88356, -2.34716, 8.06086, 8.88356, 2.34716, 8.06086, 8.88356, 2.34716, 6.93703, 6.93703, -2.34716, 6.93703, 6.93703, -2.34716, 2.53913, 9.47615, -2.34716, 3.66296, 11.4227, -2.34716, 3.66296, 11.4227, -2.34716, 8.06086, 8.88356, -2.34716, 6.93703, 6.93703, -2.34716, 2.53913, 9.47615, 2.34716, 2.53913, 9.47615, 2.34716, 3.66296, 11.4227, 2.34716, 3.66296, 11.4227, -2.34716, 3.66296, 11.4227, -2.34716, 2.53913, 9.47615, 2.34716, 2.53913, 9.47615, 2.34716, 6.93703, 6.93703, 2.34716, 8.06086, 8.88356, 2.34716, 8.06086, 8.88356, 2.34716, 3.66296, 11.4227, 2.34716, 2.53913, 9.47615, 2.34716, -2.53913, 9.47615, -2.34716, -2.53913, 9.47615, -2.34716, -3.66296, 11.4227, -2.34716, -3.66296, 11.4227, 2.34716, -3.66296, 11.4227, 2.34716, -2.53913, 9.47615, -2.34716, -2.53913, 9.47615, -2.34716, -6.93702, 6.93703, -2.34716, -8.06086, 8.88357, -2.34716, -8.06086, 8.88357, -2.34716, -3.66296, 11.4227, -2.34716, -2.53913, 9.47615, -2.34716, -6.93702, 6.93703, 2.34716, -6.93702, 6.93703, 2.34716, -8.06086, 8.88357, 2.34716, -8.06086, 8.88357, -2.34716, -8.06086, 8.88357, -2.34716, -6.93702, 6.93703, 2.34716, -6.93702, 6.93703, 2.34716, -2.53913, 9.47615, 2.34716, -3.66296, 11.4227, 2.34716, -3.66296, 11.4227, 2.34716, -8.06086, 8.88357, 2.34716, -6.93702, 6.93703, 2.34716, -9.47615, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -11.7238, 2.53913, -2.34716, -11.7238, 2.53913, 2.34716, -11.7238, 2.53913, 2.34716, -9.47615, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -9.47615, -2.53912, -2.34716, -11.7238, -2.53912, -2.34716, -11.7238, -2.53912, -2.34716, -11.7238, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -9.47615, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -11.7238, -2.53912, 2.34716, -11.7238, -2.53912, -2.34716, -11.7238, -2.53912, -2.34716, -9.47615, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -9.47615, 2.53913, 2.34716, -11.7238, 2.53913, 2.34716, -11.7238, 2.53913, 2.34716, -11.7238, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -6.93703, -6.93702, -2.34716, -6.93703, -6.93702, -2.34716, -8.06086, -8.88356, -2.34716, -8.06086, -8.88356, 2.34716, -8.06086, -8.88356, 2.34716, -6.93703, -6.93702, -2.34716, -6.93703, -6.93702, -2.34716, -2.53913, -9.47615, -2.34716, -3.66297, -11.4227, -2.34716, -3.66297, -11.4227, -2.34716, -8.06086, -8.88356, -2.34716, -6.93703, -6.93702, -2.34716, -2.53913, -9.47615, 2.34716, -2.53913, -9.47615, 2.34716, -3.66297, -11.4227, 2.34716, -3.66297, -11.4227, -2.34716, -3.66297, -11.4227, -2.34716, -2.53913, -9.47615, 2.34716, -2.53913, -9.47615, 2.34716, -6.93703, -6.93702, 2.34716, -8.06086, -8.88356, 2.34716, -8.06086, -8.88356, 2.34716, -3.66297, -11.4227, 2.34716, -2.53913, -9.47615, 2.34716, 2.53912, -9.47615, -2.34716, 2.53912, -9.47615, -2.34716, 3.66295, -11.4227, -2.34716, 3.66295, -11.4227, 2.34716, 3.66296, -11.4227, 2.34716, 2.53912, -9.47615, -2.34716, 2.53912, -9.47615, -2.34716, 6.93702, -6.93703, -2.34716, 8.06085, -8.88357, -2.34716, 8.06085, -8.88357, -2.34716, 3.66295, -11.4227, -2.34716, 2.53912, -9.47615, -2.34716, 6.93702, -6.93703, 2.34716, 6.93702, -6.93703, 2.34716, 8.06085, -8.88357, 2.34716, 8.06085, -8.88357, -2.34716, 8.06085, -8.88357, -2.34716, 6.93702, -6.93703, 2.34716, 6.93702, -6.93703, 2.34716, 2.53912, -9.47615, 2.34716, 3.66296, -11.4227, 2.34716, 3.66296, -11.4227, 2.34716, 8.06085, -8.88357, 2.34716, 6.93702, -6.93703]}}, {"type": "Geometry", "uuid": "0AF705E6-E495-351F-944E-8E96CEB82A7B", "data": {"normals": [0, 1, 0, 0, 0.999969, 0, 0, 0.866024, 0.499985, 0, 0.499985, 0.866024, 0, 0, 1, 0, -0.499985, 0.866024, 0, -0.866024, 0.499985, 0, -1, 0, 0, -0.999969, 0, 0, -0.866024, -0.499985, 0, -0.499985, -0.866024, 0, 0, -1, 0, 0.499985, -0.866024, 0, 0.866024, -0.499985, -1, 0, 0, -0.999969, 0, 0, 1, 0, 0, 0.999969, 0, 0], "name": "cylinder002Geometry", "uvs": [[0.75, 0, 0.833333, 0, 0.833333, 1, 0.75, 1, 0.916667, 0, 0.916667, 1, 1, 0, 1, 1, 0, 0, 0.083333, 0, 0.083333, 1, 0, 1, 0.166667, 0, 0.166667, 1, 0.25, 0, 0.25, 1, 0.333333, 0, 0.333333, 1, 0.416667, 0, 0.416667, 1, 0.5, 0, 0.5, 1, 0.583333, 0, 0.583333, 1, 0.666667, 0, 0.666667, 1, 0.933013, 0.75, 1, 0.5, 0.933013, 0.25, 0.75, 0.066987, 0.5, 0, 0.25, 0.066987, 0.066987, 0.25, 0, 0.5, 0.066987, 0.75, 0.25, 0.933013, 0.5, 1, 0.75, 0.933013]], "faces": [40, 0, 1, 2, 0, 1, 2, 0, 0, 1, 40, 3, 4, 5, 2, 3, 0, 0, 0, 0, 40, 6, 7, 8, 1, 4, 5, 2, 2, 2, 40, 9, 10, 11, 5, 2, 1, 2, 2, 2, 40, 12, 13, 14, 4, 6, 7, 3, 3, 3, 40, 15, 16, 17, 7, 5, 4, 3, 3, 3, 40, 18, 19, 20, 8, 9, 10, 4, 4, 4, 40, 21, 22, 23, 10, 11, 8, 4, 4, 4, 40, 24, 25, 26, 9, 12, 13, 5, 5, 5, 40, 27, 28, 29, 13, 10, 9, 5, 5, 5, 40, 30, 31, 32, 12, 14, 15, 6, 6, 6, 40, 33, 34, 35, 15, 13, 12, 6, 6, 6, 40, 36, 37, 38, 14, 16, 17, 7, 7, 8, 40, 39, 40, 41, 17, 15, 14, 7, 7, 8, 40, 42, 43, 44, 16, 18, 19, 9, 9, 9, 40, 45, 46, 47, 19, 17, 16, 9, 9, 9, 40, 48, 49, 50, 18, 20, 21, 10, 10, 10, 40, 51, 52, 53, 21, 19, 18, 10, 10, 10, 40, 54, 55, 56, 20, 22, 23, 11, 11, 11, 40, 57, 58, 59, 23, 21, 20, 11, 11, 11, 40, 60, 61, 62, 22, 24, 25, 12, 12, 12, 40, 63, 64, 65, 25, 23, 22, 12, 12, 12, 40, 66, 67, 68, 24, 0, 3, 13, 13, 13, 40, 69, 70, 71, 3, 25, 24, 13, 13, 13, 40, 72, 73, 74, 26, 27, 28, 14, 14, 14, 40, 75, 76, 77, 28, 29, 30, 14, 14, 14, 40, 78, 79, 80, 30, 31, 32, 14, 14, 14, 40, 81, 82, 83, 28, 30, 32, 14, 14, 14, 40, 84, 85, 86, 32, 33, 34, 14, 14, 14, 40, 87, 88, 89, 34, 35, 36, 14, 14, 14, 40, 90, 91, 92, 32, 34, 36, 14, 14, 14, 40, 93, 94, 95, 28, 32, 36, 14, 14, 14, 40, 96, 97, 98, 26, 28, 36, 14, 14, 14, 40, 99, 100, 101, 37, 26, 36, 15, 14, 14, 40, 102, 103, 104, 35, 34, 33, 16, 16, 16, 40, 105, 106, 107, 33, 32, 31, 16, 16, 16, 40, 108, 109, 110, 31, 30, 29, 16, 16, 16, 40, 111, 112, 113, 33, 31, 29, 16, 16, 16, 40, 114, 115, 116, 29, 28, 27, 16, 16, 16, 40, 117, 118, 119, 27, 26, 37, 16, 16, 16, 40, 120, 121, 122, 29, 27, 37, 16, 16, 16, 40, 123, 124, 125, 33, 29, 37, 16, 16, 16, 40, 126, 127, 128, 35, 33, 37, 16, 16, 16, 40, 129, 130, 131, 36, 35, 37, 16, 16, 16, 40, 132, 133, 134, 36, 35, 1, 14, 14, 14, 40, 135, 136, 137, 1, 0, 36, 14, 14, 14, 40, 138, 139, 140, 1, 2, 2, 4, 4, 4, 40, 141, 142, 143, 2, 1, 1, 4, 4, 4, 40, 144, 145, 146, 35, 36, 3, 16, 16, 16, 40, 147, 148, 149, 3, 2, 35, 16, 16, 16, 40, 150, 151, 152, 3, 0, 0, 11, 11, 11, 40, 153, 154, 155, 0, 3, 3, 11, 11, 11, 40, 156, 157, 158, 5, 4, 4, 13, 13, 13, 40, 159, 160, 161, 4, 5, 5, 13, 13, 13, 40, 162, 163, 164, 34, 33, 6, 14, 14, 14, 40, 165, 166, 167, 6, 4, 34, 15, 14, 14, 40, 168, 169, 170, 8, 11, 7, 6, 6, 6, 40, 171, 172, 173, 7, 6, 8, 6, 6, 6, 40, 174, 175, 176, 33, 34, 5, 16, 17, 16, 40, 177, 178, 179, 5, 7, 33, 16, 16, 16, 40, 180, 181, 182, 10, 9, 9, 2, 2, 2, 40, 183, 184, 185, 9, 10, 10, 2, 2, 2, 40, 186, 187, 188, 32, 31, 12, 14, 14, 14, 40, 189, 190, 191, 12, 9, 32, 14, 14, 14, 40, 192, 193, 194, 12, 13, 13, 9, 9, 9, 40, 195, 196, 197, 13, 12, 12, 9, 9, 9, 40, 198, 199, 200, 31, 32, 10, 16, 16, 16, 40, 201, 202, 203, 10, 13, 31, 16, 17, 16, 40, 204, 205, 206, 15, 14, 14, 4, 4, 4, 40, 207, 208, 209, 14, 15, 15, 4, 4, 4, 40, 210, 211, 212, 30, 29, 16, 14, 14, 14, 40, 213, 214, 215, 16, 14, 30, 14, 14, 14, 40, 216, 217, 218, 16, 17, 17, 11, 11, 11, 40, 219, 220, 221, 17, 16, 16, 11, 11, 11, 40, 222, 223, 224, 29, 30, 15, 17, 16, 16, 40, 225, 226, 227, 15, 17, 29, 17, 16, 16, 40, 228, 229, 230, 19, 18, 18, 6, 6, 6, 40, 231, 232, 233, 18, 19, 19, 6, 6, 6, 40, 234, 235, 236, 28, 27, 20, 14, 14, 14, 40, 237, 238, 239, 20, 18, 28, 14, 14, 14, 40, 240, 241, 242, 20, 21, 21, 13, 13, 13, 40, 243, 244, 245, 21, 20, 20, 13, 13, 13, 40, 246, 247, 248, 27, 28, 19, 16, 16, 16, 40, 249, 250, 251, 19, 21, 27, 16, 16, 16, 40, 252, 253, 254, 23, 22, 22, 9, 9, 9, 40, 255, 256, 257, 22, 23, 23, 9, 9, 9, 40, 258, 259, 260, 26, 37, 24, 14, 14, 14, 40, 261, 262, 263, 24, 22, 26, 14, 14, 14, 40, 264, 265, 266, 24, 25, 25, 2, 2, 2, 40, 267, 268, 269, 25, 24, 24, 2, 2, 2, 40, 270, 271, 272, 37, 26, 23, 16, 16, 16, 40, 273, 274, 275, 23, 25, 37, 16, 16, 16], "metadata": {"version": 3, "faces": 92, "uvs": 1, "generator": "io_three", "normals": 18, "vertices": 276}, "vertices": [-2.34716, 11.7238, -2.53913, -2.34716, 11.7238, 2.53912, 2.34716, 11.7238, 2.53912, 2.34716, 11.7238, 2.53912, 2.34716, 11.7238, -2.53913, -2.34716, 11.7238, -2.53913, -2.34716, 9.47615, 2.53913, -2.34716, 6.93703, 6.93703, 2.34716, 6.93703, 6.93703, 2.34716, 6.93703, 6.93703, 2.34716, 9.47615, 2.53913, -2.34716, 9.47615, 2.53913, -2.34716, 8.06086, 8.88356, -2.34716, 3.66296, 11.4227, 2.34716, 3.66296, 11.4227, 2.34716, 3.66296, 11.4227, 2.34716, 8.06086, 8.88356, -2.34716, 8.06086, 8.88356, -2.34716, 2.53913, 9.47615, -2.34716, -2.53913, 9.47615, 2.34716, -2.53913, 9.47615, 2.34716, -2.53913, 9.47615, 2.34716, 2.53913, 9.47615, -2.34716, 2.53913, 9.47615, -2.34716, -3.66296, 11.4227, -2.34716, -8.06086, 8.88357, 2.34716, -8.06086, 8.88357, 2.34716, -8.06086, 8.88357, 2.34716, -3.66296, 11.4227, -2.34716, -3.66296, 11.4227, -2.34716, -6.93702, 6.93703, -2.34716, -9.47615, 2.53913, 2.34716, -9.47615, 2.53913, 2.34716, -9.47615, 2.53913, 2.34716, -6.93702, 6.93703, -2.34716, -6.93702, 6.93703, -2.34716, -11.7238, 2.53913, -2.34716, -11.7238, -2.53912, 2.34716, -11.7238, -2.53912, 2.34716, -11.7238, -2.53912, 2.34716, -11.7238, 2.53913, -2.34716, -11.7238, 2.53913, -2.34716, -9.47615, -2.53912, -2.34716, -6.93703, -6.93702, 2.34716, -6.93703, -6.93702, 2.34716, -6.93703, -6.93702, 2.34716, -9.47615, -2.53912, -2.34716, -9.47615, -2.53912, -2.34716, -8.06086, -8.88356, -2.34716, -3.66297, -11.4227, 2.34716, -3.66297, -11.4227, 2.34716, -3.66297, -11.4227, 2.34716, -8.06086, -8.88356, -2.34716, -8.06086, -8.88356, -2.34716, -2.53913, -9.47615, -2.34716, 2.53912, -9.47615, 2.34716, 2.53912, -9.47615, 2.34716, 2.53912, -9.47615, 2.34716, -2.53913, -9.47615, -2.34716, -2.53913, -9.47615, -2.34716, 3.66295, -11.4227, -2.34716, 8.06085, -8.88357, 2.34716, 8.06085, -8.88357, 2.34716, 8.06085, -8.88357, 2.34716, 3.66296, -11.4227, -2.34716, 3.66295, -11.4227, -2.34716, 6.93702, -6.93703, -2.34716, 9.47615, -2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 6.93702, -6.93703, -2.34716, 6.93702, -6.93703, -2.34716, 2.53912, -9.47615, -2.34716, -2.53913, -9.47615, -2.34716, -6.93703, -6.93702, -2.34716, -6.93703, -6.93702, -2.34716, -9.47615, -2.53912, -2.34716, -9.47615, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -6.93702, 6.93703, -2.34716, -2.53913, 9.47615, -2.34716, -6.93703, -6.93702, -2.34716, -9.47615, 2.53913, -2.34716, -2.53913, 9.47615, -2.34716, -2.53913, 9.47615, -2.34716, 2.53913, 9.47615, -2.34716, 6.93703, 6.93703, -2.34716, 6.93703, 6.93703, -2.34716, 9.47615, 2.53913, -2.34716, 9.47615, -2.53913, -2.34716, -2.53913, 9.47615, -2.34716, 6.93703, 6.93703, -2.34716, 9.47615, -2.53913, -2.34716, -6.93703, -6.93702, -2.34716, -2.53913, 9.47615, -2.34716, 9.47615, -2.53913, -2.34716, 2.53912, -9.47615, -2.34716, -6.93703, -6.93702, -2.34716, 9.47615, -2.53913, -2.34716, 6.93702, -6.93703, -2.34716, 2.53912, -9.47615, -2.34716, 9.47615, -2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 6.93703, 6.93703, 2.34716, 2.53913, 9.47615, 2.34716, 2.53913, 9.47615, 2.34716, -2.53913, 9.47615, 2.34716, -6.93702, 6.93703, 2.34716, -6.93702, 6.93703, 2.34716, -9.47615, 2.53913, 2.34716, -9.47615, -2.53912, 2.34716, 2.53913, 9.47615, 2.34716, -6.93702, 6.93703, 2.34716, -9.47615, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -6.93703, -6.93702, 2.34716, -2.53913, -9.47615, 2.34716, -2.53913, -9.47615, 2.34716, 2.53912, -9.47615, 2.34716, 6.93702, -6.93703, 2.34716, -9.47615, -2.53912, 2.34716, -2.53913, -9.47615, 2.34716, 6.93702, -6.93703, 2.34716, 2.53913, 9.47615, 2.34716, -9.47615, -2.53912, 2.34716, 6.93702, -6.93703, 2.34716, 9.47615, 2.53913, 2.34716, 2.53913, 9.47615, 2.34716, 6.93702, -6.93703, 2.34716, 9.47615, -2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 6.93702, -6.93703, -2.34716, 9.47615, -2.53913, -2.34716, 9.47615, 2.53913, -2.34716, 11.7238, 2.53912, -2.34716, 11.7238, 2.53912, -2.34716, 11.7238, -2.53913, -2.34716, 9.47615, -2.53913, -2.34716, 9.47615, 2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 11.7238, 2.53912, 2.34716, 11.7238, 2.53912, -2.34716, 11.7238, 2.53912, -2.34716, 9.47615, 2.53913, 2.34716, 9.47615, 2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 11.7238, -2.53913, 2.34716, 11.7238, -2.53913, 2.34716, 11.7238, 2.53912, 2.34716, 9.47615, 2.53913, 2.34716, 9.47615, -2.53913, -2.34716, 9.47615, -2.53913, -2.34716, 11.7238, -2.53913, -2.34716, 11.7238, -2.53913, 2.34716, 11.7238, -2.53913, 2.34716, 9.47615, -2.53913, 2.34716, 6.93703, 6.93703, -2.34716, 6.93703, 6.93703, -2.34716, 8.06086, 8.88356, -2.34716, 8.06086, 8.88356, 2.34716, 8.06086, 8.88356, 2.34716, 6.93703, 6.93703, -2.34716, 6.93703, 6.93703, -2.34716, 2.53913, 9.47615, -2.34716, 3.66296, 11.4227, -2.34716, 3.66296, 11.4227, -2.34716, 8.06086, 8.88356, -2.34716, 6.93703, 6.93703, -2.34716, 2.53913, 9.47615, 2.34716, 2.53913, 9.47615, 2.34716, 3.66296, 11.4227, 2.34716, 3.66296, 11.4227, -2.34716, 3.66296, 11.4227, -2.34716, 2.53913, 9.47615, 2.34716, 2.53913, 9.47615, 2.34716, 6.93703, 6.93703, 2.34716, 8.06086, 8.88356, 2.34716, 8.06086, 8.88356, 2.34716, 3.66296, 11.4227, 2.34716, 2.53913, 9.47615, 2.34716, -2.53913, 9.47615, -2.34716, -2.53913, 9.47615, -2.34716, -3.66296, 11.4227, -2.34716, -3.66296, 11.4227, 2.34716, -3.66296, 11.4227, 2.34716, -2.53913, 9.47615, -2.34716, -2.53913, 9.47615, -2.34716, -6.93702, 6.93703, -2.34716, -8.06086, 8.88357, -2.34716, -8.06086, 8.88357, -2.34716, -3.66296, 11.4227, -2.34716, -2.53913, 9.47615, -2.34716, -6.93702, 6.93703, 2.34716, -6.93702, 6.93703, 2.34716, -8.06086, 8.88357, 2.34716, -8.06086, 8.88357, -2.34716, -8.06086, 8.88357, -2.34716, -6.93702, 6.93703, 2.34716, -6.93702, 6.93703, 2.34716, -2.53913, 9.47615, 2.34716, -3.66296, 11.4227, 2.34716, -3.66296, 11.4227, 2.34716, -8.06086, 8.88357, 2.34716, -6.93702, 6.93703, 2.34716, -9.47615, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -11.7238, 2.53913, -2.34716, -11.7238, 2.53913, 2.34716, -11.7238, 2.53913, 2.34716, -9.47615, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -9.47615, -2.53912, -2.34716, -11.7238, -2.53912, -2.34716, -11.7238, -2.53912, -2.34716, -11.7238, 2.53913, -2.34716, -9.47615, 2.53913, -2.34716, -9.47615, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -11.7238, -2.53912, 2.34716, -11.7238, -2.53912, -2.34716, -11.7238, -2.53912, -2.34716, -9.47615, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -9.47615, 2.53913, 2.34716, -11.7238, 2.53913, 2.34716, -11.7238, 2.53913, 2.34716, -11.7238, -2.53912, 2.34716, -9.47615, -2.53912, 2.34716, -6.93703, -6.93702, -2.34716, -6.93703, -6.93702, -2.34716, -8.06086, -8.88356, -2.34716, -8.06086, -8.88356, 2.34716, -8.06086, -8.88356, 2.34716, -6.93703, -6.93702, -2.34716, -6.93703, -6.93702, -2.34716, -2.53913, -9.47615, -2.34716, -3.66297, -11.4227, -2.34716, -3.66297, -11.4227, -2.34716, -8.06086, -8.88356, -2.34716, -6.93703, -6.93702, -2.34716, -2.53913, -9.47615, 2.34716, -2.53913, -9.47615, 2.34716, -3.66297, -11.4227, 2.34716, -3.66297, -11.4227, -2.34716, -3.66297, -11.4227, -2.34716, -2.53913, -9.47615, 2.34716, -2.53913, -9.47615, 2.34716, -6.93703, -6.93702, 2.34716, -8.06086, -8.88356, 2.34716, -8.06086, -8.88356, 2.34716, -3.66297, -11.4227, 2.34716, -2.53913, -9.47615, 2.34716, 2.53912, -9.47615, -2.34716, 2.53912, -9.47615, -2.34716, 3.66295, -11.4227, -2.34716, 3.66295, -11.4227, 2.34716, 3.66296, -11.4227, 2.34716, 2.53912, -9.47615, -2.34716, 2.53912, -9.47615, -2.34716, 6.93702, -6.93703, -2.34716, 8.06085, -8.88357, -2.34716, 8.06085, -8.88357, -2.34716, 3.66295, -11.4227, -2.34716, 2.53912, -9.47615, -2.34716, 6.93702, -6.93703, 2.34716, 6.93702, -6.93703, 2.34716, 8.06085, -8.88357, 2.34716, 8.06085, -8.88357, -2.34716, 8.06085, -8.88357, -2.34716, 6.93702, -6.93703, 2.34716, 6.93702, -6.93703, 2.34716, 2.53912, -9.47615, 2.34716, 3.66296, -11.4227, 2.34716, 3.66296, -11.4227, 2.34716, 8.06085, -8.88357, 2.34716, 6.93702, -6.93703]}}]}