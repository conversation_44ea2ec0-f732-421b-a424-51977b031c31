MikuMikuDance.exe　Ver.5.24

3Dポリゴンモデルの振り付け用ツール

■操作方法■
ニコニコ動画にて公開中
http://www.nicovideo.jp/watch/sm2420025

■WAVE出力時の注意点■
WAVEはフレーム０から録画する時にしか出力できません。

■Windows2000での注意点■
Windows2000以前のOS(XPよりも前)で使用する場合には、
MMDxShow.dllをレジストリに登録しなければ使用できません。
Dataフォルダ内にあるWin2000.batを一度実行して下さい。

■お断り■
クリプトン・フューチャー・メディア株式会社様、その他
Vocaloidシリーズに関して権利を有する方からクレームが
あった場合には、本ツールの公開を中止する場合があります。

■JPEGライブラリ■
Independent JPEG Group(http://www.ijg.org/)のJPeg-6b
ライブラリを使用しています。

■免責事項■
本ツールを実行したことによって損害・不利益・事故等が
発生した場合でも、一切の責任を負いません。

ツールを使って製作した画像・動画を公開する場合には、
こちらには連絡や許可申請は必要ありません。
pmm、vpd、vmdファイルの公開もご自由に。
ただし、問題が発生しても一切責任を負いません。
＊＊音楽等の著作権には特に気をつけて下さい！！＊＊

■物理演算モード■
物理エンジンにはBullet Physics Engineを使用しています。
(http://www.bulletphysics.com/wordpress/ )

「ヘルプ」→「モデル拡張」→「物理演算」で、モデルの物理演算化用エディタが開きます

エディタの豆知識
・剛体の形状（位置・回転・大きさ）は画面右下のＸＹＺアイコンで操作できます。

特に『大きさ』は、ＳＨＩＦＴキーを押しながら位置アイコンのドラッグで変更できますので、気付きにくいと思います。

・グループ横の欄は、その剛体と衝突判定をしないグループを羅列します

・「ボーン追従」と「物理演算」において、「物理演算」の剛体は何らかの形で「ボーン追従」の剛体とジョイントで接続されていなければ地面に落ちます

・「物理演算」の「ボーン位置合せ」は、「物理演算」の剛体に繋がっているジョイントの位置は、ボーンの位置からズレることがあるので、それを強制的にボーンの位置に合せるための仕様です。
ボーンの位置からジョイントがズレるとモデルの破綻が起きる場合にのみ使用して下さい（使うと計算が遅くなるのと、動きが不自然になります）


■連絡先■
<EMAIL>   樋口優

http://www.geocities.jp/higuchuu4/index.htm

■バージョン履歴■
Ver.5.24(2010/11/02)
・回転連動ボーンの挙動変更

Ver.5.23(2010/08/27)
・MEIKOモデル追加
・物理演算のジョイント軸を回転させた場合の挙動に関するバグ修正

Ver.5.22a(2010/01/21)
・鏡音リンact2モデル追加
 (MMD本体はVer.5.22のまま)

Ver.5.22(2009/10/25)
・ｶﾒﾗ･照明･ｱｸｾｻﾘモードでvsq,vpdを読み込むとMMDが落ちるバグ修正

Ver.5.21(2009/10/25)
・フレーム窓内でボーン名をクリックしてボーンを選択する際、トグルする仕様に変更
・フレーム窓内のボーン括りをクリックすると、括り内の全ボーンを選択する仕様追加
・範囲選択に、現在選択中のボーンで設定する『選択ﾎﾞｰﾝ』項目追加
・縦選択を、現在選択中のフレーム全てに適応する仕様に変更
・移動のみでなく、回転時も　Shift→10倍、Ctrl→0.1倍　のキー操作追加
・背景BMPの読み込みにJpegも対応
・画面保存にBMPのみでなく、Jpegも対応
・MMDの画面に、各種ファイルをドラッグ＆ドロップできる仕様追加
・その他色々バグ修正


Ver.5.20(2009/10/23)
・AVI出力時に、モデル表示フレームが1フレーム遅れていた点を修正
・VistaのUAC作動時に落ちるバグ修正
 (Windows7については未確認。直ってるといいなぁ)
・pmmデータ保存時にバッファオーバーフローを起こす危険のあるバグ修正
 (Ver.5.19のバグ。Ver.5.19は落ちる可能性がある為、使用しないで下さい)
・その他色々バグ修正

Ver.5.19(2009/10/19)
・色々細かいバグ修正

Ver.5.18(2009/10/17)
・モデルのjpeg対応バグ修正

Ver.5.17(2009/10/17)
・テクスチャにjpegファイルを使用できるよう変更
・その他色々バグ修正

Ver.5.16(2009/09/27)
・Ver.5.15修正によるバグ修正

Ver.5.15(2009/09/27)
・ﾋﾞｯﾄﾏｯﾌﾟ+ｽﾌｨｱﾏｯﾌﾟﾓﾃﾞﾙを拡張ﾓﾃﾞﾙ保存した際にｽﾌｨｱが消えるバグ修正

Ver.5.14(2009/09/24)
・ｽﾌｨｱﾏｯﾌﾟｱｸｾｻﾘとﾓﾃﾞﾙを同時表示した際のバグ修正

Ver.5.13(2009/09/15)
・物理エンジンBullet Ver.2.75 正式版公開に伴いBulletバージョン更新

Ver.5.12(2009/09/10)
・テクスチャBMP上にスフィアマップ表示する際のファイル名を
 　　"テクスチャ名.bmp*スフィア名.bmp"　に変更。
　Ver.5.11で"/"を使っていた方は"*"に変更して下さい。
・スフィアマップのファイル名拡張子"sph"を、"spa"にすることにより
 スフィアマップの展開が乗算でなく、加算で行われる仕様を追加

Ver.5.11(2009/09/7)
・スフィアマップの計算法をVer.5.09に戻す
・テクスチャBMP上にスフィアマップ表示できる仕様に変更
 テクスチャ名を "テクスチャ名.bmp/スフィア名.bmp"にすることにより
 テクスチャ上にスフィアマップが展開されます(例：fuk1.bmp/metal.sph)
 ＊ただし、ＰＭＤフォーマットはテクスチャ名の長さが19文字分しか無いため、
 モデルで使用する場合はファイル名全体(テクスチャ+スフィア)で19文字以下に
 収める必要があります(アクセサリ(xファイル)は256文字までＯＫ)
・その他バグ色々修正

Ver.5.10(2009/08/29)
・スフィアマップ計算法を入射角を考慮する方法に変更
 (髪のキューティクル表現等が可能になりました(多分))

Ver.5.09(2009/08/25)
・スフィアマップ機能追加
　スフィアマップ用BMPを拡張子sphとしてモデル・アクセサリのテクスチャに指定
 することにより、スフィアマップが展開されて表示されます

Ver.5.08(2009/08/09)
・pmm読込時ファイルが見つからなかった場合、ファイルの場所をあらためて聞く仕様を追加

Ver.5.07(2009/08/08)
・アクセサリ拡大・縮小時にアクセサリの明るさが変化するバグ修正
・アイコンによる移動時にCtrlキーを押すことにより移動距離を1/10に減らす機能を追加

Ver.5.06(2009/08/05)
・Ver.5.05のCPU負荷が高すぎたため修正
・背景AVI表示時にも地面影を表示するように変更

Ver.5.05(2009/08/05)
・色々とバグ修正 MMDxShow.dllも修正しています。
 (※※旧VerのMMDﾌｫﾙﾀﾞを利用する場合にはMMDxShow.dllも書き換えること※※)
・Windows2000以前のOS(XPよりも前)で使用する場合には、MMDxShow.dllをレジストリに登録しなければ
 使用できません。Dataフォルダ内にあるWin2000.batを一度実行して下さい(一度実行すればおｋ)

Ver.5.04(2009/08/02)
・Ver.5.03のMMDxShow.dll実行に伴い、MSVCR71.DLL関連のエラーが出る場合があったため、
　MMDxShow.dll修正(※※旧VerのMMDﾌｫﾙﾀﾞを利用する場合にはMMDxShow.dllも書き換えること※※)。
・地面影描写が、背景BMP読込時に表示されないバグ修正

Ver.5.03(2009/08/02)
・AVI出力
　・DirectShowにて出力する仕様に変更(2GBの壁がなくなりました)
　　(Dataﾌｫﾙﾀﾞ内に新たに'MMDxShow.dll'が追加されています。以前のVerのフォルダに
　　MikuMikuDance.exeをコピーして使う場合には、上記ﾌｧｲﾙもコピーして下さい）
　・アルファチャンネルをグラボ・出力画質によらず出力できるよう変更
　・WAVEを含む場合にインターリーブ出力するよう変更
　　(ただしEscｷｰによる録画中断時にはWaveが映像よりも長くなってしまうので注意してください)
・地面影描画法変更
　　半透明でも綺麗に描写されます。地面影はモデル描写の時に描かれますので、
　　アクセサリ編集より、モデルとアクセサリの描写順をうまく設定してください。
・アクセサリ数が128個を超えると、アクセサリ編集が落ちるバグ修正
・その他色々バグ修正

Ver.5.02(2009/07/14)
・PCによって再生時に音ズレが生じるバグを修正

Ver.5.01(2009/07/06)
・AVI出力にモード追加
　・画質優先　　　Ver.5β版の出力法(βよりもさらに画質は良いが遅いです)
　・速度優先　　　Ver.5.00版の出力法
　・連番分割出力　2GBを超える場合は分割して出力して下さい。下にフレーム数を入力すると
　　　　　　　　　そのフレーム数出力する毎に連番を付けた別ファイルに記録して行きます。
　　　　　　　　　ただし、WAVEは記録できなくなります。

Ver.5.00(2009/07/02)
・物理演算モード追加（カイトモデルのみ未対応）
・描写深度を10倍まで拡大
・カメラ・モデルのアイコンによる移動時にShiftキーを押すと10倍の距離移動
・AVI出力法を、スピード優先方式に戻しました
・その他バグ色々修正

Ver.4.05(2009/05/27)
・Ver.4.04に伴うバグ修正

Ver.4.04(2009/05/27)
・再生時の動画時間計算を厳密化（音ズレ防止）

Ver.4.03(2009/05/25)
・英語対応(「ヘルプ」→「English Mode」。外国語版ＯＳの場合は自動的に英語モードで立ち上がる)
・モデルの英語化法
　　1.「ヘルプ」→「モデル拡張」→「英語名編集」にて、モデル名やボーン・表情の英語名を入力
　　2.「ヘルプ」→「モデル拡張」→「拡張モデル保存」で、英語対応版pmdを保存
・pmdモデル毎にトゥーン用ﾃｸｽﾁｬを設定できるように変更
　　1.「ヘルプ」→「モデル拡張」→「ﾄｩｰﾝﾃｸｽﾁｬ変更」でﾃｸｽﾁｬをデフォルトのものから変更
　　　・ﾃｸｽﾁｬはモデルファイル(*.pmd)と同じフォルダ内に格納しておく事
　　　・ﾃｸｽﾁｬは同名のファイルがあった場合には最初に読み込んだもののみが使用されるので、
　　　　ﾃｸｽﾁｬ名はなるべく他と被らない、ユニークな名前を使用して下さい
　　2.「ヘルプ」→「モデル拡張」→「拡張モデル保存」で、ﾄｩｰﾝﾃｸｽﾁｬを変更したモデルを保存
       ・モデル公開時には、使用したﾃｸｽﾁｬをモデル(*.pmd)と一緒に公開して下さい
・ボーンフレームに空のフレーム挿入、削除機能追加
　　「フレーム編集」→「空フレーム挿入」or「列フレーム削除」
　　（複数モデルで同じモーションを行う時に、モデル毎に動作の遅れや進みを入れる時等に使用）
・その他バグ色々修正

Ver.4.02(2009/05/08)
・ボーン、カメラの回転・移動に数値入力モード追加(「ボーン編集」→「数値入力」)
・バグ色々修正

Ver.4.0(2009/03/05)
・MMD杯記念に、まささんより初音ミクVer2モデル追加
・アイコンによる回転・移動に、localモードとglobalモード追加
・モデル仕様に捩じり用ボーン追加
・AVI出力時、スピードよりも画質優先の仕様に変更
・screen.bmpの解像度変更

Ver.3.45(2009/01/25)
・エッジの太さを各モデルごとに設定できる仕様に変更

Ver.3.44(2009/01/10)
・アクセサリの地面影表示バグ修正

Ver.3.43(2009/01/08)
・pmmデータを読み込んだ際に地面影色がおかしくなるバグ修正
・エッジのＺ深度調整（視野角を極端に小さくしても、前より少しだけ綺麗に表示されるようになりますた）

Ver.3.42(2009/01/07)
・ＫＡＩＴＯモデル半目時修正
・加算合成時の表示バグ修正
・地面影に半透明モード追加
　地面影は、１．モデル描写前のアクセサリ描写　２．モデル描写　の１と２の間に描写されます。
　地面影を透けるようにするには、ステージ等のアクセサリをモデル描写の前に描く必要があります。
　あまり綺麗に描けるわけではないので、期待しないでねｗ
・セーブデータ（ｐｍｍ）にエッジの太さ、および地面影透けるモードのデータ追加

Ver.3.41(2009/01/05)
・ＫＡＩＴＯモデル表情バグ修正
・表情窓バグ修正

Ver.3.40(2009/01/05)
・ＫＡＩＴＯモデル追加
・地面影をモデル、アクセサリよりも前に描画する描画順に変更
・モデルのエッジの太さ設定機能追加

Ver.3.30(2008/12/22)
・24bitテクスチャ対応
・別ﾌﾚｰﾑへﾍﾟｰｽﾄ機能追加
　ｺﾋﾟｰ&ﾍﾟｰｽﾄの際、操作対象ボーン（画面左上に表示されるボーン）へコピーされた全ボーンの
　フレームデータをペーストします。これにより、"前髪１"フレームの内容を"左髪"フレームに
　コピーする等、別フレーム間でのｺﾋﾟｰ&ﾍﾟｰｽﾄが可能になります
・アクセサリ編集機能追加
　アクセサリ名を自由に変更可能（デフォルトはxファイル名）
　アクセサリの描画順序設定可能（半透明のモノを描写する時に使用）
　モデル描写の前にアクセサリを描写することも可能です
・アクセサリのSizeに補間計算適用
・アクセサリフレームのｺﾋﾟｰ&ﾍﾟｰｽﾄ機能追加
　アクセサリのｺﾋﾟｰ&ﾍﾟｰｽﾄは、モデルの”別ﾌﾚｰﾑへﾍﾟｰｽﾄ機能追加”と同じく現在選択中の
　アクセサリにペーストします。これにより別アクセサリ間でのフレームコピーが可能です
・アクセサリ、モデルに加算合成表示機能追加
　光の表現強化用。加算合成表示は、フレーム登録はできません。
　また、加算合成なのでバックが白だと何も表示されていないように見えるので注意
・アクセサリフレームの範囲選択機能追加
・ﾌﾚｰﾑ位置角度補正機能追加
　選択フレームポイントの位置・角度の値をｘ倍します。　ネンドロイドタイプの手足が
　短いモデルにモーションをコピーした際や、動きを大きくしたり小さくする際に利用
・表情大きさ補正機能追加
　選択フレームポイントの表情の大きさをｘ倍します。
　リップシンク後大きさを小さくしてささやくような口の動きにしたりする事が可能です
・その他様々バグ修正

Ver.3.23(2008/12/19)
・カメラフレームのバグ修正

Ver.3.22a(2008/11/29)
・咲音メイコモデル修正（手足の長さを短くしました）

Ver.3.22(2008/11/24)
・pmmファイル読込時のアクセサリ関連バグによる強制終了の回避修正
・アクセサリに咲音マイク追加

Ver.3.21b(2008/11/24)
・咲音メイコモデルスキニング修正

Ver.3.21a(2008/11/23)
・咲音メイコモデル追加

Ver.3.21(2008/11/8)
・フレーム窓コピーボタンのモデル間での不具合修正
・地面影の明るさ調整機能追加(「表示」→「地面影色設定」)

Ver.3.20b(2008/10/31)
・亞北ネルモデル不具合修正(右ｽｶｰﾄ前、後ボーンの修正)

Ver.3.20a(2008/10/30)
・亞北ネルモデル不具合修正

Ver.3.20(2008/10/30)
・亞北ネルモデル追加
・「モデル編集時カメラ・照明(アクセサリ)追従」機能のバグ修正
・モデル編集時、視点パネルの「下面」を「カメラ」に変更する仕様に修正

Ver.3.16(2008/10/09)
・vacファイル読込時ラジアン度数表示バグ修正
・各種窓内数字修正時Deleteキー反応修正
・一部読み込めないvsqファイルに対応

Ver.3.15(2008/09/27)
・アクセサリを含むpmmファイル読込時バグ修正(ver.3.14によるバグ)

Ver.3.14(2008/09/26)
・表示･IKがらみのバグ修正
・pmmロード時、アクセサリxファイルが発見出来なかった場合に
　xファイルの場所を指定するように変更

Ver.3.13(2008/09/21)
・メニュー項目ショートカットキー重複修正
・ショートカットキー追加
　A:全て選択　S:未登録選択　D:センター位置バイアス付加
・フレーム操作窓Enterキー入力時の登録処理バグ修正

Ver.3.12(2008/09/18)
・ダミーボーン範囲選択時バグ修正
・アクセサリ数128個以上時バグ修正
・フレーム選択窓縦スクロールバーバグ修正

Ver.3.11(2008/09/17)
・wave波形表示ずれ修正
・ダイアログ表示時Enterキーでフレーム登録されるバグ修正
・pmmファイル関連付け起動対応
・レンモデル瞬き・セーラー服修正

Ver.3.10a(2008/09/12)
・鏡音レンモデルフレーム窓バグ修正

Ver.3.10(2008/09/11)
・鏡音レンモデル追加
・ディスプレイ解像度が高すぎる際に生じるエラー対策追加
（樋口の環境では出ないエラーの対策のため、うまくいったかどうかは不明）

Ver.3.05(2008/09/07)
・初音ミクモデル著作権表示修正
・弱音ハクモデル右肩スキニング修正
・アクセサリモデル255個まで追加

Ver.3.04(2008/08/31)
・線形補間ボタン不具合修正
・AVI出力時キャンセルボタン不具合修正

Ver.3.03(2008/08/30)
・ハクモデル不具合修正
・vacファイル読込追加
・その他

Ver.3.02(2008/08/30)
・表情フレーム登録ができないバグ修正
・モデルを消去したあと保存したデータのロード時エラーバグ修正

Ver.3.01(2008/08/30)
・マルチモデル化
・その他多数変更

Ver.2.02(2008/07/16)
・vsqファイル（tempo変更)バグ修正
・AVI出力時のフレームずれ修正

Ver.2.01(2008/04/08)
・トゥーンレンダリングのエッジの太さを距離によって変更させた
・Vista版対応（AVI出力時、高速モードを選択すると画質が落ちます）
・フレーム窓内も、SHIFTキー選択時に選択状態をトグルさせるように変更

Ver.2.00(2008/04/03)
・演出モードの追加
・アクセサリに地面モード追加(詳しくは"ステージ.vac"の中身参照)
・背景AVIを再生時にも同期させるようにした
・その他色々