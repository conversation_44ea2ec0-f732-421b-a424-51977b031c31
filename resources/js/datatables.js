import 'datatables.net-bs5';
import 'datatables.net-bs5/css/dataTables.bootstrap5.min.css';

// DataTables configuration for admin tables
document.addEventListener('DOMContentLoaded', function() {
    // Helper function to check if table has data rows (not just empty state)
    function hasDataRows(tableId) {
        const table = document.querySelector(tableId);
        if (!table) return false;

        const tbody = table.querySelector('tbody');
        if (!tbody) return false;

        const rows = tbody.querySelectorAll('tr');
        if (rows.length === 0) return false;

        // Check if the only row is an empty state row (colspan > 1)
        if (rows.length === 1) {
            const firstRow = rows[0];
            const firstCell = firstRow.querySelector('td');
            if (firstCell && firstCell.hasAttribute('colspan')) {
                return false; // This is an empty state row
            }
        }

        return true;
    }

    // Helper function to initialize DataTable safely
    function initializeDataTable(tableId, config = {}) {
        if (hasDataRows(tableId)) {
            try {
                console.log(`Initializing DataTable for ${tableId}`);
                const table = $(tableId).DataTable({
                    ...commonConfig,
                    ...config
                });
                console.log(`Successfully initialized DataTable for ${tableId}`);
                return table;
            } catch (error) {
                console.warn(`Failed to initialize DataTable for ${tableId}:`, error);
                return null;
            }
        } else {
            console.log(`Skipping DataTable initialization for ${tableId} - no data rows found`);
            return null;
        }
    }

    // Common DataTables configuration
    const commonConfig = {
        responsive: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        language: {
            search: "Search:",
            lengthMenu: "Show _MENU_ entries",
            info: "Showing _START_ to _END_ of _TOTAL_ entries",
            infoEmpty: "Showing 0 to 0 of 0 entries",
            infoFiltered: "(filtered from _MAX_ total entries)",
            paginate: {
                first: "First",
                last: "Last",
                next: "Next",
                previous: "Previous"
            }
        },
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        order: [[0, 'asc']] // Default sort by first column
    };

    // Initialize DataTables for Account Requests tables
    initializeDataTable('#pending-requests-table', {
        order: [[3, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#approved-requests-table', {
        order: [[3, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#denied-requests-table', {
        order: [[3, 'desc']] // Sort by Created At column (newest first)
    });

    // Initialize DataTables for Lab tables
    initializeDataTable('#active-labs-table', {
        order: [[4, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#inactive-labs-table', {
        order: [[4, 'desc']] // Sort by Created At column (newest first)
    });

    // Initialize DataTables for Lab User tables
    initializeDataTable('#active-lab-users-table', {
        order: [[5, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#inactive-lab-users-table', {
        order: [[5, 'desc']] // Sort by Created At column (newest first)
    });

    // Initialize DataTables for User tables
    initializeDataTable('#active-users-table', {
        order: [[6, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#inactive-users-table', {
        order: [[6, 'desc']] // Sort by Created At column (newest first)
    });

    // Initialize DataTables for Lab Details Project tables
    initializeDataTable('#lab-cb-projects-table', {
        order: [[5, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#lab-be-projects-table', {
        order: [[4, 'desc']] // BE Projects - Created At is column 4
    });

    initializeDataTable('#lab-assertions-table', {
        order: [[5, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#lab-themelinks-table', {
        order: [[4, 'desc']] // Themelinks - Created At is column 4
    });

    // Initialize DataTables for User Details Project tables
    initializeDataTable('#user-cb-projects-table', {
        order: [[5, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#user-be-projects-table', {
        order: [[4, 'desc']] // BE Projects - Created At is column 4
    });

    initializeDataTable('#user-assertions-table', {
        order: [[5, 'desc']] // Sort by Created At column (newest first)
    });

    initializeDataTable('#user-themelinks-table', {
        order: [[4, 'desc']] // Themelinks - Created At is column 4
    });

    // Add a small indicator to show DataTables is loaded
    console.log('DataTables initialization complete');

    // Optional: Add a visual indicator for debugging
    if (document.querySelector('.dataTables_wrapper')) {
        console.log('DataTables successfully applied to at least one table');
    }
});
