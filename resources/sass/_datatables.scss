// DataTables Bootstrap 5 customizations
.dataTables_wrapper {
    // Add a subtle border to indicate DataTables is active
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    .dataTables_length,
    .dataTables_filter,
    .dataTables_info,
    .dataTables_paginate {
        margin-bottom: 1rem;
    }

    .dataTables_length {
        select {
            @extend .form-select;
            @extend .form-select-sm;
            width: auto;
            display: inline-block;
        }
    }

    .dataTables_filter {
        input {
            @extend .form-control;
            @extend .form-control-sm;
            width: auto;
            display: inline-block;
            margin-left: 0.5rem;
        }
    }

    .dataTables_paginate {
        .paginate_button {
            @extend .page-link;
            border: 1px solid var(--bs-border-color);
            margin: 0 2px;
            
            &.current {
                @extend .active;
                background-color: var(--bs-primary);
                border-color: var(--bs-primary);
                color: white;
            }
            
            &.disabled {
                @extend .disabled;
                opacity: 0.6;
                pointer-events: none;
            }
        }
    }

    .dataTables_info {
        color: var(--bs-secondary-color);
        font-size: 0.875rem;
    }

    // Ensure table headers are properly styled
    table.dataTable {
        thead {
            th {
                border-bottom: 2px solid var(--bs-border-color);
                font-weight: 600;
                
                &.sorting,
                &.sorting_asc,
                &.sorting_desc {
                    cursor: pointer;
                    position: relative;
                    
                    &:after {
                        position: absolute;
                        right: 8px;
                        top: 50%;
                        transform: translateY(-50%);
                        opacity: 0.5;
                        font-size: 0.8em;
                    }
                    
                    &.sorting:after {
                        content: "↕";
                    }
                    
                    &.sorting_asc:after {
                        content: "↑";
                        opacity: 1;
                    }
                    
                    &.sorting_desc:after {
                        content: "↓";
                        opacity: 1;
                    }
                }
            }
        }
        
        tbody {
            tr {
                &:hover {
                    background-color: var(--bs-table-hover-bg);
                }
            }
        }
    }

    // Responsive adjustments
    @media (max-width: 768px) {
        .dataTables_length,
        .dataTables_filter {
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .dataTables_info,
        .dataTables_paginate {
            text-align: center;
        }
    }
}

// Dark theme adjustments
[data-bs-theme="dark"] {
    .dataTables_wrapper {
        .dataTables_filter input {
            background-color: var(--bs-dark);
            border-color: var(--bs-border-color);
            color: var(--bs-body-color);
        }
        
        .dataTables_length select {
            background-color: var(--bs-dark);
            border-color: var(--bs-border-color);
            color: var(--bs-body-color);
        }
    }
}
