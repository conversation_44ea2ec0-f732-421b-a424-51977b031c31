<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BlockIpMiddlewareTest extends TestCase
{
    public function test_blocked_ip_gets_403()
    {
        $response = $this->withServerVariables([
            'REMOTE_ADDR' => '***************', // Blocked IP
        ])->get('/');

        $response->assertStatus(403);
    }
}
