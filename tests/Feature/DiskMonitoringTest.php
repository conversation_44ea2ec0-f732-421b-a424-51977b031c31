<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Cache;
use App\Notifications\DiskSpaceLowNotification;
use App\Notifications\DiskSpaceFullNotification;

class DiskMonitoringTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
        Cache::flush();
    }

    /** @test */
    public function it_can_check_disk_usage_successfully()
    {
        $exitCode = Artisan::call('disk:check');

        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('Starting disk usage monitoring', Artisan::output());
    }

    /** @test */
    public function it_sends_warning_notification_when_threshold_exceeded()
    {
        // Set a very low warning threshold to trigger notification
        $exitCode = Artisan::call('disk:check', [
            '--warning-threshold' => 1,
            '--critical-threshold' => 99,
            '--notify-emails' => '<EMAIL>'
        ]);
        
        $this->assertEquals(0, $exitCode);
        
        // Check that a warning notification was sent
        Notification::assertSentTo(
            new \Illuminate\Notifications\AnonymousNotifiable,
            DiskSpaceLowNotification::class
        );
    }

    /** @test */
    public function it_sends_critical_notification_when_critical_threshold_exceeded()
    {
        // Set very low thresholds to trigger critical notification
        $exitCode = Artisan::call('disk:check', [
            '--warning-threshold' => 1,
            '--critical-threshold' => 2,
            '--notify-emails' => '<EMAIL>'
        ]);
        
        $this->assertEquals(0, $exitCode);
        
        // Check that a critical notification was sent
        Notification::assertSentTo(
            new \Illuminate\Notifications\AnonymousNotifiable,
            DiskSpaceFullNotification::class
        );
    }

    /** @test */
    public function it_respects_notification_throttling()
    {
        // First call should send notification
        Artisan::call('disk:check', [
            '--warning-threshold' => 1,
            '--critical-threshold' => 99,
            '--notify-emails' => '<EMAIL>'
        ]);
        
        // Reset notification fake to clear previous notifications
        Notification::fake();
        
        // Second call should be throttled
        Artisan::call('disk:check', [
            '--warning-threshold' => 1,
            '--critical-threshold' => 99,
            '--notify-emails' => '<EMAIL>'
        ]);
        
        // Should not send another notification due to throttling
        Notification::assertNothingSent();
        $this->assertStringContainsString('notification throttled', Artisan::output());
    }

    /** @test */
    public function it_can_be_disabled_via_configuration()
    {
        config(['disk-monitoring.schedule.enabled' => false]);
        
        $exitCode = Artisan::call('disk:check');
        
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('Disk monitoring is disabled', Artisan::output());
    }

    /** @test */
    public function it_handles_invalid_path_gracefully()
    {
        $exitCode = Artisan::call('disk:check', [
            '--path' => '/nonexistent/path'
        ]);
        
        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('Could not retrieve disk usage information', Artisan::output());
    }

    /** @test */
    public function it_validates_email_addresses()
    {
        $exitCode = Artisan::call('disk:check', [
            '--warning-threshold' => 1,
            '--notify-emails' => 'invalid-email,<EMAIL>,another-invalid'
        ]);

        $this->assertEquals(0, $exitCode);

        // Should only send to valid email
        Notification::assertSentTo(
            new \Illuminate\Notifications\AnonymousNotifiable,
            DiskSpaceLowNotification::class,
            function ($notification, $channels, $notifiable) {
                return $notifiable->routes['mail'] === '<EMAIL>';
            }
        );
    }

    /** @test */
    public function it_can_list_available_disks()
    {
        $exitCode = Artisan::call('disk:check', ['--list-disks' => true]);

        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('Available disks and mount points:', Artisan::output());
    }

    /** @test */
    public function it_can_monitor_all_disks()
    {
        // Mock the notification sending to avoid database issues
        config(['disk-monitoring.notifications.fallback_to_admins' => false]);

        $exitCode = Artisan::call('disk:check', [
            '--all-disks' => true,
            '--notify-emails' => '<EMAIL>'
        ]);

        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('Starting disk usage monitoring', Artisan::output());
    }

    /** @test */
    public function it_respects_exclude_paths()
    {
        // Mock the notification sending to avoid database issues
        config(['disk-monitoring.notifications.fallback_to_admins' => false]);

        $exitCode = Artisan::call('disk:check', [
            '--all-disks' => true,
            '--exclude' => '/proc,/sys',
            '--notify-emails' => '<EMAIL>'
        ]);

        $this->assertEquals(0, $exitCode);
        // Should not contain excluded paths in output
        $output = Artisan::output();
        $this->assertStringNotContainsString('📁 Checking: /proc', $output);
        $this->assertStringNotContainsString('📁 Checking: /sys', $output);
    }

    /** @test */
    public function it_can_monitor_all_disks_via_configuration()
    {
        config(['disk-monitoring.monitor_all_disks' => true]);
        config(['disk-monitoring.notifications.fallback_to_admins' => false]);

        $exitCode = Artisan::call('disk:check', [
            '--notify-emails' => '<EMAIL>'
        ]);

        $this->assertEquals(0, $exitCode);
        $this->assertStringContainsString('Starting disk usage monitoring', Artisan::output());
    }
}
